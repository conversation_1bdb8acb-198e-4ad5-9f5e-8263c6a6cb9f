package com.ruoyi.jiangshan.controller;

import java.util.List;

import com.ruoyi.jiangshan.domain.WarningRuleShuxin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jiangshan.domain.WarningModel;
import com.ruoyi.jiangshan.service.IWarningModelService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 设备模型Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/deviceModel")
public class WarningModelController extends BaseController
{
    @Autowired
    private IWarningModelService warningModelService;

    /**
     * 查询设备模型列表
     */
//    @PreAuthorize("@ss.hasPermi('deviceModel:deviceModel:list')")
    @GetMapping("/list")
    public TableDataInfo list(WarningModel warningModel)
    {
        startPage();
        List<WarningModel> list = warningModelService.selectWarningModelList(warningModel);
        return getDataTable(list);
    }

    /**
     * 导出设备模型列表
     */
//    @PreAuthorize("@ss.hasPermi('deviceModel:deviceModel:export')")
//    @Log(title = "设备模型", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, WarningModel warningModel)
//    {
//        List<WarningModel> list = warningModelService.selectWarningModelList(warningModel);
//        ExcelUtil<WarningModel> util = new ExcelUtil<WarningModel>(WarningModel.class);
//        util.exportExcel(response, list, "设备模型数据");
//    }

    /**
     * 获取设备模型详细信息
     */
//    @PreAuthorize("@ss.hasPermi('deviceModel:deviceModel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(warningModelService.selectWarningModelById(id));
    }

    /**
     * 新增设备模型
     */
//    @PreAuthorize("@ss.hasPermi('deviceModel:deviceModel:add')")
    @Log(title = "设备模型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WarningModel warningModel)
    {
        return toAjax(warningModelService.insertWarningModel(warningModel));
    }

    /**
     * 修改设备模型
     */
//    @PreAuthorize("@ss.hasPermi('deviceModel:deviceModel:edit')")
    @Log(title = "设备模型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WarningModel warningModel)
    {
        return toAjax(warningModelService.updateWarningModel(warningModel));
    }

    /**
     * 删除设备模型
     */
//    @PreAuthorize("@ss.hasPermi('deviceModel:deviceModel:remove')")
    @Log(title = "设备模型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(warningModelService.deleteWarningModelByIds(ids));
    }

    /**
     * 保存数芯模型
     */
//    @PreAuthorize("@ss.hasPermi('deviceModel:deviceModel:remove')")
    @PostMapping("/saveSxModel")
    public AjaxResult saveSxModel(@RequestBody WarningRuleShuxin warningRuleShuxin)
    {
        warningModelService.saveSxModel(warningRuleShuxin);
        return AjaxResult.success();
    }

    /**
     * 查询数芯模型
     */
//    @PreAuthorize("@ss.hasPermi('deviceModel:deviceModel:remove')")
    @GetMapping("/getSxModel/{id}")
    public AjaxResult getSxModel(@PathVariable Long id)
    {
        return AjaxResult.success(warningModelService.getSxModel(id));
    }

    /**
     * 查询行政区划
     */
//    @PreAuthorize("@ss.hasPermi('deviceModel:deviceModel:remove')")
    @GetMapping("/getArea")
    public AjaxResult getArea()
    {
        return AjaxResult.success(warningModelService.getArea());
    }
}
