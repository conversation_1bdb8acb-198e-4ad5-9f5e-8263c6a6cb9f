<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.CitysafetyReportMapper">

    <resultMap type="CitysafetyReport" id="CitysafetyReportResult">
        <result property="id"    column="id"    />
        <result property="reportPeriods"    column="report_periods"    />
        <result property="reportName"    column="report_name"    />
        <result property="reportScene"    column="report_scene"    />
        <result property="reportType"    column="report_type"    />
        <result property="generateType"    column="generate_type"    />
        <result property="monitorStartTime"    column="monitor_start_time"    />
        <result property="monitorEndTime"    column="monitor_end_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="streetId"    column="street_id"    />
        <result property="streetName"    column="street_name"    />
    </resultMap>

    <sql id="selectCitysafetyReportVo">
        select id, report_periods, report_name, report_scene, report_type, generate_type, monitor_start_time,
               monitor_end_time, create_time, create_by, street_id, street_name from t_citysafety_report
    </sql>

    <select id="selectCitysafetyReportList" parameterType="CitysafetyReport" resultMap="CitysafetyReportResult">
        <include refid="selectCitysafetyReportVo"/>
        <where>
<!--            <if test="reportPeriods != null  and reportPeriods != ''"> and report_periods = #{reportPeriods}</if>-->
            <if test="reportName != null  and reportName != ''"> and report_name like concat('%', #{reportName}, '%')</if>
            <if test="reportScene != null  and reportScene != ''"> and report_scene = #{reportScene}</if>
            <if test="reportType != null "> and report_type = #{reportType}</if>
<!--            <if test="generateType != null "> and generate_type = #{generateType}</if>-->
            <if test="monitorStartTime != null "> and monitor_start_time <![CDATA[ >= ]]> #{monitorStartTime}</if>
            <if test="monitorEndTime != null "> and monitor_end_time <![CDATA[ <= ]]> #{monitorEndTime}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectCitysafetyReportById" parameterType="Long" resultMap="CitysafetyReportResult">
        <include refid="selectCitysafetyReportVo"/>
        where id = #{id}
    </select>

    <insert id="insertCitysafetyReport" parameterType="CitysafetyReport" useGeneratedKeys="true" keyProperty="id">
        insert into t_citysafety_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportPeriods != null">report_periods,</if>
            <if test="reportName != null">report_name,</if>
            <if test="reportScene != null">report_scene,</if>
            <if test="reportType != null">report_type,</if>
            <if test="generateType != null">generate_type,</if>
            <if test="monitorStartTime != null">monitor_start_time,</if>
            <if test="monitorEndTime != null">monitor_end_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="streetId != null">street_id,</if>
            <if test="streetName != null">street_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportPeriods != null">#{reportPeriods},</if>
            <if test="reportName != null">#{reportName},</if>
            <if test="reportScene != null">#{reportScene},</if>
            <if test="reportType != null">#{reportType},</if>
            <if test="generateType != null">#{generateType},</if>
            <if test="monitorStartTime != null">#{monitorStartTime,jdbcType=TIMESTAMP},</if>
            <if test="monitorEndTime != null">#{monitorEndTime, jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="streetId != null">#{streetId},</if>
            <if test="streetName != null">#{streetName},</if>
         </trim>
    </insert>

    <update id="updateCitysafetyReport" parameterType="CitysafetyReport">
        update t_citysafety_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="reportPeriods != null">report_periods = #{reportPeriods},</if>
            <if test="reportName != null">report_name = #{reportName},</if>
            <if test="reportScene != null">report_scene = #{reportScene},</if>
            <if test="reportType != null">report_type = #{reportType},</if>
            <if test="generateType != null">generate_type = #{generateType},</if>
            <if test="monitorStartTime != null">monitor_start_time = #{monitorStartTime},</if>
            <if test="monitorEndTime != null">monitor_end_time = #{monitorEndTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="streetId != null">street_id = #{streetId},</if>
            <if test="streetName != null">street_name = #{streetName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCitysafetyReportById" parameterType="Long">
        delete from t_citysafety_report where id = #{id}
    </delete>

    <delete id="deleteCitysafetyReportByIds" parameterType="String">
        delete from t_citysafety_report where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into t_citysafety_report
        (report_periods, report_name, report_scene, report_type, generate_type, monitor_start_time,
        monitor_end_time, create_time, create_by, street_id, street_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.reportPeriods,jdbcType=VARCHAR}, #{item.reportName,jdbcType=VARCHAR}, #{item.reportScene,jdbcType=VARCHAR},
            #{item.reportType,jdbcType=BOOLEAN}, #{item.generateType,jdbcType=BOOLEAN}, #{item.monitorStartTime,jdbcType=TIMESTAMP},
            #{item.monitorEndTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createBy,jdbcType=VARCHAR}, #{item.streetId,jdbcType=VARCHAR}, #{item.streetName,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="countAll" resultType="long">
        select count(1) from t_citysafety_report
    </select>
</mapper>
