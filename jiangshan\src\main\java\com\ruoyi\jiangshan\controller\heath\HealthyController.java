package com.ruoyi.jiangshan.controller.heath;

import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 负载均衡健康监测
 */
@RestController
@RequestMapping("/healthy")
public class HealthyController {
    @Value("${server.port}")
    private String serverPort;

    @GetMapping("/port")
    public AjaxResult getPort() {
        return AjaxResult.success(serverPort);
    }
}
