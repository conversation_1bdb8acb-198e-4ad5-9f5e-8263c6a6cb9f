package com.ruoyi.quartz.task;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.jiangshan.domain.CitysafetyPeriods;
import com.ruoyi.jiangshan.domain.CitysafetyReport;
import com.ruoyi.jiangshan.domain.CitysafetyRule;
import com.ruoyi.jiangshan.enums.WarningCenterGenerateType;
import com.ruoyi.jiangshan.enums.WarningCenterReportType;
import com.ruoyi.jiangshan.mapper.CitysafetyPeriodsMapper;
import com.ruoyi.jiangshan.mapper.CitysafetyReportMapper;
import com.ruoyi.jiangshan.mapper.CitysafetyRuleMapper;
import com.ruoyi.jiangshan.service.impl.CitysafetyReportServiceImpl;
import com.ruoyi.jiangshan.vo.CitySafetyQuartzTimeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component("warningCenterTask")
public class WarningCenterTask {

    public void dayTask() {
        CitysafetyRuleMapper citysafetyRuleMapper = SpringUtils.getBean(CitysafetyRuleMapper.class);
        CitysafetyReportMapper citysafetyReportMapper = SpringUtils.getBean(CitysafetyReportMapper.class);
        CitysafetyReportServiceImpl citysafetyReportService = SpringUtils.getBean(CitysafetyReportServiceImpl.class);

        WarningCenterReportType reportType = WarningCenterReportType.TYPE_01;

        List<CitysafetyRule> citysafetyRuleList = citysafetyRuleMapper.listByFrequnce(reportType.getCode());
        if (CollectionUtils.isEmpty(citysafetyRuleList)) {
            return;
        }

        for (CitysafetyRule citysafetyRule : citysafetyRuleList) {
            //获得开始和结束时间
            CitySafetyQuartzTimeVO timeVO = getQuartzTime(reportType, citysafetyRule, citysafetyReportService);

            generateReportList(timeVO, citysafetyRule, citysafetyReportMapper);
        }
    }


    public void weekTask() {
        CitysafetyRuleMapper citysafetyRuleMapper = SpringUtils.getBean(CitysafetyRuleMapper.class);
        CitysafetyReportMapper citysafetyReportMapper = SpringUtils.getBean(CitysafetyReportMapper.class);
        CitysafetyReportServiceImpl citysafetyReportService = SpringUtils.getBean(CitysafetyReportServiceImpl.class);

        WarningCenterReportType reportType = WarningCenterReportType.TYPE_02;

        List<CitysafetyRule> citysafetyRuleList = citysafetyRuleMapper.listByFrequnce(reportType.getCode());
        if (CollectionUtils.isEmpty(citysafetyRuleList)) {
            return;
        }

        for (CitysafetyRule citysafetyRule : citysafetyRuleList) {
            //获得开始和结束时间
            CitySafetyQuartzTimeVO timeVO = getQuartzTime(reportType, citysafetyRule, citysafetyReportService);

            generateReportList(timeVO, citysafetyRule, citysafetyReportMapper);
        }
    }

    public void monthTask() {
        CitysafetyRuleMapper citysafetyRuleMapper = SpringUtils.getBean(CitysafetyRuleMapper.class);
        CitysafetyReportMapper citysafetyReportMapper = SpringUtils.getBean(CitysafetyReportMapper.class);
        CitysafetyReportServiceImpl citysafetyReportService = SpringUtils.getBean(CitysafetyReportServiceImpl.class);

        WarningCenterReportType reportType = WarningCenterReportType.TYPE_03;

        List<CitysafetyRule> citysafetyRuleList = citysafetyRuleMapper.listByFrequnce(reportType.getCode());
        if (CollectionUtils.isEmpty(citysafetyRuleList)) {
            return;
        }

        for (CitysafetyRule citysafetyRule : citysafetyRuleList) {
            //获得开始和结束时间
            CitySafetyQuartzTimeVO timeVO = getQuartzTime(reportType, citysafetyRule, citysafetyReportService);

            generateReportList(timeVO, citysafetyRule, citysafetyReportMapper);
        }
    }

    public void halfYearTask() {
        CitysafetyRuleMapper citysafetyRuleMapper = SpringUtils.getBean(CitysafetyRuleMapper.class);
        CitysafetyReportMapper citysafetyReportMapper = SpringUtils.getBean(CitysafetyReportMapper.class);
        CitysafetyReportServiceImpl citysafetyReportService = SpringUtils.getBean(CitysafetyReportServiceImpl.class);

        WarningCenterReportType reportType = WarningCenterReportType.TYPE_04;

        List<CitysafetyRule> citysafetyRuleList = citysafetyRuleMapper.listByFrequnce(reportType.getCode());
        if (CollectionUtils.isEmpty(citysafetyRuleList)) {
            return;
        }

        for (CitysafetyRule citysafetyRule : citysafetyRuleList) {
            //获得开始和结束时间
            CitySafetyQuartzTimeVO timeVO = getQuartzTime(reportType, citysafetyRule, citysafetyReportService);

            generateReportList(timeVO, citysafetyRule, citysafetyReportMapper);
        }
    }

    public void yearTask() {
        CitysafetyRuleMapper citysafetyRuleMapper = SpringUtils.getBean(CitysafetyRuleMapper.class);
        CitysafetyReportMapper citysafetyReportMapper = SpringUtils.getBean(CitysafetyReportMapper.class);
        CitysafetyReportServiceImpl citysafetyReportService = SpringUtils.getBean(CitysafetyReportServiceImpl.class);

        WarningCenterReportType reportType = WarningCenterReportType.TYPE_05;

        List<CitysafetyRule> citysafetyRuleList = citysafetyRuleMapper.listByFrequnce(reportType.getCode());
        if (CollectionUtils.isEmpty(citysafetyRuleList)) {
            return;
        }

        for (CitysafetyRule citysafetyRule : citysafetyRuleList) {
            //获得开始和结束时间
            CitySafetyQuartzTimeVO timeVO = getQuartzTime(reportType, citysafetyRule, citysafetyReportService);

            generateReportList(timeVO, citysafetyRule, citysafetyReportMapper);
        }
    }

    private CitySafetyQuartzTimeVO getQuartzTime(WarningCenterReportType type,
                                                 CitysafetyRule citysafetyRule, CitysafetyReportServiceImpl citysafetyReportService) {
        Calendar calendar = Calendar.getInstance();

        CitySafetyQuartzTimeVO timeVO = new CitySafetyQuartzTimeVO();

        CitysafetyPeriods citysafetyPeriods = citysafetyReportService.getCitysafetyPeriods(calendar,
                citysafetyRule.getGenerateType(), citysafetyRule.getScene());

        timeVO.setCitysafetyPeriods(citysafetyPeriods);

        Date startTime = null;
        Date endTime = null;

        switch (type) {
            case TYPE_01: {
                // 设置时间为当天的0时0分0秒
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);

                // 减去一天
                calendar.add(Calendar.DAY_OF_MONTH, -1);

                // 获取前一天0时0分0秒的 Date 对象
                startTime = calendar.getTime();

                // 设置时间为当天的23时59分59秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 0);

                endTime = calendar.getTime();

                break;
            }
            case TYPE_02: {
                // 计算上周一的日期
                Calendar lastMonday = (Calendar) calendar.clone();
                lastMonday.add(Calendar.WEEK_OF_YEAR, -1);
                lastMonday.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                lastMonday.set(Calendar.HOUR_OF_DAY, 0);
                lastMonday.set(Calendar.MINUTE, 0);
                lastMonday.set(Calendar.SECOND, 0);
                lastMonday.set(Calendar.MILLISECOND, 0);

                // 计算上周日的日期
                Calendar lastSunday = (Calendar) calendar.clone();
                lastSunday.add(Calendar.WEEK_OF_YEAR, -1);
                lastSunday.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
                lastSunday.add(Calendar.DAY_OF_MONTH, 1);
                lastSunday.set(Calendar.HOUR_OF_DAY, 23);
                lastSunday.set(Calendar.MINUTE, 59);
                lastSunday.set(Calendar.SECOND, 59);
                lastSunday.set(Calendar.MILLISECOND, 0);

                // 创建 Date 对象
                startTime = lastMonday.getTime();
                endTime = lastSunday.getTime();

                break;
            }
            case TYPE_03: {
                // 计算上个月的第一天
                Calendar firstDayOfLastMonth = (Calendar) calendar.clone();
                firstDayOfLastMonth.add(Calendar.MONTH, -1);
                firstDayOfLastMonth.set(Calendar.DAY_OF_MONTH, 1);
                firstDayOfLastMonth.set(Calendar.HOUR_OF_DAY, 0);
                firstDayOfLastMonth.set(Calendar.MINUTE, 0);
                firstDayOfLastMonth.set(Calendar.SECOND, 0);
                firstDayOfLastMonth.set(Calendar.MILLISECOND, 0);

                // 计算上个月的最后一天
                Calendar lastDayOfLastMonth = (Calendar) firstDayOfLastMonth.clone();
                lastDayOfLastMonth.add(Calendar.MONTH, 1);
                lastDayOfLastMonth.add(Calendar.DATE, -1);
                lastDayOfLastMonth.set(Calendar.HOUR_OF_DAY, 23);
                lastDayOfLastMonth.set(Calendar.MINUTE, 59);
                lastDayOfLastMonth.set(Calendar.SECOND, 59);
                lastDayOfLastMonth.set(Calendar.MILLISECOND, 0);

                // 创建 Date 对象
                startTime = firstDayOfLastMonth.getTime();
                endTime = lastDayOfLastMonth.getTime();

                break;
            }
            case TYPE_04: {
                // 获取当前月份
                int currentMonth = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH 从 0 开始

                // 判断当前月份属于上半年还是下半年
                boolean isFirstHalf = currentMonth <= 6;

                // 计算上一个半年的第一天
                Calendar firstDayOfPreviousHalf = (Calendar) calendar.clone();
                if (isFirstHalf) {
                    // 当前月份在上半年，上一个半年是去年的7月1日
                    firstDayOfPreviousHalf.set(Calendar.YEAR, calendar.get(Calendar.YEAR) - 1);
                    firstDayOfPreviousHalf.set(Calendar.MONTH, Calendar.JULY);
                    firstDayOfPreviousHalf.set(Calendar.DAY_OF_MONTH, 1);
                } else {
                    // 当前月份在下半年，上一个半年是今年的1月1日
                    firstDayOfPreviousHalf.set(Calendar.MONTH, Calendar.JANUARY);
                    firstDayOfPreviousHalf.set(Calendar.DAY_OF_MONTH, 1);
                }
                firstDayOfPreviousHalf.set(Calendar.HOUR_OF_DAY, 0);
                firstDayOfPreviousHalf.set(Calendar.MINUTE, 0);
                firstDayOfPreviousHalf.set(Calendar.SECOND, 0);
                firstDayOfPreviousHalf.set(Calendar.MILLISECOND, 0);

                // 计算上一个半年的最后一天
                Calendar lastDayOfPreviousHalf = (Calendar) firstDayOfPreviousHalf.clone();
                if (isFirstHalf) {
                    // 当前月份在上半年，上一个半年是去年的12月31日
                    lastDayOfPreviousHalf.set(Calendar.MONTH, Calendar.DECEMBER);
                    lastDayOfPreviousHalf.set(Calendar.DAY_OF_MONTH, 31);
                } else {
                    // 当前月份在下半年，上一个半年是今年的6月30日
                    lastDayOfPreviousHalf.set(Calendar.MONTH, Calendar.JUNE);
                    lastDayOfPreviousHalf.set(Calendar.DAY_OF_MONTH, 30);
                }
                lastDayOfPreviousHalf.set(Calendar.HOUR_OF_DAY, 23);
                lastDayOfPreviousHalf.set(Calendar.MINUTE, 59);
                lastDayOfPreviousHalf.set(Calendar.SECOND, 59);
                // 设置毫秒为最大值
                lastDayOfPreviousHalf.set(Calendar.MILLISECOND, 0);

                // 创建 Date 对象
                startTime = firstDayOfPreviousHalf.getTime();
                endTime = lastDayOfPreviousHalf.getTime();

                break;
            }
            case TYPE_05: {
                // 计算上一年1月1号的日期
                Calendar firstDayOfYear = (Calendar) calendar.clone();
                firstDayOfYear.add(Calendar.YEAR, -1);
                firstDayOfYear.set(Calendar.MONTH, Calendar.JANUARY);
                firstDayOfYear.set(Calendar.DAY_OF_MONTH, 1);
                firstDayOfYear.set(Calendar.HOUR_OF_DAY, 0);
                firstDayOfYear.set(Calendar.MINUTE, 0);
                firstDayOfYear.set(Calendar.SECOND, 0);
                firstDayOfYear.set(Calendar.MILLISECOND, 0);

                // 计算上一年最后一天的日期
                Calendar lastDayOfYear = (Calendar) firstDayOfYear.clone();
                lastDayOfYear.set(Calendar.MONTH, Calendar.DECEMBER);
                lastDayOfYear.set(Calendar.DAY_OF_MONTH, 31);
                lastDayOfYear.set(Calendar.HOUR_OF_DAY, 23);
                lastDayOfYear.set(Calendar.MINUTE, 59);
                lastDayOfYear.set(Calendar.SECOND, 59);
                lastDayOfYear.set(Calendar.MILLISECOND, 0); // 设置毫秒为最大值

                // 创建 Date 对象
                startTime = firstDayOfYear.getTime();
                endTime = lastDayOfYear.getTime();

                break;
            }
            default: {
                throw new RuntimeException("未知的更新频率");
            }
        }

        timeVO.setStartTime(startTime);
        timeVO.setEndTime(endTime);

        return timeVO;
    }

    private void generateReportList(CitySafetyQuartzTimeVO timeVO,
                                    CitysafetyRule citysafetyRule,
                                    CitysafetyReportMapper citysafetyReportMapper) {
        String scene = citysafetyRule.getScene();
        CitysafetyPeriods citysafetyPeriods = timeVO.getCitysafetyPeriods();
        Date startTime = timeVO.getStartTime();
        Date endTime = timeVO.getEndTime();

        String[] sceneArray = StringUtils.split(scene, ",");

        Integer generateType = citysafetyRule.getGenerateType();
        if (Objects.equals(generateType, WarningCenterGenerateType.TYPE_01.getCode())) {
            CitysafetyReport citysafetyReport = new CitysafetyReport();
            citysafetyReport.setReportPeriods(getReportPeriodsStr(citysafetyPeriods));
            citysafetyReport.setReportName(getReportName(citysafetyPeriods, citysafetyRule.getGenerateFrequency(),
                    startTime, citysafetyRule.getScene()));
            citysafetyReport.setReportScene(citysafetyRule.getScene());
            citysafetyReport.setReportType(citysafetyRule.getGenerateFrequency());
            citysafetyReport.setGenerateType(citysafetyRule.getGenerateType());
            citysafetyReport.setMonitorStartTime(startTime);
            citysafetyReport.setMonitorEndTime(endTime);
            citysafetyReport.setCreateTime(new Date());
            citysafetyReport.setCreateBy("自动生成");

            citysafetyReportMapper.insertCitysafetyReport(citysafetyReport);
        } else if (Objects.equals(generateType, WarningCenterGenerateType.TYPE_02.getCode())) {
            List<CitysafetyReport> reportList = Lists.newArrayList();

            for (String sceneSplit : sceneArray) {
                CitysafetyReport citysafetyReport = new CitysafetyReport();
                citysafetyReport.setReportPeriods(getReportPeriodsStr(citysafetyPeriods));
                citysafetyReport.setReportName(getReportName(citysafetyPeriods, citysafetyRule.getGenerateFrequency(),
                        startTime, sceneSplit));
                citysafetyReport.setReportScene(sceneSplit);
                citysafetyReport.setReportType(citysafetyRule.getGenerateFrequency());
                citysafetyReport.setGenerateType(citysafetyRule.getGenerateType());
                citysafetyReport.setMonitorStartTime(startTime);
                citysafetyReport.setMonitorEndTime(endTime);
                citysafetyReport.setCreateTime(new Date());
                citysafetyReport.setCreateBy("自动生成");

                reportList.add(citysafetyReport);
            }

            citysafetyReportMapper.batchInsert(reportList);
        }
    }

    private String getReportName(CitysafetyPeriods citysafetyPeriods, Integer generateFrequency,
                                 Date startTime, String scene) {
        StringBuilder sb = new StringBuilder();
        sb.append("城市运行报告");

        if (Objects.equals(generateFrequency, WarningCenterReportType.TYPE_01.getCode())) {
            //日报
            String dateStr = DateUtils.parseDateToStr("yyyyMMdd", startTime);
            sb.append(dateStr);
            sb.append(WarningCenterReportType.TYPE_01.getDesc());
        } else if (Objects.equals(generateFrequency, WarningCenterReportType.TYPE_02.getCode())) {
            //周报
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startTime);

            int weekCount = calendar.get(Calendar.WEEK_OF_YEAR);

            sb.append("第");
            sb.append(weekCount);
            sb.append(WarningCenterReportType.TYPE_02.getDesc());
        } else if (Objects.equals(generateFrequency, WarningCenterReportType.TYPE_03.getCode())) {
            //月报
            sb.append(DateUtils.parseDateToStr("yyyy年MM" + WarningCenterReportType.TYPE_03.getDesc(), startTime));
        } else if (Objects.equals(generateFrequency, WarningCenterReportType.TYPE_04.getCode())) {
            //半年报
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startTime);

            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH);


            sb.append(year);
            sb.append("年");
            sb.append(month == 0 ? "上" : "下");
            sb.append("半年报");
        } else if (Objects.equals(generateFrequency, WarningCenterReportType.TYPE_05.getCode())) {
            sb.append(DateUtils.parseDateToStr("yyyy" + WarningCenterReportType.TYPE_05.getDesc(), startTime));
        }

        sb.append("-" + getNameStr(scene));

        return sb.toString();
    }

    private String getNameStr(String sceneStr) {
        String nameStr = sceneStr;
        if (sceneStr.length() > 30) {
            nameStr = "全部场景";
        }

        return nameStr;
    }

    private String getReportPeriodsStr(CitysafetyPeriods citysafetyPeriods) {
        Integer periods = citysafetyPeriods.getNextPeriods();

        NumberFormat formatter = NumberFormat.getInstance();
        formatter.setMinimumIntegerDigits(3);
        formatter.setGroupingUsed(false);
        String formattedNumber = formatter.format(periods);

        return citysafetyPeriods.getYear() + formattedNumber + "期";
    }

}
