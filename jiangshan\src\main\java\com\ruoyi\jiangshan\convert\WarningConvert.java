package com.ruoyi.jiangshan.convert;

import com.alibaba.fastjson.JSON;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.domain.WarningEventJczzLog;
import com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin;
import com.ruoyi.jiangshan.openapi.vo.WarningEventOpenApiVO;
import com.ruoyi.jiangshan.openapi.vo.WarningEventThresholdSaveVO;
import com.ruoyi.jiangshan.vo.jczz.WarningEventAttachmentVO;
import com.ruoyi.jiangshan.vo.jczz.WarningEventJczzFlowVO;
import com.ruoyi.jiangshan.vo.jczz.WarningEventJczzLogVO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper
public interface WarningConvert {
    WarningConvert INSTANCE = Mappers.getMapper(WarningConvert.class);

    WarningEvent openApi2WarningEvent(WarningEventOpenApiVO apiVO);

    WarningRuleConditionShuxin openApi2ConditionShuxin(WarningEventThresholdSaveVO saveVO);

    @Mapping(target = "eventFlowList", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    WarningEventJczzLogVO jczzDO2VO(WarningEventJczzLog warningEventJczzLog);

    @AfterMapping
    default void afterMapping(@MappingTarget WarningEventJczzLogVO target,
                                               WarningEventJczzLog source) {
        target.setAttachments(JSON.parseArray(source.getAttachments(), WarningEventAttachmentVO.class));

        target.setEventFlowList(JSON.parseArray(source.getEventFlowList(), WarningEventJczzFlowVO.class));
    }

    @Mapping(target = "eventFlowList", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    WarningEventJczzLog jczzVO2DO(WarningEventJczzLogVO jczzLogVO);

    @AfterMapping
    default void afterMapping2(WarningEventJczzLogVO source,
                              @MappingTarget WarningEventJczzLog target) {
        target.setAttachments(JSON.toJSONString(source.getAttachments()));

        target.setEventFlowList(JSON.toJSONString(source.getEventFlowList()));
    }
}
