package com.ruoyi.jiangshan.controller.screen;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.service.BridgeSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 桥梁驾驶舱
 */
@RestController
@RequestMapping("/bridge")
public class BridgeSceneController {
    @Autowired
    private BridgeSceneService bridgeSceneService;

    /**
     * 监测设备总览
     * @return
     */
    @GetMapping("/equipment")
    public AjaxResult getEquipment() {
        return AjaxResult.success(bridgeSceneService.getEquipment());
    }

    /**
     * 桥梁监控
     * @return
     */
    @GetMapping("/getMonitor")
    public AjaxResult getBridgeMonitor() {
        return AjaxResult.success(bridgeSceneService.getBridgeMonitor());
    }

    /**
     * 安全风险统计
     * @return
     */
    @GetMapping("/safetyCount")
    public AjaxResult getSafetyCount(String dateStr) {
        return AjaxResult.success(bridgeSceneService.getSafetyCount(dateStr));
    }

    /**
     * 实时预警
     * @return
     */
    @GetMapping("/warning")
    public AjaxResult getWarning(Integer warningLevel) {
        return AjaxResult.success(bridgeSceneService.getWarning(warningLevel));
    }

    /**
     * 安全风险事件排序
     * @return
     */
    @GetMapping("/event")
    public AjaxResult getEvent(String eventName, Integer status, Integer warningLevel) {
        return AjaxResult.success(bridgeSceneService.getEvent(eventName, status, warningLevel));
    }

    /**
     * 桥梁维护统计
     * @return
     */
    @GetMapping("/maintance")
    public AjaxResult getMaintance(Long monitorId) {
        return AjaxResult.success(bridgeSceneService.getMaintance(monitorId));
    }

    /**
     * 获取所有桥梁
     * @return
     */
    @GetMapping("/getAllBridge")
    public AjaxResult getAllBridge() {
        return AjaxResult.success(bridgeSceneService.getAllBridge());
    }

    /**
     * 根据桥梁获取视频监控
     * @return
     */
    @GetMapping("/listBridgeVideo")
    public AjaxResult listBridgeVideo(Long monitorId) {
        return AjaxResult.success(bridgeSceneService.listBridgeVideo(monitorId));
    }

    /**
     * 获取桥梁所有设备
     * @return
     */
    @GetMapping("/listBridgeDevice")
    public AjaxResult listBridgeDevice() {
        return AjaxResult.success(bridgeSceneService.listBridgeDevice());
    }

}
