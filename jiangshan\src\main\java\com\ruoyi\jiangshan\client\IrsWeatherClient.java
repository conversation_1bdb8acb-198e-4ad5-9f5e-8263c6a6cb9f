package com.ruoyi.jiangshan.client;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.zlb.HmacAuthUtil;
import com.ruoyi.jiangshan.constant.IrsConstants;
import com.ruoyi.jiangshan.util.HttpUtil;
import com.ruoyi.jiangshan.vo.irs.IrsRainFallOneHourDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.ruoyi.jiangshan.domain.WarningEventWeather;
import com.ruoyi.jiangshan.mapper.WarningEventWeatherMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class IrsWeatherClient {

    private final String token = "4thRebEe8yhDdNDzdtkE";

    @Autowired
    private WarningEventWeatherMapper warningEventWeatherMapper;

    /**
     * 江山市最近一小时累计实况降水
     */
    public Map getJiangShanRainFallOneHour() {
        String url = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020250609000001/gridmete-api/index.php/portal/index/getawsdata";

        String method = "POST";

        IrsRainFallOneHourDTO reqDTO = new IrsRainFallOneHourDTO();
        reqDTO.setToken(token);

        Map params = JSON.parseObject(JSON.toJSONString(reqDTO), Map.class);

        Map resultMap = sendIrsRemote(url, method, params);

        String data = JSON.toJSONString(resultMap.get("data"));

        Map res = JSON.parseObject(data, Map.class);

        return res;
    }

    /**
     * 江山市智能网格预报3小时累计降水
     */
    public Map getJiangShanRainFallThreeHour() {
        String url = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020250609000002/gridmete-api/index.php/portal/fcst/map";

        String method = "POST";

        IrsRainFallOneHourDTO reqDTO = new IrsRainFallOneHourDTO();
        reqDTO.setToken(token);

        // 生成dtValid字段
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        int year = now.getYear();
        int month = now.getMonthValue();
        int day = now.getDayOfMonth();
        int hour = now.getHour();
        // 预报时次列表
        int[] validHours = {2, 5, 8, 11, 14, 17, 20, 23};
        int nearest = validHours[0];
        for (int h : validHours) {
            if (hour <= h) {
                nearest = h;
                break;
            }
        }
        // 如果当前小时大于23点，取23点
        if (hour >= 23) nearest = 23;
        String dtValid = String.format("%04d%02d%02d%02d", year, month, day, nearest);
        dtValid = dtValid.substring(2);
        reqDTO.setDtValid(dtValid);

        Map params = JSON.parseObject(JSON.toJSONString(reqDTO), Map.class);

        Map resultMap = sendIrsRemote(url, method, params);

        String data = JSON.toJSONString(resultMap.get("data"));

        Map res = JSON.parseObject(data, Map.class);

        return res;
    }

    /**
     * 江山市实时气象预警信号查询
     */
    public Map getQiXiangWarning() {
        String url = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220818000011/grid-monitor/api/index.php/index/meteWarns";
        String method = "GET";
        Map resultMap = sendIrsRemoteGet(url, method);
        String data = JSON.toJSONString(resultMap.get("data"));
        // 解析为List<Map>
        List<Map> list = JSON.parseObject(data, List.class);
        if (list != null) {
            for (Map item : list) {
                WarningEventWeather weather = new WarningEventWeather();
                weather.setYjPtime((String) item.get("yj_ptime"));
                weather.setYjContent((String) item.get("yj_content"));
                weather.setYjCode((String) item.get("yj_code"));
                weather.setYjText((String) item.get("yj_text"));
                weather.setYjType((String) item.get("yj_type"));
                weather.setYjAddr((String) item.get("yj_addr"));
                weather.setUnitName((String) item.get("unit_name"));
                weather.setYjStatus((String) item.get("yj_status"));
                weather.setYjCh((String) item.get("yj_ch"));
                weather.setYjId((String) item.get("yj_id"));
                weather.setUnitLong((String) item.get("unit_long"));
                weather.setUnitLat((String) item.get("unit_lat"));
                weather.setYjRecovery((String) item.get("yj_recovery"));
                weather.setWlevel((String) item.get("wlevel"));
                // 其他推送相关字段、创建人等可根据实际情况补充
                warningEventWeatherMapper.insert(weather);
            }
        }
        Map res = new HashMap();
        res.put("data", list);
        return res;
    }

    private Map sendIrsRemoteGet(String url, String httpMethod) {
        Map<String, String> headers = HmacAuthUtil.generateHeader(url, httpMethod,
                IrsConstants.COMPONENT_APP_KEY, IrsConstants.COMPONENT_APP_SECRET);

        String response = null;
        try {
            response = HttpUtils.sendGetWithHeader(url, null, headers);
        } catch (Exception e) {
            log.error("method:{}, httpError, error:{}", e.toString());
        }

        Map map =  JSON.parseObject(response, Map.class);

        Integer code = (Integer) map.get("code");

        if (code != 200) {
            log.info("method:{}, response:{}", response);
            throw new RuntimeException("method:, error, response:" + response);
        }

        return map;
    }

    private Map sendIrsRemote(String url, String httpMethod, Map params) {
        Map<String, String> header = HmacAuthUtil.generateHeader(url, httpMethod,
                IrsConstants.COMPONENT_APP_KEY, IrsConstants.COMPONENT_APP_SECRET);

        String response = null;
        try {
            response = HttpUtils.sendPost(url, params, header);
        } catch (Exception e) {
            log.error("url:{}, httpError, error:{}", url, e.toString());
        }

        Map map =  JSON.parseObject(response, Map.class);

        Integer code = (Integer) map.get("code");

        if (code != 0) {
            log.info("url:{}, response:{}", url, response);
            throw new RuntimeException("method:, error, response:" + response);
        }

        return map;
    }
}
