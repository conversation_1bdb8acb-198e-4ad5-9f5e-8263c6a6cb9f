<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceInfoFavoriteMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.DeviceInfoFavorite">
    <!--@mbg.generated-->
    <!--@Table t_device_info_favorite-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="device_id" jdbcType="BIGINT" property="deviceId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, device_id, user_id, create_time, create_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_device_info_favorite
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_device_info_favorite
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoFavorite" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_favorite (device_id, user_id, create_time,
      create_by)
    values (#{deviceId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoFavorite" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_favorite
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deviceId != null">
        #{deviceId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoFavorite">
    <!--@mbg.generated-->
    update t_device_info_favorite
    <set>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoFavorite">
    <!--@mbg.generated-->
    update t_device_info_favorite
    set device_id = #{deviceId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_device_info_favorite
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="device_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_favorite
    (device_id, user_id, create_time, create_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deviceId,jdbcType=BIGINT}, #{item.userId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.createBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="getByUserId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_device_info_favorite
    where user_id = #{userId,jdbcType=BIGINT}
    and device_id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByUserId">
    delete from t_device_info_favorite
    where user_id = #{userId,jdbcType=BIGINT}
    and device_id = #{id,jdbcType=BIGINT}
  </delete>
</mapper>
