package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningInfo;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.jiangshan.vo.*;
import org.apache.ibatis.annotations.Param;

public interface WarningInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningInfo record);

    int insertSelective(WarningInfo record);

    WarningInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningInfo record);

    int updateByPrimaryKey(WarningInfo record);

    int updateBatch(List<WarningInfo> list);

    int batchInsert(@Param("list") List<WarningInfo> list);

    List<BusinessCountVO> countByMonitorType(@Param("list") List<String> sceneList,
                                             @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<WarningInfo> listLastFourWarning();

    long countAll();

    List<WarningInfoVO> listByLevelAndScene(@Param("warningLevel") Integer warningLevel, @Param("scene") String scene);


    List<WarningLevelCountVO> countWarningLevelByScene(@Param("scene") String scene);

    List<BusinessCountVO> countByMonitorScene(@Param("scene") String scene,
                                              @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<WarningLayerVO> listByDate(@Param("scene") String scene,
                                    @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<WarningInfo> listLastByMonitorIdListAndDate(@Param("list") List<Long> monitorIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<BusinessCountVO> countBySceneListAndDate(@Param("sceneList") String[] sceneList,
                                                  @Param("monitorStartTime") Date monitorStartTime,
                                                  @Param("monitorEndTime") Date monitorEndTime);

    List<WarningInfo> listReportLine(@Param("sceneList") String[] sceneList,
                                     @Param("monitorStartTime") Date monitorStartTime,
                                     @Param("monitorEndTime") Date monitorEndTime,
                                     @Param("warningLevelList") List<Integer> warningLevelList);

    List<WarningInfo> listByWarningIdList(@Param("warningIdList") List<Long> warningIdList);

    WarningInfo getByEventThirdId(@Param("eventThirdId") String eventThirdId);

    List<WarningInfo> listByDeviceThird(@Param("deviceThirdIdList") List<String> deviceThirdIdList);

    List<WarningInfo> listByDeviceThirdListAndDate(@Param("deviceThirdIdList") List<String> deviceThirdIdList,
                                                   @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<BusinessCountVO> countByScene(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<WarningOccupationVO> listWarningOccupation();

    List<WarningInfo> listByMonitorIdListAndDate(@Param("monitorIdList") List<Long> monitorIdList,
                                                 @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    WarningInfo checkExistByTime(@Param("deviceThirdId") String deviceThirdId, @Param("monitorItem") String monitorItem, @Param("hour") int hour);

    void deleteByIds(Long[] warningIdArray);

    List<WarningInfo> listPushValue(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
