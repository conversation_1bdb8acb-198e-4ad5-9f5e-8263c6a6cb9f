package com.ruoyi.jiangshan.mapper;

import java.util.List;
import com.ruoyi.jiangshan.domain.CitysafetyRule;
import org.apache.ibatis.annotations.Param;

/**
 * 报告生成管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
public interface CitysafetyRuleMapper
{
    /**
     * 查询报告生成管理
     *
     * @param id 报告生成管理主键
     * @return 报告生成管理
     */
    public CitysafetyRule selectCitysafetyRuleById(Long id);

    /**
     * 查询报告生成管理列表
     *
     * @param citysafetyRule 报告生成管理
     * @return 报告生成管理集合
     */
    public List<CitysafetyRule> selectCitysafetyRuleList(CitysafetyRule citysafetyRule);

    /**
     * 新增报告生成管理
     *
     * @param citysafetyRule 报告生成管理
     * @return 结果
     */
    public int insertCitysafetyRule(CitysafetyRule citysafetyRule);

    /**
     * 修改报告生成管理
     *
     * @param citysafetyRule 报告生成管理
     * @return 结果
     */
    public int updateCitysafetyRule(CitysafetyRule citysafetyRule);

    /**
     * 删除报告生成管理
     *
     * @param id 报告生成管理主键
     * @return 结果
     */
    public int deleteCitysafetyRuleById(Long id);

    /**
     * 批量删除报告生成管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCitysafetyRuleByIds(Long[] ids);

    CitysafetyRule getOne(Integer generateFrequency);

    CitysafetyRule getByFrequnce(@Param("reportType") Integer reportType);

    List<CitysafetyRule> listByFrequnce(@Param("reportType") Integer reportType);

    List<CitysafetyRule> listByFrequnceWithOutStatus(@Param("reportType") Integer reportType);
}
