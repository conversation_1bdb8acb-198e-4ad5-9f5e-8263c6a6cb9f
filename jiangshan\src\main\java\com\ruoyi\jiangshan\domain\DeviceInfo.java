package com.ruoyi.jiangshan.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 设备信息对象 t_device_info
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Data
public class DeviceInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 设备第三方id */
    @Excel(name = "设备第三方id")
    private String deviceThirdId;

    /** 设备编号 */
    @Excel(name = "设备编号")
    private String deviceNum;

    /** 设备场景 */
    @Excel(name = "设备场景")
    private String deviceScene;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 设备类型 */
    @Excel(name = "设备类型")
    private String deviceType;

    /** 设备地址 */
    @Excel(name = "设备地址")
    private String deviceAddress;

    /** 设备部门id */
    @Excel(name = "设备部门id")
    private Long deviceDepartmentId;

    /** 设备部门name */
    @Excel(name = "设备部门name")
    private String deviceDepartmentName;

    /** 设备状态  0下线 1上线 2维护 6拆除 */
    @Excel(name = "设备在线状态", readConverterExp = "0=离线,1=在线,2=维护,6=拆除")
    private Integer deviceStatus;

    /** 监测对象id */
    @Excel(name = "监测对象id")
    private Long monitorId;

    /** 监测对象name */
    @Excel(name = "监测对象name")
    private String monitorName;

    /** 设备图片url */
    @Excel(name = "设备图片url")
    private String fileUrl;

    /** 经度 */
    @Excel(name = "经度")
    private String lon;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    /** 公开方式 0私有 1公开 */
    @Excel(name = "公开方式", readConverterExp = "0=私有,1=公开")
    private Integer openFlag;

    /** 最后连接时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后连接时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastConnectTime;

    /** 最后连接时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后连接时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastDisConnectTime;

    /**
     * 是否收藏
     */
    private Boolean favoriteFlag;

    /**
     * 是否预警
     */
    private Boolean warningFlag = false;

    private List<DeviceValue> valueList;

    private String deviceStreet;

    private String deviceStreetAreaCode;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "拓展字段1")
    private String field01;

    @Excel(name = "拓展字段2")
    private String field02;

    @Excel(name = "拓展字段3")
    private String field03;

    @Excel(name = "拓展字段4")
    private String field04;

    @Excel(name = "拓展字段5")
    private String field05;

    private List<BusinessFile> fileList;

    /**
     *
     */
    private String excludeDeviceInfo;

    @Excel(name = "设备来源")
    private String deviceSource;
}
