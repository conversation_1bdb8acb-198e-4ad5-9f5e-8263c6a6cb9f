<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningEventHikvisionMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningEventHikvision">
    <!--@mbg.generated-->
    <!--@Table t_warning_event_hikvision-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ip_address" jdbcType="VARCHAR" property="ipAddress" />
    <result column="protocol_type" jdbcType="VARCHAR" property="protocolType" />
    <result column="mac_address" jdbcType="VARCHAR" property="macAddress" />
    <result column="channelID" jdbcType="VARCHAR" property="channelID" />
    <result column="date_time" jdbcType="VARCHAR" property="dateTime" />
    <result column="active_post_count" jdbcType="VARCHAR" property="activePostCount" />
    <result column="event_type" jdbcType="VARCHAR" property="eventType" />
    <result column="event_state" jdbcType="VARCHAR" property="eventState" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="event_description" jdbcType="VARCHAR" property="eventDescription" />
    <result column="deviceID" jdbcType="VARCHAR" property="deviceID" />
    <result column="fireEscapeDetection" jdbcType="LONGVARCHAR" property="fireEscapeDetection" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, ip_address, protocol_type, mac_address, channelID, date_time, active_post_count,
    event_type, event_state, channel_name, event_description, deviceID, fireEscapeDetection,
    create_time, create_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_event_hikvision
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_event_hikvision
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventHikvision" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_hikvision (ip_address, protocol_type, mac_address,
      channelID, date_time, active_post_count,
      event_type, event_state, channel_name,
      event_description, deviceID, fireEscapeDetection,
      create_time, create_by)
    values (#{ipAddress,jdbcType=VARCHAR}, #{protocolType,jdbcType=VARCHAR}, #{macAddress,jdbcType=VARCHAR},
      #{channelID,jdbcType=VARCHAR}, #{dateTime,jdbcType=VARCHAR}, #{activePostCount,jdbcType=VARCHAR},
      #{eventType,jdbcType=VARCHAR}, #{eventState,jdbcType=VARCHAR}, #{channelName,jdbcType=VARCHAR},
      #{eventDescription,jdbcType=VARCHAR}, #{deviceID,jdbcType=VARCHAR}, #{fireEscapeDetection,jdbcType=LONGVARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventHikvision" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_hikvision
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ipAddress != null">
        ip_address,
      </if>
      <if test="protocolType != null">
        protocol_type,
      </if>
      <if test="macAddress != null">
        mac_address,
      </if>
      <if test="channelID != null">
        channelID,
      </if>
      <if test="dateTime != null">
        date_time,
      </if>
      <if test="activePostCount != null">
        active_post_count,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="eventState != null">
        event_state,
      </if>
      <if test="channelName != null">
        channel_name,
      </if>
      <if test="eventDescription != null">
        event_description,
      </if>
      <if test="deviceID != null">
        deviceID,
      </if>
      <if test="fireEscapeDetection != null">
        fireEscapeDetection,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ipAddress != null">
        #{ipAddress,jdbcType=VARCHAR},
      </if>
      <if test="protocolType != null">
        #{protocolType,jdbcType=VARCHAR},
      </if>
      <if test="macAddress != null">
        #{macAddress,jdbcType=VARCHAR},
      </if>
      <if test="channelID != null">
        #{channelID,jdbcType=VARCHAR},
      </if>
      <if test="dateTime != null">
        #{dateTime,jdbcType=VARCHAR},
      </if>
      <if test="activePostCount != null">
        #{activePostCount,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="eventState != null">
        #{eventState,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="eventDescription != null">
        #{eventDescription,jdbcType=VARCHAR},
      </if>
      <if test="deviceID != null">
        #{deviceID,jdbcType=VARCHAR},
      </if>
      <if test="fireEscapeDetection != null">
        #{fireEscapeDetection,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningEventHikvision">
    <!--@mbg.generated-->
    update t_warning_event_hikvision
    <set>
      <if test="ipAddress != null">
        ip_address = #{ipAddress,jdbcType=VARCHAR},
      </if>
      <if test="protocolType != null">
        protocol_type = #{protocolType,jdbcType=VARCHAR},
      </if>
      <if test="macAddress != null">
        mac_address = #{macAddress,jdbcType=VARCHAR},
      </if>
      <if test="channelID != null">
        channelID = #{channelID,jdbcType=VARCHAR},
      </if>
      <if test="dateTime != null">
        date_time = #{dateTime,jdbcType=VARCHAR},
      </if>
      <if test="activePostCount != null">
        active_post_count = #{activePostCount,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="eventState != null">
        event_state = #{eventState,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="eventDescription != null">
        event_description = #{eventDescription,jdbcType=VARCHAR},
      </if>
      <if test="deviceID != null">
        deviceID = #{deviceID,jdbcType=VARCHAR},
      </if>
      <if test="fireEscapeDetection != null">
        fireEscapeDetection = #{fireEscapeDetection,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningEventHikvision">
    <!--@mbg.generated-->
    update t_warning_event_hikvision
    set ip_address = #{ipAddress,jdbcType=VARCHAR},
      protocol_type = #{protocolType,jdbcType=VARCHAR},
      mac_address = #{macAddress,jdbcType=VARCHAR},
      channelID = #{channelID,jdbcType=VARCHAR},
      date_time = #{dateTime,jdbcType=VARCHAR},
      active_post_count = #{activePostCount,jdbcType=VARCHAR},
      event_type = #{eventType,jdbcType=VARCHAR},
      event_state = #{eventState,jdbcType=VARCHAR},
      channel_name = #{channelName,jdbcType=VARCHAR},
      event_description = #{eventDescription,jdbcType=VARCHAR},
      deviceID = #{deviceID,jdbcType=VARCHAR},
      fireEscapeDetection = #{fireEscapeDetection,jdbcType=LONGVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_event_hikvision
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ip_address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ipAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="protocol_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.protocolType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="mac_address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.macAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channelID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelID,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="date_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dateTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="active_post_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.activePostCount,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_state = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventState,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventDescription,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="deviceID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceID,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="fireEscapeDetection = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fireEscapeDetection,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_hikvision
    (ip_address, protocol_type, mac_address, channelID, date_time, active_post_count,
      event_type, event_state, channel_name, event_description, deviceID, fireEscapeDetection,
      create_time, create_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.ipAddress,jdbcType=VARCHAR}, #{item.protocolType,jdbcType=VARCHAR}, #{item.macAddress,jdbcType=VARCHAR},
        #{item.channelID,jdbcType=VARCHAR}, #{item.dateTime,jdbcType=VARCHAR}, #{item.activePostCount,jdbcType=VARCHAR},
        #{item.eventType,jdbcType=VARCHAR}, #{item.eventState,jdbcType=VARCHAR}, #{item.channelName,jdbcType=VARCHAR},
        #{item.eventDescription,jdbcType=VARCHAR}, #{item.deviceID,jdbcType=VARCHAR}, #{item.fireEscapeDetection,jdbcType=LONGVARCHAR},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
