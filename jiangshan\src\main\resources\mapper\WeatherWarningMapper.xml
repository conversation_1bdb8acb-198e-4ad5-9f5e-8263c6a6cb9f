<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WeatherWarningMapper">

    <resultMap type="WeatherWarning" id="WeatherWarningResult">
        <result property="id"    column="id"    />
        <result property="warningId"    column="warning_id"    />
        <result property="riskZoneNum"    column="risk_zone_num"    />
        <result property="riskZoneId"    column="risk_zone_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="warningLevel"    column="warning_level"    />
        <result property="stationCode"    column="station_code"    />
        <result property="remark"    column="remark"    />
        <result property="warningType"    column="warning_type"    />
        <result property="warningTime"    column="warning_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="syncTime"    column="sync_time"    />
    </resultMap>

    <sql id="selectWeatherWarningVo">
        select id, warning_id, risk_zone_num, risk_zone_id, area_code, warning_level, station_code, remark, warning_type, warning_time, create_time, update_time, sync_time from t_weather_warning
    </sql>

    <select id="selectWeatherWarningList" parameterType="WeatherWarning" resultMap="WeatherWarningResult">
        <include refid="selectWeatherWarningVo"/>
        <where>
            <if test="warningId != null  and warningId != ''"> and warning_id = #{warningId}</if>
            <if test="riskZoneNum != null  and riskZoneNum != ''"> and risk_zone_num like concat('%', #{riskZoneNum}, '%')</if>
            <if test="riskZoneId != null  and riskZoneId != ''"> and risk_zone_id = #{riskZoneId}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="warningLevel != null "> and warning_level = #{warningLevel}</if>
            <if test="stationCode != null  and stationCode != ''"> and station_code = #{stationCode}</if>
            <if test="warningType != null "> and warning_type = #{warningType}</if>
            <if test="warningTime != null "> and warning_time = #{warningTime}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(warning_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(warning_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by warning_time desc
    </select>

    <select id="selectWeatherWarningById" parameterType="Long" resultMap="WeatherWarningResult">
        <include refid="selectWeatherWarningVo"/>
        where id = #{id}
    </select>

    <select id="selectWeatherWarningByWarningId" parameterType="String" resultMap="WeatherWarningResult">
        <include refid="selectWeatherWarningVo"/>
        where warning_id = #{warningId}
    </select>

    <insert id="insertWeatherWarning" parameterType="WeatherWarning" useGeneratedKeys="true" keyProperty="id">
        insert into t_weather_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warningId != null">warning_id,</if>
            <if test="riskZoneNum != null">risk_zone_num,</if>
            <if test="riskZoneId != null">risk_zone_id,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="warningLevel != null">warning_level,</if>
            <if test="stationCode != null">station_code,</if>
            <if test="remark != null">remark,</if>
            <if test="warningType != null">warning_type,</if>
            <if test="warningTime != null">warning_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="syncTime != null">sync_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warningId != null">#{warningId},</if>
            <if test="riskZoneNum != null">#{riskZoneNum},</if>
            <if test="riskZoneId != null">#{riskZoneId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="warningLevel != null">#{warningLevel},</if>
            <if test="stationCode != null">#{stationCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="warningType != null">#{warningType},</if>
            <if test="warningTime != null">#{warningTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="syncTime != null">#{syncTime},</if>
        </trim>
    </insert>

    <insert id="batchInsertWeatherWarning" parameterType="java.util.List">
        insert into t_weather_warning (warning_id, risk_zone_num, risk_zone_id, area_code, warning_level, station_code, remark, warning_type, warning_time, create_time, update_time, sync_time) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.warningId}, #{item.riskZoneNum}, #{item.riskZoneId}, #{item.areaCode}, #{item.warningLevel}, #{item.stationCode}, #{item.remark}, #{item.warningType}, #{item.warningTime}, #{item.createTime}, #{item.updateTime}, #{item.syncTime})
        </foreach>
    </insert>

    <update id="updateWeatherWarning" parameterType="WeatherWarning">
        update t_weather_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="warningId != null">warning_id = #{warningId},</if>
            <if test="riskZoneNum != null">risk_zone_num = #{riskZoneNum},</if>
            <if test="riskZoneId != null">risk_zone_id = #{riskZoneId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="warningLevel != null">warning_level = #{warningLevel},</if>
            <if test="stationCode != null">station_code = #{stationCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="warningType != null">warning_type = #{warningType},</if>
            <if test="warningTime != null">warning_time = #{warningTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateWeatherWarningByWarningId" parameterType="WeatherWarning">
        update t_weather_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="riskZoneNum != null">risk_zone_num = #{riskZoneNum},</if>
            <if test="riskZoneId != null">risk_zone_id = #{riskZoneId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="warningLevel != null">warning_level = #{warningLevel},</if>
            <if test="stationCode != null">station_code = #{stationCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="warningType != null">warning_type = #{warningType},</if>
            <if test="warningTime != null">warning_time = #{warningTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
        </trim>
        where warning_id = #{warningId}
    </update>

    <delete id="deleteWeatherWarningById" parameterType="Long">
        delete from t_weather_warning where id = #{id}
    </delete>

    <delete id="deleteWeatherWarningByIds" parameterType="String">
        delete from t_weather_warning where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
