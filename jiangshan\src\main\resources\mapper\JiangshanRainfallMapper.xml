<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.JiangshanRainfallMapper">

    <resultMap type="com.ruoyi.jiangshan.domain.JiangshanRainfall" id="JiangshanRainfallResult">
        <id property="id" column="id"/>
        <result property="p" column="p"/>
        <result property="town" column="town"/>
        <result property="name" column="name"/>
        <result property="x" column="x"/>
        <result property="county" column="county"/>
        <result property="y" column="y"/>
        <result property="v1" column="v1"/>
        <result property="monitorTime" column="monitor_time"/>
    </resultMap>

    <insert id="insert" parameterType="com.ruoyi.jiangshan.domain.JiangshanRainfall" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_jiangshan_rainfall (
            p, town, name, x, county, y, v1, monitor_time
        ) VALUES (
            #{p}, #{town}, #{name}, #{x}, #{county}, #{y}, #{v1}, #{monitorTime}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_jiangshan_rainfall (
            p, town, name, x, county, y, v1, monitor_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.p}, #{item.town}, #{item.name}, #{item.x}, #{item.county}, #{item.y}, #{item.v1}, #{item.monitorTime})
        </foreach>
    </insert>

    <select id="selectByName" parameterType="string" resultMap="JiangshanRainfallResult">
        SELECT * FROM t_jiangshan_rainfall
        WHERE name = #{name}
        ORDER BY monitor_time DESC
    </select>

    <select id="listByNameAndHour" resultMap="JiangshanRainfallResult">
        SELECT *
        FROM t_jiangshan_rainfall
        WHERE name = #{deviceStreet}
        AND monitor_time >= DATE_SUB(now(), INTERVAL #{hour} HOUR)
        ORDER BY monitor_time DESC
    </select>

    <insert id="batchInsertSelf" parameterType="java.util.List">
        INSERT INTO t_jiangshan_rainfall_self (
        p, town, name, x, county, y, v1, monitor_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.p}, #{item.town}, #{item.name}, #{item.x}, #{item.county}, #{item.y}, #{item.v1}, #{item.monitorTime})
        </foreach>
    </insert>

    <select id="listSelfByTime" resultMap="JiangshanRainfallResult">
        SELECT *
        FROM t_jiangshan_rainfall_self
        WHERE monitor_time >= NOW() - INTERVAL #{hour} hour
        ORDER BY monitor_time DESC
    </select>
</mapper>
