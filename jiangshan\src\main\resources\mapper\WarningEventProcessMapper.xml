<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningEventProcessMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningEventProcess">
    <!--@mbg.generated-->
    <!--@Table t_warning_event_process-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="event_id" jdbcType="BIGINT" property="eventId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_mobile" jdbcType="VARCHAR" property="userMobile" />
    <result column="process_step" jdbcType="TINYINT" property="processStep" />
    <result column="process_content" jdbcType="VARCHAR" property="processContent" />
    <result column="process_time" jdbcType="TIMESTAMP" property="processTime" />
    <result column="reject_content" jdbcType="VARCHAR" property="rejectContent" />
    <result column="stop_content" jdbcType="VARCHAR" property="stopContent" />
    <result column="stop_start_time" jdbcType="TIMESTAMP" property="stopStartTime" />
    <result column="stop_end_time" jdbcType="TIMESTAMP" property="stopEndTime" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="over_flag" jdbcType="BOOLEAN" property="overFlag" />
    <result column="supervise_user_id" jdbcType="BIGINT" property="superviseUserId" />
    <result column="supervise_user" jdbcType="VARCHAR" property="superviseUser" />
    <result column="supervise_content" jdbcType="VARCHAR" property="superviseContent" />
    <result column="expected_complete_time" jdbcType="TIMESTAMP" property="expectedCompleteTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, event_id, department_id, department_name, user_id, user_name, user_mobile, process_step,
    process_content, process_time, reject_content, stop_content, stop_start_time, stop_end_time,
    `status`, over_flag, supervise_user_id, supervise_user, supervise_content, expected_complete_time,
    create_time, create_by, update_time, update_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_event_process
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_event_process
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventProcess" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_process (event_id, department_id, department_name,
      user_id, user_name, user_mobile,
      process_step, process_content, process_time,
      reject_content, stop_content, stop_start_time,
      stop_end_time, `status`, over_flag,
      supervise_user_id, supervise_user, supervise_content,
      expected_complete_time, create_time, create_by,
      update_time, update_by)
    values (#{eventId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{departmentName,jdbcType=VARCHAR},
      #{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{userMobile,jdbcType=VARCHAR},
      #{processStep,jdbcType=BOOLEAN}, #{processContent,jdbcType=VARCHAR}, #{processTime,jdbcType=TIMESTAMP},
      #{rejectContent,jdbcType=VARCHAR}, #{stopContent,jdbcType=VARCHAR}, #{stopStartTime,jdbcType=TIMESTAMP},
      #{stopEndTime,jdbcType=TIMESTAMP}, #{status,jdbcType=BOOLEAN}, #{overFlag,jdbcType=BOOLEAN},
      #{superviseUserId,jdbcType=BIGINT}, #{superviseUser,jdbcType=VARCHAR}, #{superviseContent,jdbcType=VARCHAR},
      #{expectedCompleteTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventProcess" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="eventId != null">
        event_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="departmentName != null">
        department_name,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userMobile != null">
        user_mobile,
      </if>
      <if test="processStep != null">
        process_step,
      </if>
      <if test="processContent != null">
        process_content,
      </if>
      <if test="processTime != null">
        process_time,
      </if>
      <if test="rejectContent != null">
        reject_content,
      </if>
      <if test="stopContent != null">
        stop_content,
      </if>
      <if test="stopStartTime != null">
        stop_start_time,
      </if>
      <if test="stopEndTime != null">
        stop_end_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="overFlag != null">
        over_flag,
      </if>
      <if test="superviseUserId != null">
        supervise_user_id,
      </if>
      <if test="superviseUser != null">
        supervise_user,
      </if>
      <if test="superviseContent != null">
        supervise_content,
      </if>
      <if test="expectedCompleteTime != null">
        expected_complete_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="eventId != null">
        #{eventId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userMobile != null">
        #{userMobile,jdbcType=VARCHAR},
      </if>
      <if test="processStep != null">
        #{processStep,jdbcType=BOOLEAN},
      </if>
      <if test="processContent != null">
        #{processContent,jdbcType=VARCHAR},
      </if>
      <if test="processTime != null">
        #{processTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rejectContent != null">
        #{rejectContent,jdbcType=VARCHAR},
      </if>
      <if test="stopContent != null">
        #{stopContent,jdbcType=VARCHAR},
      </if>
      <if test="stopStartTime != null">
        #{stopStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopEndTime != null">
        #{stopEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="overFlag != null">
        #{overFlag,jdbcType=BOOLEAN},
      </if>
      <if test="superviseUserId != null">
        #{superviseUserId,jdbcType=BIGINT},
      </if>
      <if test="superviseUser != null">
        #{superviseUser,jdbcType=VARCHAR},
      </if>
      <if test="superviseContent != null">
        #{superviseContent,jdbcType=VARCHAR},
      </if>
      <if test="expectedCompleteTime != null">
        #{expectedCompleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningEventProcess">
    <!--@mbg.generated-->
    update t_warning_event_process
    <set>
      <if test="eventId != null">
        event_id = #{eventId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="departmentName != null">
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userMobile != null">
        user_mobile = #{userMobile,jdbcType=VARCHAR},
      </if>
      <if test="processStep != null">
        process_step = #{processStep,jdbcType=BOOLEAN},
      </if>
      <if test="processContent != null">
        process_content = #{processContent,jdbcType=VARCHAR},
      </if>
      <if test="processTime != null">
        process_time = #{processTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rejectContent != null">
        reject_content = #{rejectContent,jdbcType=VARCHAR},
      </if>
      <if test="stopContent != null">
        stop_content = #{stopContent,jdbcType=VARCHAR},
      </if>
      <if test="stopStartTime != null">
        stop_start_time = #{stopStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopEndTime != null">
        stop_end_time = #{stopEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="overFlag != null">
        over_flag = #{overFlag,jdbcType=BOOLEAN},
      </if>
      <if test="superviseUserId != null">
        supervise_user_id = #{superviseUserId,jdbcType=BIGINT},
      </if>
      <if test="superviseUser != null">
        supervise_user = #{superviseUser,jdbcType=VARCHAR},
      </if>
      <if test="superviseContent != null">
        supervise_content = #{superviseContent,jdbcType=VARCHAR},
      </if>
      <if test="expectedCompleteTime != null">
        expected_complete_time = #{expectedCompleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningEventProcess">
    <!--@mbg.generated-->
    update t_warning_event_process
    set event_id = #{eventId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      department_name = #{departmentName,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      user_mobile = #{userMobile,jdbcType=VARCHAR},
      process_step = #{processStep,jdbcType=BOOLEAN},
      process_content = #{processContent,jdbcType=VARCHAR},
      process_time = #{processTime,jdbcType=TIMESTAMP},
      reject_content = #{rejectContent,jdbcType=VARCHAR},
      stop_content = #{stopContent,jdbcType=VARCHAR},
      stop_start_time = #{stopStartTime,jdbcType=TIMESTAMP},
      stop_end_time = #{stopEndTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=BOOLEAN},
      over_flag = #{overFlag,jdbcType=BOOLEAN},
      supervise_user_id = #{superviseUserId,jdbcType=BIGINT},
      supervise_user = #{superviseUser,jdbcType=VARCHAR},
      supervise_content = #{superviseContent,jdbcType=VARCHAR},
      expected_complete_time = #{expectedCompleteTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_event_process
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="event_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.departmentId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="department_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.departmentName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="user_mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="process_step = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.processStep,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="process_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.processContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="process_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.processTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="reject_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.rejectContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="stop_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.stopContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="stop_start_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.stopStartTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="stop_end_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.stopEndTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="over_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.overFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="supervise_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviseUserId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="supervise_user = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviseUser,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervise_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviseContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="expected_complete_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.expectedCompleteTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_process
    (event_id, department_id, department_name, user_id, user_name, user_mobile, process_step,
      process_content, process_time, reject_content, stop_content, stop_start_time, stop_end_time,
      `status`, over_flag, supervise_user_id, supervise_user, supervise_content, expected_complete_time,
      create_time, create_by, update_time, update_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.eventId,jdbcType=BIGINT}, #{item.departmentId,jdbcType=BIGINT}, #{item.departmentName,jdbcType=VARCHAR},
        #{item.userId,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR}, #{item.userMobile,jdbcType=VARCHAR},
        #{item.processStep,jdbcType=BOOLEAN}, #{item.processContent,jdbcType=VARCHAR},
        #{item.processTime,jdbcType=TIMESTAMP}, #{item.rejectContent,jdbcType=VARCHAR},
        #{item.stopContent,jdbcType=VARCHAR}, #{item.stopStartTime,jdbcType=TIMESTAMP},
        #{item.stopEndTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=BOOLEAN}, #{item.overFlag,jdbcType=BOOLEAN},
        #{item.superviseUserId,jdbcType=BIGINT}, #{item.superviseUser,jdbcType=VARCHAR},
        #{item.superviseContent,jdbcType=VARCHAR}, #{item.expectedCompleteTime,jdbcType=TIMESTAMP},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
        #{item.updateBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="listByEventIdList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_process
    where event_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>

  <select id="listNonOvertimeByLastProcess" resultMap="BaseResultMap">
    select max(process_time) as process_time, id, event_id, department_id, department_name, user_id,
           user_name, user_mobile, process_step, process_content, stop_start_time, stop_end_time, `status`,
           over_flag, supervise_user_id, supervise_user, supervise_content, create_time, create_by, update_time,
           update_by, expected_complete_time
    from (select distinct * from t_warning_event_process where status = 0 and over_flag = 0 order by process_time desc) t3
    group by event_id
  </select>

  <select id="getByEventId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_process
    where event_id = #{id,jdbcType=BIGINT}
    and process_step = #{status,jdbcType=INTEGER}
    and status in (0, 2, 3)
    order by create_time desc
    limit 1
  </select>

  <select id="getLastByEventId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_process
    where event_id = #{id,jdbcType=BIGINT}
    order by create_time desc
    limit 1
    </select>

  <select id="getLastByEventIdAndStep" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_process
    where event_id = #{eventId,jdbcType=BIGINT}
    and process_step = #{status,jdbcType=INTEGER}
    order by create_time desc
    limit 1
  </select>

  <update id="updateEventStatus">
    update t_warning_event_process
    set status = 3
    where event_id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateHandleStatus">
    update t_warning_event_process
    set user_id = #{completeFlowVO.userId},
    user_name = #{completeFlowVO.userName},
    user_mobile = #{completeFlowVO.mobilePhone},
    process_content = #{completeFlowVO.suggestion},
    process_time = #{completeFlowVO.processDate},
    expected_complete_time = #{completeFlowVO.processDate},
    status = 3
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listNonCompleteProcess" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_process
    where event_id = #{id}
    and status not in (1, 3)
    order by create_time desc
  </select>

  <select id="listPushValue" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_process
    where 1=1
    <if test="startTime != null">
      and create_time >= #{startTime, jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and create_time <![CDATA[ <= ]]> #{endTime, jdbcType=TIMESTAMP}
    </if>
    </select>
</mapper>
