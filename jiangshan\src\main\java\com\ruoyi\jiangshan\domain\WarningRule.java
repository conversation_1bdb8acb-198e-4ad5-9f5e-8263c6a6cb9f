package com.ruoyi.jiangshan.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 设备规则对象 t_warning_rule
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Data
public class WarningRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String ruleName;

    /** 适用模型 */
    @Excel(name = "适用模型")
    private String modelId;

    /** 适用模型name */
    @Excel(name = "适用模型name")
    private String modelName;

    /** 发布渠道 1浙政钉推送 */
    @Excel(name = "发布渠道 1浙政钉推送")
    private Integer publishWay;

    /** 详细描述 */
    @Excel(name = "详细描述")
    private String ruleDetail;

    /** 推送预警的用户id */
    @Excel(name = "推送预警的用户id")
    private Long warningUserId;

    /** 推送预警的用户名称 */
    @Excel(name = "推送预警的用户名称")
    private String warningUserName;

    private List<WarningRuleCondition> conditionList;

    private List<String> otherUserIdList;

    private String otherUserId;

    private String allAccountId;

    private String allMobile;


}
