package com.ruoyi.jiangshan.domain;

public class EmergencyMaterial {
    private Integer id;

    /**
    * 街道
    */
    private String street;

    /**
    * 村
    */
    private String village;

    /**
    * 级别
    */
    private String level;

    /**
    * 物资类别
    */
    private String materialCategory;

    /**
    * 物资名称
    */
    private String materialName;

    /**
    * 入库时间
    */
    private String storageTime;

    /**
    * 入库数量
    */
    private String storageCount;

    /**
    * 库存数量
    */
    private String currentCount;

    /**
    * 管理人姓名
    */
    private String userName;

    /**
    * 管理人手机号
    */
    private String userMobile;

    /**
    * 备注
    */
    private String remark;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getVillage() {
        return village;
    }

    public void setVillage(String village) {
        this.village = village;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getMaterialCategory() {
        return materialCategory;
    }

    public void setMaterialCategory(String materialCategory) {
        this.materialCategory = materialCategory;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getStorageTime() {
        return storageTime;
    }

    public void setStorageTime(String storageTime) {
        this.storageTime = storageTime;
    }

    public String getStorageCount() {
        return storageCount;
    }

    public void setStorageCount(String storageCount) {
        this.storageCount = storageCount;
    }

    public String getCurrentCount() {
        return currentCount;
    }

    public void setCurrentCount(String currentCount) {
        this.currentCount = currentCount;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}