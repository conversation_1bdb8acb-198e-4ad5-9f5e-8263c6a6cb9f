package com.ruoyi.jiangshan.service;

import java.util.List;
import com.ruoyi.jiangshan.domain.CitysafetyRule;

/**
 * 报告生成管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-14
 */
public interface ICitysafetyRuleService 
{
    /**
     * 查询报告生成管理
     * 
     * @param id 报告生成管理主键
     * @return 报告生成管理
     */
    public CitysafetyRule selectCitysafetyRuleById(Long id);

    /**
     * 查询报告生成管理列表
     * 
     * @param citysafetyRule 报告生成管理
     * @return 报告生成管理集合
     */
    public List<CitysafetyRule> selectCitysafetyRuleList(CitysafetyRule citysafetyRule);

    /**
     * 新增报告生成管理
     * 
     * @param citysafetyRule 报告生成管理
     * @return 结果
     */
    public int insertCitysafetyRule(CitysafetyRule citysafetyRule);

    /**
     * 修改报告生成管理
     * 
     * @param citysafetyRule 报告生成管理
     * @return 结果
     */
    public int updateCitysafetyRule(CitysafetyRule citysafetyRule);

    /**
     * 批量删除报告生成管理
     * 
     * @param ids 需要删除的报告生成管理主键集合
     * @return 结果
     */
    public int deleteCitysafetyRuleByIds(Long[] ids);

    /**
     * 删除报告生成管理信息
     * 
     * @param id 报告生成管理主键
     * @return 结果
     */
    public int deleteCitysafetyRuleById(Long id);
}
