package com.ruoyi.jiangshan.controller.screen;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.service.FireSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消防场景驾驶舱
 */
@RestController
@RequestMapping("/fire")
public class FireSceneController {
    @Autowired
    private FireSceneService fireSceneService;

    /**
     * 登高面监控监测
     * @return
     */
    @GetMapping("/monitor")
    public AjaxResult getMonitor(Long monitorId) {
        return AjaxResult.success(fireSceneService.getMonitor(monitorId));
    }

    /**
     * 登高面占用提示
     * @return
     */
    @GetMapping("/occupationList")
    public AjaxResult getOccupationList() {
        return AjaxResult.success(fireSceneService.getOccupationList());
    }

    /**
     * 登高面占用统计
     * @return
     */
    @GetMapping("/occupationCount")
    public AjaxResult getOccupationCount(String dateStr) {
        return AjaxResult.success(fireSceneService.getOccupationCount(dateStr));
    }

    /**
     * 实时预警
     * @return
     */
    @GetMapping("/warning")
    public AjaxResult getWarning(Integer warningLevel) {
        return AjaxResult.success(fireSceneService.getWarning(warningLevel));
    }

    /**
     * 安全风险统计
     * @return
     */
    @GetMapping("/safetyCount")
    public AjaxResult getSafetyCount(String dateStr) {
        dateStr = "年";

        return AjaxResult.success(fireSceneService.getSafetyCount(dateStr));
    }

    /**
     * 消防栓情况统计
     * @return
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        return AjaxResult.success(fireSceneService.getStatistics());
    }

    /**
     * 消防栓情况统计-查看详情
     * @return
     */
    @GetMapping("/deviceList")
    public AjaxResult getDeviceList() {
        return AjaxResult.success(fireSceneService.getDeviceList());
    }
}
