package com.ruoyi.jiangshan.controller.screen;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.service.ComprehensiveSceneService;
import com.ruoyi.jiangshan.service.GeologicSceneService;
import com.ruoyi.jiangshan.vo.RiskPreventVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 地址灾害场景驾驶舱
 */
@RestController
@RequestMapping("/geologic")
public class GeologicSceneController {
    @Autowired
    private GeologicSceneService geologicSceneService;

    /**
     * 获取所有防范区
     * @return
     */
    @GetMapping("/getAllMonitor")
    public AjaxResult getAllMonitor() {
        return AjaxResult.success(geologicSceneService.getAllMonitor());
    }

    /**
     * 获取所有设备
     * @return
     */
    @GetMapping("/getAllDevice")
    public AjaxResult getAllDevice() {
        return AjaxResult.success(geologicSceneService.getAllDevice());
    }

    /**
     * 累计降雨量
     * @return
     */
    @GetMapping("/rainFall")
    public AjaxResult getRainFall(Long id) {
        return AjaxResult.success(geologicSceneService.getRainFall(id));
    }

    /**
     * 安全风险统计
     * @return
     */
    @GetMapping("/safetyCount")
    public AjaxResult getSafetyCount(String dateStr) {
        return AjaxResult.success(geologicSceneService.getSafetyCount(dateStr));
    }

    /**
     * 安全风险事件排序
     * @return
     */
    @GetMapping("/event")
    public AjaxResult getEvent(String eventName, Integer status, Integer warningLevel) {
        return AjaxResult.success(geologicSceneService.getEvent(eventName, status, warningLevel));
    }

    /**
     * 实时预警
     * @return
     */
    @GetMapping("/warning")
    public AjaxResult getWarning(Integer warningLevel) {
        return AjaxResult.success(geologicSceneService.getWarning(warningLevel));
    }

    /**
     * 群测群防
     * @return
     */
    @GetMapping("/groupPrevent")
    public AjaxResult getGroupPrevent() {
        return AjaxResult.success(geologicSceneService.getGroupPrevent());
    }

    /**
     * 风险防范区统计
     * @return
     */
    @GetMapping("/riskPrevention")
    public AjaxResult getRiskPrevention(RiskPreventVO riskPreventVO) {
        return AjaxResult.success(geologicSceneService.getRiskPrevention(riskPreventVO));
    }

}
