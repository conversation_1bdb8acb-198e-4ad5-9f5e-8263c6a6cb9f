package com.ruoyi.jiangshan.openapi.config;

import com.ruoyi.jiangshan.domain.WarningEventSceneDepartment;
import com.ruoyi.jiangshan.mapper.WarningEventSceneDepartmentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class WarningSceneDepartmentConfig {

    @Autowired
    private WarningEventSceneDepartmentMapper warningEventSceneDepartmentMapper;

    public static Map<String, WarningEventSceneDepartment> SCENE_DEPARTMENT_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
        List<WarningEventSceneDepartment> departmentList = warningEventSceneDepartmentMapper.listAll();

        SCENE_DEPARTMENT_MAP = departmentList.stream()
                .collect(Collectors.toMap(WarningEventSceneDepartment::getMonitorScene,
                        Function.identity()));
    }
}
