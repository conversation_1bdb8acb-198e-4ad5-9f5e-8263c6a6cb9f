package com.ruoyi.framework.dtalk;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> yx-0176
 * @description
 * @date : 2021/10/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserResp {

    /**
     * 账号id
     */
    private String accountId;

    /**
     * 姓名
     */
    private String lastName;
    /**
     * 应用标识
     */
    private String clientId;
    /**
     * 租户id
     */
    private String realmId;
    /**
     * 租户名
     */
    private String realmName;
    /**
     * 账号类型标识
     */
    private String namespace;
    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 租户名
     */
    private String tenantName;
    /**
     * 昵称
     */
    private String nickNameCn;
    /**
     * 员工在当前企业内的唯一标识
     */
    private String tenantUserId;
    /**
     * 登录账号
     */
    private String account;

    private String phone;

    private String idCard;

    private String employeeCode;

    private String organizationCode;

    private String organizationName;


    private String accessToken;

}
