package com.ruoyi.quartz.task;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.jiangshan.client.IrsWeatherClient;
import com.ruoyi.jiangshan.domain.JiangshanRainfall;
import com.ruoyi.jiangshan.mapper.JiangshanRainfallMapper;
import com.ruoyi.jiangshan.service.ComprehensiveSceneService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.service.impl.WarningEventServiceImpl;
import com.ruoyi.jiangshan.vo.compre.DeviceYuLiangExtendV2VO;
import com.ruoyi.jiangshan.vo.compre.DeviceYuLiangStreetAllV2VO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Slf4j
@Component("jiangshanRainfallTask")
public class JiangshanRainfallTask {

    DecimalFormat df = new DecimalFormat("0.00");

    /**
     * 每小时更新降雨量
     */
    public void insertRainfallTask() throws ExecutionException {
        IrsWeatherClient irsWeatherClient = SpringUtils.getBean(IrsWeatherClient.class);
        JiangshanRainfallMapper jiangshanRainfallMapper = SpringUtils.getBean(JiangshanRainfallMapper.class);

        log.info("IrsWeatherClient, insertRainfallTask, start");

        Date currentTime = DateUtils.getLastHour();

        Map rainFallMap = irsWeatherClient.getJiangShanRainFallOneHour();
        List<JiangshanRainfall> jiangshanRainfallList = JSON.parseArray(JSON.toJSONString(rainFallMap.get("data")), JiangshanRainfall.class);

        double avgJiangShan = 0.0;
        double totalJiangShan = 0.0;
        for (JiangshanRainfall jiangshanRainfall : jiangshanRainfallList) {
            jiangshanRainfall.setMonitorTime(currentTime);
            jiangshanRainfall.setId(null);

            double value = Double.parseDouble(jiangshanRainfall.getV1());

            totalJiangShan += value;
        }

        avgJiangShan = totalJiangShan / jiangshanRainfallList.size();

        String format = df.format(avgJiangShan);

        JiangshanRainfall jiangshanAll = new JiangshanRainfall();
        jiangshanAll.setName("江山市");
        jiangshanAll.setCounty("江山市");
        jiangshanAll.setTown("江山市");
        jiangshanAll.setMonitorTime(currentTime);
        jiangshanAll.setV1(format);
        jiangshanAll.setX("118.60164");
        jiangshanAll.setY("28.617417");

        jiangshanRainfallList.add(jiangshanAll);

        jiangshanRainfallMapper.batchInsert(jiangshanRainfallList);

        log.info("IrsWeatherClient, insertRainfallTask, end");
    }

    /**
     * 每小时更新降雨量-自建设备
     */
    public void insertRainfallTaskBySelf() {
        ComprehensiveSceneService comprehensiveSceneService = SpringUtils.getBean(ComprehensiveSceneService.class);
        JiangshanRainfallMapper jiangshanRainfallMapper = SpringUtils.getBean(JiangshanRainfallMapper.class);

        Date currentTime = DateUtils.getThisHour();

        List<JiangshanRainfall> resultList = Lists.newArrayList();
        DeviceYuLiangStreetAllV2VO waterExtendV2 = comprehensiveSceneService.getWaterExtendByHour(1);

        JiangshanRainfall allRainFall = new JiangshanRainfall();
        allRainFall.setP("1");
        allRainFall.setTown(waterExtendV2.getDeviceStreet());
        allRainFall.setName(waterExtendV2.getDeviceStreet());
        allRainFall.setX(waterExtendV2.getLon());
        allRainFall.setY(waterExtendV2.getLat());
        allRainFall.setCounty("江山");
        allRainFall.setV1(waterExtendV2.getRainFallAvgWithOutUnit());
        allRainFall.setMonitorTime(currentTime);

        resultList.add(allRainFall);

        for (DeviceYuLiangExtendV2VO extendV2VO : waterExtendV2.getStreetList()) {
            JiangshanRainfall jiangshanRainfall = new JiangshanRainfall();
            jiangshanRainfall.setP("1");
            jiangshanRainfall.setTown(extendV2VO.getDeviceStreet());
            jiangshanRainfall.setName(extendV2VO.getDeviceStreet());
            jiangshanRainfall.setX(extendV2VO.getLon());
            jiangshanRainfall.setY(extendV2VO.getLat());
            jiangshanRainfall.setCounty("江山");
            jiangshanRainfall.setV1(extendV2VO.getRainFallAvgWithOutUnit());
            jiangshanRainfall.setMonitorTime(currentTime);

            resultList.add(jiangshanRainfall);
        }

        jiangshanRainfallMapper.batchInsertSelf(resultList);
    }
}
