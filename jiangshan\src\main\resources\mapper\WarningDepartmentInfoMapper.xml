<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningDepartmentInfoMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningDepartmentInfo">
    <!--@mbg.generated-->
    <!--@Table t_warning_department_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="ancestors" jdbcType="VARCHAR" property="ancestors" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="organization_code" jdbcType="VARCHAR" property="organizationCode" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="leader_id" jdbcType="BIGINT" property="leaderId" />
    <result column="leader_name" jdbcType="VARCHAR" property="leaderName" />
    <result column="leader_mobile" jdbcType="VARCHAR" property="leaderMobile" />
    <result column="leader_account_id" jdbcType="VARCHAR" property="leaderAccountId" />
    <result column="leader_employee_code" jdbcType="VARCHAR" property="leaderEmployeeCode" />
    <result column="messenger_id" jdbcType="BIGINT" property="messengerId" />
    <result column="messenger_name" jdbcType="VARCHAR" property="messengerName" />
    <result column="messenger_mobile" jdbcType="VARCHAR" property="messengerMobile" />
    <result column="messenger_account_id" jdbcType="VARCHAR" property="messengerAccountId" />
    <result column="messenger_employee_code" jdbcType="VARCHAR" property="messengerEmployeeCode" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="regulated_industry" jdbcType="VARCHAR" property="regulatedIndustry" />
    <result column="main_duty" jdbcType="VARCHAR" property="mainDuty" />
    <result column="score" jdbcType="VARCHAR" property="score" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, parent_id, ancestors, order_num, organization_code, department_name, leader_id,
    leader_name, leader_mobile, leader_account_id, leader_employee_code, messenger_id,
    messenger_name, messenger_mobile, messenger_account_id, messenger_employee_code,
    area, area_code, regulated_industry, main_duty, score, create_time, create_by, update_time,
    update_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_department_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_department_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningDepartmentInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_department_info (parent_id, ancestors, order_num,
      organization_code, department_name, leader_id,
      leader_name, leader_mobile, leader_account_id,
      leader_employee_code, messenger_id, messenger_name,
      messenger_mobile, messenger_account_id, messenger_employee_code,
      area, area_code, regulated_industry,
      main_duty, score, create_time,
      create_by, update_time, update_by
      )
    values (#{parentId,jdbcType=BIGINT}, #{ancestors,jdbcType=VARCHAR}, #{orderNum,jdbcType=INTEGER},
      #{organizationCode,jdbcType=VARCHAR}, #{departmentName,jdbcType=VARCHAR}, #{leaderId,jdbcType=BIGINT},
      #{leaderName,jdbcType=VARCHAR}, #{leaderMobile,jdbcType=VARCHAR}, #{leaderAccountId,jdbcType=VARCHAR},
      #{leaderEmployeeCode,jdbcType=VARCHAR}, #{messengerId,jdbcType=BIGINT}, #{messengerName,jdbcType=VARCHAR},
      #{messengerMobile,jdbcType=VARCHAR}, #{messengerAccountId,jdbcType=VARCHAR}, #{messengerEmployeeCode,jdbcType=VARCHAR},
      #{area,jdbcType=VARCHAR}, #{areaCode,jdbcType=VARCHAR}, #{regulatedIndustry,jdbcType=VARCHAR},
      #{mainDuty,jdbcType=VARCHAR}, #{score,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningDepartmentInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_department_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="ancestors != null">
        ancestors,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="organizationCode != null">
        organization_code,
      </if>
      <if test="departmentName != null">
        department_name,
      </if>
      <if test="leaderId != null">
        leader_id,
      </if>
      <if test="leaderName != null">
        leader_name,
      </if>
      <if test="leaderMobile != null">
        leader_mobile,
      </if>
      <if test="leaderAccountId != null">
        leader_account_id,
      </if>
      <if test="leaderEmployeeCode != null">
        leader_employee_code,
      </if>
      <if test="messengerId != null">
        messenger_id,
      </if>
      <if test="messengerName != null">
        messenger_name,
      </if>
      <if test="messengerMobile != null">
        messenger_mobile,
      </if>
      <if test="messengerAccountId != null">
        messenger_account_id,
      </if>
      <if test="messengerEmployeeCode != null">
        messenger_employee_code,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="regulatedIndustry != null">
        regulated_industry,
      </if>
      <if test="mainDuty != null">
        main_duty,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="ancestors != null">
        #{ancestors,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="organizationCode != null">
        #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="leaderId != null">
        #{leaderId,jdbcType=BIGINT},
      </if>
      <if test="leaderName != null">
        #{leaderName,jdbcType=VARCHAR},
      </if>
      <if test="leaderMobile != null">
        #{leaderMobile,jdbcType=VARCHAR},
      </if>
      <if test="leaderAccountId != null">
        #{leaderAccountId,jdbcType=VARCHAR},
      </if>
      <if test="leaderEmployeeCode != null">
        #{leaderEmployeeCode,jdbcType=VARCHAR},
      </if>
      <if test="messengerId != null">
        #{messengerId,jdbcType=BIGINT},
      </if>
      <if test="messengerName != null">
        #{messengerName,jdbcType=VARCHAR},
      </if>
      <if test="messengerMobile != null">
        #{messengerMobile,jdbcType=VARCHAR},
      </if>
      <if test="messengerAccountId != null">
        #{messengerAccountId,jdbcType=VARCHAR},
      </if>
      <if test="messengerEmployeeCode != null">
        #{messengerEmployeeCode,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="regulatedIndustry != null">
        #{regulatedIndustry,jdbcType=VARCHAR},
      </if>
      <if test="mainDuty != null">
        #{mainDuty,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        #{score,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningDepartmentInfo">
    <!--@mbg.generated-->
    update t_warning_department_info
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="ancestors != null">
        ancestors = #{ancestors,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="organizationCode != null">
        organization_code = #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="leaderId != null">
        leader_id = #{leaderId,jdbcType=BIGINT},
      </if>
      <if test="leaderName != null">
        leader_name = #{leaderName,jdbcType=VARCHAR},
      </if>
      <if test="leaderMobile != null">
        leader_mobile = #{leaderMobile,jdbcType=VARCHAR},
      </if>
      <if test="leaderAccountId != null">
        leader_account_id = #{leaderAccountId,jdbcType=VARCHAR},
      </if>
      <if test="leaderEmployeeCode != null">
        leader_employee_code = #{leaderEmployeeCode,jdbcType=VARCHAR},
      </if>
      <if test="messengerId != null">
        messenger_id = #{messengerId,jdbcType=BIGINT},
      </if>
      <if test="messengerName != null">
        messenger_name = #{messengerName,jdbcType=VARCHAR},
      </if>
      <if test="messengerMobile != null">
        messenger_mobile = #{messengerMobile,jdbcType=VARCHAR},
      </if>
      <if test="messengerAccountId != null">
        messenger_account_id = #{messengerAccountId,jdbcType=VARCHAR},
      </if>
      <if test="messengerEmployeeCode != null">
        messenger_employee_code = #{messengerEmployeeCode,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="regulatedIndustry != null">
        regulated_industry = #{regulatedIndustry,jdbcType=VARCHAR},
      </if>
      <if test="mainDuty != null">
        main_duty = #{mainDuty,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningDepartmentInfo">
    <!--@mbg.generated-->
    update t_warning_department_info
    set parent_id = #{parentId,jdbcType=BIGINT},
      ancestors = #{ancestors,jdbcType=VARCHAR},
      order_num = #{orderNum,jdbcType=INTEGER},
      organization_code = #{organizationCode,jdbcType=VARCHAR},
      department_name = #{departmentName,jdbcType=VARCHAR},
      leader_id = #{leaderId,jdbcType=BIGINT},
      leader_name = #{leaderName,jdbcType=VARCHAR},
      leader_mobile = #{leaderMobile,jdbcType=VARCHAR},
      leader_account_id = #{leaderAccountId,jdbcType=VARCHAR},
      leader_employee_code = #{leaderEmployeeCode,jdbcType=VARCHAR},
      messenger_id = #{messengerId,jdbcType=BIGINT},
      messenger_name = #{messengerName,jdbcType=VARCHAR},
      messenger_mobile = #{messengerMobile,jdbcType=VARCHAR},
      messenger_account_id = #{messengerAccountId,jdbcType=VARCHAR},
      messenger_employee_code = #{messengerEmployeeCode,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      area_code = #{areaCode,jdbcType=VARCHAR},
      regulated_industry = #{regulatedIndustry,jdbcType=VARCHAR},
      main_duty = #{mainDuty,jdbcType=VARCHAR},
      score = #{score,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_department_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ancestors = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ancestors,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="order_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.orderNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="organization_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.organizationCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="department_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.departmentName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="leader_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.leaderId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="leader_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.leaderName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="leader_mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.leaderMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="leader_account_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.leaderAccountId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="leader_employee_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.leaderEmployeeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="messenger_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.messengerId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="messenger_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.messengerName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="messenger_mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.messengerMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="messenger_account_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.messengerAccountId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="messenger_employee_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.messengerEmployeeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="area = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.area,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="area_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.areaCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="regulated_industry = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.regulatedIndustry,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="main_duty = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.mainDuty,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="score = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.score,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_department_info
    (parent_id, ancestors, order_num, organization_code, department_name, leader_id,
      leader_name, leader_mobile, leader_account_id, leader_employee_code, messenger_id,
      messenger_name, messenger_mobile, messenger_account_id, messenger_employee_code,
      area, area_code, regulated_industry, main_duty, score, create_time, create_by,
      update_time, update_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.parentId,jdbcType=BIGINT}, #{item.ancestors,jdbcType=VARCHAR}, #{item.orderNum,jdbcType=INTEGER},
        #{item.organizationCode,jdbcType=VARCHAR}, #{item.departmentName,jdbcType=VARCHAR},
        #{item.leaderId,jdbcType=BIGINT}, #{item.leaderName,jdbcType=VARCHAR}, #{item.leaderMobile,jdbcType=VARCHAR},
        #{item.leaderAccountId,jdbcType=VARCHAR}, #{item.leaderEmployeeCode,jdbcType=VARCHAR},
        #{item.messengerId,jdbcType=BIGINT}, #{item.messengerName,jdbcType=VARCHAR}, #{item.messengerMobile,jdbcType=VARCHAR},
        #{item.messengerAccountId,jdbcType=VARCHAR}, #{item.messengerEmployeeCode,jdbcType=VARCHAR},
        #{item.area,jdbcType=VARCHAR}, #{item.areaCode,jdbcType=VARCHAR}, #{item.regulatedIndustry,jdbcType=VARCHAR},
        #{item.mainDuty,jdbcType=VARCHAR}, #{item.score,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

    <select id="selectWarningDepartmentInfoList" parameterType="WarningDepartmentInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List">
        </include>
        from t_warning_department_info
        <where>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="organizationCode != null  and organizationCode != ''">
                and organization_code = #{organizationCode}
            </if>
            <if test="departmentName != null  and departmentName != ''">
                and department_name like concat('%', #{departmentName}, '%')
            </if>
            <if test="leaderId != null">
                and leader_id = #{leaderId}
            </if>
            <if test="leaderName != null  and leaderName != ''">
                and leader_name like concat('%', #{leaderName}, '%')
            </if>
            <if test="leaderMobile != null  and leaderMobile != ''">
                and leader_mobile = #{leaderMobile}
            </if>
            <if test="messengerId != null">
                and messenger_id = #{messengerId}
            </if>
            <if test="messengerName != null  and messengerName != ''">
                and messenger_name like concat('%', #{messengerName}, '%')
            </if>
            <if test="messengerMobile != null  and messengerMobile != ''">
                and messenger_mobile = #{messengerMobile}
            </if>
            <if test="area != null  and area != ''">
                and area = #{area}
            </if>
            <if test="areaCode != null  and areaCode != ''">
                and area_code = #{areaCode}
            </if>
            <if test="regulatedIndustry != null  and regulatedIndustry != ''">
                and regulated_industry = #{regulatedIndustry}
            </if>
            <if test="mainDuty != null  and mainDuty != ''">
                and main_duty = #{mainDuty}
            </if>
            <if test="score != null  and score != ''">
                and score = #{score}
            </if>
        </where>
        order by order_num
    </select>

    <select id="selectWarningDepartmentInfoById" parameterType="Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List">
    </include>
        from t_warning_department_info
        where id = #{id}
    </select>

    <insert id="insertWarningDepartmentInfo" keyProperty="id" parameterType="WarningDepartmentInfo" useGeneratedKeys="true">
        insert into t_warning_department_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="organizationCode != null">
                organization_code,
            </if>
            <if test="departmentName != null">
                department_name,
            </if>
            <if test="leaderId != null">
                leader_id,
            </if>
            <if test="leaderName != null">
                leader_name,
            </if>
            <if test="leaderMobile != null">
                leader_mobile,
            </if>
            <if test="messengerId != null">
                messenger_id,
            </if>
            <if test="messengerName != null">
                messenger_name,
            </if>
            <if test="messengerMobile != null">
                messenger_mobile,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="areaCode != null">
                area_code,
            </if>
            <if test="regulatedIndustry != null">
                regulated_industry,
            </if>
            <if test="mainDuty != null">
                main_duty,
            </if>
            <if test="score != null">
                score,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="organizationCode != null">
                #{organizationCode},
            </if>
            <if test="departmentName != null">
                #{departmentName},
            </if>
            <if test="leaderId != null">
                #{leaderId},
            </if>
            <if test="leaderName != null">
                #{leaderName},
            </if>
            <if test="leaderMobile != null">
                #{leaderMobile},
            </if>
            <if test="messengerId != null">
                #{messengerId},
            </if>
            <if test="messengerName != null">
                #{messengerName},
            </if>
            <if test="messengerMobile != null">
                #{messengerMobile},
            </if>
            <if test="area != null">
                #{area},
            </if>
            <if test="areaCode != null">
                #{areaCode},
            </if>
            <if test="regulatedIndustry != null">
                #{regulatedIndustry},
            </if>
            <if test="mainDuty != null">
                #{mainDuty},
            </if>
            <if test="score != null">
                #{score},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
        </trim>
    </insert>

    <update id="updateWarningDepartmentInfo" parameterType="WarningDepartmentInfo">
        update t_warning_department_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="organizationCode != null">
                organization_code = #{organizationCode},
            </if>
            <if test="departmentName != null">
                department_name = #{departmentName},
            </if>
            <if test="leaderId != null">
                leader_id = #{leaderId},
            </if>
            <if test="leaderName != null">
                leader_name = #{leaderName},
            </if>
            <if test="leaderMobile != null">
                leader_mobile = #{leaderMobile},
            </if>
            <if test="messengerId != null">
                messenger_id = #{messengerId},
            </if>
            <if test="messengerName != null">
                messenger_name = #{messengerName},
            </if>
            <if test="messengerMobile != null">
                messenger_mobile = #{messengerMobile},
            </if>
            <if test="area != null">
                area = #{area},
            </if>
            <if test="areaCode != null">
                area_code = #{areaCode},
            </if>
            <if test="regulatedIndustry != null">
                regulated_industry = #{regulatedIndustry},
            </if>
            <if test="mainDuty != null">
                main_duty = #{mainDuty},
            </if>
            <if test="score != null">
                score = #{score},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWarningDepartmentInfoById" parameterType="Long">
        delete
        from t_warning_department_info
        where id = #{id}
    </delete>

    <delete id="deleteWarningDepartmentInfoByIds" parameterType="String">
        delete
        from t_warning_department_info where id in
        <foreach close=")" collection="array" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </delete>

    <select id="listByParentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_warning_department_info
        where parent_id = #{parentId}
        order by order_num
    </select>

  <select id="getByOrgCode" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_department_info
    where organization_code = #{organizationCode,jdbcType=VARCHAR}
    limit 1
  </select>

  <select id="listCopyDept" resultType="com.ruoyi.jiangshan.domain.WarningDepartmentInfo">
    select dept_id as id,
      parent_id as parentId,
      ancestors as ancestors,
      order_num as orderNum,
      dept_name as departmentName
    from sys_dept
    where dept_name like concat('%',#{keywords,jdbcType=VARCHAR},'%')
    and dept_id in (222, 3257, 3256)
    </select>
</mapper>
