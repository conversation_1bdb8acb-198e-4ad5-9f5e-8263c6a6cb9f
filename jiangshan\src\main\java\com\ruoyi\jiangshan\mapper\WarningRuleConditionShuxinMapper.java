package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WarningRuleConditionShuxinMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningRuleConditionShuxin record);

    int insertSelective(WarningRuleConditionShuxin record);

    WarningRuleConditionShuxin selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningRuleConditionShuxin record);

    int updateByPrimaryKey(WarningRuleConditionShuxin record);

    int updateBatch(List<WarningRuleConditionShuxin> list);

    int batchInsert(@Param("list") List<WarningRuleConditionShuxin> list);

    List<WarningRuleConditionShuxin> listByModelId(Long id);

    List<String> getArea();

    WarningRuleConditionShuxin getByFxqbh(@Param("fxqbh") String fxqbh);

    void updateByFxqbh(WarningRuleConditionShuxin apiVO);
}
