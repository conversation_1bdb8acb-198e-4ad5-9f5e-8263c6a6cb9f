<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.EmergencyMaterialMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.EmergencyMaterial">
    <!--@mbg.generated-->
    <!--@Table t_emergency_material-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="street" jdbcType="VARCHAR" property="street" />
    <result column="village" jdbcType="VARCHAR" property="village" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="material_category" jdbcType="VARCHAR" property="materialCategory" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="storage_time" jdbcType="VARCHAR" property="storageTime" />
    <result column="storage_count" jdbcType="VARCHAR" property="storageCount" />
    <result column="current_count" jdbcType="VARCHAR" property="currentCount" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_mobile" jdbcType="VARCHAR" property="userMobile" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, street, village, `level`, material_category, material_name, storage_time, storage_count,
    current_count, user_name, user_mobile, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_emergency_material
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_emergency_material
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.EmergencyMaterial" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_emergency_material (street, village, `level`,
      material_category, material_name, storage_time,
      storage_count, current_count, user_name,
      user_mobile, remark)
    values (#{street,jdbcType=VARCHAR}, #{village,jdbcType=VARCHAR}, #{level,jdbcType=VARCHAR},
      #{materialCategory,jdbcType=VARCHAR}, #{materialName,jdbcType=VARCHAR}, #{storageTime,jdbcType=VARCHAR},
      #{storageCount,jdbcType=VARCHAR}, #{currentCount,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR},
      #{userMobile,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.EmergencyMaterial" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_emergency_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="street != null">
        street,
      </if>
      <if test="village != null">
        village,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="materialCategory != null">
        material_category,
      </if>
      <if test="materialName != null">
        material_name,
      </if>
      <if test="storageTime != null">
        storage_time,
      </if>
      <if test="storageCount != null">
        storage_count,
      </if>
      <if test="currentCount != null">
        current_count,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userMobile != null">
        user_mobile,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="street != null">
        #{street,jdbcType=VARCHAR},
      </if>
      <if test="village != null">
        #{village,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="materialCategory != null">
        #{materialCategory,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="storageTime != null">
        #{storageTime,jdbcType=VARCHAR},
      </if>
      <if test="storageCount != null">
        #{storageCount,jdbcType=VARCHAR},
      </if>
      <if test="currentCount != null">
        #{currentCount,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userMobile != null">
        #{userMobile,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.EmergencyMaterial">
    <!--@mbg.generated-->
    update t_emergency_material
    <set>
      <if test="street != null">
        street = #{street,jdbcType=VARCHAR},
      </if>
      <if test="village != null">
        village = #{village,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=VARCHAR},
      </if>
      <if test="materialCategory != null">
        material_category = #{materialCategory,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        material_name = #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="storageTime != null">
        storage_time = #{storageTime,jdbcType=VARCHAR},
      </if>
      <if test="storageCount != null">
        storage_count = #{storageCount,jdbcType=VARCHAR},
      </if>
      <if test="currentCount != null">
        current_count = #{currentCount,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userMobile != null">
        user_mobile = #{userMobile,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.EmergencyMaterial">
    <!--@mbg.generated-->
    update t_emergency_material
    set street = #{street,jdbcType=VARCHAR},
      village = #{village,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=VARCHAR},
      material_category = #{materialCategory,jdbcType=VARCHAR},
      material_name = #{materialName,jdbcType=VARCHAR},
      storage_time = #{storageTime,jdbcType=VARCHAR},
      storage_count = #{storageCount,jdbcType=VARCHAR},
      current_count = #{currentCount,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      user_mobile = #{userMobile,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_emergency_material
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="street = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.street,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="village = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.village,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`level` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="material_category = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.materialCategory,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="material_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.materialName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="storage_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.storageTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="storage_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.storageCount,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="current_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.currentCount,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.userName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="user_mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.userMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_emergency_material
    (street, village, `level`, material_category, material_name, storage_time, storage_count,
      current_count, user_name, user_mobile, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.street,jdbcType=VARCHAR}, #{item.village,jdbcType=VARCHAR}, #{item.level,jdbcType=VARCHAR},
        #{item.materialCategory,jdbcType=VARCHAR}, #{item.materialName,jdbcType=VARCHAR},
        #{item.storageTime,jdbcType=VARCHAR}, #{item.storageCount,jdbcType=VARCHAR}, #{item.currentCount,jdbcType=VARCHAR},
        #{item.userName,jdbcType=VARCHAR}, #{item.userMobile,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

  <select id="listByStreetAndVillage" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_emergency_material
    where street = #{street,jdbcType=VARCHAR}
    <if test="village != null and village != ''">
      and village = #{village,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>
