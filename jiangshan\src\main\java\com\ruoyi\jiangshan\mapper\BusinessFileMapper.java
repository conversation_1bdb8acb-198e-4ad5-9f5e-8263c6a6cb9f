package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.BusinessFile;

import java.util.ArrayList;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BusinessFileMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BusinessFile record);

    int insertSelective(BusinessFile record);

    BusinessFile selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BusinessFile record);

    int updateByPrimaryKey(BusinessFile record);

    int updateBatch(List<BusinessFile> list);

    int batchInsert(@Param("list") List<BusinessFile> list);

    List<BusinessFile> listByBusinessIdListAndType(@Param("idList") List<Long> idList,
                                                   @Param("type") Integer type);

    void deleteByBusinessIdAndTypeList(@Param("id") Long id, @Param("list") List<Integer> typeList);

    List<BusinessFile> listByBusinessIdListAndTypeList(@Param("monitorIdList") List<Long> monitorIdList, @Param("typeList") List<Integer> typeList);


    List<BusinessFile> listByType(@Param("type") Integer type);
}
