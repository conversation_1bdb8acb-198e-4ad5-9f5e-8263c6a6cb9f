package com.ruoyi.jiangshan.service.impl.irs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.jiangshan.constant.IrsConstants;
import com.ruoyi.jiangshan.domain.TbIrsResult;
import com.ruoyi.jiangshan.service.irs.BaseIrsService;
import com.ruoyi.jiangshan.util.HttpUtil;
import com.ruoyi.jiangshan.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * irs请求
 *
 * <AUTHOR>
 * @date 2022年11月18日 15:23
 */
@Service
@Slf4j
public class BaseIrsServiceImpl implements BaseIrsService {

    private Map<String, String> provinceTokenMap = new HashMap<>();

    /**
     * irs接口查询
     *
     * @param tbIrsResult
     * @return
     */
    @Override
    public Object actuator(TbIrsResult tbIrsResult, Map<String, String> keyParams) {
        log.info("鉴权接口开始---------------------");

        if (MapUtils.isEmpty(keyParams)) {
            keyParams = getProvinceToken(IrsConstants.APP_KEY, IrsConstants.APP_SECRET);
        }

        Map<String, String> params = new TreeMap<>();
        params.put("requestTime", keyParams.get("requestTime"));
        params.put("sign", keyParams.get("sign"));
        params.put("appKey", keyParams.get("appKey"));
        params.put("Observtimes", tbIrsResult.getObservtimes());

        log.info("鉴权接口结束---------------------");

        log.info(tbIrsResult.getName() + ":irs开始调用---------------- url:" + tbIrsResult.getUrl());

        log.info("tbIrsResult:{}, keyParams:{}", tbIrsResult, keyParams);
        //请求irs接口
        HttpUtil httpUtil = HttpUtil.getInstance();
        String resultData = httpUtil.sendHttpPost(tbIrsResult.getUrl(), params);

        log.info(tbIrsResult.getName() + "irs接口返回:{}", resultData);

        if (StringUtils.isBlank(resultData)) {
            //重试
            refreshRequestToken();
        }

        resultData = resultData.replace("{\\\"code\\\":500,\\\"message\\\":\\\"服务器异常\\\"}", "");
        log.info(tbIrsResult.getName() + "irs调用结束");

        try {
            //调用irs结果
            Map resultDataMap = JSONObject.parseObject(resultData, Map.class);

            //第一层datas数据
            Object object = resultDataMap.get("datas");
            if (StringUtils.isNull(object)) {
                Map resultMap = (Map) resultDataMap.get("data");

                return resultMap;
            }

            Map datasMaps = null;
            try {
                //数据在datas data下面
                datasMaps = JSONObject.parseObject(object.toString(), Map.class);
            } catch (Exception e) {
                //datas数据转JSON
                List<LinkedHashMap> datalist = parseObjOfString(JSONObject.parseArray(object.toString(), LinkedHashMap.class));

                resultDataMap.put("datas", datalist);

                log.info("irs接口开始响应----------");
                return resultDataMap;
            }


            if (StringUtils.isNotNull(datasMaps.get("data"))) {
                //不分页获取datas里面data
                String data = JSONObject.toJSONString(datasMaps.get("data"));

                //data数据转JSON
                Map resultMap = JSONObject.parseObject(data, LinkedHashMap.class);

                return resultMap;
            }

            Object datas = resultDataMap.get("datas");
            if (StringUtils.isNotNull(datas)) {
                datasMaps = JSONObject.parseObject(datas.toString(), Map.class);
                resultDataMap.put("datas", datasMaps);
            }
            return JSON.toJSONString(resultDataMap);
        } catch (Exception e) {
            log.error("irs接口解析json失败", e.getMessage());
            return resultData;
        }

        /*String str = JSONObject.toJSONString(tbIrsResult);
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, str);
        Request request = new Request.Builder()
                .url("https://csdn.dsjj.jinhua.gov.cn:9601/adm-api/mis/irs/actuator")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", getCsdnToken())
                .build();
        try {
            Response response = client.newCall(request).execute();
            String string = response.body().string();
            return string;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }*/
    }

    public Map<String, String> getProvinceToken(String appKey, String appSecret) {
        try {
            long time = System.currentTimeMillis();

            HttpUtil httpUtil = HttpUtil.getInstance();
            String sign = Md5Utils.hash(appKey + appSecret + time);
            Map<String, String> params = new TreeMap<>();
            params.put("appKey", appKey);
            params.put("sign", sign);
            params.put("requestTime", +time + "");
            String result = httpUtil.sendHttpPost(IrsConstants.CITY_URL, params);

            log.info("getProvinceToken, result:{}", result);

            JSONObject jsonObject = JSONObject.parseObject(result);
            ObjectMapper objectMapper = new ObjectMapper();
            Map map = objectMapper.convertValue(jsonObject, Map.class);
            Object datas = map.get("datas");
            Map datasMap = objectMapper.convertValue(datas, Map.class);
            Object requestSecretObject = datasMap.get("requestSecret");
            String requestSecret = requestSecretObject.toString();

            Object refreshSecret = datasMap.get("refreshSecret");
            String refreshSecretStr = refreshSecret.toString();
            params.clear();

            String dataSign = Md5Utils.hash(appKey + requestSecret + time);
            params.put("requestTime", time + "");
            params.put("sign", dataSign);
            params.put("appKey", appKey);
            params.put("refreshSecret", refreshSecretStr);

            provinceTokenMap = params;

            log.info("refreshRefreshToken:{}", JSON.toJSONString(provinceTokenMap));

            return params;
        } catch (Exception e) {
            Map<String, String> map = new HashMap<>();
            map.put("message", "获取省秘钥失败");
            map.put("data", e.getMessage());
            return map;
        }
    }

    public Map<String, String> getProvinceTokenRefresh(String appKey, String appSecret) {
        try {
            long time = System.currentTimeMillis();

            HttpUtil httpUtil = HttpUtil.getInstance();
            String sign = Md5Utils.hash(appKey + appSecret + time);
            Map<String, String> params = new TreeMap<>();
            params.put("appKey", appKey);
            params.put("sign", sign);
            params.put("requestTime", +time + "");
            String result = httpUtil.sendHttpPost(IrsConstants.CITY_URL_REFRESH, params);

            log.info("getProvinceToken, result:{}", result);

            JSONObject jsonObject = JSONObject.parseObject(result);
            ObjectMapper objectMapper = new ObjectMapper();
            Map map = objectMapper.convertValue(jsonObject, Map.class);
            Object datas = map.get("datas");
            Map datasMap = objectMapper.convertValue(datas, Map.class);
            Object requestSecretObject = datasMap.get("requestSecret");
            String requestSecret = requestSecretObject.toString();

            Object refreshSecret = datasMap.get("refreshSecret");
            String refreshSecretStr = refreshSecret.toString();
            params.clear();

            String dataSign = Md5Utils.hash(appKey + requestSecret + time);
            params.put("requestTime", time + "");
            params.put("sign", dataSign);
            params.put("appKey", appKey);
            params.put("refreshSecret", refreshSecretStr);

            provinceTokenMap = params;

            log.info("refreshRefreshToken:{}", JSON.toJSONString(provinceTokenMap));

            return params;
        } catch (Exception e) {
            Map<String, String> map = new HashMap<>();
            map.put("message", "获取省秘钥失败");
            map.put("data", e.getMessage());
            return map;
        }
    }

    @Override
    public void refreshRequestToken() {
        String refreshSecret = provinceTokenMap.get("refreshSecret");

        if (StringUtils.isBlank(refreshSecret)) {
            return;
        }

        provinceTokenMap = getProvinceTokenRefresh(IrsConstants.APP_KEY, refreshSecret);

        log.info("refreshRequestToken:{}", JSON.toJSONString(provinceTokenMap));
    }

    @Override
    public Map<String, String> getRequestMap() {
        return provinceTokenMap;
    }


    /**
     * 转json
     *
     * @param dataList
     * @return
     */
    public List<LinkedHashMap> parseObjOfString(List<LinkedHashMap> dataList) {
        if (dataList != null) {
            for (int i = 0; i < dataList.size(); i++) {
                if (dataList.get(i) != null) {
                    for (Object name : dataList.get(i).keySet()) {
                        if (StringUtils.isNotBlank(String.valueOf(dataList.get(i).get(name)))
                                && ("[".equals(String.valueOf(dataList.get(i).get(name)).substring(0, 1))
                                || ("{".equals(String.valueOf(dataList.get(i).get(name)).substring(0, 1))))) {
                            String str = String.valueOf(dataList.get(i).get(name));
                            //判断字符串是否能转为son
                            boolean isJson = JsonUtils.isJson(str);
                            if (isJson) {
                                //可以转为json
                                dataList.get(i).put(name, JSONObject.parse(String.valueOf(dataList.get(i).get(name))));
                            } else {
                                //不能转为json
                                dataList.get(i).put(name, str);
                            }
                        }
                    }
                }
            }
        }
        return dataList;
    }
}
