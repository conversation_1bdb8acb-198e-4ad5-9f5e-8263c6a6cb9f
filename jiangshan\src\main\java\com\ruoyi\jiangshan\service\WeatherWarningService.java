package com.ruoyi.jiangshan.service;

import com.ruoyi.jiangshan.domain.WeatherWarning;

import java.util.List;

/**
 * 气象预警+专业监测预警Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface WeatherWarningService
{
    /**
     * 查询气象预警+专业监测预警
     *
     * @param id 气象预警+专业监测预警主键
     * @return 气象预警+专业监测预警
     */
    public WeatherWarning selectWeatherWarningById(Long id);

    /**
     * 查询气象预警+专业监测预警列表
     *
     * @param weatherWarning 气象预警+专业监测预警
     * @return 气象预警+专业监测预警集合
     */
    public List<WeatherWarning> selectWeatherWarningList(WeatherWarning weatherWarning);

    /**
     * 新增气象预警+专业监测预警
     *
     * @param weatherWarning 气象预警+专业监测预警
     * @return 结果
     */
    public int insertWeatherWarning(WeatherWarning weatherWarning);

    /**
     * 修改气象预警+专业监测预警
     *
     * @param weatherWarning 气象预警+专业监测预警
     * @return 结果
     */
    public int updateWeatherWarning(WeatherWarning weatherWarning);

    /**
     * 批量删除气象预警+专业监测预警
     *
     * @param ids 需要删除的气象预警+专业监测预警主键集合
     * @return 结果
     */
    public int deleteWeatherWarningByIds(Long[] ids);

    /**
     * 删除气象预警+专业监测预警信息
     *
     * @param id 气象预警+专业监测预警主键
     * @return 结果
     */
    public int deleteWeatherWarningById(Long id);
}
