package com.ruoyi.jiangshan.domain;

import lombok.Data;

import java.util.Date;

/**
    * 海康威视视频监控
    */
@Data
public class DeviceInfoHikvisionVideo {
    /**
    * 主键
    */
    private Long id;

    /**
    * 监控点编号（通用唯一识别码UUID）
    */
    private String cameraIndexCode;

    /**
    * 监控点国标编号
    */
    private String gbIndexCode;

    /**
    * 监控点名称
    */
    private String name;

    /**
    * 所属设备编号（通用唯一识别码UUID）
    */
    private String deviceIndexCode;

    /**
    * 经度（WGS84坐标系）
    */
    private String longitude;

    /**
    * 纬度（WGS84坐标系）
    */
    private String latitude;

    /**
    * 海拔高度（WGS84坐标系，单位：米）
    */
    private String altitude;

    /**
    * 摄像机像素（1-普通像素，2-130万高清，3-200万高清，4-300万高清，取值参考数据字典，typeCode为xresmgr.piexl）
    */
    private Integer pixel;

    /**
    * 监控点类型（0-枪机,1-半球,2-快球,3-带云台枪机,取值参考数据字典，typeCode为xresmgr.camera_type）
    */
    private Integer cameraType;

    /**
    * 监控点类型说明
    */
    private String cameraTypeName;

    /**
    * 安装位置
    */
    private String installPlace;

    /**
    * 矩阵编号
    */
    private String matrixCode;

    /**
    * 矩阵编号
    */
    private String chanNum;

    /**
    * 可视域相关（JSON格式），该字段具体使用方式参考【监控点可视域字段说明】。
    */
    private String viewshed;

    /**
    * 能力集（详见数据字典，typeCode为xresmgr.capability_set）
    */
    private String capabilitySet;

    /**
    * 能力集说明
    */
    private String capabilitySetName;

    /**
    * 智能分析能力集（详见数据字典，typeCode为xresmgr.intelligent_set）
    */
    private String intelligentSet;

    /**
    * 智能分析能力集说明
    */
    private String intelligentSetName;

    /**
    * 录像存储位置（0-中心存储，1-设备存储，取值参考数据字典，typeCode为xresmgr.record_location）
    */
    private String recordLocation;

    /**
    * 录像存储位置说明
    */
    private String recordLocationName;

    /**
    * 云台控制（1-DVR,2-模拟矩阵,3-MU4000,4-NC600，取值参考数据字典，typeCode为xresmgr.ptz_control_type）
    */
    private Integer ptzController;

    /**
    * 云台控制说明
    */
    private String ptzControllerName;

    /**
    * 所属设备类型（详见数据字典，typeCode为xresmgr.resource_type）
    */
    private String deviceResourceType;

    /**
    * 所属设备类型说明
    */
    private String deviceResourceTypeName;

    /**
    * 通道子类型（详见数据字典，typeCode为xresmgr.device_type_code.camera）
    */
    private String channelType;

    /**
    * 通道子类型说明
    */
    private String channelTypeName;

    /**
    * 传输协议（0-UDP，1-TCP，取值参考数据字典，typeCode为xresmgr.transType）
    */
    private Integer transType;

    /**
    * 传输协议类型说明
    */
    private String transTypeName;

    /**
    * 监控点更新时间（ISO8601格式yyyy-MM-dd
    */
    private Date updateTime;

    /**
    * 所属组织编号（通用唯一识别码UUID）
    */
    private String unitIndexCode;

    /**
    * 接入协议（详见数据字典，typeCode为xresmgr.protocol_type）
    */
    private String treatyType;

    /**
    * 接入协议类型说明
    */
    private String treatyTypeName;

    /**
    * 监控点创建时间（ISO8601格式yyyy-MM-dd
    */
    private Date createTime;

    /**
    * 在线状态（0-不在线，1-在线，取值参考数据字典，typeCode为xresmgr.status）
    */
    private Integer status;

    /**
    * 状态说明
    */
    private String statusname;
}
