package com.ruoyi.jiangshan.service.impl;

import com.google.common.collect.Lists;
import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.service.BridgeSceneService;
import com.ruoyi.jiangshan.service.IDeviceInfoService;
import com.ruoyi.jiangshan.service.IDeviceMonitorService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.vo.BridgeMaintanceVO;
import com.ruoyi.jiangshan.vo.BusinessCountVO;
import com.ruoyi.jiangshan.vo.WarningCountVO;
import com.ruoyi.jiangshan.vo.WarningInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BridgeSceneServiceImpl implements BridgeSceneService {
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private WarningEventSuperviseMapper warningEventSuperviseMapper;
    @Autowired
    private IDeviceMonitorService deviceMonitorService;
    @Autowired
    private IWarningEventService warningEventService;

    private final static String TYPE = "桥梁";
    private final static String SCENE = MonitorSceneType.SCENE_02.getDesc();
    private static List<String> DEVICE_TYPE_LIST = Lists.newArrayList("视频监控", "视觉形变监测仪");

    @Autowired
    private DeviceInfoTypeMapper deviceInfoTypeMapper;
    @PostConstruct
    public void init() {
        DEVICE_TYPE_LIST = deviceInfoTypeMapper.listByScene(SCENE);
    }

    @Override
    public Map<String, Object> getEquipment() {
        Map<String, Object> resultMap = new HashMap<>();

        List<BusinessCountVO> countVOList = deviceInfoMapper.countByMonitorSceneAndStatus(SCENE);

        Map<String, Long> existMap = countVOList.stream()
                .collect(Collectors.toMap(BusinessCountVO::getKey,BusinessCountVO::getValue));

        if (MapUtils.isEmpty(existMap)) {
            resultMap.put("online", 0L);
            resultMap.put("offline", 0L);
            resultMap.put("maintain", 0L);
        } else {
            resultMap.put("offline", Objects.isNull(existMap.get("0")) ? 0L : existMap.get("0"));
            resultMap.put("online", Objects.isNull(existMap.get("1")) ? 0L : existMap.get("1"));
            resultMap.put("maintain", Objects.isNull(existMap.get("2")) ? 0L : existMap.get("2"));
        }

        return resultMap;
    }

    @Override
    public List<DeviceMonitor> getBridgeMonitor() {
        List<DeviceMonitor> resultList = deviceMonitorService.getMonitor(TYPE);

        return resultList;
    }

    @Override
    public WarningCountVO getSafetyCount(String dateStr) {
        return warningEventService.getSafetyCount(dateStr, SCENE);
    }

    @Override
    public List<WarningInfoVO> getWarning(Integer warningLevel) {
        return warningInfoMapper.listByLevelAndScene(warningLevel, SCENE);
    }

    @Override
    public List<WarningEvent> getEvent(String eventName, Integer status, Integer warningLevel) {
        return warningEventMapper.listByConditionLimit(SCENE, eventName, status, warningLevel, 3);
    }

    @Override
    public List<BridgeMaintanceVO> getMaintance(Long monitorId) {
        // TODO: 2024/8/28 桥梁维护统计

//        return Lists.newArrayList(new BridgeMaintanceVO("桥梁维护", "100万元", new Date()));
        return Lists.newArrayList();
    }

    @Override
    public List<DeviceMonitor> getAllBridge() {
        return deviceMonitorMapper.listByType(Lists.newArrayList(TYPE));
    }

    @Override
    public List<DeviceInfo> listBridgeVideo(Long monitorId) {
        return deviceInfoMapper.listByMonitorItemAndDeviceType(monitorId, "视频监控");
    }

    @Override
    public List<DeviceInfo> listBridgeDevice() {
        return deviceInfoMapper.listByScene(SCENE);
    }

}
