package com.ruoyi.jiangshan.vo.hk;

import lombok.Data;

@Data
public class HkControlVideoVO {

    /**
     * 设备编号
     */
    private String cameraIndexCode;

    /**
     * 开始或停止操作(0 开始  1 停止)
     */
    private Integer action;

    /**
     * 控制命令(不区分大小写)说明：
     * LEFT左转
     * RIGHT右转
     * UP上转
     * DOWN下转
     * ZOOM_IN焦距变大
     * ZOOM_OUT焦距变小
     * LEFT_UP左上
     * LEFT_DOWN左下
     * RIGHT_UP右上
     * RIGHT_DOWN右下
     * FOCUS_NEAR焦点前移
     * FOCUS_FAR焦点后移
     * IRIS_ENLARGE光圈扩大
     * IRIS_REDUCE光圈缩小
     * 以下命令presetIndex不可为空：
     * GOTO_PRESET到预置点
     */
    private String command;

    /**
     * 云台速度(取值范围1-100,默认40)
     */
    private String speed;

    /**
     * 预置点编号(取值范围为1-128)
     */
    private Integer presetIndex;
}
