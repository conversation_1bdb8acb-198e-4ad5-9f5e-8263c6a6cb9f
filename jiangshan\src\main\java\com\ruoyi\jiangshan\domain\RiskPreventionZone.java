package com.ruoyi.jiangshan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 风险防范区对象 t_risk_prevention_zone
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class RiskPreventionZone extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 编号 */
    @Excel(name = "编号")
    private String num;

    /** 风险区名称 */
    @Excel(name = "风险区名称")
    private String name;

    /** 行政区编号 */
    @Excel(name = "行政区编号")
    private String areaCode;

    /** 所在市 */
    @Excel(name = "所在市")
    private String szs;

    /** 所在区县 */
    @Excel(name = "所在区县")
    private String szqx;

    /** 所在乡镇 */
    @Excel(name = "所在乡镇")
    private String szz;

    /** 风险等级(00:较低,01:低,02:较高,03:高) */
    @Excel(name = "风险等级", readConverterExp = "00=较低,01=低,02=较高,03=高")
    private String riskLevel;

    /** 稳定性(00:较稳定,01:不稳定,02:极不稳定) */
    @Excel(name = "稳定性", readConverterExp = "00=较稳定,01=不稳定,02=极不稳定")
    private String stability;

    /** 防范类型(00:重点防范区,01:次重点防范区,02:一般防范区) */
    @Excel(name = "防范类型", readConverterExp = "00=重点防范区,01=次重点防范区,02=一般防范区")
    private String datatype;

    /** 状态(0:新增,1:现状,2:核减校核中,3:核减校核退回,4:空) */
    @Excel(name = "状态", readConverterExp = "0=新增,1=现状,2=核减校核中,3=核减校核退回,4=空")
    private String status;

    /** 受影响人数 */
    @Excel(name = "受影响人数")
    private Integer affectedPeople;

    /** 受影响户数 */
    @Excel(name = "受影响户数")
    private Integer affectedFamily;

    /** 受影响财产(万元) */
    @Excel(name = "受影响财产(万元)")
    private BigDecimal affectedAsset;

    /** 常住人口 */
    @Excel(name = "常住人口")
    private Integer permanentResidents;

    /** 致灾体类型(00:滑坡隐患,01:崩塌隐患,02:泥石流隐患,03:地面塌陷隐患,04:风险点,05:不) */
    @Excel(name = "致灾体类型", readConverterExp = "00=滑坡隐患,01=崩塌隐患,02=泥石流隐患,03=地面塌陷隐患,04=风险点,05=不")
    private String hazardAffectedBody;

    /** 所在地负责人 */
    @Excel(name = "所在地负责人")
    private String placeLeader;

    /** 所在地负责人电话 */
    @Excel(name = "所在地负责人电话")
    private String placeLeaderTel;

    /** 县级负责人 */
    @Excel(name = "县级负责人")
    private String countyLeader;

    /** 县级负责人电话 */
    @Excel(name = "县级负责人电话")
    private String countyLeaderTel;

    /** 村级负责人 */
    @Excel(name = "村级负责人")
    private String villagesLeader;

    /** 村级负责人电话 */
    @Excel(name = "村级负责人电话")
    private String villageLeaderTel;

    /** 基层责任人 */
    @Excel(name = "基层责任人")
    private String grassRoots;

    /** 基层责任人电话 */
    @Excel(name = "基层责任人电话")
    private String grassRootsTel;

    /** 移交单位 */
    @Excel(name = "移交单位")
    private String handUnit;

    /** 撤销原因(01:工程治理,02:搬迁避让,03:自然消失,04:其他) */
    @Excel(name = "撤销原因", readConverterExp = "01=工程治理,02=搬迁避让,03=自然消失,04=其他")
    private String cancelReason;

    /** 撤销原因其他 */
    @Excel(name = "撤销原因其他")
    private String cancelReasonOther;

    /** 撤销时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "撤销时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date revokeDate;

    /** 上报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上报时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date commitDate;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 面数据 */
    @Excel(name = "面数据")
    private String geo;

    /** 点数据 */
    @Excel(name = "点数据")
    private String pointGeo;

    /** 1小时雨量1级 */
    @Excel(name = "1小时雨量1级")
    private BigDecimal oneHour1;

    /** 1小时雨量2级 */
    @Excel(name = "1小时雨量2级")
    private BigDecimal oneHour2;

    /** 1小时雨量3级 */
    @Excel(name = "1小时雨量3级")
    private BigDecimal oneHour3;

    /** 3小时雨量1级 */
    @Excel(name = "3小时雨量1级")
    private BigDecimal threeHour1;

    /** 3小时雨量2级 */
    @Excel(name = "3小时雨量2级")
    private BigDecimal threeHour2;

    /** 3小时雨量3级 */
    @Excel(name = "3小时雨量3级")
    private BigDecimal threeHour3;

    /** 6小时雨量1级 */
    @Excel(name = "6小时雨量1级")
    private BigDecimal sixHour1;

    /** 6小时雨量2级 */
    @Excel(name = "6小时雨量2级")
    private BigDecimal sixHour2;

    /** 6小时雨量3级 */
    @Excel(name = "6小时雨量3级")
    private BigDecimal sixHour3;

    /** 12小时雨量1级 */
    @Excel(name = "12小时雨量1级")
    private BigDecimal twelveHour1;

    /** 12小时雨量2级 */
    @Excel(name = "12小时雨量2级")
    private BigDecimal twelveHour2;

    /** 12小时雨量3级 */
    @Excel(name = "12小时雨量3级")
    private BigDecimal twelveHour3;

    /** 24小时雨量1级 */
    @Excel(name = "24小时雨量1级")
    private BigDecimal twentyFourHour1;

    /** 24小时雨量2级 */
    @Excel(name = "24小时雨量2级")
    private BigDecimal twentyFourHour2;

    /** 24小时雨量3级 */
    @Excel(name = "24小时雨量3级")
    private BigDecimal twentyFourHour3;

    /** 同步时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "同步时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date syncTime;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setNum(String num)
    {
        this.num = num;
    }

    public String getNum()
    {
        return num;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setAreaCode(String areaCode)
    {
        this.areaCode = areaCode;
    }

    public String getAreaCode()
    {
        return areaCode;
    }
    public void setSzs(String szs)
    {
        this.szs = szs;
    }

    public String getSzs()
    {
        return szs;
    }
    public void setSzqx(String szqx)
    {
        this.szqx = szqx;
    }

    public String getSzqx()
    {
        return szqx;
    }
    public void setSzz(String szz)
    {
        this.szz = szz;
    }

    public String getSzz()
    {
        return szz;
    }
    public void setRiskLevel(String riskLevel)
    {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel()
    {
        return riskLevel;
    }
    public void setStability(String stability)
    {
        this.stability = stability;
    }

    public String getStability()
    {
        return stability;
    }
    public void setDatatype(String datatype)
    {
        this.datatype = datatype;
    }

    public String getDatatype()
    {
        return datatype;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setAffectedPeople(Integer affectedPeople)
    {
        this.affectedPeople = affectedPeople;
    }

    public Integer getAffectedPeople()
    {
        return affectedPeople;
    }
    public void setAffectedFamily(Integer affectedFamily)
    {
        this.affectedFamily = affectedFamily;
    }

    public Integer getAffectedFamily()
    {
        return affectedFamily;
    }
    public void setAffectedAsset(BigDecimal affectedAsset)
    {
        this.affectedAsset = affectedAsset;
    }

    public BigDecimal getAffectedAsset()
    {
        return affectedAsset;
    }
    public void setPermanentResidents(Integer permanentResidents)
    {
        this.permanentResidents = permanentResidents;
    }

    public Integer getPermanentResidents()
    {
        return permanentResidents;
    }
    public void setHazardAffectedBody(String hazardAffectedBody)
    {
        this.hazardAffectedBody = hazardAffectedBody;
    }

    public String getHazardAffectedBody()
    {
        return hazardAffectedBody;
    }
    public void setPlaceLeader(String placeLeader)
    {
        this.placeLeader = placeLeader;
    }

    public String getPlaceLeader()
    {
        return placeLeader;
    }
    public void setPlaceLeaderTel(String placeLeaderTel)
    {
        this.placeLeaderTel = placeLeaderTel;
    }

    public String getPlaceLeaderTel()
    {
        return placeLeaderTel;
    }
    public void setCountyLeader(String countyLeader)
    {
        this.countyLeader = countyLeader;
    }

    public String getCountyLeader()
    {
        return countyLeader;
    }
    public void setCountyLeaderTel(String countyLeaderTel)
    {
        this.countyLeaderTel = countyLeaderTel;
    }

    public String getCountyLeaderTel()
    {
        return countyLeaderTel;
    }
    public void setVillagesLeader(String villagesLeader)
    {
        this.villagesLeader = villagesLeader;
    }

    public String getVillagesLeader()
    {
        return villagesLeader;
    }
    public void setVillageLeaderTel(String villageLeaderTel)
    {
        this.villageLeaderTel = villageLeaderTel;
    }

    public String getVillageLeaderTel()
    {
        return villageLeaderTel;
    }
    public void setGrassRoots(String grassRoots)
    {
        this.grassRoots = grassRoots;
    }

    public String getGrassRoots()
    {
        return grassRoots;
    }
    public void setGrassRootsTel(String grassRootsTel)
    {
        this.grassRootsTel = grassRootsTel;
    }

    public String getGrassRootsTel()
    {
        return grassRootsTel;
    }
    public void setHandUnit(String handUnit)
    {
        this.handUnit = handUnit;
    }

    public String getHandUnit()
    {
        return handUnit;
    }
    public void setCancelReason(String cancelReason)
    {
        this.cancelReason = cancelReason;
    }

    public String getCancelReason()
    {
        return cancelReason;
    }
    public void setCancelReasonOther(String cancelReasonOther)
    {
        this.cancelReasonOther = cancelReasonOther;
    }

    public String getCancelReasonOther()
    {
        return cancelReasonOther;
    }
    public void setRevokeDate(Date revokeDate)
    {
        this.revokeDate = revokeDate;
    }

    public Date getRevokeDate()
    {
        return revokeDate;
    }
    public void setCommitDate(Date commitDate)
    {
        this.commitDate = commitDate;
    }

    public Date getCommitDate()
    {
        return commitDate;
    }
    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }

    public Date getCreateTime()
    {
        return createTime;
    }
    public void setUpdateTime(Date updateTime)
    {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime()
    {
        return updateTime;
    }
    public void setGeo(String geo)
    {
        this.geo = geo;
    }

    public String getGeo()
    {
        return geo;
    }
    public void setPointGeo(String pointGeo)
    {
        this.pointGeo = pointGeo;
    }

    public String getPointGeo()
    {
        return pointGeo;
    }
    public void setOneHour1(BigDecimal oneHour1)
    {
        this.oneHour1 = oneHour1;
    }

    public BigDecimal getOneHour1()
    {
        return oneHour1;
    }
    public void setOneHour2(BigDecimal oneHour2)
    {
        this.oneHour2 = oneHour2;
    }

    public BigDecimal getOneHour2()
    {
        return oneHour2;
    }
    public void setOneHour3(BigDecimal oneHour3)
    {
        this.oneHour3 = oneHour3;
    }

    public BigDecimal getOneHour3()
    {
        return oneHour3;
    }
    public void setThreeHour1(BigDecimal threeHour1)
    {
        this.threeHour1 = threeHour1;
    }

    public BigDecimal getThreeHour1()
    {
        return threeHour1;
    }
    public void setThreeHour2(BigDecimal threeHour2)
    {
        this.threeHour2 = threeHour2;
    }

    public BigDecimal getThreeHour2()
    {
        return threeHour2;
    }
    public void setThreeHour3(BigDecimal threeHour3)
    {
        this.threeHour3 = threeHour3;
    }

    public BigDecimal getThreeHour3()
    {
        return threeHour3;
    }
    public void setSixHour1(BigDecimal sixHour1)
    {
        this.sixHour1 = sixHour1;
    }

    public BigDecimal getSixHour1()
    {
        return sixHour1;
    }
    public void setSixHour2(BigDecimal sixHour2)
    {
        this.sixHour2 = sixHour2;
    }

    public BigDecimal getSixHour2()
    {
        return sixHour2;
    }
    public void setSixHour3(BigDecimal sixHour3)
    {
        this.sixHour3 = sixHour3;
    }

    public BigDecimal getSixHour3()
    {
        return sixHour3;
    }
    public void setTwelveHour1(BigDecimal twelveHour1)
    {
        this.twelveHour1 = twelveHour1;
    }

    public BigDecimal getTwelveHour1()
    {
        return twelveHour1;
    }
    public void setTwelveHour2(BigDecimal twelveHour2)
    {
        this.twelveHour2 = twelveHour2;
    }

    public BigDecimal getTwelveHour2()
    {
        return twelveHour2;
    }
    public void setTwelveHour3(BigDecimal twelveHour3)
    {
        this.twelveHour3 = twelveHour3;
    }

    public BigDecimal getTwelveHour3()
    {
        return twelveHour3;
    }
    public void setTwentyFourHour1(BigDecimal twentyFourHour1)
    {
        this.twentyFourHour1 = twentyFourHour1;
    }

    public BigDecimal getTwentyFourHour1()
    {
        return twentyFourHour1;
    }
    public void setTwentyFourHour2(BigDecimal twentyFourHour2)
    {
        this.twentyFourHour2 = twentyFourHour2;
    }

    public BigDecimal getTwentyFourHour2()
    {
        return twentyFourHour2;
    }
    public void setTwentyFourHour3(BigDecimal twentyFourHour3)
    {
        this.twentyFourHour3 = twentyFourHour3;
    }

    public BigDecimal getTwentyFourHour3()
    {
        return twentyFourHour3;
    }
    public void setSyncTime(Date syncTime)
    {
        this.syncTime = syncTime;
    }

    public Date getSyncTime()
    {
        return syncTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("num", getNum())
                .append("name", getName())
                .append("areaCode", getAreaCode())
                .append("szs", getSzs())
                .append("szqx", getSzqx())
                .append("szz", getSzz())
                .append("riskLevel", getRiskLevel())
                .append("stability", getStability())
                .append("datatype", getDatatype())
                .append("status", getStatus())
                .append("affectedPeople", getAffectedPeople())
                .append("affectedFamily", getAffectedFamily())
                .append("affectedAsset", getAffectedAsset())
                .append("permanentResidents", getPermanentResidents())
                .append("hazardAffectedBody", getHazardAffectedBody())
                .append("placeLeader", getPlaceLeader())
                .append("placeLeaderTel", getPlaceLeaderTel())
                .append("countyLeader", getCountyLeader())
                .append("countyLeaderTel", getCountyLeaderTel())
                .append("villagesLeader", getVillagesLeader())
                .append("villageLeaderTel", getVillageLeaderTel())
                .append("grassRoots", getGrassRoots())
                .append("grassRootsTel", getGrassRootsTel())
                .append("handUnit", getHandUnit())
                .append("cancelReason", getCancelReason())
                .append("cancelReasonOther", getCancelReasonOther())
                .append("revokeDate", getRevokeDate())
                .append("commitDate", getCommitDate())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("geo", getGeo())
                .append("pointGeo", getPointGeo())
                .append("oneHour1", getOneHour1())
                .append("oneHour2", getOneHour2())
                .append("oneHour3", getOneHour3())
                .append("threeHour1", getThreeHour1())
                .append("threeHour2", getThreeHour2())
                .append("threeHour3", getThreeHour3())
                .append("sixHour1", getSixHour1())
                .append("sixHour2", getSixHour2())
                .append("sixHour3", getSixHour3())
                .append("twelveHour1", getTwelveHour1())
                .append("twelveHour2", getTwelveHour2())
                .append("twelveHour3", getTwelveHour3())
                .append("twentyFourHour1", getTwentyFourHour1())
                .append("twentyFourHour2", getTwentyFourHour2())
                .append("twentyFourHour3", getTwentyFourHour3())
                .append("syncTime", getSyncTime())
                .toString();
    }
}
