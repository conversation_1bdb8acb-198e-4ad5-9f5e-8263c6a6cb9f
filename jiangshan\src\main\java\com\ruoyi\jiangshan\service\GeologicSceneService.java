package com.ruoyi.jiangshan.service;

import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.vo.RiskPreventVO;
import com.ruoyi.jiangshan.vo.WarningCountVO;
import com.ruoyi.jiangshan.vo.WarningInfoVO;

import java.util.List;
import java.util.Map;

public interface GeologicSceneService {

    Map<String, Object> getRainFall(Long id);

    WarningCountVO getSafetyCount(String dateStr);

    List<WarningEvent> getEvent(String eventName, Integer status, Integer warningLevel);

    List<WarningInfoVO> getWarning(Integer warningLevel);

    Map<String, Object> getGroupPrevent();

    List<RiskPreventVO> getRiskPrevention(RiskPreventVO riskPreventVO);

    List<DeviceMonitor> getAllMonitor();

    List<DeviceInfo> getAllDevice();

}
