package com.ruoyi.jiangshan.client;

import com.ruoyi.jiangshan.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class VoiceClient {

    private final String ACCOUNT = "jsszcsaq";
    private final String PASSWORD = "Szjh2024!@#";
    private final String PASSWORD_MD5 = "648b75102969b3d08df5e786eb139681";
    private final String URL = "https://oa.zjyxt1.net.cn/d-oa-system";

    public void sendMobileMsg(String mobile) {
        String url = URL + "/api/voice/addSameNotify";

        Map<String, Object> params = new HashMap<>();
        params.put("account", ACCOUNT);
        params.put("password", PASSWORD_MD5);
        params.put("displayNumber", "");
        params.put("outId", "");
        params.put("phone", mobile);
        params.put("option", 1);
        params.put("content", "您有一条新的事件告警，请及时签收。");

        HttpUtil httpUtil = HttpUtil.getInstance();
        String res = httpUtil.sendHttpPostJSON(url, params);

        log.info("response:{}", res);
    }

    public void sendMobileMsgWithContent(String mobile, String content) {
        String url = URL + "/api/voice/addSameNotify";

        Map<String, Object> params = new HashMap<>();
        params.put("account", ACCOUNT);
        params.put("password", PASSWORD_MD5);
        params.put("displayNumber", "");
        params.put("outId", "");
        params.put("phone", mobile);
        params.put("option", 1);
        params.put("content", content);

        HttpUtil httpUtil = HttpUtil.getInstance();
        String res = httpUtil.sendHttpPostJSON(url, params);

        log.info("response:{}", res);
    }
}
