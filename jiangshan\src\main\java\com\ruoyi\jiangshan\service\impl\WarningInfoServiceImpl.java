package com.ruoyi.jiangshan.service.impl;

import com.ruoyi.jiangshan.mapper.WarningInfoMapper;
import com.ruoyi.jiangshan.service.WarningInfoService;
import com.ruoyi.jiangshan.vo.WarningLevelCountVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class WarningInfoServiceImpl implements WarningInfoService {

    @Resource
    private WarningInfoMapper warningInfoMapper;

    @Override
    public List<List<Integer>> countWarningLevelByScenes(List<String> scenes) {
        List<List<Integer>> returnValue = new ArrayList<>();
        for (String scene : scenes) {
            List<Integer> sceneCount = Arrays.asList(0, 0, 0, 0);
            List<WarningLevelCountVO> warningLevelCountVOList = warningInfoMapper.countWarningLevelByScene(scene);
            for (WarningLevelCountVO warningLevelCountVO : warningLevelCountVOList) {
                int level = warningLevelCountVO.getLevel();
                int count = warningLevelCountVO.getCount();
                sceneCount.set(level - 1, count);
            }
            returnValue.add(sceneCount);
        }
        return returnValue;
    }
}
