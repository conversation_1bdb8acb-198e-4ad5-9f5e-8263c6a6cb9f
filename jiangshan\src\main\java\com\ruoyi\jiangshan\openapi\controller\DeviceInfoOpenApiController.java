package com.ruoyi.jiangshan.openapi.controller;

/**
 * openApi
 */
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin;
import com.ruoyi.jiangshan.openapi.vo.*;
import com.ruoyi.jiangshan.service.IDeviceInfoService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.service.IWarningModelService;
import com.ruoyi.jiangshan.vo.jczz.WarningEventJczzLogVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备信息相关openApi
 */
@RestController
@RequestMapping("/openapi/deviceInfo")
@Slf4j
public class DeviceInfoOpenApiController {
    @Autowired
    private IDeviceInfoService deviceInfoService;

    /**
     * 获取设备数量
     * @return
     */
    @GetMapping("/countNum")
    public AjaxResult countNum() {
        DeviceInfoCountOpenApiVO openApiVO = deviceInfoService.countNum();

        return AjaxResult.success("操作成功", openApiVO);
    }
}
