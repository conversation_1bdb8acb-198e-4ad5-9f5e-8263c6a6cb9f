package com.ruoyi.jiangshan.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.service.IDeviceMonitorService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.service.WaterSceneService;
import com.ruoyi.jiangshan.util.HttpUtil;
import com.ruoyi.jiangshan.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class WaterSceneServiceImpl implements WaterSceneService {

    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private IDeviceMonitorService deviceMonitorService;
    @Autowired
    private IWarningEventService warningEventService;

    private static final String SCENE = MonitorSceneType.SCENE_01.getDesc();
    private static final String KEY = "14dfe18dfba7d9fdce3ca1922bb6227b";
    private static final String URL = "https://restapi.amap.com/v3/weather/weatherInfo";
//    private static final String URL = "http://192.168.202.226:8501/prod-api/water/weather";
    private static final String CITY = "330700";
    private static final String EXTENSIONS= "all";
    private static List<String> DEVICE_TYPE_LIST = Lists.newArrayList();

    @Autowired
    private DeviceInfoTypeMapper deviceInfoTypeMapper;
    @PostConstruct
    public void init() {
        DEVICE_TYPE_LIST = deviceInfoTypeMapper.listByScene(SCENE);
    }

    @Override
    public List<CastVO> getWeather() {
        List<CastVO> cacheList = redisCache.getCacheList("WEATHER");
        if (CollectionUtils.isEmpty(cacheList)) {

            HttpUtil httpUtil = HttpUtil.getInstance();

            String response = httpUtil.sendHttpGet(URL + "?key=" + KEY + "&city=" + CITY + "&extensions=" + EXTENSIONS);

            Map map = JSON.parseObject(response, Map.class);


            List<ForecastVO> weather = JSON.parseArray(JSON.toJSONString(map.get("forecasts")), ForecastVO.class) ;
            for(ForecastVO forecast : weather){
                if (CollectionUtils.isNotEmpty(forecast.getCasts())) {
                    cacheList.addAll(forecast.getCasts());
                }
            }

            redisCache.setCacheList("WEATHER", cacheList);
            redisCache.expire("WEATHER", 6, TimeUnit.HOURS);

            return cacheList;
        }

        return cacheList;
    }

    @Override
    public List<DeviceMonitor> getMonitor(String type) {

        return deviceMonitorService.getMonitor(type);
    }

    @Override
    public Map<String, Object> getEquipment(String type) {
        List<BusinessCountVO> countVOList = deviceInfoMapper.countByMonitorTypeAndStatus(Lists.newArrayList(type));

        Map<String, List<BusinessCountVO>> existMap = countVOList.stream()
                .collect(Collectors.groupingBy(BusinessCountVO::getKey));

        List<BusinessCountVO> resultList = existMap.get(type);
        Map<String, Object> resultMap = new HashMap<>();

        if (CollectionUtils.isEmpty(resultList)) {
            resultMap.put("total", 0L);
            resultMap.put("online", 0L);
            resultMap.put("offline", 0L);
            resultMap.put("maintain", 0L);
        } else {
            Long total = resultList.stream()
                    .map(BusinessCountVO::getValue)
                    .reduce(0L, Long::sum);

            resultMap.put("total", total);

            Map<String, Long> map = resultList.stream()
                    .collect(Collectors.toMap(BusinessCountVO::getTmpKey, BusinessCountVO::getValue));

            resultMap.put("offline", Objects.isNull(map.get("0")) ? 0L : map.get("0"));
            resultMap.put("online", Objects.isNull(map.get("1")) ? 0L : map.get("1"));
            resultMap.put("maintain", Objects.isNull(map.get("2")) ? 0L : map.get("2"));
        }

        List<BusinessCountVO> warningCountList = warningInfoMapper.countByMonitorType(Lists.newArrayList(type), null, null);

        Map<String, List<BusinessCountVO>> warningMap = warningCountList.stream()
                .collect(Collectors.groupingBy(BusinessCountVO::getTmpKey));


        List<BusinessCountVO> warningOneCountList = warningMap.get(type);

        if (CollectionUtils.isEmpty(warningOneCountList)) {
            resultMap.put("warningLevel1", 0L);
            resultMap.put("warningLevel2", 0L);
            resultMap.put("warningLevel3", 0L);
            resultMap.put("warningLevel4", 0L);
            resultMap.put("warningLevel5", 0L);
        } else {
            Map<String, Long> warningOneCountMap = warningOneCountList.stream()
                    .collect(Collectors.toMap(BusinessCountVO::getKey, BusinessCountVO::getValue));

            resultMap.put("warningLevel1", Objects.isNull(warningOneCountMap.get("1")) ? 0L : warningOneCountMap.get("1"));
            resultMap.put("warningLevel2", Objects.isNull(warningOneCountMap.get("2")) ? 0L : warningOneCountMap.get("2"));
            resultMap.put("warningLevel3", Objects.isNull(warningOneCountMap.get("3")) ? 0L : warningOneCountMap.get("3"));
            resultMap.put("warningLevel4", Objects.isNull(warningOneCountMap.get("4")) ? 0L : warningOneCountMap.get("4"));
            resultMap.put("warningLevel5", Objects.isNull(warningOneCountMap.get("5")) ? 0L : warningOneCountMap.get("5"));
        }

        return resultMap;
    }

    @Override
    public List<WarningInfoVO> getWarning(Integer warningLevel) {
        return warningInfoMapper.listByLevelAndScene(warningLevel, SCENE);
    }

    @Override
    public Map<String, Object> getWarningDispose(Date startTime, Date endTime) {
        Map<String, Object> resultMap = warningEventService.getWarningDispose(SCENE, startTime, endTime);

        return resultMap;
    }

}
