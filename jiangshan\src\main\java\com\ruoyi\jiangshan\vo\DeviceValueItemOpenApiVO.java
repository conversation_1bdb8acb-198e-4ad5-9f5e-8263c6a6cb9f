package com.ruoyi.jiangshan.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DeviceValueItemOpenApiVO {

    /**
     * 监测数据
     */
    @NotBlank(message = "监测数据不能为空")
    private String monitorValue;

    /**
     * 监测项中文名称
     */
    @NotBlank(message = "监测项中文名称不能为空")
    private String monitorItem;

    /**
     * 监测项英文名称
     */
    @NotBlank(message = "监测项英文名称不能为空")
    private String monitorItemEnglish;

    /**
     * 监测数据单位
     */
    @NotBlank(message = "监测数据单位不能为空")
    private String monitorUnit;
}
