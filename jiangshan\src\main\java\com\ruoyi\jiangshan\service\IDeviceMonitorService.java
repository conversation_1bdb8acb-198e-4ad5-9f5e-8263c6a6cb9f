package com.ruoyi.jiangshan.service;

import java.util.List;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.vo.DeviceMonitorVO;

/**
 * 设备监测对象Service接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface IDeviceMonitorService
{
    /**
     * 查询设备监测对象
     *
     * @param id 设备监测对象主键
     * @return 设备监测对象
     */
    public DeviceMonitor selectDeviceMonitorById(Long id);

    /**
     * 查询设备监测对象列表
     *
     * @param deviceMonitor 设备监测对象
     * @return 设备监测对象集合
     */
    public List<DeviceMonitor> selectDeviceMonitorList(DeviceMonitor deviceMonitor);

    /**
     * 新增设备监测对象
     *
     * @param deviceMonitor 设备监测对象
     * @return 结果
     */
    public int insertDeviceMonitor(DeviceMonitor deviceMonitor);

    /**
     * 修改设备监测对象
     *
     * @param deviceMonitor 设备监测对象
     * @return 结果
     */
    public int updateDeviceMonitor(DeviceMonitor deviceMonitor);

    /**
     * 批量删除设备监测对象
     *
     * @param ids 需要删除的设备监测对象主键集合
     * @return 结果
     */
    public int deleteDeviceMonitorByIds(Long[] ids);

    /**
     * 删除设备监测对象信息
     *
     * @param id 设备监测对象主键
     * @return 结果
     */
    public int deleteDeviceMonitorById(Long id);

    List<DeviceMonitor> getMonitor(String type);

    List<DeviceMonitorVO> listByScene(String keyword, String monitorScene);

    List<DeviceMonitor> listAll();

    void excelImport(List<DeviceMonitor> list);
}
