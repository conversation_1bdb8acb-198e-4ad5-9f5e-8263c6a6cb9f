package com.ruoyi.jiangshan.openapi.controller;

/**
 * openApi
 */
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jiangshan.constant.CommonConstant;
import com.ruoyi.jiangshan.enums.RedisChannelMapping;
import com.ruoyi.jiangshan.mapper.DeviceInfoMapper;
import com.ruoyi.jiangshan.openapi.publish.RedisMessagePublisher;
import com.ruoyi.jiangshan.openapi.vo.DeviceValueOpenApiVO;
import com.ruoyi.jiangshan.service.IDeviceValueService;
import com.ruoyi.jiangshan.vo.DeviceValueItemOpenApiVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 设备数据openApi
 */
@RestController
@RequestMapping("/openapi/deviceValue")
@Slf4j
public class DeviceValueOpenApiController {
    @Autowired
    private RedisMessagePublisher redisMessagePublisher;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;

    @Value("${server.port}")
    private String serverPort;

    private List<String> BRIDGE_LIST = Lists.newArrayList();

    @PostConstruct
    public void init() {
        BRIDGE_LIST = deviceInfoMapper.listAllSjxbjcy();
    }

    @PostMapping("/save")
    public AjaxResult saveDeviceValue(@RequestBody @Valid DeviceValueOpenApiVO apiVO) {
        if (BRIDGE_LIST.contains(apiVO.getDeviceThirdId())) {
            DeviceValueItemOpenApiVO itemOpenApiVO = apiVO.getMonitorList().get(0);

            //限流
            String limitKey = "PUSH_" + apiVO.getDeviceThirdId() + "_" + itemOpenApiVO.getMonitorItemEnglish()
                    + "_" + apiVO.getTarget();

            Object cacheObject = redisCache.getCacheObject(limitKey);
            if (Objects.isNull(cacheObject)) {
                redisCache.setCacheObject(limitKey, 1);
                redisCache.expire(limitKey, 20, TimeUnit.SECONDS);
            } else {
                log.info("onMessageLock, deviceThirdId:{}, monitorItem:{}, monitorTime:{} ",
                        apiVO.getDeviceThirdId(), itemOpenApiVO.getMonitorItemEnglish(), apiVO.getMonitorTime());
                return AjaxResult.success("操作成功");
            }
        }

        String redisChannel = RedisChannelMapping.getByPort(serverPort);

        String msg = JSON.toJSONString(apiVO);

        redisMessagePublisher.publish(redisChannel, msg);

        return AjaxResult.success("操作成功");
    }
}
