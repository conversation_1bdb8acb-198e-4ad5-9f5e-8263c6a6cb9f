package com.ruoyi.jiangshan.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 设备监测对象对象 t_device_monitor
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Data
public class DeviceMonitor extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    @Excel(name = "对象编号")
    private String monitorThirdId;

    /** 对象名称 */
    @Excel(name = "对象名称")
    private String monitorName;

    /** 对象场景 */
    @Excel(name = "对象场景", combo = { "水利场景", "桥梁场景", "地质灾害场景", "内涝场景", "污水管线场景", "燃气场景", "消防场景"})
    private String monitorScene;

    /** 对象类别 */
    @Excel(name = "对象类别")
    private String monitorType;

    /** 经度 */
    @Excel(name = "经度")
    private String lon;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    /** 服务地址 */
//    @Excel(name = "服务地址")
    private String serviceUrl;

    /** 对象图片url */
//    @Excel(name = "对象图片url")
    private String fileUrl;

    /** 对象责任部门id */
//    @Excel(name = "对象责任部门id")
    private Long monitorDepartmentId;

    /** 对象责任部门名称 */
//    @Excel(name = "对象责任部门名称")
    private String monitorDepartmentName;

    @Excel(name = "规模大小")
    private String scale;

    @Excel(name = "计量单位")
    private String scaleUnit;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    private List<BusinessFile> pictureList;

    private List<BusinessFile> attachmentList;

    private String newType;
}
