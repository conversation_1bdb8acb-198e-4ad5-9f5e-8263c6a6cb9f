package com.ruoyi.jiangshan.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.service.WarningInfoService;
import com.ruoyi.jiangshan.vo.WarningInfoReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;


@RestController
@RequestMapping("/warningCenter")
public class WarningCenterController {

    @Autowired
    private WarningInfoService warningInfoService;

    @PostMapping("/export")
    public AjaxResult test(@RequestBody WarningInfoReqVO warningInfoReqVO) {
        // 1
        List<List<Integer>> overallSituations = warningInfoService.countWarningLevelByScenes(warningInfoReqVO.getScenes());
        // 2

        // 3

        // 4

        // 组合

        return AjaxResult.success(overallSituations);
    }
}
