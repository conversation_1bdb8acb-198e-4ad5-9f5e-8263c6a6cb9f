package com.ruoyi.jiangshan.vo;

import lombok.Data;

/**
 * resultMap.put("total", 0L);
 *             resultMap.put("online", 0L);
 *             resultMap.put("offline", 0L);
 *             resultMap.put("maintain", 0L);
 */
@Data
public class DeviceOnlineStatusVO {

    private Long total;

    private Long online;

    private Long offline;

    private Long maintain;

    private Long remove;

    public Long sumTotalOnlineOffline(){
        return online + offline;
    }

    public Long sumTotal(){
        return online + offline + maintain + remove;
    }
}
