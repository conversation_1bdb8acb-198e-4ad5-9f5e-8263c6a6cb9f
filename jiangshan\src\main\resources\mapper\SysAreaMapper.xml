<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.SysAreaMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.SysArea">
    <!--@mbg.generated-->
    <!--@Table sys_area-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="lat" jdbcType="DECIMAL" property="lat" />
    <result column="lng" jdbcType="DECIMAL" property="lng" />
    <result column="location_path" jdbcType="VARCHAR" property="locationPath" />
    <result column="layered" jdbcType="INTEGER" property="layered" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="level_code" jdbcType="VARCHAR" property="levelCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, parent_id, area_name, lat, lng, location_path, layered, sort, level_code, address
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from sys_area
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from sys_area
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.SysArea" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_area (parent_id, area_name, lat,
      lng, location_path, layered,
      sort, level_code, address
      )
    values (#{parentId,jdbcType=BIGINT}, #{areaName,jdbcType=VARCHAR}, #{lat,jdbcType=DECIMAL},
      #{lng,jdbcType=DECIMAL}, #{locationPath,jdbcType=VARCHAR}, #{layered,jdbcType=INTEGER},
      #{sort,jdbcType=INTEGER}, #{levelCode,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.SysArea" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="locationPath != null">
        location_path,
      </if>
      <if test="layered != null">
        layered,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="levelCode != null">
        level_code,
      </if>
      <if test="address != null">
        address,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DECIMAL},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DECIMAL},
      </if>
      <if test="locationPath != null">
        #{locationPath,jdbcType=VARCHAR},
      </if>
      <if test="layered != null">
        #{layered,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="levelCode != null">
        #{levelCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.SysArea">
    <!--@mbg.generated-->
    update sys_area
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DECIMAL},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DECIMAL},
      </if>
      <if test="locationPath != null">
        location_path = #{locationPath,jdbcType=VARCHAR},
      </if>
      <if test="layered != null">
        layered = #{layered,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="levelCode != null">
        level_code = #{levelCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.SysArea">
    <!--@mbg.generated-->
    update sys_area
    set parent_id = #{parentId,jdbcType=BIGINT},
      area_name = #{areaName,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=DECIMAL},
      lng = #{lng,jdbcType=DECIMAL},
      location_path = #{locationPath,jdbcType=VARCHAR},
      layered = #{layered,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      level_code = #{levelCode,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update sys_area
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.parentId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="area_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.areaName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="lat = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lat,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="lng = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lng,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="location_path = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.locationPath,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="layered = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.layered,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="sort = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sort,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="level_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.levelCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.address,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_area
    (parent_id, area_name, lat, lng, location_path, layered, sort, level_code, address
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.parentId,jdbcType=BIGINT}, #{item.areaName,jdbcType=VARCHAR}, #{item.lat,jdbcType=DECIMAL},
        #{item.lng,jdbcType=DECIMAL}, #{item.locationPath,jdbcType=VARCHAR}, #{item.layered,jdbcType=INTEGER},
        #{item.sort,jdbcType=INTEGER}, #{item.levelCode,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

  <select id="getAreaTree" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from sys_area
    where parent_id = #{parentId}
  </select>
</mapper>
