package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningEventJczzLog;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WarningEventJczzLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningEventJczzLog record);

    int insertSelective(WarningEventJczzLog record);

    WarningEventJczzLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningEventJczzLog record);

    int updateByPrimaryKey(WarningEventJczzLog record);

    int updateBatch(List<WarningEventJczzLog> list);

    int batchInsert(@Param("list") List<WarningEventJczzLog> list);
}