package com.ruoyi.jiangshan.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.dto.DateDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.framework.web.service.dtalk.UserDtalkService;
import com.ruoyi.jiangshan.constant.CommonConstant;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.enums.FileTypeEnum;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.enums.WarningEventStepEnum;
import com.ruoyi.jiangshan.enums.WarningLevelType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.openapi.config.WarningSceneDepartmentConfig;
import com.ruoyi.jiangshan.openapi.vo.DeviceValueOpenApiVO;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.util.HttpUtil;
import com.ruoyi.jiangshan.vo.*;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ruoyi.jiangshan.service.IDeviceValueService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 设备实时数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
@Slf4j
public class DeviceValueServiceImpl implements IDeviceValueService
{
    @Autowired
    private DeviceValueMapper deviceValueMapper;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private WarningRuleConditionMapper warningRuleConditionMapper;
    @Autowired
    private WarningRuleMapper warningRuleMapper;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private BusinessFileMapper businessFileMapper;
    @Autowired
    private UserDtalkService userDtalkService;
    @Autowired
    private IWarningEventService warningEventService;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Value("${single.url}")
    private String singleUrl;
    @Value("${single.pcUrl}")
    private String singlePcUrl;

    private final String APPCODE = "QEWFAS4124SDVWT5";

    /**
     * 查询设备实时数据
     *
     * @param id 设备实时数据主键
     * @return 设备实时数据
     */
    @Override
    public DeviceValue selectDeviceValueById(Long id)
    {
        DeviceValue deviceValue = deviceValueMapper.selectDeviceValueById(id);

        fillFileList(Lists.newArrayList(deviceValue));

        return deviceValue;
    }

    /**
     * 查询设备实时数据列表
     *
     * @param deviceValue 设备实时数据
     * @return 设备实时数据
     */
    @Override
    public List<DeviceValue> selectDeviceValueList(DeviceValue deviceValue)
    {
        List<DeviceValue> deviceValueList = deviceValueMapper.selectDeviceValueList(deviceValue);

        fillFileList(deviceValueList);
        fillYuLiangDevice(deviceValueList);

        return deviceValueList;
    }

    private void fillYuLiangDevice(List<DeviceValue> deviceValueList) {
        if (CollectionUtils.isEmpty(deviceValueList)) {
            return;
        }

        List<DeviceValue> nonDeviceList = deviceValueList.stream()
                .filter(value -> StringUtils.isBlank(value.getDeviceName()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(nonDeviceList)) {
            return;
        }

        List<String> deviceThirdIdList = nonDeviceList.stream()
                .map(DeviceValue::getDeviceThirdId)
                .collect(Collectors.toList());

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByDeviceNum(deviceThirdIdList);

        Map<String, DeviceInfo> deviceMap = deviceInfoList.stream()
                .collect(Collectors.toMap(DeviceInfo::getDeviceNum, Function.identity()));

        for (DeviceValue deviceValue : nonDeviceList) {
            DeviceInfo deviceInfo = deviceMap.get(deviceValue.getDeviceThirdId());
            if (Objects.nonNull(deviceInfo)) {
                deviceValue.setDeviceId(deviceInfo.getId());
                deviceValue.setDeviceScene(deviceInfo.getDeviceScene());
                deviceValue.setDeviceName(deviceInfo.getDeviceName());
                deviceValue.setDeviceType(deviceInfo.getDeviceType());
                deviceValue.setMonitorId(deviceInfo.getMonitorId());
                deviceValue.setMonitorObject(deviceInfo.getMonitorName());
            }
        }
    }

    private void fillFileList(List<DeviceValue> deviceValueList) {
        if (CollectionUtils.isEmpty(deviceValueList)) {
            return;
        }

        List<Long> valueIdList = deviceValueList.stream()
                .map(DeviceValue::getId)
                .collect(Collectors.toList());

        List<BusinessFile> fileList =
                businessFileMapper.listByBusinessIdListAndType(valueIdList, FileTypeEnum.TYPE_01.getCode());

        Map<Long, List<BusinessFile>> fileMap = fileList.stream()
                .collect(Collectors.groupingBy(BusinessFile::getBusinessId));

        for (DeviceValue value : deviceValueList) {
            List<BusinessFile> fileOneList = fileMap.get(value.getId());

            value.setFileList(fileOneList);
        }
    }

    /**
     * 新增设备实时数据
     *
     * @param deviceValue 设备实时数据
     * @return 结果
     */
    @Override
    public int insertDeviceValue(DeviceValue deviceValue)
    {
        deviceValue.setCreateTime(DateUtils.getNowDate());
        return deviceValueMapper.insertDeviceValue(deviceValue);
    }

    /**
     * 修改设备实时数据
     *
     * @param deviceValue 设备实时数据
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDeviceValue(DeviceValue deviceValue)
    {
        int i = deviceValueMapper.updateDeviceValue(deviceValue);

        //删除原先文件
        businessFileMapper.deleteByBusinessIdAndTypeList(deviceValue.getId(),
                Lists.newArrayList(FileTypeEnum.TYPE_01.getCode()));

        if (CollectionUtils.isNotEmpty(deviceValue.getFileList())) {
            for (BusinessFile businessFile : deviceValue.getFileList()) {
                businessFile.setBusinessId(deviceValue.getId());
                businessFile.setBusinessType(FileTypeEnum.TYPE_01.getCode());
            }

            businessFileMapper.updateBatch(deviceValue.getFileList());
        }

        return 1;
    }

    /**
     * 批量删除设备实时数据
     *
     * @param ids 需要删除的设备实时数据主键
     * @return 结果
     */
    @Override
    public int deleteDeviceValueByIds(Long[] ids)
    {
        return deviceValueMapper.deleteDeviceValueByIds(ids);
    }

    /**
     * 删除设备实时数据信息
     *
     * @param id 设备实时数据主键
     * @return 结果
     */
    @Override
    public int deleteDeviceValueById(Long id)
    {
        return deviceValueMapper.deleteDeviceValueById(id);
    }

    @Override
    public void saveDeviceValue(DeviceValueOpenApiVO apiVO) {
        List<DeviceValueItemOpenApiVO> monitorList = apiVO.getMonitorList();

        List<DeviceValue> deviceValueList = Lists.newArrayList();

        for (DeviceValueItemOpenApiVO itemOpenApiVO : monitorList) {
            if (StringUtils.isNotBlank(itemOpenApiVO.getMonitorUnit())
                    && "Pa".equals(itemOpenApiVO.getMonitorUnit())) {
                itemOpenApiVO.setMonitorUnit("MPa");
            }
            if ("设备连接状态".equals(itemOpenApiVO.getMonitorItem())) {
                updateDeviceConnectStatus(apiVO.getDeviceThirdId(), itemOpenApiVO.getMonitorValue(), apiVO.getMonitorTime());
            }

            DeviceValue deviceValue = new DeviceValue();
            deviceValue.setDeviceThirdId(apiVO.getDeviceThirdId());
            deviceValue.setMonitorTime(apiVO.getMonitorTime());
            deviceValue.setTarget(apiVO.getTarget());

            deviceValue.setMonitorValue(itemOpenApiVO.getMonitorValue());
            deviceValue.setMonitorItem(itemOpenApiVO.getMonitorItem());
            deviceValue.setMonitorItemEnglish(itemOpenApiVO.getMonitorItemEnglish());
            deviceValue.setMonitorUnit(itemOpenApiVO.getMonitorUnit());
            deviceValue.setCreateTime(new Date());
            deviceValue.setCreateBy("推送数据");

            deviceValueList.add(deviceValue);
        }

        //组装跟设备的绑定关系
        DeviceInfo deviceInfo = deviceInfoMapper.getByThirdId(apiVO.getDeviceThirdId());

        if (Objects.nonNull(deviceInfo)) {
            for (DeviceValue deviceValue : deviceValueList) {
                deviceValue.setMonitorId(deviceInfo.getMonitorId());
                deviceValue.setMonitorObject(deviceInfo.getMonitorName());
                deviceValue.setDeviceId(deviceInfo.getId());
            }

//            if (deviceInfo.getDeviceStatus() == 0) {
//                //更新离线状态
//                deviceInfoMapper.updateDeviceConnectStatus(deviceInfo.getDeviceThirdId(), 1, new Date());
//            }
        }

        deviceValueMapper.batchInsert(deviceValueList);

        List<String> monitorItemList = deviceValueList.stream()
                .map(DeviceValue::getMonitorItemEnglish)
                .collect(Collectors.toList());

        List<WarningRuleCondition> ruleConditionList =
                warningRuleConditionMapper.listByMonitorItemAndDevice(apiVO.getDeviceThirdId(), monitorItemList);

        if (CollectionUtils.isNotEmpty(ruleConditionList)) {
            Map<String, List<WarningRuleCondition>> conditionMap = ruleConditionList.stream()
                    .collect(Collectors.groupingBy(WarningRuleCondition::getMonitorItemEnglish));

            List<WarningInfo> warningInfoList = Lists.newArrayList();
            List<WarningEvent> warningEventList = Lists.newArrayList();

            List<WarningInfo> updateList = Lists.newArrayList();

            for (DeviceValue deviceValue : deviceValueList) {
                List<WarningRuleCondition> ruleConditionOneList = conditionMap.get(deviceValue.getMonitorItemEnglish());

                if (CollectionUtils.isEmpty(ruleConditionOneList)) {
                    continue;
                }

                for (WarningRuleCondition warningRuleCondition : ruleConditionOneList) {
                    BigDecimal monitorValue = new BigDecimal(warningRuleCondition.getMonitorValue());
                    BigDecimal newValue = new BigDecimal(deviceValue.getMonitorValue());

                    if (">".equals(warningRuleCondition.getRuleCondition())) {
                        if (newValue.compareTo(monitorValue) > 0) {
                            fillWarningInfo(warningRuleCondition, deviceValue, deviceInfo, warningInfoList, warningEventList, updateList);
                            break;
                        }
                    } else if (">=".equals(warningRuleCondition.getRuleCondition())) {
                        if (newValue.compareTo(monitorValue) >= 0) {
                            fillWarningInfo(warningRuleCondition, deviceValue, deviceInfo, warningInfoList, warningEventList, updateList);
                            break;
                        }
                    } else if ("<".equals(warningRuleCondition.getRuleCondition())) {
                        if (newValue.compareTo(monitorValue) < 0) {
                            fillWarningInfo(warningRuleCondition, deviceValue, deviceInfo, warningInfoList, warningEventList, updateList);
                            break;
                        }
                    } else if ("<=".equals(warningRuleCondition.getRuleCondition())) {
                        if (newValue.compareTo(monitorValue) <= 0) {
                            fillWarningInfo(warningRuleCondition, deviceValue, deviceInfo, warningInfoList, warningEventList, updateList);
                            break;
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(warningInfoList)) {
                warningInfoMapper.batchInsert(warningInfoList);

                for (WarningInfo warningInfo : warningInfoList) {
                    warningEventList.add(createWarningEvent(deviceInfo, warningInfo));
                }
            }

            if (CollectionUtils.isNotEmpty(warningEventList)) {
                warningEventService.fillEventBizCode(warningEventList, deviceInfo.getDeviceScene());

                warningEventMapper.batchInsert(warningEventList);

                for (WarningEvent warningEvent : warningEventList) {
                    createNewWarningEventDispatch(warningEvent, null, null);

                    //141事件上报2.1
                    warningEventService.push141PlatForm(warningEvent.getId());
                }
            }

            if (CollectionUtils.isNotEmpty(updateList)) {
                warningInfoMapper.updateBatch(updateList);

                for (WarningInfo warningInfo : updateList) {
                    WarningEvent warningEvent = new WarningEvent();
                    warningEvent.setWarningId(warningInfo.getId());
                    warningEvent.setEventName(warningInfo.getWarningName());
                    warningEvent.setWarningDetail(warningInfo.getWarningReason());
                    warningEvent.setWarningLevel(warningInfo.getWarningLevel());
                    warningEvent.setWarningTime(warningInfo.getWarningTime());
//                    warningEvent.setDeliveryTime(warningInfo.getWarningTime());

                    warningEvent.setUpdateTime(new Date());

                    warningEventMapper.updateByWarningId(warningEvent);
                }
            }
        }
    }



    private void fillEventBizCode(List<WarningEvent> warningEventList, String scene) {
        int bizCodeCount = 1;
        Long warningCount = warningEventMapper.getCountByMonthAndScene(scene);
        if (Objects.isNull(warningCount)) {
            warningCount = 0L;
        }
        String abbr = MonitorSceneType.getAbbrByScene(scene);
        String monthStr = DateUtils.formatYearMonthV3(new Date());
        for (WarningEvent warningEvent : warningEventList) {
            long currentCount = warningCount + bizCodeCount;

            String currentCountStr = "";
            if (currentCount < 100) {
                currentCountStr = "0" + currentCount;
            } else {
                currentCountStr = String.valueOf(currentCount);
            }

            String bizCode = abbr + "-" + monthStr + "-" + currentCountStr;

            warningEvent.setBizCode(bizCode);

            bizCodeCount++;
        }
    }

    private void updateDeviceConnectStatus(String deviceThirdId, String status, Date monitorTime) {
        deviceInfoMapper.updateDeviceConnectStatus(deviceThirdId, Integer.valueOf(status), monitorTime);
    }

    @Override
    public void createNewWarningEventDispatch(WarningEvent warningEvent, SysUser sysUser, String accountId) {
        WarningEventDispatchVO dispatchVO = new WarningEventDispatchVO();
        dispatchVO.setId(warningEvent.getId());

        String employeeMobile = "";

        WarningEventSceneDepartment warningEventSceneDepartment = WarningSceneDepartmentConfig.SCENE_DEPARTMENT_MAP.get(warningEvent.getEventType());
        if (Objects.nonNull(warningEvent.getConditionId())) {
            WarningRule warningRule = warningRuleMapper.getByConditionId(warningEvent.getConditionId());

            sysUser = sysUserMapper.selectUserById(warningRule.getWarningUserId());

            accountId = warningEventSceneDepartment.getAccountId();
            if (StringUtils.isNotBlank(warningRule.getAllAccountId())) {
                accountId = warningRule.getAllAccountId();
            }
            if (StringUtils.isNotBlank(warningRule.getAllMobile())) {
                employeeMobile = warningRule.getAllMobile();
            }
        }

        if (Objects.nonNull(sysUser)) {
            dispatchVO.setDepartmentId(sysUser.getDeptId());
            SysDept dept = sysUser.getDept();
            if (Objects.nonNull(dept)) {
                dispatchVO.setDepartmentName(dept.getDeptName());
            }
            dispatchVO.setAccountId(sysUser.getAccountId());
            dispatchVO.setEmployeeCode(sysUser.getEmployeeCode());
            dispatchVO.setEmployeeName(sysUser.getNickName());
            dispatchVO.setEmployeeMobile(sysUser.getPhonenumber());

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            dispatchVO.setExpectedCompleteTime(calendar.getTime());
        } else {
            dispatchVO.setDepartmentId(warningEventSceneDepartment.getDepartmentId());
            dispatchVO.setDepartmentName(warningEventSceneDepartment.getDepartmentName());
            dispatchVO.setAccountId(warningEventSceneDepartment.getAccountId());
            dispatchVO.setEmployeeCode(warningEventSceneDepartment.getEmployeeCode());
            dispatchVO.setEmployeeName(warningEventSceneDepartment.getUserName());

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, Integer.parseInt(warningEventSceneDepartment.getExpectedCompleteDay()));
            dispatchVO.setExpectedCompleteTime(calendar.getTime());
            dispatchVO.setOrganizationCode(warningEventSceneDepartment.getOrganizationCode());
        }

        try {
            warningEventService.dispatch(dispatchVO, accountId, employeeMobile);
        } catch (Exception e) {
            WarningDepartmentInfoMapper warningDepartmentInfoMapper = SpringUtils.getBean(WarningDepartmentInfoMapper.class);
            WarningDepartmentInfo warningDepartmentInfo = warningDepartmentInfoMapper
                    .getByOrgCode(warningEventSceneDepartment.getOrganizationCode());

            if (Objects.nonNull(warningDepartmentInfo) && StringUtils.isNotBlank(warningDepartmentInfo.getLeaderAccountId())) {
                String mobileUrl = getMobileUrl(warningEvent.getId(), warningDepartmentInfo.getLeaderId());

                //自动派单失败
                CompletableFuture.runAsync(() -> {
                    userDtalkService.workNotification(StringUtils.join(warningDepartmentInfo.getLeaderAccountId(), ","),
                            CommonConstant.WARNING_INFO_DTALK_DISPATCH_MSG + warningEvent.getId(),
                            "您有一条新的预警事件需要手动派单，请及时处理！",
                            "您有一条新的预警事件需要手动派单，请及时处理！",
                            mobileUrl, singlePcUrl);
                });
            }
        }
    }

    private String getMobileUrl(Long id, Long operateUserId) {
        String url = singleUrl + "?id=" + id;

        if (Objects.isNull(operateUserId)) {
            operateUserId = 79757845L;
        }

        Map map = new HashMap();

        map.put("userId", operateUserId);

        String encodeStr = Base64Encoder.encode(JSON.toJSONString(map));

        url = singleUrl + "?id=" + id + "&params=" + encodeStr;

        return url;
    }

    @Override
    public List<BusinessDeviceValueLineVO> getLineData(DeviceValue deviceValue) {
        if (Objects.isNull(deviceValue.getStartTime()) && Objects.isNull(deviceValue.getEndTime())) {
            DateDTO dateDTO = DateUtils.getTimeRange(new Date(), "近7天");
            deviceValue.setStartTime(dateDTO.getStartTime());
            deviceValue.setEndTime(dateDTO.getEndTime());
        }

        List<DeviceValue> deviceValueList = deviceValueMapper.getLineData(deviceValue.getDeviceThirdId(),
                deviceValue.getStartTime(), deviceValue.getEndTime());

        if (CollectionUtils.isEmpty(deviceValueList)) {
            return Lists.newArrayList();
        }

        List<String> itemList = deviceValueList.stream()
                .map(DeviceValue::getMonitorItem)
                .distinct()
                .collect(Collectors.toList());

        Map<String, List<DeviceValue>> monitorItemMap = deviceValueList.stream()
                .collect(Collectors.groupingBy(DeviceValue::getMonitorItem));

        List<BusinessDeviceValueLineVO> resultList = Lists.newArrayList();
        for (String item : itemList) {
            BusinessDeviceValueLineVO lineVO = new BusinessDeviceValueLineVO();
            lineVO.setMonitorItemName(item);

            List<DeviceValue> valueOneList = monitorItemMap.get(item);
            List<BusinessDateStrCountVO> lineList = valueOneList.stream().map(value -> {
                return new BusinessDateStrCountVO(value.getMonitorTime(), value.getMonitorValue());
            }).collect(Collectors.toList());

            lineVO.setLineList(lineList);

            resultList.add(lineVO);
        }

        return resultList;
    }

    private void fillWarningInfo(WarningRuleCondition warningRuleCondition, DeviceValue deviceValue,
                                 DeviceInfo deviceInfo, List<WarningInfo> warningInfoList, List<WarningEvent> warningEventList,
                                 List<WarningInfo> updateList) {
        WarningInfo warningInfo = createWarningInfo(warningRuleCondition, deviceValue, deviceInfo);

//        warningInfoList.add(warningInfo);
//        WarningEvent warningEvent = createWarningEvent(warningRuleCondition, deviceValue, deviceInfo, warningInfo);

//        //设置频率
//        WarningInfo existInfo = null;
//        if (Objects.equals(warningRuleCondition.getWarningLevel(), WarningLevelType.WARNING_LEVEL_2.getCode())) {
//            existInfo = warningInfoMapper.checkExistByTime(deviceValue.getDeviceThirdId(), deviceValue.getMonitorItem(), 6);
//        } else if (Objects.equals(warningRuleCondition.getWarningLevel(), WarningLevelType.WARNING_LEVEL_3.getCode())) {
//            existInfo = warningInfoMapper.checkExistByTime(deviceValue.getDeviceThirdId(), deviceValue.getMonitorItem(), 12);
//        } else if (Objects.equals(warningRuleCondition.getWarningLevel(), WarningLevelType.WARNING_LEVEL_4.getCode())) {
//            existInfo = warningInfoMapper.checkExistByTime(deviceValue.getDeviceThirdId(), deviceValue.getMonitorItem(), 24);
//        }

        WarningEvent existInfo = warningEventMapper.checkExist(deviceValue.getDeviceThirdId());

        if (Objects.isNull(existInfo)
                || existInfo.getStatus() >= WarningEventStepEnum.STATUS_05.getCode()) {
            warningInfoList.add(warningInfo);
        } else {
            //更新预警信息
            warningInfo.setId(existInfo.getWarningId());
            warningInfo.setCreateBy(null);
            warningInfo.setCreateTime(null);
            updateList.add(warningInfo);
        }
    }

    @Override
    public Map<String, Object> getMonitor(String dateStr, Long deviceId, String monitorItem) {
        DeviceInfo deviceInfo = deviceInfoMapper.getById(deviceId);
        if (Objects.isNull(deviceInfo)) {
            return null;
        }

        String deviceThirdId = deviceInfo.getDeviceThirdId();

        Map<String, Object> resultMap = new HashMap<>();

        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), dateStr);

        List<BusinessDateCountVO> countVOList = deviceValueMapper.listByDateAndDeviceId(
                dateDTO.getStartTime(), dateDTO.getEndTime(), deviceThirdId, monitorItem);

        List<String> dateRange = DateUtils.getDateRange(dateStr);
        List<BusinessCountVO> linkList = Lists.newArrayList();
        for (String dateKey : dateRange) {
            BusinessCountVO businessCountVO = new BusinessCountVO();
            businessCountVO.setKey(dateKey);
            businessCountVO.setValue(0L);

            linkList.add(businessCountVO);
        }

        Map<String, BusinessCountVO> linkMap = linkList.stream()
                .collect(Collectors.toMap(BusinessCountVO::getKey, Function.identity()));

        // TODO: 2024/8/28 是否要指定监测项

        Map<String, List<BusinessDateCountVO>> countMap = new HashMap<>();
        if ("日".equals(dateStr)) {
            countMap = countVOList.stream()
                    .collect(Collectors.groupingBy((countVO) -> DateUtils.formatHour(countVO.getKey())));
        } else if ("周".equals(dateStr)) {
            countMap = countVOList.stream()
                    .collect(Collectors.groupingBy((countVO) -> DateUtils.formatDate(countVO.getKey())));
        } else if ("月".equals(dateStr)) {
            countMap = countVOList.stream()
                    .collect(Collectors.groupingBy((countVO) -> DateUtils.formatDate(countVO.getKey())));
        } else if ("年".equals(dateStr)) {
            countMap = countVOList.stream()
                    .collect(Collectors.groupingBy((countVO) -> DateUtils.formatYearMonth(countVO.getKey())));
        }

        for (Map.Entry<String, List<BusinessDateCountVO>> entry : countMap.entrySet()) {
            List<BusinessDateCountVO> valueList = entry.getValue();
            if (CollectionUtils.isNotEmpty(valueList)) {
                BusinessDateCountVO businessDateCountVO = valueList.get(0);

                BusinessCountVO businessCountVO = linkMap.get(entry.getKey());

                businessCountVO.setValue(businessDateCountVO.getValue());
            }
        }

        resultMap.put("lineMap", linkList);

        return resultMap;
    }

    @Override
    public List<BusinessCountVO> getOneLineByMonitorIdAndItem(Long deviceId, String monitorItem) {
        if (Objects.isNull(deviceId)) {
            deviceId = 2107L;
        }

        DeviceInfo deviceInfo = deviceInfoMapper.getById(deviceId);

        List<BusinessCountVO> resultList = Lists.newArrayList();
        List<String> dateList = DateUtils.getLastSixDate();
        for (String date : dateList) {
            resultList.add(new BusinessCountVO(date, 0L));
        }

        Map<String, BusinessCountVO> resultOneMap = resultList.stream()
                .collect(Collectors.toMap(BusinessCountVO::getKey, Function.identity()));

        List<BusinessDateCountVO> findList = deviceValueMapper.countByDeviceThirdIdAndItem(deviceInfo.getDeviceThirdId(), monitorItem);
        Map<String, List<BusinessDateCountVO>> rainfallMap = findList.stream()
                .collect(Collectors.groupingBy(count -> DateUtils.formatDate(count.getKey())));

        for (Map.Entry<String, List<BusinessDateCountVO>> entry : rainfallMap.entrySet()) {
            BusinessCountVO businessCountVO = resultOneMap.get(entry.getKey());

            List<BusinessDateCountVO> valueList = entry.getValue();
            if (CollectionUtils.isNotEmpty(valueList)) {
                BusinessDateCountVO businessDateCountVO = valueList.get(0);
                if (Objects.isNull(businessCountVO)) {
                    continue;
                }
                businessCountVO.setValue(businessDateCountVO.getValue());
            }
        }

        return resultList;
    }

//    @Override
//    public Map<String, Object> getMonitorV2(String dateStr, String deviceThirdId) {
//        Map<String, Object> resultMap = new HashMap<>();
//
//        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), dateStr);
//
//        List<BusinessDateCountVO> countVOList = deviceValueMapper.listByMonitorIdAndItem(deviceThirdId, "日降雨量",
//                dateDTO.getStartTime(), dateDTO.getEndTime());
//
////        List<BusinessDateCountVO> countVOList = deviceValueMapper.listByDateAndDeviceId(
////                dateDTO.getStartTime(), dateDTO.getEndTime(), deviceId);
//
//        List<String> dateRange = DateUtils.getDateRange(dateStr);
//        List<BusinessCountVO> linkList = Lists.newArrayList();
//        for (String dateKey : dateRange) {
//            BusinessCountVO businessCountVO = new BusinessCountVO();
//            businessCountVO.setKey(dateKey);
//            businessCountVO.setValue(0L);
//
//            linkList.add(businessCountVO);
//        }
//
//        Map<String, BusinessCountVO> linkMap = linkList.stream()
//                .collect(Collectors.toMap(BusinessCountVO::getKey, Function.identity()));
//
//        // TODO: 2024/8/28 是否要指定监测项
//
//        Map<String, List<BusinessDateCountVO>> countMap = new HashMap<>();
//        if ("日".equals(dateStr)) {
//            countMap = countVOList.stream()
//                    .collect(Collectors.groupingBy((countVO) -> DateUtils.formatHour(countVO.getKey())));
//        } else if ("周".equals(dateStr)) {
//            countMap = countVOList.stream()
//                    .collect(Collectors.groupingBy((countVO) -> DateUtils.formatDate(countVO.getKey())));
//        } else if ("月".equals(dateStr)) {
//            countMap = countVOList.stream()
//                    .collect(Collectors.groupingBy((countVO) -> DateUtils.formatDate(countVO.getKey())));
//        } else if ("年".equals(dateStr)) {
//            countMap = countVOList.stream()
//                    .collect(Collectors.groupingBy((countVO) -> DateUtils.formatYearMonth(countVO.getKey())));
//        }
//
//        for (Map.Entry<String, List<BusinessDateCountVO>> entry : countMap.entrySet()) {
//            List<BusinessDateCountVO> valueList = entry.getValue();
//            if (CollectionUtils.isNotEmpty(valueList)) {
//                BusinessDateCountVO businessDateCountVO = valueList.get(0);
//
//                BusinessCountVO businessCountVO = linkMap.get(entry.getKey());
//                if (Objects.nonNull(businessCountVO)) {
//                    businessCountVO.setValue(businessDateCountVO.getValue());
//                }
//            }
//        }
//
//        resultMap.put("lineMap", linkList);
//
//        return resultMap;
//    }

    private WarningInfo createWarningInfo(WarningRuleCondition warningRuleCondition, DeviceValue deviceValue,
                                           DeviceInfo deviceInfo) {
        WarningInfo warningInfo = new WarningInfo();
        warningInfo.setConditionId(warningRuleCondition.getId());
        warningInfo.setMonitorId(deviceInfo.getMonitorId());
        warningInfo.setDeviceId(deviceInfo.getId());
        warningInfo.setDeviceThirdId(deviceInfo.getDeviceThirdId());
        warningInfo.setRuleCondition(warningRuleCondition.getRuleCondition());
        warningInfo.setMonitorItem(warningRuleCondition.getMonitorItem());
        warningInfo.setMonitorValue(warningRuleCondition.getMonitorValue());
        warningInfo.setMonitorUnit(deviceValue.getMonitorUnit());
//        warningInfo.setWarningLevel(warningRuleCondition.getWarningLevel());
        warningInfo.setWarningLevel(5);
        warningInfo.setWarningTime(deviceValue.getMonitorTime());
        warningInfo.setWarningAddress(deviceInfo.getDeviceAddress());
//        warningInfo.setWarningName(deviceInfo.getDeviceName() + WarningLevelType.getByCode(warningRuleCondition.getWarningLevel()));
        warningInfo.setWarningName(deviceInfo.getDeviceName() + "设备告警");
        warningInfo.setWarningData(deviceValue.getMonitorValue());
        warningInfo.setWarningScene(deviceInfo.getDeviceScene());
        warningInfo.setDeviceValueId(deviceValue.getId());
        warningInfo.setWarningScene(deviceInfo.getDeviceScene());
        warningInfo.setCreateBy(warningRuleCondition.getWarningUserId() + "");
        warningInfo.setCreateTime(new Date());
        warningInfo.setLon(deviceInfo.getLon());
        warningInfo.setLat(deviceInfo.getLat());

        String warningReason = getWarningReason(warningRuleCondition, deviceValue, deviceInfo);

        warningInfo.setWarningReason(warningReason);

        return warningInfo;
    }

    private WarningEvent createWarningEvent(DeviceInfo deviceInfo, WarningInfo warningInfo) {
        WarningEventSceneDepartment warningEventSceneDepartment = WarningSceneDepartmentConfig.SCENE_DEPARTMENT_MAP
                .get(warningInfo.getWarningScene());

        WarningEvent warningEvent = new WarningEvent();
        warningEvent.setWarningId(warningInfo.getId());
        warningEvent.setEventName(warningInfo.getWarningName() + "事件");
        warningEvent.setEventType(warningInfo.getWarningScene());
        warningEvent.setDepartmentId(warningEventSceneDepartment.getDepartmentId());
        warningEvent.setDepartmentName(warningEventSceneDepartment.getDepartmentName());
        warningEvent.setEventAddress(warningInfo.getWarningAddress());
        warningEvent.setWarningDetail(warningInfo.getWarningReason());
        warningEvent.setWarningLevel(warningInfo.getWarningLevel());
        warningEvent.setWarningTime(warningInfo.getWarningTime());
        warningEvent.setStatus(0);
        warningEvent.setStopFlag(0);
        warningEvent.setOverFlag(0);
        warningEvent.setSuperviseFlag(1);
        warningEvent.setCreateTime(new Date());
        warningEvent.setCreateBy(warningEventSceneDepartment.getEmployeeCode());
        warningEvent.setLon(deviceInfo.getLon());
        warningEvent.setLat(deviceInfo.getLat());
        warningEvent.setEventStreet(deviceInfo.getDeviceStreet());
        warningEvent.setEventStreetAreaCode(deviceInfo.getDeviceStreetAreaCode());
        warningEvent.setDeviceThirdId(deviceInfo.getDeviceThirdId());
        warningEvent.setDeviceName(deviceInfo.getDeviceName());
        warningEvent.setConditionId(warningInfo.getConditionId());
        warningEvent.setFirstWarningTime(warningInfo.getWarningTime());

        return warningEvent;
    }

    private String getWarningReason(WarningRuleCondition warningRuleCondition, DeviceValue deviceValue, DeviceInfo deviceInfo) {
        StringBuilder sb = new StringBuilder();
//        sb.append(DateUtils.formatDateSecond(deviceValue.getMonitorTime()));
//        if (Objects.nonNull(deviceInfo)) {
//            sb.append(deviceInfo.getDeviceAddress())
//                    .append(deviceInfo.getDeviceName())
//                    .append("设备");
//        } else {
//            sb.append("设备编号为")
//                    .append(deviceValue.getDeviceThirdId())
//                    .append("的设备");
//        }
        sb.append("监测到")
                .append(deviceValue.getMonitorItem())
                .append("数据值异常");

        sb.append("(监测值")
                .append(deviceValue.getMonitorValue())
                .append(deviceValue.getMonitorUnit())
                .append(",阈值")
                .append(warningRuleCondition.getMonitorValue())
                .append(deviceValue.getMonitorUnit())
                .append("), ");

        sb.append("疑似").append(deviceValue.getMonitorItem()).append("异常, 请及时处置。");

        return sb.toString();

//        return warningRuleCondition.getMonitorItem() + warningRuleCondition.getRuleCondition() + warningRuleCondition.getMonitorValue()
//                + ", 产生" + WarningLevelType.getByCode(warningRuleCondition.getWarningLevel());
    }
}
