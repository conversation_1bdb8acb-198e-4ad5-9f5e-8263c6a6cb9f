<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceMonitorShantangMapper">

    <resultMap type="DeviceMonitorShantang" id="DeviceMonitorShantangResult">
        <result property="id"    column="id"    />
        <result property="stmc"    column="stmc"    />
        <result property="stlm"    column="stlm"    />
        <result property="stbm"    column="stbm"    />
        <result property="szs"    column="szs"    />
        <result property="szxsq"    column="szxsq"    />
        <result property="szxzc"    column="szxzc"    />
        <result property="zygn"    column="zygn"    />
        <result property="jd"    column="jd"    />
        <result property="wd"    column="wd"    />
        <result property="sfjxstzz"    column="sfjxstzz"    />
        <result property="zzwcsj"    column="zzwcsj"    />
        <result property="sjdw"    column="sjdw"    />
        <result property="jymj"    column="jymj"    />
        <result property="zlcd"    column="zlcd"    />
        <result property="jhhsw"    column="jhhsw"    />
        <result property="jhhswdyrj"    column="jhhswdyrj"    />
        <result property="jhhsp"    column="jhhsp"    />
        <result property="sjhsw"    column="sjhsw"    />
        <result property="sjhsw1"    column="sjhsw1"    />
        <result property="sjhs"    column="sjhs"    />
        <result property="zcxsc"    column="zcxsc"    />
        <result property="zcxsw"    column="zcxsw"    />
        <result property="yxqy"    column="yxqy"    />
        <result property="yxqynczgs"    column="yxqynczgs"    />
        <result property="yxrk"    column="yxrk"    />
        <result property="rgsnl"    column="rgsnl"    />
        <result property="ggmj"    column="ggmj"    />
        <result property="bz"    column="bz"    />
        <result property="dsjzwbx"    column="dsjzwbx"    />
        <result property="dsjzwbdgc"    column="dsjzwbdgc"    />
        <result property="dsjzwbsbjgc"    column="dsjzwbsbjgc"    />
        <result property="dsjzwzdbg"    column="dsjzwzdbg"    />
        <result property="dsjzwysppb"    column="dsjzwysppb"    />
        <result property="dsjzwbsppb"    column="dsjzwbsppb"    />
        <result property="dsjzwbdgd"    column="dsjzwbdgd"    />
        <result property="dsjzwbdgd1"    column="dsjzwbdgd1"    />
        <result property="xhjzwxs"    column="xhjzwxs"    />
        <result property="xhjzwyhgd"    column="xhjzwyhgd"    />
        <result property="ssjzwxs"    column="ssjzwxs"    />
        <result property="ssjzwcd"    column="ssjzwcd"    />
        <result property="ssjzwdmcc"    column="ssjzwdmcc"    />
        <result property="sfyjsjg"    column="sfyjsjg"    />
        <result property="sfsl"    column="sfsl"    />
        <result property="glf"    column="glf"    />
        <result property="glfmj"    column="glfmj"    />
        <result property="sfwcbzhgl"    column="sfwcbzhgl"    />
        <result property="glfwz"    column="glfwz"    />
        <result property="gcss"    column="gcss"    />
        <result property="aqpdjl"    column="aqpdjl"    />
        <result property="sfsmlst"    column="sfsmlst"    />
    </resultMap>

    <sql id="selectDeviceMonitorShantangVo">
        select id, stmc, stlm, stbm, szs, szxsq, szxzc, zygn, jd, wd, sfjxstzz, zzwcsj, sjdw, jymj, zlcd, jhhsw, jhhswdyrj, jhhsp, sjhsw, sjhsw1, sjhs, zcxsc, zcxsw, yxqy, yxqynczgs, yxrk, rgsnl, ggmj, bz, dsjzwbx, dsjzwbdgc, dsjzwbsbjgc, dsjzwzdbg, dsjzwysppb, dsjzwbsppb, dsjzwbdgd, dsjzwbdgd1, xhjzwxs, xhjzwyhgd, ssjzwxs, ssjzwcd, ssjzwdmcc, sfyjsjg, sfsl, glf, glfmj, sfwcbzhgl, glfwz, gcss, aqpdjl, sfsmlst from t_device_monitor_shantang
    </sql>

    <select id="selectDeviceMonitorShantangList" parameterType="DeviceMonitorShantang" resultMap="DeviceMonitorShantangResult">
        <include refid="selectDeviceMonitorShantangVo"/>
        <where>
            <if test="stmc != null  and stmc != ''"> and stmc = #{stmc}</if>
            <if test="stlm != null  and stlm != ''"> and stlm = #{stlm}</if>
            <if test="stbm != null  and stbm != ''"> and stbm = #{stbm}</if>
            <if test="szs != null  and szs != ''"> and szs = #{szs}</if>
            <if test="szxsq != null  and szxsq != ''"> and szxsq = #{szxsq}</if>
            <if test="szxzc != null  and szxzc != ''"> and szxzc = #{szxzc}</if>
            <if test="zygn != null  and zygn != ''"> and zygn = #{zygn}</if>
            <if test="jd != null  and jd != ''"> and jd = #{jd}</if>
            <if test="wd != null  and wd != ''"> and wd = #{wd}</if>
            <if test="sfjxstzz != null  and sfjxstzz != ''"> and sfjxstzz = #{sfjxstzz}</if>
            <if test="zzwcsj != null  and zzwcsj != ''"> and zzwcsj = #{zzwcsj}</if>
            <if test="sjdw != null  and sjdw != ''"> and sjdw = #{sjdw}</if>
            <if test="jymj != null  and jymj != ''"> and jymj = #{jymj}</if>
            <if test="zlcd != null  and zlcd != ''"> and zlcd = #{zlcd}</if>
            <if test="jhhsw != null  and jhhsw != ''"> and jhhsw = #{jhhsw}</if>
            <if test="jhhswdyrj != null  and jhhswdyrj != ''"> and jhhswdyrj = #{jhhswdyrj}</if>
            <if test="jhhsp != null  and jhhsp != ''"> and jhhsp = #{jhhsp}</if>
            <if test="sjhsw != null  and sjhsw != ''"> and sjhsw = #{sjhsw}</if>
            <if test="sjhsw1 != null  and sjhsw1 != ''"> and sjhsw1 = #{sjhsw1}</if>
            <if test="sjhs != null  and sjhs != ''"> and sjhs = #{sjhs}</if>
            <if test="zcxsc != null  and zcxsc != ''"> and zcxsc = #{zcxsc}</if>
            <if test="zcxsw != null  and zcxsw != ''"> and zcxsw = #{zcxsw}</if>
            <if test="yxqy != null  and yxqy != ''"> and yxqy = #{yxqy}</if>
            <if test="yxqynczgs != null  and yxqynczgs != ''"> and yxqynczgs = #{yxqynczgs}</if>
            <if test="yxrk != null  and yxrk != ''"> and yxrk = #{yxrk}</if>
            <if test="rgsnl != null  and rgsnl != ''"> and rgsnl = #{rgsnl}</if>
            <if test="ggmj != null  and ggmj != ''"> and ggmj = #{ggmj}</if>
            <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
            <if test="dsjzwbx != null  and dsjzwbx != ''"> and dsjzwbx = #{dsjzwbx}</if>
            <if test="dsjzwbdgc != null  and dsjzwbdgc != ''"> and dsjzwbdgc = #{dsjzwbdgc}</if>
            <if test="dsjzwbsbjgc != null  and dsjzwbsbjgc != ''"> and dsjzwbsbjgc = #{dsjzwbsbjgc}</if>
            <if test="dsjzwzdbg != null  and dsjzwzdbg != ''"> and dsjzwzdbg = #{dsjzwzdbg}</if>
            <if test="dsjzwysppb != null  and dsjzwysppb != ''"> and dsjzwysppb = #{dsjzwysppb}</if>
            <if test="dsjzwbsppb != null  and dsjzwbsppb != ''"> and dsjzwbsppb = #{dsjzwbsppb}</if>
            <if test="dsjzwbdgd != null  and dsjzwbdgd != ''"> and dsjzwbdgd = #{dsjzwbdgd}</if>
            <if test="dsjzwbdgd1 != null  and dsjzwbdgd1 != ''"> and dsjzwbdgd1 = #{dsjzwbdgd1}</if>
            <if test="xhjzwxs != null  and xhjzwxs != ''"> and xhjzwxs = #{xhjzwxs}</if>
            <if test="xhjzwyhgd != null  and xhjzwyhgd != ''"> and xhjzwyhgd = #{xhjzwyhgd}</if>
            <if test="ssjzwxs != null  and ssjzwxs != ''"> and ssjzwxs = #{ssjzwxs}</if>
            <if test="ssjzwcd != null  and ssjzwcd != ''"> and ssjzwcd = #{ssjzwcd}</if>
            <if test="ssjzwdmcc != null  and ssjzwdmcc != ''"> and ssjzwdmcc = #{ssjzwdmcc}</if>
            <if test="sfyjsjg != null  and sfyjsjg != ''"> and sfyjsjg = #{sfyjsjg}</if>
            <if test="sfsl != null  and sfsl != ''"> and sfsl = #{sfsl}</if>
            <if test="glf != null  and glf != ''"> and glf = #{glf}</if>
            <if test="glfmj != null  and glfmj != ''"> and glfmj = #{glfmj}</if>
            <if test="sfwcbzhgl != null  and sfwcbzhgl != ''"> and sfwcbzhgl = #{sfwcbzhgl}</if>
            <if test="glfwz != null  and glfwz != ''"> and glfwz = #{glfwz}</if>
            <if test="gcss != null  and gcss != ''"> and gcss = #{gcss}</if>
            <if test="aqpdjl != null  and aqpdjl != ''"> and aqpdjl = #{aqpdjl}</if>
            <if test="sfsmlst != null  and sfsmlst != ''"> and sfsmlst = #{sfsmlst}</if>
        </where>
    </select>

    <select id="selectDeviceMonitorShantangById" parameterType="String" resultMap="DeviceMonitorShantangResult">
        <include refid="selectDeviceMonitorShantangVo"/>
        where id = #{id}
    </select>

    <insert id="insertDeviceMonitorShantang" parameterType="DeviceMonitorShantang">
        insert into t_device_monitor_shantang
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="stmc != null">stmc,</if>
            <if test="stlm != null">stlm,</if>
            <if test="stbm != null">stbm,</if>
            <if test="szs != null">szs,</if>
            <if test="szxsq != null">szxsq,</if>
            <if test="szxzc != null">szxzc,</if>
            <if test="zygn != null">zygn,</if>
            <if test="jd != null">jd,</if>
            <if test="wd != null">wd,</if>
            <if test="sfjxstzz != null">sfjxstzz,</if>
            <if test="zzwcsj != null">zzwcsj,</if>
            <if test="sjdw != null">sjdw,</if>
            <if test="jymj != null">jymj,</if>
            <if test="zlcd != null">zlcd,</if>
            <if test="jhhsw != null">jhhsw,</if>
            <if test="jhhswdyrj != null">jhhswdyrj,</if>
            <if test="jhhsp != null">jhhsp,</if>
            <if test="sjhsw != null">sjhsw,</if>
            <if test="sjhsw1 != null">sjhsw1,</if>
            <if test="sjhs != null">sjhs,</if>
            <if test="zcxsc != null">zcxsc,</if>
            <if test="zcxsw != null">zcxsw,</if>
            <if test="yxqy != null">yxqy,</if>
            <if test="yxqynczgs != null">yxqynczgs,</if>
            <if test="yxrk != null">yxrk,</if>
            <if test="rgsnl != null">rgsnl,</if>
            <if test="ggmj != null">ggmj,</if>
            <if test="bz != null">bz,</if>
            <if test="dsjzwbx != null">dsjzwbx,</if>
            <if test="dsjzwbdgc != null">dsjzwbdgc,</if>
            <if test="dsjzwbsbjgc != null">dsjzwbsbjgc,</if>
            <if test="dsjzwzdbg != null">dsjzwzdbg,</if>
            <if test="dsjzwysppb != null">dsjzwysppb,</if>
            <if test="dsjzwbsppb != null">dsjzwbsppb,</if>
            <if test="dsjzwbdgd != null">dsjzwbdgd,</if>
            <if test="dsjzwbdgd1 != null">dsjzwbdgd1,</if>
            <if test="xhjzwxs != null">xhjzwxs,</if>
            <if test="xhjzwyhgd != null">xhjzwyhgd,</if>
            <if test="ssjzwxs != null">ssjzwxs,</if>
            <if test="ssjzwcd != null">ssjzwcd,</if>
            <if test="ssjzwdmcc != null">ssjzwdmcc,</if>
            <if test="sfyjsjg != null">sfyjsjg,</if>
            <if test="sfsl != null">sfsl,</if>
            <if test="glf != null">glf,</if>
            <if test="glfmj != null">glfmj,</if>
            <if test="sfwcbzhgl != null">sfwcbzhgl,</if>
            <if test="glfwz != null">glfwz,</if>
            <if test="gcss != null">gcss,</if>
            <if test="aqpdjl != null">aqpdjl,</if>
            <if test="sfsmlst != null">sfsmlst,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="stmc != null">#{stmc},</if>
            <if test="stlm != null">#{stlm},</if>
            <if test="stbm != null">#{stbm},</if>
            <if test="szs != null">#{szs},</if>
            <if test="szxsq != null">#{szxsq},</if>
            <if test="szxzc != null">#{szxzc},</if>
            <if test="zygn != null">#{zygn},</if>
            <if test="jd != null">#{jd},</if>
            <if test="wd != null">#{wd},</if>
            <if test="sfjxstzz != null">#{sfjxstzz},</if>
            <if test="zzwcsj != null">#{zzwcsj},</if>
            <if test="sjdw != null">#{sjdw},</if>
            <if test="jymj != null">#{jymj},</if>
            <if test="zlcd != null">#{zlcd},</if>
            <if test="jhhsw != null">#{jhhsw},</if>
            <if test="jhhswdyrj != null">#{jhhswdyrj},</if>
            <if test="jhhsp != null">#{jhhsp},</if>
            <if test="sjhsw != null">#{sjhsw},</if>
            <if test="sjhsw1 != null">#{sjhsw1},</if>
            <if test="sjhs != null">#{sjhs},</if>
            <if test="zcxsc != null">#{zcxsc},</if>
            <if test="zcxsw != null">#{zcxsw},</if>
            <if test="yxqy != null">#{yxqy},</if>
            <if test="yxqynczgs != null">#{yxqynczgs},</if>
            <if test="yxrk != null">#{yxrk},</if>
            <if test="rgsnl != null">#{rgsnl},</if>
            <if test="ggmj != null">#{ggmj},</if>
            <if test="bz != null">#{bz},</if>
            <if test="dsjzwbx != null">#{dsjzwbx},</if>
            <if test="dsjzwbdgc != null">#{dsjzwbdgc},</if>
            <if test="dsjzwbsbjgc != null">#{dsjzwbsbjgc},</if>
            <if test="dsjzwzdbg != null">#{dsjzwzdbg},</if>
            <if test="dsjzwysppb != null">#{dsjzwysppb},</if>
            <if test="dsjzwbsppb != null">#{dsjzwbsppb},</if>
            <if test="dsjzwbdgd != null">#{dsjzwbdgd},</if>
            <if test="dsjzwbdgd1 != null">#{dsjzwbdgd1},</if>
            <if test="xhjzwxs != null">#{xhjzwxs},</if>
            <if test="xhjzwyhgd != null">#{xhjzwyhgd},</if>
            <if test="ssjzwxs != null">#{ssjzwxs},</if>
            <if test="ssjzwcd != null">#{ssjzwcd},</if>
            <if test="ssjzwdmcc != null">#{ssjzwdmcc},</if>
            <if test="sfyjsjg != null">#{sfyjsjg},</if>
            <if test="sfsl != null">#{sfsl},</if>
            <if test="glf != null">#{glf},</if>
            <if test="glfmj != null">#{glfmj},</if>
            <if test="sfwcbzhgl != null">#{sfwcbzhgl},</if>
            <if test="glfwz != null">#{glfwz},</if>
            <if test="gcss != null">#{gcss},</if>
            <if test="aqpdjl != null">#{aqpdjl},</if>
            <if test="sfsmlst != null">#{sfsmlst},</if>
         </trim>
    </insert>

    <update id="updateDeviceMonitorShantang" parameterType="DeviceMonitorShantang">
        update t_device_monitor_shantang
        <trim prefix="SET" suffixOverrides=",">
            <if test="stmc != null">stmc = #{stmc},</if>
            <if test="stlm != null">stlm = #{stlm},</if>
            <if test="stbm != null">stbm = #{stbm},</if>
            <if test="szs != null">szs = #{szs},</if>
            <if test="szxsq != null">szxsq = #{szxsq},</if>
            <if test="szxzc != null">szxzc = #{szxzc},</if>
            <if test="zygn != null">zygn = #{zygn},</if>
            <if test="jd != null">jd = #{jd},</if>
            <if test="wd != null">wd = #{wd},</if>
            <if test="sfjxstzz != null">sfjxstzz = #{sfjxstzz},</if>
            <if test="zzwcsj != null">zzwcsj = #{zzwcsj},</if>
            <if test="sjdw != null">sjdw = #{sjdw},</if>
            <if test="jymj != null">jymj = #{jymj},</if>
            <if test="zlcd != null">zlcd = #{zlcd},</if>
            <if test="jhhsw != null">jhhsw = #{jhhsw},</if>
            <if test="jhhswdyrj != null">jhhswdyrj = #{jhhswdyrj},</if>
            <if test="jhhsp != null">jhhsp = #{jhhsp},</if>
            <if test="sjhsw != null">sjhsw = #{sjhsw},</if>
            <if test="sjhsw1 != null">sjhsw1 = #{sjhsw1},</if>
            <if test="sjhs != null">sjhs = #{sjhs},</if>
            <if test="zcxsc != null">zcxsc = #{zcxsc},</if>
            <if test="zcxsw != null">zcxsw = #{zcxsw},</if>
            <if test="yxqy != null">yxqy = #{yxqy},</if>
            <if test="yxqynczgs != null">yxqynczgs = #{yxqynczgs},</if>
            <if test="yxrk != null">yxrk = #{yxrk},</if>
            <if test="rgsnl != null">rgsnl = #{rgsnl},</if>
            <if test="ggmj != null">ggmj = #{ggmj},</if>
            <if test="bz != null">bz = #{bz},</if>
            <if test="dsjzwbx != null">dsjzwbx = #{dsjzwbx},</if>
            <if test="dsjzwbdgc != null">dsjzwbdgc = #{dsjzwbdgc},</if>
            <if test="dsjzwbsbjgc != null">dsjzwbsbjgc = #{dsjzwbsbjgc},</if>
            <if test="dsjzwzdbg != null">dsjzwzdbg = #{dsjzwzdbg},</if>
            <if test="dsjzwysppb != null">dsjzwysppb = #{dsjzwysppb},</if>
            <if test="dsjzwbsppb != null">dsjzwbsppb = #{dsjzwbsppb},</if>
            <if test="dsjzwbdgd != null">dsjzwbdgd = #{dsjzwbdgd},</if>
            <if test="dsjzwbdgd1 != null">dsjzwbdgd1 = #{dsjzwbdgd1},</if>
            <if test="xhjzwxs != null">xhjzwxs = #{xhjzwxs},</if>
            <if test="xhjzwyhgd != null">xhjzwyhgd = #{xhjzwyhgd},</if>
            <if test="ssjzwxs != null">ssjzwxs = #{ssjzwxs},</if>
            <if test="ssjzwcd != null">ssjzwcd = #{ssjzwcd},</if>
            <if test="ssjzwdmcc != null">ssjzwdmcc = #{ssjzwdmcc},</if>
            <if test="sfyjsjg != null">sfyjsjg = #{sfyjsjg},</if>
            <if test="sfsl != null">sfsl = #{sfsl},</if>
            <if test="glf != null">glf = #{glf},</if>
            <if test="glfmj != null">glfmj = #{glfmj},</if>
            <if test="sfwcbzhgl != null">sfwcbzhgl = #{sfwcbzhgl},</if>
            <if test="glfwz != null">glfwz = #{glfwz},</if>
            <if test="gcss != null">gcss = #{gcss},</if>
            <if test="aqpdjl != null">aqpdjl = #{aqpdjl},</if>
            <if test="sfsmlst != null">sfsmlst = #{sfsmlst},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceMonitorShantangById" parameterType="String">
        delete from t_device_monitor_shantang where id = #{id}
    </delete>

    <delete id="deleteDeviceMonitorShantangByIds" parameterType="String">
        delete from t_device_monitor_shantang where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="listAll" resultMap="DeviceMonitorShantangResult">
        <include refid="selectDeviceMonitorShantangVo">
        </include>
        group by stmc
    </select>

    <select id="getByMonitorName" resultMap="DeviceMonitorShantangResult">
        <include refid="selectDeviceMonitorShantangVo">
        </include>
        where stmc = #{stmc}
    </select>
</mapper>
