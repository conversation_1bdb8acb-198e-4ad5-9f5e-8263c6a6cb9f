package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.DeviceStreet;
import java.util.List;

import com.ruoyi.jiangshan.vo.street.DeviceStreetVO;
import org.apache.ibatis.annotations.Param;

public interface DeviceStreetMapper {
    int deleteByPrimaryKey(String id);

    int insert(DeviceStreet record);

    int insertSelective(DeviceStreet record);

    DeviceStreet selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(DeviceStreet record);

    int updateByPrimaryKey(DeviceStreet record);

    int updateBatch(List<DeviceStreet> list);

    int batchInsert(@Param("list") List<DeviceStreet> list);

    void updateByName(DeviceStreet deviceStreet);

    List<DeviceStreetVO> listAll();

    List<DeviceStreet> listWithOutParentId();

    DeviceStreet getByParentId(@Param("parentId") String parentId);

    DeviceStreet getStreet(@Param("lon") String lon, @Param("lat") String lat);
}
