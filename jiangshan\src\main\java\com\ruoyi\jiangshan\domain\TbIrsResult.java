package com.ruoyi.jiangshan.domain;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * irs调用
 *
 * <AUTHOR>
 * @date 2022-03-17
 */
@Data
public class TbIrsResult implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "irs接口名称不能为空")
    private String name;

    @NotNull(message = "irs接口地址不能为空")
    private String url;

    /**
     * 观测时间
     */
    private String Observtimes;

}
