package com.ruoyi.jiangshan.service;

import java.util.List;

import com.ruoyi.jiangshan.domain.DeviceInfoItem;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.WarningRule;
import com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin;
import com.ruoyi.jiangshan.openapi.vo.WarningRuleOpenApiVO;

/**
 * 设备规则Service接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface IWarningRuleService
{
    /**
     * 查询设备规则
     *
     * @param id 设备规则主键
     * @return 设备规则
     */
    public WarningRule selectWarningRuleById(Long id);

    /**
     * 查询设备规则列表
     *
     * @param warningRule 设备规则
     * @return 设备规则集合
     */
    public List<WarningRule> selectWarningRuleList(WarningRule warningRule);

    /**
     * 新增设备规则
     *
     * @param warningRule 设备规则
     * @return 结果
     */
    public int insertWarningRule(WarningRule warningRule);

    /**
     * 修改设备规则
     *
     * @param warningRule 设备规则
     * @return 结果
     */
    public int updateWarningRule(WarningRule warningRule);

    /**
     * 批量删除设备规则
     *
     * @param ids 需要删除的设备规则主键集合
     * @return 结果
     */
    public int deleteWarningRuleByIds(Long[] ids);

    /**
     * 删除设备规则信息
     *
     * @param id 设备规则主键
     * @return 结果
     */
    public int deleteWarningRuleById(Long id);

    List<DeviceInfoItem> listMonitorItemByType(String type);

    void saveWarningRule(WarningRuleOpenApiVO apiVO);
}
