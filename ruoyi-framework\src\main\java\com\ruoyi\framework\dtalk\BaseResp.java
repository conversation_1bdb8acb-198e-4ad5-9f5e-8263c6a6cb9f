package com.ruoyi.framework.dtalk;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> yx-0176
 * @description
 * @date : 2021/10/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseResp<T> {

    private Boolean success;

    private String message;

    private Content<T> content;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
   public static class Content<T> {
        private T data;
        private Boolean success;
        private String responseMessage;
        private Integer responseCode;
        private String requestId;
    }
}
