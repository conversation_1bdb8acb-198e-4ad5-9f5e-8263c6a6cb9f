package com.ruoyi.jiangshan.service;

import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.vo.*;

import java.util.List;
import java.util.Map;

public interface FireSceneService {
    List<WarningEvent> getMonitor(Long monitorId);

    List<WarningOccupationVO> getOccupationList();

    List<BusinessCountVO> getOccupationCount(String dateStr);

    List<WarningInfoVO> getWarning(Integer warningLevel);

    WarningCountVO getSafetyCount(String dateStr);

    List<BusinessMultiLineVO> getStatistics();

    Map<String, List<DeviceInfo>> getDeviceList();

}
