package com.ruoyi.jiangshan.mapper;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.vo.BusinessCountVO;
import com.ruoyi.jiangshan.vo.compre.VideoStreetVO;
import org.apache.ibatis.annotations.Param;

/**
 * 设备信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface DeviceInfoMapper
{
    /**
     * 查询设备信息
     *
     * @param id 设备信息主键
     * @return 设备信息
     */
    public DeviceInfo selectDeviceInfoById(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 查询设备信息列表
     *
     * @param deviceInfo 设备信息
     * @return 设备信息集合
     */
    public List<DeviceInfo> selectDeviceInfoList(DeviceInfo deviceInfo);

    /**
     * 新增设备信息
     *
     * @param deviceInfo 设备信息
     * @return 结果
     */
    public int insertDeviceInfo(DeviceInfo deviceInfo);

    /**
     * 修改设备信息
     *
     * @param deviceInfo 设备信息
     * @return 结果
     */
    public int updateDeviceInfo(DeviceInfo deviceInfo);

    /**
     * 删除设备信息
     *
     * @param id 设备信息主键
     * @return 结果
     */
    public int deleteDeviceInfoById(Long id);

    /**
     * 批量删除设备信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceInfoByIds(Long[] ids);

    List<BusinessCountVO> countByType(@Param("list") List<String> typeList);

    List<BusinessCountVO> countByMonitorTypeAndStatus(@Param("list") List<String> list);

    List<BusinessCountVO> countByMonitorSceneAndStatus(@Param("scene") String scene);

    DeviceInfo getByThirdId(@Param("deviceThirdId") String deviceThirdId);

    List<DeviceInfo> listByDeviceType(@Param("deviceType") String deviceType);

    List<DeviceInfo> listAll();

    List<DeviceInfo> listByScene(@Param("scene") String scene);

    List<DeviceInfo> listByDeviceTypeAndScene(@Param("deviceType") String deviceType, @Param("scene") String scene,
                                              @Param("mapFlag") Boolean mapFlag);

    List<DeviceInfo> listByDeviceTypeAndSceneAndMonitorId(@Param("deviceType") String deviceType,
                                                          @Param("scene") String scene,
                                                          @Param("monitorId") Long monitorId,
                                                          @Param("mapFlag") Boolean mapFlag,
                                                          @Param("includeList") List<String> includeList,
                                                          @Param("excludeList") List<String> excludeList);

    List<DeviceInfo> listBySceneList(@Param("sceneList") List<String> sceneList);

    Long sumByType(@Param("typeList") List<String> typeList);

    List<DeviceInfo> listByDeviceThirdIdList(@Param("deviceThirdIdList") List<String> deviceThirdIdList);

    void batchInsert(List<DeviceInfo> deviceInfoList);

    List<String> getDeviceName(@Param("name") String name);

    List<VideoStreetVO> getStreetByDevice(@Param("deviceType") String deviceType);

    List<DeviceInfo> getDeviceByStreet(@Param("deviceStreetAreaCode") String deviceStreetAreaCode,
                                       @Param("scene") String scene, @Param("deviceType") String deviceType);

    List<DeviceInfo> listByDeviceTypeList(@Param("deviceTypeList") List<String> deviceTypeList);

    DeviceInfo getById(@Param("deviceId") Long deviceId);

    Long sumExcludeType(@Param("videoList") List<String> videoList);

    List<DeviceInfo> listDeviceInfoByGem(@Param("geometry") String geometry);

    void updateAddressByIdList(@Param("address") String address, @Param("street") String street,
                               @Param("streetCode") String streetCode, @Param("deviceIdList") List<Long> deviceIdList);

    List<DeviceInfo> listByIdList(@Param("list") List<Long> deviceIdList);

    void updateDeviceConnectStatus(@Param("deviceThirdId") String deviceThirdId, @Param("status") Integer status,
                                   @Param("connectTime") Date connectTime);

    List<DeviceInfo> listByMonitorItemAndDeviceType(@Param("monitorId") Long monitorId,
                                                    @Param("deviceType") String deviceType);

    List<String> listAllDeviceType();

    List<DeviceInfo> listByDeviceTypeAndStatus(@Param("deviceType") String deviceType, @Param("status") Integer status);

    List<DeviceInfo> listByDeviceNum(@Param("list") List<String> deviceThirdIdList);

    List<DeviceInfo> listInlineByDeviceType(@Param("deviceType") String deviceType, @Param("monitorId") Long monitorId);

    DeviceInfo getByField05(String deviceID);

    void updateStatusByDeviceThirdId(DeviceInfo deviceInfo);

    List<String> listAllSjxbjcy();

    List<DeviceInfo> listByMonitorIdsAndType(@Param("monitorIdList") List<Long> monitorIdList,
                                             @Param("deviceTypeList") List<String> deviceTypeList);

    Long countByStatusAndSource(@Param("deviceSource") String deviceSource,
                                @Param("status") Integer status);

    List<DeviceInfo> listByYuLiang(@Param("typeLike") String typeLike);

    List<DeviceInfo> listByDeviceTypeOrMonitorId(@Param("deviceType") String deviceType, @Param("monitorId") Long monitorId,
                                                 @Param("excludeList") List<String> excludeList);

    List<DeviceInfo> listByDeviceTypeAndDeviceStreet(@Param("deviceStreet") String deviceStreet, @Param("deviceType") String deviceType);

    List<DeviceInfo> listPushValue(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
