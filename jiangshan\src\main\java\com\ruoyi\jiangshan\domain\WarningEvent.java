package com.ruoyi.jiangshan.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.jiangshan.vo.WarningEventProcessLogVO;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.EqualsAndHashCode;

/**
 * 设备预警事件对象 t_warning_event
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Data
public class WarningEvent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /**
     * 事件编号
     */
    @Excel(name = "事件编号")
    private String bizCode;

    /** 预警id */
//    @Excel(name = "预警id")
    private Long warningId;

    /** 事件第三方id */
//    @Excel(name = "事件第三方id")
    private String eventThirdId;

    /** 事件名称 */
    @Excel(name = "事件名称")
    private String eventName;

    /** 事件类型(场景) */
    @Excel(name = "事件类型")
    private String eventType;

    /** 风险等级 1-4 */
    @Excel(name = "风险等级", readConverterExp = "1=一级告警,2=二级告警,3=三级告警,4=四级告警")
    private Integer warningLevel;

    /** 处置部门id */
//    @Excel(name = "处置部门id")
    private Long departmentId;

    /** 处置部门name */
    @Excel(name = "处置部门")
    private String departmentName;

    private String departmentOrganizationCode;

    /** 事件地点 */
    @Excel(name = "事件地点")
    private String eventAddress;

    /** 风险点具体描述 */
    @Excel(name = "风险点具体描述")
    private String warningDetail;

    /** 预警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date warningTime;

    /** 状态 0未派单 1待签收-核实 2待核实 3待签收-处置 4处置中 5-已完成 */
    @Excel(name = "状态", readConverterExp = "0=未派单,1=待签收-核实,2=2待核实,3=待签收-处置,4=处置中,5=已完成")
    private Integer status;

    /** 派单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "派单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;

    /**
     * 派单状态
     */
//    @Excel(name = "派单状态", readConverterExp = "1=未派单,2=已退回,3=已完成")
    private Integer deliveryStatus;

    /** 挂起状态 0-未挂起 1-已挂起 */
    @Excel(name = "挂起状态", readConverterExp = "0=未挂起,1=已挂起")
    private Integer stopFlag;

    /** 超期状态 0-未超期 1-已超期 */
    @Excel(name = "超期状态", readConverterExp = "0=未超期,1=已超期")
    private Integer overFlag;

    /** 是否督办 0-否 1-是 */
    @Excel(name = "是否督办", readConverterExp = "0=否,1=是")
    private Integer superviseFlag;

    /** 督办类型 抄送、超时、重点关注 */
    @Excel(name = "督办类型", readConverterExp = "1=抄送,2=超时,3=重点关注")
    private String superviseType;

    /**
     * 领导批示
     */
    @Excel(name = "领导批示")
    private String leaderContent;

    private List<BusinessFile> fileList;

    private List<WarningEventProcess> processList;

    private List<WarningEventDepartment> departmentList;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 派送说明
     */
    private String deliveryReason;

    /**
     * 是否能操作 0不能 1能
     */
    private Integer operatorFlag = 0;

    /** 退回说明 */
    @Excel(name = "退回说明")
    private String rejectReason;

    private List<WarningEventProcessLogVO> logList;

    /**
     * 事件街道名称
     */
    private String eventStreet;

    /**
     * 事件区划code
     */
    private String eventStreetAreaCode;

    /**
     * 设备编号
     */
    private String deviceThirdId;

    /**
     * 设备名称
     */
    private String deviceName;

    private Date expectedCompleteTime;

    private Long conditionId;

    /**
     * 二维码地址
     */
    private String qrCode;

    /**
     * 建议处置部门
     */
    private String adviceDepartment;

    /**
     * 占用时长
     */
    private Long occupyRange;

    /**
     * 首次告警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstWarningTime;

}
