<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningEventSceneDepartmentMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningEventSceneDepartment">
    <!--@mbg.generated-->
    <!--@Table t_warning_event_scene_department-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="monitor_scene" jdbcType="VARCHAR" property="monitorScene" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="organization_code" jdbcType="VARCHAR" property="organizationCode" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="employee_code" jdbcType="VARCHAR" property="employeeCode" />
    <result column="expected_complete_day" jdbcType="VARCHAR" property="expectedCompleteDay" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, monitor_scene, department_id, department_name, organization_code, user_id, user_name,
    account_id, employee_code, expected_complete_day, role_id, create_by, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_event_scene_department
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_event_scene_department
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventSceneDepartment" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_scene_department (monitor_scene, department_id, department_name,
      organization_code, user_id, user_name,
      account_id, employee_code, expected_complete_day,
      role_id, create_by, create_time
      )
    values (#{monitorScene,jdbcType=VARCHAR}, #{departmentId,jdbcType=BIGINT}, #{departmentName,jdbcType=VARCHAR},
      #{organizationCode,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR},
      #{accountId,jdbcType=VARCHAR}, #{employeeCode,jdbcType=VARCHAR}, #{expectedCompleteDay,jdbcType=VARCHAR},
      #{roleId,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventSceneDepartment" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_scene_department
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="monitorScene != null">
        monitor_scene,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="departmentName != null">
        department_name,
      </if>
      <if test="organizationCode != null">
        organization_code,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="employeeCode != null">
        employee_code,
      </if>
      <if test="expectedCompleteDay != null">
        expected_complete_day,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="monitorScene != null">
        #{monitorScene,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="organizationCode != null">
        #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="employeeCode != null">
        #{employeeCode,jdbcType=VARCHAR},
      </if>
      <if test="expectedCompleteDay != null">
        #{expectedCompleteDay,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningEventSceneDepartment">
    <!--@mbg.generated-->
    update t_warning_event_scene_department
    <set>
      <if test="monitorScene != null">
        monitor_scene = #{monitorScene,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="departmentName != null">
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="organizationCode != null">
        organization_code = #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="employeeCode != null">
        employee_code = #{employeeCode,jdbcType=VARCHAR},
      </if>
      <if test="expectedCompleteDay != null">
        expected_complete_day = #{expectedCompleteDay,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningEventSceneDepartment">
    <!--@mbg.generated-->
    update t_warning_event_scene_department
    set monitor_scene = #{monitorScene,jdbcType=VARCHAR},
      department_id = #{departmentId,jdbcType=BIGINT},
      department_name = #{departmentName,jdbcType=VARCHAR},
      organization_code = #{organizationCode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      user_name = #{userName,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=VARCHAR},
      employee_code = #{employeeCode,jdbcType=VARCHAR},
      expected_complete_day = #{expectedCompleteDay,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_event_scene_department
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="monitor_scene = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorScene,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.departmentId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="department_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.departmentName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="organization_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.organizationCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="account_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.accountId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="employee_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.employeeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="expected_complete_day = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.expectedCompleteDay,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="role_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.roleId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_scene_department
    (monitor_scene, department_id, department_name, organization_code, user_id, user_name,
      account_id, employee_code, expected_complete_day, role_id, create_by, create_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.monitorScene,jdbcType=VARCHAR}, #{item.departmentId,jdbcType=BIGINT}, #{item.departmentName,jdbcType=VARCHAR},
        #{item.organizationCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT}, #{item.userName,jdbcType=VARCHAR},
        #{item.accountId,jdbcType=VARCHAR}, #{item.employeeCode,jdbcType=VARCHAR}, #{item.expectedCompleteDay,jdbcType=VARCHAR},
        #{item.roleId,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

  <select id="listAll" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_scene_department
  </select>

  <select id="listByDeptId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_scene_department
    where department_id = #{deptId}
  </select>
</mapper>
