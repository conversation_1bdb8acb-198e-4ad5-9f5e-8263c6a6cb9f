package com.ruoyi.jiangshan.vo.cityrisk;

import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.vo.WarningEventProcessLogVO;
import lombok.Data;

import java.util.List;

@Data
public class CityRiskSafetyEventVO {

    private Long eventId;

    /**
     * 影响场景
     */
    private List<String> sceneList;

    /**
     * 预计影响范围（橙色范围）
     */
    private String expectedImpactRange;

    /**
     * 预计影响范围单位（橙色范围）
     */
    private String expectedImpactRangeUnit;

    /**
     * 预计影响范围（蓝色范围）
     */
    private String expectedImpactRangeBlue;

    /**
     * 预计影响范围单位（蓝色范围）
     */
    private String expectedImpactRangeUnitBlue;

    /**
     * 影响设备
     */
    private Integer deviceCount;

    /**
     * 影响设备实时数据
     */
    private List<DeviceInfo> deviceInfoList;

    /**
     * 流程日志
     */
    private List<WarningEventProcessLogVO> logList;

    /**
     * 处置人员联系方式
     */
    private String personContact;
}
