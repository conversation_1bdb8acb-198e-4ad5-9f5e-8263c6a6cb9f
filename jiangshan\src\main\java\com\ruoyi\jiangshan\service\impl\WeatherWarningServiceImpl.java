package com.ruoyi.jiangshan.service.impl;

import com.ruoyi.jiangshan.domain.WeatherWarning;
import com.ruoyi.jiangshan.mapper.WeatherWarningMapper;
import com.ruoyi.jiangshan.service.WeatherWarningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 气象预警+专业监测预警Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class WeatherWarningServiceImpl implements WeatherWarningService
{
    @Autowired
    private WeatherWarningMapper weatherWarningMapper;

    /**
     * 查询气象预警+专业监测预警
     *
     * @param id 气象预警+专业监测预警主键
     * @return 气象预警+专业监测预警
     */
    @Override
    public WeatherWarning selectWeatherWarningById(Long id)
    {
        return weatherWarningMapper.selectWeatherWarningById(id);
    }

    /**
     * 查询气象预警+专业监测预警列表
     *
     * @param weatherWarning 气象预警+专业监测预警
     * @return 气象预警+专业监测预警
     */
    @Override
    public List<WeatherWarning> selectWeatherWarningList(WeatherWarning weatherWarning)
    {
        return weatherWarningMapper.selectWeatherWarningList(weatherWarning);
    }

    /**
     * 新增气象预警+专业监测预警
     *
     * @param weatherWarning 气象预警+专业监测预警
     * @return 结果
     */
    @Override
    public int insertWeatherWarning(WeatherWarning weatherWarning)
    {
        return weatherWarningMapper.insertWeatherWarning(weatherWarning);
    }

    /**
     * 修改气象预警+专业监测预警
     *
     * @param weatherWarning 气象预警+专业监测预警
     * @return 结果
     */
    @Override
    public int updateWeatherWarning(WeatherWarning weatherWarning)
    {
        return weatherWarningMapper.updateWeatherWarning(weatherWarning);
    }

    /**
     * 批量删除气象预警+专业监测预警
     *
     * @param ids 需要删除的气象预警+专业监测预警主键
     * @return 结果
     */
    @Override
    public int deleteWeatherWarningByIds(Long[] ids)
    {
        return weatherWarningMapper.deleteWeatherWarningByIds(ids);
    }

    /**
     * 删除气象预警+专业监测预警信息
     *
     * @param id 气象预警+专业监测预警主键
     * @return 结果
     */
    @Override
    public int deleteWeatherWarningById(Long id)
    {
        return weatherWarningMapper.deleteWeatherWarningById(id);
    }
}
