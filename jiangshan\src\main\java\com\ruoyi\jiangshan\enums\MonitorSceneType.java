package com.ruoyi.jiangshan.enums;


import com.google.common.collect.Lists;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.jiangshan.domain.WarningEventSceneDepartment;
import com.ruoyi.jiangshan.mapper.WarningEventSceneDepartmentMapper;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum MonitorSceneType {
    SCENE_01(1, "水利场景", "SL", Lists.newArrayList("水库", "山塘"), "江山市水利局"),
    SCENE_02(2, "桥梁场景", "QL", Lists.newArrayList("桥梁"), "江山市住建局"),
    SCENE_03(3, "地质灾害场景", "DZZH", Lists.newArrayList("风险防范区"), "江山市自然资源和规划局"),
    SCENE_04(4, "内涝场景", "NL", Lists.newArrayList("河道"), "江山市住建局"),
    SCENE_05(5, "污水管线场景", "WSGX", Lists.newArrayList("污水管", "雨水管"), "江山市住建局"),
    SCENE_06(6, "燃气场景", "RQ", Lists.newArrayList("燃气阀井", "个体工商户"), "江山市江城管道燃气有限公司"),
    SCENE_07(7, "消防场景", "XF", Lists.newArrayList("消防登高面", "消防栓"), "江山市消防救援局"),
    ;

    private Integer code;
    private String desc;
    private String abbr;
    private List<String> typeList;
    private String adviceDepartment;

    MonitorSceneType(Integer code, String desc, String abbr, List<String> typeList, String adviceDepartment) {
        this.code = code;
        this.desc = desc;
        this.abbr = abbr;
        this.typeList = typeList;
        this.adviceDepartment = adviceDepartment;
    }

    public static String getAdviceDepartmentByScene(String scene) {
        for (MonitorSceneType value : values()) {
            if (value.getDesc().equals(scene)) {
                return value.getAdviceDepartment();
            }
        }

        return null;
    }

    public static List<String> getDeviceTypeByScene(String scene) {
        for (MonitorSceneType value : values()) {
            if (value.getDesc().equals(scene)) {
                return value.getTypeList();
            }
        }

        return Lists.newArrayList();
    }

    public static boolean checkExist(String eventType) {
        for (MonitorSceneType value : values()) {
            if (value.getDesc().equals(eventType)) {
                return true;
            }
        }

        return false;
    }

    public static List<String> getScenePermissionByUser(SysUser sysUser,
                                                        WarningEventSceneDepartmentMapper warningEventSceneDepartmentMapper) {
        if (Objects.isNull(sysUser)) {
            return listAllScene();
        }
        List<SysRole> roleList = sysUser.getRoles();
        if (CollectionUtils.isEmpty(roleList)) {
            return listAllScene();
        }
        for (SysRole sysRole : roleList) {
            if ("1".equals(sysRole.getDataScope())) {
                return listAllScene();
            }
        }
        List<WarningEventSceneDepartment> sceneDepartmentList =
                warningEventSceneDepartmentMapper.listByDeptId(sysUser.getDeptId());
        if (CollectionUtils.isEmpty(sceneDepartmentList)) {
            return listAllScene();
        }
        List<String> sceneList = sceneDepartmentList.stream()
                .map(WarningEventSceneDepartment::getMonitorScene)
                .collect(Collectors.toList());

        return sceneList;
    }

    public static String getAbbrByScene(String deviceScene) {
        for (MonitorSceneType value : values()) {
            if (value.getDesc().equals(deviceScene)) {
                return value.getAbbr();
            }
        }

        return null;
    }

    public String getAdviceDepartment() {
        return adviceDepartment;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public List<String> getTypeList() {
        return typeList;
    }

    public String getAbbr() {
        return abbr;
    }

    public static MonitorSceneType getByDesc(String desc) {
        for (MonitorSceneType value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }

        return null;
    }

    public static List<String> listAllScene() {
        List<String> resultList = Lists.newArrayList();

        for (MonitorSceneType value : values()) {
            resultList.add(value.getDesc());
        }

        return resultList;
    }
}
