package com.ruoyi.jiangshan.vo;

import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.DeviceValue;
import lombok.Data;

import java.util.List;

@Data
public class WaterLayerVO {

    /**
     * id
     */
    private Long id;

    /**
     * 监测对象名称
     */
    private String monitorName;

    /**
     * 监测对象类型
     */
    private String monitorType;

    /**
     * 投入式水位计设备
     */
    private DeviceInfo deviceInfo;

    /**
     * 视频监控list
     */
    private List<DeviceInfo> videoList;

    /**
     * 设备数据
     */
    private List<DeviceValue> deviceValueList;

    /**
     * 阈值
     */
    private String threshold;

    /**
     * 蓄水率
     */
    private String waterStorageRate;

    /**
     * 预警等级
     */
    private Integer warningLevel;

    /**
     * 预警详情
     */
    private String warningDetail;

    /**
     * 空间范围
     */
    private String geometry;

    private String lon;

    private String lat;

    private String newType;
}
