package com.ruoyi.jiangshan.convert;

import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.openapi.vo.DeviceValueOpenApiVO;
import com.ruoyi.jiangshan.vo.DeviceInfoImportVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DeviceConvert {
    DeviceConvert INSTANCE = Mappers.getMapper(DeviceConvert.class);

    DeviceValue openApi2Do(DeviceValueOpenApiVO apiVO);

    DeviceInfo excelVo2Do(DeviceInfoImportVO deviceInfoImportVO);

}
