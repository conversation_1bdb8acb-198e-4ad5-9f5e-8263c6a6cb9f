package com.ruoyi.jiangshan.controller.screen;

import com.google.common.collect.Lists;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.service.CityRiskSceneService;
import com.ruoyi.jiangshan.service.ComprehensiveSceneService;
import com.ruoyi.jiangshan.vo.WarningEventPageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 城市风险综合研判中心
 */
@RestController
@RequestMapping("/cityRisk")
public class CityRiskSceneController {
    @Autowired
    private ComprehensiveSceneService comprehensiveSceneService;
    @Autowired
    private CityRiskSceneService cityRiskSceneService;

    /**
     * 风险监控
     * @return
     */
    @GetMapping("/riskMonitor")
    public AjaxResult getRiskMonitor() {
        return AjaxResult.success(comprehensiveSceneService.getRiskMonitor());
    }

    /**
     * 预警模型
     * @return
     */
    @GetMapping("/warningModel")
    public AjaxResult getWarningModel() {
        return AjaxResult.success(cityRiskSceneService.getWarningModel());
    }

    /**
     * 预警分析
     * @return
     */
    @GetMapping("/warningAnalysis")
    public AjaxResult getWarningAnalysis() {
        return AjaxResult.success(cityRiskSceneService.getWarningAnalysis());
    }

    /**
     * 风险列表
     * @return
     */
    @GetMapping("/riskList")
    public AjaxResult getRiskList() {
        return AjaxResult.success(comprehensiveSceneService.getRiskList());
    }

    /**
     * 预警处置概览
     * @return
     */
    @GetMapping("/warningDispose")
    public AjaxResult getWarningDispose() {
        return AjaxResult.success(comprehensiveSceneService.getWarningDispose());
    }

    /**
     * 联动处置详情
     * @return
     */
    @GetMapping("/linkDispose")
    public AjaxResult getLinkDispose() {
        return AjaxResult.success(comprehensiveSceneService.getLinkDispose());
    }

    /**
     * 联动处置详情V2
     * @return
     */
    @PostMapping("/linkDisposeV2")
    public AjaxResult getLinkDisposeV2(@RequestBody WarningEventPageVO pageVO) {
        return AjaxResult.success(comprehensiveSceneService.getLinkDisposeV2(pageVO));
    }

    /**
     * 风险top5
     * @return
     */
    @GetMapping("/warningTop5")
    public AjaxResult getWarningTop5() {
        return AjaxResult.success(cityRiskSceneService.getWarningTop5());
    }

    /**
     * 街道/社区四色图
     * @return
     */
    @GetMapping("/street")
    public AjaxResult getStreet(String areaCode) {
        return AjaxResult.success(cityRiskSceneService.getStreet(areaCode));
    }

    /**
     * 预警事件
     * @return
     */
    @GetMapping("/event")
    public AjaxResult getEvent() {
        return AjaxResult.success(cityRiskSceneService.getEvent());
    }

    /**
     * 水利局四色图
     * @return
     */
    @GetMapping("/waterDepartment")
    public AjaxResult getWaterDepartment() {
        return AjaxResult.success(cityRiskSceneService
                .getDepartmentDevice(Lists.newArrayList(MonitorSceneType.SCENE_01.getDesc())));
    }

    /**
     * 住建局四色图
     * @return
     */
    @GetMapping("/buildDepartment")
    public AjaxResult getFireDepartment() {
        return AjaxResult.success(cityRiskSceneService.getDepartmentDevice(
                Lists.newArrayList(MonitorSceneType.SCENE_02.getDesc())));
    }

    /**
     * 燃气大队四色图
     * @return
     */
    @GetMapping("/fireDepartment")
    public AjaxResult getBuildDepartment() {
        return AjaxResult.success(cityRiskSceneService.getDepartmentDevice(
                Lists.newArrayList(MonitorSceneType.SCENE_07.getDesc())));
    }

    /**
     * 资规局四色图
     * @return
     */
    @GetMapping("/manageDepartment")
    public AjaxResult getManageDepartment() {
        return AjaxResult.success(cityRiskSceneService.getDepartmentDevice(
                Lists.newArrayList(MonitorSceneType.SCENE_03.getDesc(), MonitorSceneType.SCENE_04.getDesc())));
    }

    /**
     * 燃气公司四色图
     * @return
     */
    @GetMapping("/gasDepartment")
    public AjaxResult getGasDepartment() {
        return AjaxResult.success(cityRiskSceneService.getDepartmentDevice(
                Lists.newArrayList(MonitorSceneType.SCENE_06.getDesc())));
    }

    /**
     * 关注提醒
     * @return
     */
    @GetMapping("/notify")
    public AjaxResult notifyMsg(String accountId) {
        cityRiskSceneService.notifyMsg(accountId);
        return AjaxResult.success();
    }

    /**
     * 专项安全监测诊断图
     * @return
     */
    @GetMapping("/safety")
    public AjaxResult getSafety() {
        return AjaxResult.success(cityRiskSceneService.getSafety());
    }

    /**
     * 专项安全监测诊断图折线图
     */
    @GetMapping("/safetyLine")
    public AjaxResult getSafety(String deviceThirdId, String monitorItem) {
        return AjaxResult.success(cityRiskSceneService.getSafetyLine(deviceThirdId, monitorItem));
    }
}
