package com.ruoyi.jiangshan.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 监测对象-山塘对象 t_device_monitor_shantang
 * 
 * <AUTHOR>
 * @date 2025-03-26
 */
public class DeviceMonitorShantang extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String stmc;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String stlm;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String stbm;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String szs;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String szxsq;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String szxzc;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String zygn;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String jd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String wd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sfjxstzz;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String zzwcsj;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sjdw;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String jymj;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String zlcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String jhhsw;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String jhhswdyrj;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String jhhsp;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sjhsw;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sjhsw1;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sjhs;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String zcxsc;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String zcxsw;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String yxqy;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String yxqynczgs;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String yxrk;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String rgsnl;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String ggmj;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bz;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String dsjzwbx;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String dsjzwbdgc;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String dsjzwbsbjgc;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String dsjzwzdbg;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String dsjzwysppb;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String dsjzwbsppb;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String dsjzwbdgd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String dsjzwbdgd1;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String xhjzwxs;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String xhjzwyhgd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String ssjzwxs;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String ssjzwcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String ssjzwdmcc;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sfyjsjg;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sfsl;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String glf;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String glfmj;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sfwcbzhgl;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String glfwz;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String gcss;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String aqpdjl;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sfsmlst;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setStmc(String stmc) 
    {
        this.stmc = stmc;
    }

    public String getStmc() 
    {
        return stmc;
    }
    public void setStlm(String stlm) 
    {
        this.stlm = stlm;
    }

    public String getStlm() 
    {
        return stlm;
    }
    public void setStbm(String stbm) 
    {
        this.stbm = stbm;
    }

    public String getStbm() 
    {
        return stbm;
    }
    public void setSzs(String szs) 
    {
        this.szs = szs;
    }

    public String getSzs() 
    {
        return szs;
    }
    public void setSzxsq(String szxsq) 
    {
        this.szxsq = szxsq;
    }

    public String getSzxsq() 
    {
        return szxsq;
    }
    public void setSzxzc(String szxzc) 
    {
        this.szxzc = szxzc;
    }

    public String getSzxzc() 
    {
        return szxzc;
    }
    public void setZygn(String zygn) 
    {
        this.zygn = zygn;
    }

    public String getZygn() 
    {
        return zygn;
    }
    public void setJd(String jd) 
    {
        this.jd = jd;
    }

    public String getJd() 
    {
        return jd;
    }
    public void setWd(String wd) 
    {
        this.wd = wd;
    }

    public String getWd() 
    {
        return wd;
    }
    public void setSfjxstzz(String sfjxstzz) 
    {
        this.sfjxstzz = sfjxstzz;
    }

    public String getSfjxstzz() 
    {
        return sfjxstzz;
    }
    public void setZzwcsj(String zzwcsj) 
    {
        this.zzwcsj = zzwcsj;
    }

    public String getZzwcsj() 
    {
        return zzwcsj;
    }
    public void setSjdw(String sjdw) 
    {
        this.sjdw = sjdw;
    }

    public String getSjdw() 
    {
        return sjdw;
    }
    public void setJymj(String jymj) 
    {
        this.jymj = jymj;
    }

    public String getJymj() 
    {
        return jymj;
    }
    public void setZlcd(String zlcd) 
    {
        this.zlcd = zlcd;
    }

    public String getZlcd() 
    {
        return zlcd;
    }
    public void setJhhsw(String jhhsw) 
    {
        this.jhhsw = jhhsw;
    }

    public String getJhhsw() 
    {
        return jhhsw;
    }
    public void setJhhswdyrj(String jhhswdyrj) 
    {
        this.jhhswdyrj = jhhswdyrj;
    }

    public String getJhhswdyrj() 
    {
        return jhhswdyrj;
    }
    public void setJhhsp(String jhhsp) 
    {
        this.jhhsp = jhhsp;
    }

    public String getJhhsp() 
    {
        return jhhsp;
    }
    public void setSjhsw(String sjhsw) 
    {
        this.sjhsw = sjhsw;
    }

    public String getSjhsw() 
    {
        return sjhsw;
    }
    public void setSjhsw1(String sjhsw1) 
    {
        this.sjhsw1 = sjhsw1;
    }

    public String getSjhsw1() 
    {
        return sjhsw1;
    }
    public void setSjhs(String sjhs) 
    {
        this.sjhs = sjhs;
    }

    public String getSjhs() 
    {
        return sjhs;
    }
    public void setZcxsc(String zcxsc) 
    {
        this.zcxsc = zcxsc;
    }

    public String getZcxsc() 
    {
        return zcxsc;
    }
    public void setZcxsw(String zcxsw) 
    {
        this.zcxsw = zcxsw;
    }

    public String getZcxsw() 
    {
        return zcxsw;
    }
    public void setYxqy(String yxqy) 
    {
        this.yxqy = yxqy;
    }

    public String getYxqy() 
    {
        return yxqy;
    }
    public void setYxqynczgs(String yxqynczgs) 
    {
        this.yxqynczgs = yxqynczgs;
    }

    public String getYxqynczgs() 
    {
        return yxqynczgs;
    }
    public void setYxrk(String yxrk) 
    {
        this.yxrk = yxrk;
    }

    public String getYxrk() 
    {
        return yxrk;
    }
    public void setRgsnl(String rgsnl) 
    {
        this.rgsnl = rgsnl;
    }

    public String getRgsnl() 
    {
        return rgsnl;
    }
    public void setGgmj(String ggmj) 
    {
        this.ggmj = ggmj;
    }

    public String getGgmj() 
    {
        return ggmj;
    }
    public void setBz(String bz) 
    {
        this.bz = bz;
    }

    public String getBz() 
    {
        return bz;
    }
    public void setDsjzwbx(String dsjzwbx) 
    {
        this.dsjzwbx = dsjzwbx;
    }

    public String getDsjzwbx() 
    {
        return dsjzwbx;
    }
    public void setDsjzwbdgc(String dsjzwbdgc) 
    {
        this.dsjzwbdgc = dsjzwbdgc;
    }

    public String getDsjzwbdgc() 
    {
        return dsjzwbdgc;
    }
    public void setDsjzwbsbjgc(String dsjzwbsbjgc) 
    {
        this.dsjzwbsbjgc = dsjzwbsbjgc;
    }

    public String getDsjzwbsbjgc() 
    {
        return dsjzwbsbjgc;
    }
    public void setDsjzwzdbg(String dsjzwzdbg) 
    {
        this.dsjzwzdbg = dsjzwzdbg;
    }

    public String getDsjzwzdbg() 
    {
        return dsjzwzdbg;
    }
    public void setDsjzwysppb(String dsjzwysppb) 
    {
        this.dsjzwysppb = dsjzwysppb;
    }

    public String getDsjzwysppb() 
    {
        return dsjzwysppb;
    }
    public void setDsjzwbsppb(String dsjzwbsppb) 
    {
        this.dsjzwbsppb = dsjzwbsppb;
    }

    public String getDsjzwbsppb() 
    {
        return dsjzwbsppb;
    }
    public void setDsjzwbdgd(String dsjzwbdgd) 
    {
        this.dsjzwbdgd = dsjzwbdgd;
    }

    public String getDsjzwbdgd() 
    {
        return dsjzwbdgd;
    }
    public void setDsjzwbdgd1(String dsjzwbdgd1) 
    {
        this.dsjzwbdgd1 = dsjzwbdgd1;
    }

    public String getDsjzwbdgd1() 
    {
        return dsjzwbdgd1;
    }
    public void setXhjzwxs(String xhjzwxs) 
    {
        this.xhjzwxs = xhjzwxs;
    }

    public String getXhjzwxs() 
    {
        return xhjzwxs;
    }
    public void setXhjzwyhgd(String xhjzwyhgd) 
    {
        this.xhjzwyhgd = xhjzwyhgd;
    }

    public String getXhjzwyhgd() 
    {
        return xhjzwyhgd;
    }
    public void setSsjzwxs(String ssjzwxs) 
    {
        this.ssjzwxs = ssjzwxs;
    }

    public String getSsjzwxs() 
    {
        return ssjzwxs;
    }
    public void setSsjzwcd(String ssjzwcd) 
    {
        this.ssjzwcd = ssjzwcd;
    }

    public String getSsjzwcd() 
    {
        return ssjzwcd;
    }
    public void setSsjzwdmcc(String ssjzwdmcc) 
    {
        this.ssjzwdmcc = ssjzwdmcc;
    }

    public String getSsjzwdmcc() 
    {
        return ssjzwdmcc;
    }
    public void setSfyjsjg(String sfyjsjg) 
    {
        this.sfyjsjg = sfyjsjg;
    }

    public String getSfyjsjg() 
    {
        return sfyjsjg;
    }
    public void setSfsl(String sfsl) 
    {
        this.sfsl = sfsl;
    }

    public String getSfsl() 
    {
        return sfsl;
    }
    public void setGlf(String glf) 
    {
        this.glf = glf;
    }

    public String getGlf() 
    {
        return glf;
    }
    public void setGlfmj(String glfmj) 
    {
        this.glfmj = glfmj;
    }

    public String getGlfmj() 
    {
        return glfmj;
    }
    public void setSfwcbzhgl(String sfwcbzhgl) 
    {
        this.sfwcbzhgl = sfwcbzhgl;
    }

    public String getSfwcbzhgl() 
    {
        return sfwcbzhgl;
    }
    public void setGlfwz(String glfwz) 
    {
        this.glfwz = glfwz;
    }

    public String getGlfwz() 
    {
        return glfwz;
    }
    public void setGcss(String gcss) 
    {
        this.gcss = gcss;
    }

    public String getGcss() 
    {
        return gcss;
    }
    public void setAqpdjl(String aqpdjl) 
    {
        this.aqpdjl = aqpdjl;
    }

    public String getAqpdjl() 
    {
        return aqpdjl;
    }
    public void setSfsmlst(String sfsmlst) 
    {
        this.sfsmlst = sfsmlst;
    }

    public String getSfsmlst() 
    {
        return sfsmlst;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("stmc", getStmc())
            .append("stlm", getStlm())
            .append("stbm", getStbm())
            .append("szs", getSzs())
            .append("szxsq", getSzxsq())
            .append("szxzc", getSzxzc())
            .append("zygn", getZygn())
            .append("jd", getJd())
            .append("wd", getWd())
            .append("sfjxstzz", getSfjxstzz())
            .append("zzwcsj", getZzwcsj())
            .append("sjdw", getSjdw())
            .append("jymj", getJymj())
            .append("zlcd", getZlcd())
            .append("jhhsw", getJhhsw())
            .append("jhhswdyrj", getJhhswdyrj())
            .append("jhhsp", getJhhsp())
            .append("sjhsw", getSjhsw())
            .append("sjhsw1", getSjhsw1())
            .append("sjhs", getSjhs())
            .append("zcxsc", getZcxsc())
            .append("zcxsw", getZcxsw())
            .append("yxqy", getYxqy())
            .append("yxqynczgs", getYxqynczgs())
            .append("yxrk", getYxrk())
            .append("rgsnl", getRgsnl())
            .append("ggmj", getGgmj())
            .append("bz", getBz())
            .append("dsjzwbx", getDsjzwbx())
            .append("dsjzwbdgc", getDsjzwbdgc())
            .append("dsjzwbsbjgc", getDsjzwbsbjgc())
            .append("dsjzwzdbg", getDsjzwzdbg())
            .append("dsjzwysppb", getDsjzwysppb())
            .append("dsjzwbsppb", getDsjzwbsppb())
            .append("dsjzwbdgd", getDsjzwbdgd())
            .append("dsjzwbdgd1", getDsjzwbdgd1())
            .append("xhjzwxs", getXhjzwxs())
            .append("xhjzwyhgd", getXhjzwyhgd())
            .append("ssjzwxs", getSsjzwxs())
            .append("ssjzwcd", getSsjzwcd())
            .append("ssjzwdmcc", getSsjzwdmcc())
            .append("sfyjsjg", getSfyjsjg())
            .append("sfsl", getSfsl())
            .append("glf", getGlf())
            .append("glfmj", getGlfmj())
            .append("sfwcbzhgl", getSfwcbzhgl())
            .append("glfwz", getGlfwz())
            .append("gcss", getGcss())
            .append("aqpdjl", getAqpdjl())
            .append("sfsmlst", getSfsmlst())
            .toString();
    }
}
