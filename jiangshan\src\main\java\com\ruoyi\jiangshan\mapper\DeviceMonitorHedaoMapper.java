package com.ruoyi.jiangshan.mapper;

import java.util.List;
import com.ruoyi.jiangshan.domain.DeviceMonitorHedao;

/**
 * 监测对象-河道Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface DeviceMonitorHedaoMapper
{
    /**
     * 查询监测对象-河道
     *
     * @param jcmc 监测对象-河道主键
     * @return 监测对象-河道
     */
    public DeviceMonitorHedao selectDeviceMonitorHedaoByJcmc(String jcmc);

    /**
     * 查询监测对象-河道列表
     *
     * @param deviceMonitorHedao 监测对象-河道
     * @return 监测对象-河道集合
     */
    public List<DeviceMonitorHedao> selectDeviceMonitorHedaoList(DeviceMonitorHedao deviceMonitorHedao);

    /**
     * 新增监测对象-河道
     *
     * @param deviceMonitorHedao 监测对象-河道
     * @return 结果
     */
    public int insertDeviceMonitorHedao(DeviceMonitorHedao deviceMonitorHedao);

    /**
     * 修改监测对象-河道
     *
     * @param deviceMonitorHedao 监测对象-河道
     * @return 结果
     */
    public int updateDeviceMonitorHedao(DeviceMonitorHedao deviceMonitorHedao);

    /**
     * 删除监测对象-河道
     *
     * @param jcmc 监测对象-河道主键
     * @return 结果
     */
    public int deleteDeviceMonitorHedaoByJcmc(String jcmc);

    /**
     * 批量删除监测对象-河道
     *
     * @param jcmcs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceMonitorHedaoByJcmcs(String[] jcmcs);

    List<DeviceMonitorHedao> listAll();

    DeviceMonitorHedao getByMonitorName(String monitorName);
}
