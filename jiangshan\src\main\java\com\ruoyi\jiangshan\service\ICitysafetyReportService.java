package com.ruoyi.jiangshan.service;

import java.util.Calendar;
import java.util.List;

import com.ruoyi.jiangshan.domain.CitysafetyPeriods;
import com.ruoyi.jiangshan.domain.CitysafetyReport;
import com.ruoyi.jiangshan.domain.CitysafetyRule;
import com.ruoyi.jiangshan.domain.DeviceStreet;
import com.ruoyi.jiangshan.mapper.CitysafetyPeriodsMapper;
import com.ruoyi.jiangshan.vo.WarningReportVO;
import com.ruoyi.jiangshan.vo.street.DeviceStreetVO;

import javax.servlet.http.HttpServletResponse;

/**
 * 城市安全体检报告管理Service接口
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
public interface ICitysafetyReportService
{
    /**
     * 查询城市安全体检报告管理
     *
     * @param id 城市安全体检报告管理主键
     * @return 城市安全体检报告管理
     */
    public CitysafetyReport selectCitysafetyReportById(Long id);

    /**
     * 查询城市安全体检报告管理列表
     *
     * @param citysafetyReport 城市安全体检报告管理
     * @return 城市安全体检报告管理集合
     */
    public List<CitysafetyReport> selectCitysafetyReportList(CitysafetyReport citysafetyReport);

    /**
     * 新增城市安全体检报告管理
     *
     * @param citysafetyReport 城市安全体检报告管理
     * @return 结果
     */
    public int insertCitysafetyReport(CitysafetyReport citysafetyReport);

    /**
     * 修改城市安全体检报告管理
     *
     * @param citysafetyReport 城市安全体检报告管理
     * @return 结果
     */
    public int updateCitysafetyReport(CitysafetyReport citysafetyReport);

    /**
     * 批量删除城市安全体检报告管理
     *
     * @param ids 需要删除的城市安全体检报告管理主键集合
     * @return 结果
     */
    public int deleteCitysafetyReportByIds(Long[] ids);

    /**
     * 删除城市安全体检报告管理信息
     *
     * @param id 城市安全体检报告管理主键
     * @return 结果
     */
    public int deleteCitysafetyReportById(Long id);

    List<WarningReportVO> getInfo(Long id, CitysafetyReport existReport);

    int generateReport(CitysafetyReport citysafetyReport);

    CitysafetyPeriods getCitysafetyPeriods(Calendar calendar, Integer generateType, String scene);

    List<WarningReportVO> previewReport(CitysafetyReport citysafetyReport);

    void createPdf(Long id, HttpServletResponse response);

    void createBatchPdf(Long[] idList, HttpServletResponse response);

    List<DeviceStreetVO> getStreet();
}
