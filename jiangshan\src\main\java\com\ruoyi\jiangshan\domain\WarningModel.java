package com.ruoyi.jiangshan.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 设备模型对象 t_warning_model
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Data
public class WarningModel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 模型名称 */
    @Excel(name = "模型名称")
    private String modelName;

    /** 适用场景 */
    @Excel(name = "适用场景")
    private String modelScene;

    /** 状态 0停用 1启用 */
    @Excel(name = "状态 0停用 1启用")
    private Integer status;

}
