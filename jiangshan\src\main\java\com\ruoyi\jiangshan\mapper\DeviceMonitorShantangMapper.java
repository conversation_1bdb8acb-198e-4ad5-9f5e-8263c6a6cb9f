package com.ruoyi.jiangshan.mapper;

import java.util.List;
import com.ruoyi.jiangshan.domain.DeviceMonitorShantang;

/**
 * 监测对象-山塘Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface DeviceMonitorShantangMapper
{
    /**
     * 查询监测对象-山塘
     *
     * @param id 监测对象-山塘主键
     * @return 监测对象-山塘
     */
    public DeviceMonitorShantang selectDeviceMonitorShantangById(String id);

    /**
     * 查询监测对象-山塘列表
     *
     * @param deviceMonitorShantang 监测对象-山塘
     * @return 监测对象-山塘集合
     */
    public List<DeviceMonitorShantang> selectDeviceMonitorShantangList(DeviceMonitorShantang deviceMonitorShantang);

    /**
     * 新增监测对象-山塘
     *
     * @param deviceMonitorShantang 监测对象-山塘
     * @return 结果
     */
    public int insertDeviceMonitorShantang(DeviceMonitorShantang deviceMonitorShantang);

    /**
     * 修改监测对象-山塘
     *
     * @param deviceMonitorShantang 监测对象-山塘
     * @return 结果
     */
    public int updateDeviceMonitorShantang(DeviceMonitorShantang deviceMonitorShantang);

    /**
     * 删除监测对象-山塘
     *
     * @param id 监测对象-山塘主键
     * @return 结果
     */
    public int deleteDeviceMonitorShantangById(String id);

    /**
     * 批量删除监测对象-山塘
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceMonitorShantangByIds(String[] ids);

    List<DeviceMonitorShantang> listAll();

    DeviceMonitorShantang getByMonitorName(String monitorName);
}
