package com.ruoyi.jiangshan.controller.screen;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.service.WaterSceneService;
import com.ruoyi.jiangshan.service.WaterloggingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 内涝场景驾驶舱
 */
@RestController
@RequestMapping("/waterlogging")
public class WaterloggingSceneController {

    @Autowired
    private WaterloggingService waterloggingService;
    @Autowired
    private WaterSceneService waterSceneService;

    /**
     * 天气预报
     * @return
     */
    @GetMapping("/weather")
    public AjaxResult getWeather() {
        return AjaxResult.success(waterSceneService.getWeather());
    }

    /**
     * 安全风险统计
     * @return
     */
    @GetMapping("/safetyCount")
    public AjaxResult getSafetyCount(String dateStr) {
        return AjaxResult.success(waterloggingService.getSafetyCount(dateStr));
    }

    /**
     * 易捞点统计
     * @return
     */
    @GetMapping("/floodCount")
    public AjaxResult getFloodCount() {
        return AjaxResult.success(waterloggingService.getFloodCount());
    }

    /**
     * 实时预警
     * @return
     */
    @GetMapping("/warning")
    public AjaxResult getWarning(Integer warningLevel) {
        return AjaxResult.success(waterloggingService.getWarning(warningLevel));
    }

    /**
     * 降雨趋势统计
     * @return
     */
    @GetMapping("/rainfallCount")
    public AjaxResult getRainfallCount(Long monitorId) {
        return AjaxResult.success(waterloggingService.getRainfallCount(monitorId));
    }

    /**
     * 设备在线情况监控
     * @return
     */
    @GetMapping("/status")
    public AjaxResult getOnlineStatus() {
        return AjaxResult.success(waterloggingService.getOnlineStatus());
    }

}
