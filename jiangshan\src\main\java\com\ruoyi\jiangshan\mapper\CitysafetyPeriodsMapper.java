package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.CitysafetyPeriods;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CitysafetyPeriodsMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CitysafetyPeriods record);

    int insertSelective(CitysafetyPeriods record);

    CitysafetyPeriods selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CitysafetyPeriods record);

    int updateByPrimaryKey(CitysafetyPeriods record);

    int updateBatch(List<CitysafetyPeriods> list);

    int batchInsert(@Param("list") List<CitysafetyPeriods> list);

    CitysafetyPeriods getByYear(int year);
}
