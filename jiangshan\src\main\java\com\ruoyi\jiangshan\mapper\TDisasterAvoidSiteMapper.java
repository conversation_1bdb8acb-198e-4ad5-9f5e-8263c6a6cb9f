package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.TDisasterAvoidSite;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TDisasterAvoidSiteMapper {
    int deleteByPrimaryKey(String id);

    int insert(TDisasterAvoidSite record);

    int insertSelective(TDisasterAvoidSite record);

    TDisasterAvoidSite selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TDisasterAvoidSite record);

    int updateByPrimaryKey(TDisasterAvoidSite record);

    int updateBatch(List<TDisasterAvoidSite> list);

    int batchInsert(@Param("list") List<TDisasterAvoidSite> list);

    List<TDisasterAvoidSite> listAllDisasterAvoid();

}
