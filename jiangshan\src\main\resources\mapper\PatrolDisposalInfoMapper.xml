<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.PatrolDisposalInfoMapper">

    <resultMap type="PatrolDisposalInfo" id="PatrolDisposalInfoResult">
        <result property="id"    column="id"    />
        <result property="disposalId"    column="disposal_id"    />
        <result property="riskZoneNum"    column="risk_zone_num"    />
        <result property="riskZoneName"    column="risk_zone_name"    />
        <result property="areaCode"    column="area_code"    />
        <result property="warningLevel"    column="warning_level"    />
        <result property="warningType"    column="warning_type"    />
        <result property="readStatus"    column="read_status"    />
        <result property="deadStatus"    column="dead_status"    />
        <result property="createTimeXc"    column="create_time_xc"    />
        <result property="userId"    column="user_id"    />
        <result property="isPositive"    column="is_positive"    />
        <result property="isSlid"    column="is_slid"    />
        <result property="type"    column="type"    />
        <result property="content"    column="content"    />
        <result property="dealPeople"    column="deal_people"    />
        <result property="dealTime"    column="deal_time"    />
        <result property="monitorPointCode"    column="monitor_point_code"    />
        <result property="status"    column="status"    />
        <result property="createTimeCz"    column="create_time_cz"    />
        <result property="updateTimeCz"    column="update_time_cz"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="isCallNotify"    column="is_call_notify"    />
        <result property="isTriggerBroadcast"    column="is_trigger_broadcast"    />
        <result property="monitorPointName"    column="monitor_point_name"    />
        <result property="syncTime"    column="sync_time"    />
    </resultMap>

    <sql id="selectPatrolDisposalInfoVo">
        select id, disposal_id, risk_zone_num, risk_zone_name, area_code, warning_level, warning_type, read_status, dead_status, create_time_xc, user_id, is_positive, is_slid, type, content, deal_people, deal_time, monitor_point_code, status, create_time_cz, update_time_cz, create_by, update_by, is_deleted, is_call_notify, is_trigger_broadcast, monitor_point_name, sync_time from t_patrol_disposal_info
    </sql>

    <select id="selectPatrolDisposalInfoList" parameterType="PatrolDisposalInfo" resultMap="PatrolDisposalInfoResult">
        <include refid="selectPatrolDisposalInfoVo"/>
        <where>
            <if test="disposalId != null  and disposalId != ''"> and disposal_id = #{disposalId}</if>
            <if test="riskZoneNum != null  and riskZoneNum != ''"> and risk_zone_num like concat('%', #{riskZoneNum}, '%')</if>
            <if test="riskZoneName != null  and riskZoneName != ''"> and risk_zone_name like concat('%', #{riskZoneName}, '%')</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="warningLevel != null  and warningLevel != ''"> and warning_level = #{warningLevel}</if>
            <if test="warningType != null  and warningType != ''"> and warning_type = #{warningType}</if>
            <if test="readStatus != null  and readStatus != ''"> and read_status = #{readStatus}</if>
            <if test="deadStatus != null  and deadStatus != ''"> and dead_status = #{deadStatus}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="dealPeople != null  and dealPeople != ''"> and deal_people like concat('%', #{dealPeople}, '%')</if>
            <if test="monitorPointCode != null  and monitorPointCode != ''"> and monitor_point_code like concat('%', #{monitorPointCode}, '%')</if>
            <if test="monitorPointName != null  and monitorPointName != ''"> and monitor_point_name like concat('%', #{monitorPointName}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time_xc,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time_xc,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time_xc desc
    </select>

    <select id="selectPatrolDisposalInfoById" parameterType="Long" resultMap="PatrolDisposalInfoResult">
        <include refid="selectPatrolDisposalInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectPatrolDisposalInfoByDisposalId" parameterType="String" resultMap="PatrolDisposalInfoResult">
        <include refid="selectPatrolDisposalInfoVo"/>
        where disposal_id = #{disposalId}
    </select>

    <insert id="insertPatrolDisposalInfo" parameterType="PatrolDisposalInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_patrol_disposal_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="disposalId != null">disposal_id,</if>
            <if test="riskZoneNum != null">risk_zone_num,</if>
            <if test="riskZoneName != null">risk_zone_name,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="warningLevel != null">warning_level,</if>
            <if test="warningType != null">warning_type,</if>
            <if test="readStatus != null">read_status,</if>
            <if test="deadStatus != null">dead_status,</if>
            <if test="createTimeXc != null">create_time_xc,</if>
            <if test="userId != null">user_id,</if>
            <if test="isPositive != null">is_positive,</if>
            <if test="isSlid != null">is_slid,</if>
            <if test="type != null">type,</if>
            <if test="content != null">content,</if>
            <if test="dealPeople != null">deal_people,</if>
            <if test="dealTime != null">deal_time,</if>
            <if test="monitorPointCode != null">monitor_point_code,</if>
            <if test="status != null">status,</if>
            <if test="createTimeCz != null">create_time_cz,</if>
            <if test="updateTimeCz != null">update_time_cz,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="isCallNotify != null">is_call_notify,</if>
            <if test="isTriggerBroadcast != null">is_trigger_broadcast,</if>
            <if test="monitorPointName != null">monitor_point_name,</if>
            <if test="syncTime != null">sync_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="disposalId != null">#{disposalId},</if>
            <if test="riskZoneNum != null">#{riskZoneNum},</if>
            <if test="riskZoneName != null">#{riskZoneName},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="warningLevel != null">#{warningLevel},</if>
            <if test="warningType != null">#{warningType},</if>
            <if test="readStatus != null">#{readStatus},</if>
            <if test="deadStatus != null">#{deadStatus},</if>
            <if test="createTimeXc != null">#{createTimeXc},</if>
            <if test="userId != null">#{userId},</if>
            <if test="isPositive != null">#{isPositive},</if>
            <if test="isSlid != null">#{isSlid},</if>
            <if test="type != null">#{type},</if>
            <if test="content != null">#{content},</if>
            <if test="dealPeople != null">#{dealPeople},</if>
            <if test="dealTime != null">#{dealTime},</if>
            <if test="monitorPointCode != null">#{monitorPointCode},</if>
            <if test="status != null">#{status},</if>
            <if test="createTimeCz != null">#{createTimeCz},</if>
            <if test="updateTimeCz != null">#{updateTimeCz},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="isCallNotify != null">#{isCallNotify},</if>
            <if test="isTriggerBroadcast != null">#{isTriggerBroadcast},</if>
            <if test="monitorPointName != null">#{monitorPointName},</if>
            <if test="syncTime != null">#{syncTime},</if>
        </trim>
    </insert>

    <insert id="batchInsertPatrolDisposalInfo" parameterType="java.util.List">
        insert into t_patrol_disposal_info (disposal_id, risk_zone_num, risk_zone_name, area_code, warning_level, warning_type, read_status, dead_status, create_time_xc, user_id, is_positive, is_slid, type, content, deal_people, deal_time, monitor_point_code, status, create_time_cz, update_time_cz, create_by, update_by, is_deleted, is_call_notify, is_trigger_broadcast, monitor_point_name, sync_time) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.disposalId}, #{item.riskZoneNum}, #{item.riskZoneName}, #{item.areaCode}, #{item.warningLevel}, #{item.warningType}, #{item.readStatus}, #{item.deadStatus}, #{item.createTimeXc}, #{item.userId}, #{item.isPositive}, #{item.isSlid}, #{item.type}, #{item.content}, #{item.dealPeople}, #{item.dealTime}, #{item.monitorPointCode}, #{item.status}, #{item.createTimeCz}, #{item.updateTimeCz}, #{item.createBy}, #{item.updateBy}, #{item.isDeleted}, #{item.isCallNotify}, #{item.isTriggerBroadcast}, #{item.monitorPointName}, #{item.syncTime})
        </foreach>
    </insert>

    <update id="updatePatrolDisposalInfo" parameterType="PatrolDisposalInfo">
        update t_patrol_disposal_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="disposalId != null">disposal_id = #{disposalId},</if>
            <if test="riskZoneNum != null">risk_zone_num = #{riskZoneNum},</if>
            <if test="riskZoneName != null">risk_zone_name = #{riskZoneName},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="warningLevel != null">warning_level = #{warningLevel},</if>
            <if test="warningType != null">warning_type = #{warningType},</if>
            <if test="readStatus != null">read_status = #{readStatus},</if>
            <if test="deadStatus != null">dead_status = #{deadStatus},</if>
            <if test="createTimeXc != null">create_time_xc = #{createTimeXc},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="isPositive != null">is_positive = #{isPositive},</if>
            <if test="isSlid != null">is_slid = #{isSlid},</if>
            <if test="type != null">type = #{type},</if>
            <if test="content != null">content = #{content},</if>
            <if test="dealPeople != null">deal_people = #{dealPeople},</if>
            <if test="dealTime != null">deal_time = #{dealTime},</if>
            <if test="monitorPointCode != null">monitor_point_code = #{monitorPointCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTimeCz != null">create_time_cz = #{createTimeCz},</if>
            <if test="updateTimeCz != null">update_time_cz = #{updateTimeCz},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="isCallNotify != null">is_call_notify = #{isCallNotify},</if>
            <if test="isTriggerBroadcast != null">is_trigger_broadcast = #{isTriggerBroadcast},</if>
            <if test="monitorPointName != null">monitor_point_name = #{monitorPointName},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updatePatrolDisposalInfoByDisposalId" parameterType="PatrolDisposalInfo">
        update t_patrol_disposal_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="riskZoneNum != null">risk_zone_num = #{riskZoneNum},</if>
            <if test="riskZoneName != null">risk_zone_name = #{riskZoneName},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="warningLevel != null">warning_level = #{warningLevel},</if>
            <if test="warningType != null">warning_type = #{warningType},</if>
            <if test="readStatus != null">read_status = #{readStatus},</if>
            <if test="deadStatus != null">dead_status = #{deadStatus},</if>
            <if test="createTimeXc != null">create_time_xc = #{createTimeXc},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="isPositive != null">is_positive = #{isPositive},</if>
            <if test="isSlid != null">is_slid = #{isSlid},</if>
            <if test="type != null">type = #{type},</if>
            <if test="content != null">content = #{content},</if>
            <if test="dealPeople != null">deal_people = #{dealPeople},</if>
            <if test="dealTime != null">deal_time = #{dealTime},</if>
            <if test="monitorPointCode != null">monitor_point_code = #{monitorPointCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTimeCz != null">create_time_cz = #{createTimeCz},</if>
            <if test="updateTimeCz != null">update_time_cz = #{updateTimeCz},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="isCallNotify != null">is_call_notify = #{isCallNotify},</if>
            <if test="isTriggerBroadcast != null">is_trigger_broadcast = #{isTriggerBroadcast},</if>
            <if test="monitorPointName != null">monitor_point_name = #{monitorPointName},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
        </trim>
        where disposal_id = #{disposalId}
    </update>

    <delete id="deletePatrolDisposalInfoById" parameterType="Long">
        delete from t_patrol_disposal_info where id = #{id}
    </delete>

    <delete id="deletePatrolDisposalInfoByIds" parameterType="String">
        delete from t_patrol_disposal_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
