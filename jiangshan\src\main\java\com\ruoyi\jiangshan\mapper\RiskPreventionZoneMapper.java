package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.RiskPreventionZone;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 风险防范区Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface RiskPreventionZoneMapper
{
    /**
     * 查询风险防范区
     *
     * @param id 风险防范区主键
     * @return 风险防范区
     */
    public RiskPreventionZone selectRiskPreventionZoneById(Long id);

    /**
     * 查询风险防范区列表
     *
     * @param riskPreventionZone 风险防范区
     * @return 风险防范区集合
     */
    public List<RiskPreventionZone> selectRiskPreventionZoneList(RiskPreventionZone riskPreventionZone);

    /**
     * 新增风险防范区
     *
     * @param riskPreventionZone 风险防范区
     * @return 结果
     */
    public int insertRiskPreventionZone(RiskPreventionZone riskPreventionZone);

    /**
     * 修改风险防范区
     *
     * @param riskPreventionZone 风险防范区
     * @return 结果
     */
    public int updateRiskPreventionZone(RiskPreventionZone riskPreventionZone);

    /**
     * 删除风险防范区
     *
     * @param id 风险防范区主键
     * @return 结果
     */
    public int deleteRiskPreventionZoneById(Long id);

    /**
     * 批量删除风险防范区
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRiskPreventionZoneByIds(Long[] ids);

    /**
     * 根据编号查询是否存在
     *
     * @param num 编号
     * @return 结果
     */
    public RiskPreventionZone selectRiskPreventionZoneByNum(String num);

    /**
     * 批量插入风险防范区数据
     *
     * @param riskPreventionZoneList 风险防范区列表
     * @return 结果
     */
    public int batchInsertRiskPreventionZone(List<RiskPreventionZone> riskPreventionZoneList);

    /**
     * 根据编号更新数据
     *
     * @param riskPreventionZone 风险防范区
     * @return 结果
     */
    public int updateRiskPreventionZoneByNum(RiskPreventionZone riskPreventionZone);
}
