package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;

import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.WarningEventSceneDepartmentMapper;
import com.ruoyi.jiangshan.util.PasswordValidator;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.system.service.ISysMenuService;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private WarningEventSceneDepartmentMapper warningEventSceneDepartmentMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        //弱密码
        if (!PasswordValidator.validatePassword(loginBody.getPassword())) {
            ajax.put("weakFlag", 1);
        } else {
            ajax.put("weakFlag", 0);
        }
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 浙政钉扫码登录
     *
     * @return 结果
     */
    @GetMapping("/loginV2")
    public AjaxResult loginV2(String code) throws ExecutionException {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        Map<String, Object> map = loginService.loginV2(code);
        ajax.putAll(map);
        return ajax;
    }

    /**
     * 浙政钉免登
     *
     * @return 结果
     */
    @GetMapping("/loginFree")
    public AjaxResult loginFree(String code) throws ExecutionException {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        Map<String, Object> map = loginService.loginFree(code);
        ajax.putAll(map);
        return ajax;
    }

    /**
     * 单点登录
     *
     * @return 结果
     */
    @GetMapping("/loginThird")
    public AjaxResult loginThird(String employeeCode) throws ExecutionException {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        Map<String, Object> map = loginService.loginThird(employeeCode);
        ajax.putAll(map);
        return ajax;
    }

    /**
     * 浙政钉/钉钉单点登录
     *
     * @param operateUserId 登录信息
     * @return 结果
     */
    @GetMapping("/loginByUserId")
    public AjaxResult loginByOperateUserId(String operateUserId)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        Map<String, Object> map = loginService.loginByOperateUserId(operateUserId);
        ajax.putAll(map);
        return ajax;
    }

    /**
     * 浙政钉登录测试
     *
     * @return 结果
     */
    @GetMapping("/loginTest")
    public AjaxResult loginTest() throws ExecutionException {
        loginService.loginTest();
        return AjaxResult.success();
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);

        //添加当前用户能访问的场景
        SysUser currentUser = sysUserMapper.selectUserById(user.getUserId());
        List<String> sceneList = MonitorSceneType.getScenePermissionByUser(currentUser, warningEventSceneDepartmentMapper);
        ajax.put("sceneList", sceneList);

        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
