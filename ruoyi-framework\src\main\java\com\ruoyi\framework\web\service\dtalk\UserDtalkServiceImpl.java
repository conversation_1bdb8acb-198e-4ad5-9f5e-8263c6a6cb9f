package com.ruoyi.framework.web.service.dtalk;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.xxpt.gateway.shared.api.request.*;
import com.alibaba.xxpt.gateway.shared.api.response.*;
import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.IntelligentGetClient;
import com.alibaba.xxpt.gateway.shared.client.http.IntelligentPostClient;
import com.alibaba.xxpt.gateway.shared.client.http.PostClient;
import com.alibaba.xxpt.gateway.shared.client.http.api.OapiSpResultContent;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.framework.config.properties.DtalkProperties;
import com.ruoyi.framework.dtalk.*;
import com.ruoyi.framework.vo.DtalkAppUserVO;
import com.ruoyi.framework.vo.DtalkEmployeeVo;
import com.ruoyi.framework.vo.EmployeeInfo;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> yx-0176
 * @description
 * @date : 2021/10/18
 */
@Service
@Slf4j
public class UserDtalkServiceImpl implements UserDtalkService {

    /**
     * 引入处理扫码登录信息的client信息
     */
    @Resource(name = "executableClientLogin")
    private ExecutableClient executableClient;

    @Resource(name = "executableClientFreeLogin")
    private ExecutableClient executableFreeClient;

    @Autowired
    private DtalkProperties dtalkProperties;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public DtalkAppUserVO getDingtalkAppUserByFree(String authCode) {
        //executableClient保证单例
        IntelligentPostClient intelligentPostClient = executableFreeClient.newIntelligentPostClient("/rpc/oauth2/dingtalk_app_user.json");
        OapiRpcOauth2DingtalkAppUserJsonRequest oapiRpcOauth2DingtalkAppUserJsonRequest = new OapiRpcOauth2DingtalkAppUserJsonRequest();
        //登录access_token
        oapiRpcOauth2DingtalkAppUserJsonRequest.setAccess_token(getAccessToken());
        //临时授权码
        oapiRpcOauth2DingtalkAppUserJsonRequest.setAuth_code(authCode);
        //获取结果
        OapiRpcOauth2DingtalkAppUserJsonResponse apiResult = intelligentPostClient.post(oapiRpcOauth2DingtalkAppUserJsonRequest);

        if (!apiResult.getSuccess()) {
            log.error("getDingtalkAppUserByFree, error:{}", JSON.toJSONString(apiResult));
            throw new RuntimeException(apiResult.getMessage());
        }

        log.info("getDingtalkAppUserByFree, apiResult:{}", apiResult.getContent());

//        JSONObject jsonObject = JSON.parseObject("{\"success\":true,\"content\":{\"data\":{\"lastName\":\"吴宏阳\",\"realmId\":196729,\"clientId\":\"jscsaqyjxt_dingoa\",\"openid\":\"985e9499c17507499ed56552a8504aec\",\"realmName\":\"浙政钉租户\",\"nickNameCn\":\"吴宏阳\",\"tenantUserId\":\"196729$********\",\"avatar\":\"$hQHNKaUCo2pwZwMGDAEN2gBAU01sV0Zxa0FzbHVwaVBvL290empGalk1V3FJdTIwVXBQWjBmOWZGZWZoQWM5UU0xeExsUVFkalBQNFVJakZ0aA\",\"employeeCode\":\"GE_2697494e7a6443d19f712c28e6221c6a\",\"accountId\":********,\"tenantName\":\"浙政钉租户\",\"referId\":\"********\",\"namespace\":\"local\",\"tenantId\":196729,\"account\":\"jhsjtjtyxgs-why\"},\"success\":true,\"responseMessage\":\"成功\",\"responseCode\":\"0\"}}");
        JSONObject jsonObject = JSON.parseObject(apiResult.getContent());

        JSONObject content = jsonObject.getJSONObject("content");

        String data = JSON.toJSONString(content.get("data"));

        log.info("getDingtalkAppUserByFree, data:{}", data);

        DtalkAppUserVO dtalkAppUserVO = JSON.parseObject(data, DtalkAppUserVO.class);

        log.info("getDingtalkAppUserByFree, dtalkAppUserVO:{}", JSON.toJSONString(dtalkAppUserVO));

        return dtalkAppUserVO;
    }

    /**
     * 获取登录授权token
     *
     * @return
     */
    @Override
    public String getAccessToken() {
        String api = "/gettoken.json";
        PostClient client = executableClient.newPostClient(api);
        client.addParameter("appkey", dtalkProperties.getLoginAppkey());
        client.addParameter("appsecret", dtalkProperties.getLoginAppsecret());
        //调用API
        String apiResult = client.post();
        log.info("getAccessToken参数打印：{}", client);
        log.info("getAccessToken返回结果打印：" + apiResult);
        BaseResp baseResult = JSON.parseObject(apiResult, BaseResp.class);
        if (!baseResult.getSuccess()) {
            throw new RuntimeException(baseResult.getMessage());
        }
        AccessTokenResp accessTokenResp = JSON.parseObject(baseResult.getContent().getData().toString(), AccessTokenResp.class);
        return accessTokenResp.getAccessToken();
    }

    /**
     * 通过临时授权码获取用户信息
     *
     * @param code 临时授权码
     * @return
     */
    @Override
    public UserResp getDingtalkAppUser(String code) {
        String accessToken = getAccessToken();
        String api = "/rpc/oauth2/getuserinfo_bycode.json";
        PostClient postClient = executableClient.newPostClient(api);
        postClient.addParameter("access_token", accessToken);
        postClient.addParameter("code", code);
        String apiResult = postClient.post();
        log.info("getDingtalkAppUser返回结果打印：" + apiResult);
        BaseResp baseResult = JSON.parseObject(apiResult, BaseResp.class);
        if (!baseResult.getContent().getSuccess()) {
            throw new RuntimeException(baseResult.getContent().getResponseMessage());
        }
        UserResp userResp = JSON.parseObject(baseResult.getContent().getData().toString(), UserResp.class);

        userResp.setAccessToken(accessToken);
        return userResp;
    }

    @Override
    public MobileUserResp getDingtalkAppUserByMobile(String mobile) throws Exception {

        log.info("OapiMoziEmployeeGetByMobilesResponse, mobile:{}", mobile);

        IntelligentGetClient getClient = executableFreeClient.newIntelligentGetClient("/mozi/employee/get_by_mobiles");

        OapiMoziEmployeeGetByMobilesRequest oapiMoziEmployeeGetByMobilesRequest = new OapiMoziEmployeeGetByMobilesRequest();
        //手机区号(没有特别说明，固定填写86)
        oapiMoziEmployeeGetByMobilesRequest.setAreaCode("86");
        //手机号码列表，逗号分隔，最多50个
        oapiMoziEmployeeGetByMobilesRequest.setMobiles(mobile);
        //租户ID
        oapiMoziEmployeeGetByMobilesRequest.setTenantId(dtalkProperties.getTenantId());
        //账号类型（没有特别说明,固定填写local）
        oapiMoziEmployeeGetByMobilesRequest.setNamespace("local");
        OapiMoziEmployeeGetByMobilesResponse apiResult = getClient.get(oapiMoziEmployeeGetByMobilesRequest);

        log.info("OapiMoziEmployeeGetByMobilesResponse, result:{}", JSON.toJSONString(apiResult));

        if (!apiResult.getSuccess()) {
            throw new RuntimeException("获取用户信息出错");
        }
        if (!apiResult.getContent().getSuccess()) {
            if ("该手机号不存在".equals(apiResult.getContent().getResponseMessage())) {
                throw new RuntimeException("权限不够，无法查看此手机号浙政钉信息");
            }
            throw new RuntimeException(apiResult.getContent().getResponseMessage());
        }
        return JSONArray.parseArray(apiResult.getContent().getData(),MobileUserResp.class).get(0);
    }

    @Override
    public String getUserByAccountId(String accountId){
        String api ="/mozi/employee/listGovEmployeeCodesByAccountIds";
        PostClient postClient = executableFreeClient.newPostClient(api);

        postClient.addParameter("accountIds", accountId);
//accountIds 为多个时传参如下
//        String[] accountIds ={"1000","2000"};
//        for (String accountId : accountIds) {
//            postClient.addParameter("accountIds", accountId);
//        }
        postClient.addParameter("tenantId", dtalkProperties.getTenantId() + "");
//调用API
        String apiResult = postClient.post();

        Map resultMap = JSON.parseObject(apiResult, Map.class);

        Boolean success = (Boolean) resultMap.get("success");

        if (!success) {
            throw new RuntimeException("查询浙政钉用户失败, msg:" + apiResult);
        }

        Map content = (Map)resultMap.get("content");
        if (Objects.isNull(content)) {
            throw new RuntimeException("查询浙政钉用户失败, msg:" + apiResult);
        }

        List<Map> data = (List<Map>)content.get("data");
        if (CollectionUtils.isEmpty(data)) {
            throw new RuntimeException("浙政钉用户不存在, msg:" + apiResult);
        }

        Map map = data.get(0);
        String employeeCode = (String) map.get("employeeCode");

        return employeeCode;
    }

    @Override
    public UserEmployeeDTO getUserByEmployeeCode(String employeeCode) {
        IntelligentGetClient intelligentGetClient = executableFreeClient.newIntelligentGetClient("/mozi/employee/getEmployeeByCode");
        OapiMoziEmployeeGetEmployeeByCodeRequest oapiMoziEmployeeGetEmployeeByCodeRequest = new OapiMoziEmployeeGetEmployeeByCodeRequest();
        oapiMoziEmployeeGetEmployeeByCodeRequest.setEmployeeCode(employeeCode);
        oapiMoziEmployeeGetEmployeeByCodeRequest.setTenantId(dtalkProperties.getTenantId());
        //获取结果
        OapiMoziEmployeeGetEmployeeByCodeResponse apiResult = intelligentGetClient.get(oapiMoziEmployeeGetEmployeeByCodeRequest);
        if (!apiResult.getSuccess()) {
            throw new RuntimeException("获取浙政钉用户失败, msg:" + JSON.toJSONString(apiResult));
        }

        OapiSpResultContent content = apiResult.getContent();
        UserEmployeeDTO userEmployeeDTO = JSON.parseObject(content.getData(), UserEmployeeDTO.class);

        return userEmployeeDTO;
    }

    /**
     * 发布工作通知
     *
     */
    @Override
    public boolean workNotification(String receiverIds, String bizMsgId,
                                    String title, String content, String singleUrl, String singlePcUrl) {
        Long tenantId = dtalkProperties.getTenantId();//租户id

        log.info("workNotification, receiverIds:{}, bizMsgId:{}, title:{}, content:{}, singleUrl:{}, singlePcUrl:{}",
                receiverIds, bizMsgId, title, content, singleUrl, singlePcUrl);

        //executableClient保证单例
        IntelligentGetClient intelligentGetClient = executableFreeClient.newIntelligentGetClient("/message/workNotification");
        OapiMessageWorkNotificationRequest oapiMessageWorkNotificationRequest = new OapiMessageWorkNotificationRequest();
        //接收者的部门id列表
        //oapiMessageWorkNotificationRequest.setOrganizationCodes("字符串");
        //接收人用户ID,多个人时使用半角逗号分隔
        oapiMessageWorkNotificationRequest.setReceiverIds(receiverIds);
        //租户ID
        oapiMessageWorkNotificationRequest.setTenantId(tenantId + "");
        //业务消息id
        oapiMessageWorkNotificationRequest.setBizMsgId(bizMsgId);
        //消息对象
        oapiMessageWorkNotificationRequest.setMsg(getJsonWorkNotificationMsg(title, content, singleUrl, singlePcUrl));
        //获取结果
        OapiMessageWorkNotificationResponse apiResult = intelligentGetClient.get(oapiMessageWorkNotificationRequest);

        log.info("workNotification：" + JSON.toJSONString(apiResult));

        return null != apiResult && apiResult.getSuccess();
    }

    @Override
    public List<String> listAccountByIds(List<String> warningUserIdList) {
        return sysUserMapper.listAccountByIds(warningUserIdList);
    }

    @Override
    public List<EmployeeInfo> getOrganizationUserList(String organizationCode, Integer pageSize, Integer pageNum) {
        //executableClient保证单例
        IntelligentGetClient intelligentGetClient = executableFreeClient.newIntelligentGetClient("/mozi/organization/pageOrganizationEmployeeCodes");
        OapiMoziOrganizationPageOrganizationEmployeeCodesRequest oapiMoziOrganizationPageOrganizationEmployeeCodesRequest = new OapiMoziOrganizationPageOrganizationEmployeeCodesRequest();
        //null
        oapiMoziOrganizationPageOrganizationEmployeeCodesRequest.setReturnTotalSize(true);
//        oapiMoziOrganizationPageOrganizationEmployeeCodesRequest.setEmployeePositionStatus("字符串");
        oapiMoziOrganizationPageOrganizationEmployeeCodesRequest.setPageSize(pageSize);
        oapiMoziOrganizationPageOrganizationEmployeeCodesRequest.setOrganizationCode(organizationCode);
        oapiMoziOrganizationPageOrganizationEmployeeCodesRequest.setPageNo(pageNum);
        oapiMoziOrganizationPageOrganizationEmployeeCodesRequest.setTenantId(dtalkProperties.getTenantId());
        //获取结果
        OapiMoziOrganizationPageOrganizationEmployeeCodesResponse apiResult = intelligentGetClient.get(oapiMoziOrganizationPageOrganizationEmployeeCodesRequest);

        /**
         * // 网关失败返回
         * {
         *     "_RequestId":"ac140b3e15834783672523535d000c",
         *     "Message":"The input parameter 'tenantId' that is mandatory for processing this request is not supplied or value is empty.",
         *     "success":false,
         *     "errorCode":"MissingParameter",
         *     "HostId":"gudao-openplatform-daily",
         *     "Code":"MissingParameter",
         *     "errorMsg":"The input parameter 'tenantId' that is mandatory for processing this request is not supplied or value is empty.",
         *     "errorLevel":"error"
         * }
         *
         * // 业务失败返回
         * {
         *     "success":true,
         *     "content":{
         *         "success":false,
         *         "requestId":"e8b309ab1e7047b3af09feea609f6cb5",
         *         "responseMessage":"tenantId could not be found !",
         *         "responseCode":"238001"
         *     }
         * }
         */

        if (!apiResult.getSuccess()) {
            throw new RuntimeException(apiResult.getMessage());
        }

        /**
         * 成功返回案例
         * {
         *     "success": true,
         *     "content": {
         *         "totalSize": 2,
         *         "data": ["GE_f6757f0fe6c048dc9dd8f62fdf156aef",
         *                  "GE_b5ec29b8c869419db1ac3fbba71983fb"],
         *         "success": true,
         *         "requestId": "f04d59de-ea7c-43fc-87b6-12b139cf64f3",
         *         "pageSize": 10,
         *         "responseMessage": "OK",
         *         "currentPage": 1,
         *         "responseCode": "0"
         *     }
         * }
         */
        String data = apiResult.getContent().getData();
        List<String> lists = JSONArray.parseArray(data, String.class);
        if (org.springframework.util.CollectionUtils.isEmpty(lists)) {
            return new ArrayList<>();
        }
        List<EmployeeInfo> userList = getUserList(lists);
        return userList;
    }

    @Override
    public List<DtalkEmployeeVo> pageSearchEmployee(Integer pageNum, Integer pageSize,
                                                    String organizationCode, String nameKeyword) {
        List<DtalkEmployeeVo> dtalkEmployeeVos = Lists.newArrayList();

        OapiMoziFusionPageSearchEmployeeRequest oapiMoziFusionPageSearchEmployeeRequest = new OapiMoziFusionPageSearchEmployeeRequest();
        IntelligentGetClient intelligentGetClient = executableFreeClient.newIntelligentGetClient("/mozi/fusion/pageSearchEmployee");

        //是否返回查询结果总数  默认不需要
        oapiMoziFusionPageSearchEmployeeRequest.setReturnTotalSize(true);
        //组织code,在当前组织下查询 优先级高于cascadeOrganizationCode 两个参数至少有一个
//        oapiMoziFusionPageSearchEmployeeRequest.setInOrganizationCode("");
        //每页条数, 默认20, 最大只能100
        oapiMoziFusionPageSearchEmployeeRequest.setPageSize(pageSize);
        //当前页码, 开始页码为1, 小于1认为为1
        oapiMoziFusionPageSearchEmployeeRequest.setPageNo(pageNum);
        //租户id
        oapiMoziFusionPageSearchEmployeeRequest.setTenantId(dtalkProperties.getTenantId());
        //组织code,在组织级联下级中查询
        oapiMoziFusionPageSearchEmployeeRequest.setCascadeOrganizationCode(organizationCode);
        //A/F （在职/离职）默认返回所有
//        oapiMoziFusionPageSearchEmployeeRequest.setStatus("A");
        //人员姓名关键字
        oapiMoziFusionPageSearchEmployeeRequest.setNameKeywords(nameKeyword);
        //获取结果
        OapiMoziFusionPageSearchEmployeeResponse apiResult = intelligentGetClient.get(oapiMoziFusionPageSearchEmployeeRequest);
        OapiSpResultContent content = apiResult.getContent();
        if (Objects.isNull(content)) {
            return Lists.newArrayList();
        }

        if (content.getSuccess()) {
            /**
             * 正常数据响应
             * {
             *  "data": "[{\"employeeName\":\"天问\",\"accountId\":818600,\"account\":\"tw-sjznsyx\",\"employeeCode\":\"GE_fe567eb28e534672ba3bf253630bbe6e\",\"status\":\"A\"}]",
             *  "responseCode": "0",
             *  "responseMessage": "OK",
             *  "success": true,
             *  "totalSize": 1
             *  }
             */
            //循环查询，查询部门内所有的数据
            List<String> strings = JSONArray.parseArray(content.getData(), String.class);
            if (org.springframework.util.CollectionUtils.isEmpty(strings)) {
                return dtalkEmployeeVos;
            }
            strings.forEach(item -> {
                DtalkEmployeeVo dtalkEmployeeVo = JSON.parseObject(item, DtalkEmployeeVo.class);
                dtalkEmployeeVos.add(dtalkEmployeeVo);
            });
//            if (jsonArray.size() == pageSize) {
//                getPage(pageNo + 1, pageSize, dtalkEmployeeVos, organizationCode, nameKeyword);
//            }
            return dtalkEmployeeVos;
        } else {
            /**
             * 错误响应数据
             *  {
             *    "responseCode": "300037",
             *    "responseMessage": "jhsgt没有[GO_836bd141d4614baa9977bd1eb78c463]组织的操作权限",
             *    "success": false
             *  }
             */
            throw new RuntimeException(content.getResponseMessage());
        }
    }

    /**
     * 根据职员code列表，获取职员信息
     *
     * @return
     */
    private List<EmployeeInfo> getUserList(List<String> codeList) {
        //executableClient保证单例
        IntelligentPostClient intelligentPostClient = executableFreeClient.newIntelligentPostClient("/mozi/employee/listEmployeesByCodes");
        OapiMoziEmployeeListEmployeesByCodesRequest oapiMoziEmployeeListEmployeesByCodesRequest = new OapiMoziEmployeeListEmployeesByCodesRequest();
        //null
        oapiMoziEmployeeListEmployeesByCodesRequest.setEmployeeCodes(codeList);
        //null
        oapiMoziEmployeeListEmployeesByCodesRequest.setTenantId(dtalkProperties.getTenantId());
        //获取结果
        OapiMoziEmployeeListEmployeesByCodesResponse apiResult = intelligentPostClient.post(oapiMoziEmployeeListEmployeesByCodesRequest);
        String data = apiResult.getContent().getData();
        List<EmployeeInfo> employeeInfos = JSONArray.parseArray(data, EmployeeInfo.class);
        return employeeInfos;
    }

    /**
     * 工作通知
     * json形式的
     *
     * @param title
     * @param content
     * @return
     */
    public String getJsonWorkNotificationMsg(String title, String content, String singleUrl, String singlePcUrl) {
        String msg = "{\n" +
                "    \"msgtype\": \"action_card\",\n" +
                "    \"action_card\": {\n" +
                "        \"title\": \"" + title + "\",\n" +
                "        \"markdown\": \"" + content + "\" ,\n" +
                "        \"single_url\":\"" + singleUrl + "\", \n" +
                "        \"single_pc_url\":\"" + singlePcUrl + "\",\n" +
//                    "        \"single_pc_url\": \"http://jkd.wjw.jinhua.gov.cn:8095/jhfy/yujingfankui/feedback.html?id="+dbId+"\" \n" +
                "        \"single_title\": \"查看详情\"\n" +
                // "        \"single_title\":\""+single_title+"\"\n" +
                "    }\n" +
                "}";

        log.info("getJsonWorkNotificationMsg, msg:{}", msg);

        return msg;
    }

}
