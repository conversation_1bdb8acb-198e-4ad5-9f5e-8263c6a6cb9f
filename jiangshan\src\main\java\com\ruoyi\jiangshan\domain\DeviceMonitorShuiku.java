package com.ruoyi.jiangshan.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 监测对象-水库对象 t_device_monitor_shuiku
 * 
 * <AUTHOR>
 * @date 2025-03-26
 */
public class DeviceMonitorShuiku extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f1;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f2;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f3;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f4;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f5;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f6;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f7;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f8;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f9;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f10;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f11;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f12;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f13;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f14;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f15;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f16;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f17;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f18;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f19;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f20;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f21;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f22;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f23;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f24;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f25;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String f26;

    public void setF1(String f1) 
    {
        this.f1 = f1;
    }

    public String getF1() 
    {
        return f1;
    }
    public void setF2(String f2) 
    {
        this.f2 = f2;
    }

    public String getF2() 
    {
        return f2;
    }
    public void setF3(String f3) 
    {
        this.f3 = f3;
    }

    public String getF3() 
    {
        return f3;
    }
    public void setF4(String f4) 
    {
        this.f4 = f4;
    }

    public String getF4() 
    {
        return f4;
    }
    public void setF5(String f5) 
    {
        this.f5 = f5;
    }

    public String getF5() 
    {
        return f5;
    }
    public void setF6(String f6) 
    {
        this.f6 = f6;
    }

    public String getF6() 
    {
        return f6;
    }
    public void setF7(String f7) 
    {
        this.f7 = f7;
    }

    public String getF7() 
    {
        return f7;
    }
    public void setF8(String f8) 
    {
        this.f8 = f8;
    }

    public String getF8() 
    {
        return f8;
    }
    public void setF9(String f9) 
    {
        this.f9 = f9;
    }

    public String getF9() 
    {
        return f9;
    }
    public void setF10(String f10) 
    {
        this.f10 = f10;
    }

    public String getF10() 
    {
        return f10;
    }
    public void setF11(String f11) 
    {
        this.f11 = f11;
    }

    public String getF11() 
    {
        return f11;
    }
    public void setF12(String f12) 
    {
        this.f12 = f12;
    }

    public String getF12() 
    {
        return f12;
    }
    public void setF13(String f13) 
    {
        this.f13 = f13;
    }

    public String getF13() 
    {
        return f13;
    }
    public void setF14(String f14) 
    {
        this.f14 = f14;
    }

    public String getF14() 
    {
        return f14;
    }
    public void setF15(String f15) 
    {
        this.f15 = f15;
    }

    public String getF15() 
    {
        return f15;
    }
    public void setF16(String f16) 
    {
        this.f16 = f16;
    }

    public String getF16() 
    {
        return f16;
    }
    public void setF17(String f17) 
    {
        this.f17 = f17;
    }

    public String getF17() 
    {
        return f17;
    }
    public void setF18(String f18) 
    {
        this.f18 = f18;
    }

    public String getF18() 
    {
        return f18;
    }
    public void setF19(String f19) 
    {
        this.f19 = f19;
    }

    public String getF19() 
    {
        return f19;
    }
    public void setF20(String f20) 
    {
        this.f20 = f20;
    }

    public String getF20() 
    {
        return f20;
    }
    public void setF21(String f21) 
    {
        this.f21 = f21;
    }

    public String getF21() 
    {
        return f21;
    }
    public void setF22(String f22) 
    {
        this.f22 = f22;
    }

    public String getF22() 
    {
        return f22;
    }
    public void setF23(String f23) 
    {
        this.f23 = f23;
    }

    public String getF23() 
    {
        return f23;
    }
    public void setF24(String f24) 
    {
        this.f24 = f24;
    }

    public String getF24() 
    {
        return f24;
    }
    public void setF25(String f25) 
    {
        this.f25 = f25;
    }

    public String getF25() 
    {
        return f25;
    }
    public void setF26(String f26) 
    {
        this.f26 = f26;
    }

    public String getF26() 
    {
        return f26;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("f1", getF1())
            .append("f2", getF2())
            .append("f3", getF3())
            .append("f4", getF4())
            .append("f5", getF5())
            .append("f6", getF6())
            .append("f7", getF7())
            .append("f8", getF8())
            .append("f9", getF9())
            .append("f10", getF10())
            .append("f11", getF11())
            .append("f12", getF12())
            .append("f13", getF13())
            .append("f14", getF14())
            .append("f15", getF15())
            .append("f16", getF16())
            .append("f17", getF17())
            .append("f18", getF18())
            .append("f19", getF19())
            .append("f20", getF20())
            .append("f21", getF21())
            .append("f22", getF22())
            .append("f23", getF23())
            .append("f24", getF24())
            .append("f25", getF25())
            .append("f26", getF26())
            .toString();
    }
}
