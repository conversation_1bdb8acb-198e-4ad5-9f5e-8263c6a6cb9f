package com.ruoyi.jiangshan.service.impl;

import com.google.common.collect.Lists;
import com.ruoyi.common.dto.DateDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.DeviceMonitorRanqijing;
import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.service.GasSceneService;
import com.ruoyi.jiangshan.service.IDeviceValueService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.vo.BusinessCountVO;
import com.ruoyi.jiangshan.vo.WarningCountVO;
import com.ruoyi.jiangshan.vo.WarningInfoVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class GasSceneServiceImpl implements GasSceneService {
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Autowired
    private DeviceValueMapper deviceValueMapper;
    @Autowired
    private IWarningEventService warningEventService;
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private IDeviceValueService deviceValueService;
    @Autowired
    private DeviceMonitorRanqijingMapper deviceMonitorRanqijingMapper;

    private final static String TYPE = "燃气阀井";
    private final static String SCENE = MonitorSceneType.SCENE_06.getDesc();
    private static List<String> DEVICE_TYPE_LIST = Lists.newArrayList("燃气阀井监测仪", "可燃气体探测器");

    @Autowired
    private DeviceInfoTypeMapper deviceInfoTypeMapper;
    @PostConstruct
    public void init() {
        DEVICE_TYPE_LIST = deviceInfoTypeMapper.listByScene(SCENE);
    }


    @Override
    public Map<String, Object> getPiplineCount() {
        Map<String, Object> resultMap = new HashMap<>();

//        List<BusinessCountVO> countVOList = deviceMonitorMapper.countScaleByType(Lists.newArrayList(TYPE));
//        if (CollectionUtils.isNotEmpty(countVOList)) {
//            BusinessCountVO businessCountVO = countVOList.get(0);
//
//            resultMap.put("totalLength", businessCountVO.getValue());
//        } else {
//            resultMap.put("totalLength", 0L);
//        }
        List<DeviceMonitor> monitorList = deviceMonitorMapper.listByType(Lists.newArrayList(TYPE));
        if (CollectionUtils.isEmpty(monitorList)) {
            resultMap.put("totalLength", 0L);
        } else {
            resultMap.put("totalLength", (long) monitorList.size());
        }

        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), "日");

        double maxMethane = deviceValueMapper.getMaxValueByMonitorItem("甲烷浓度", dateDTO.getStartTime(), dateDTO.getEndTime());
        double avgMethane = deviceValueMapper.getAvgValueByMonitorItem("甲烷浓度", dateDTO.getStartTime(), dateDTO.getEndTime());

        resultMap.put("maxMethane", maxMethane);
        resultMap.put("avgMethane", avgMethane);

        return resultMap;
    }

    @Override
    public WarningCountVO getSafetyCount(String dateStr) {
        return warningEventService.getSafetyCount(dateStr, SCENE);
    }

    @Override
    public List<WarningEvent> getEvent(String eventName, Integer status, Integer warningLevel) {
        return warningEventMapper.listByConditionLimit(SCENE, eventName, status, warningLevel, 3);
    }

    @Override
    public List<WarningInfoVO> getWarning(Integer warningLevel) {
        return warningInfoMapper.listByLevelAndScene(warningLevel, SCENE);
    }

    @Override
    public Map<String, Object> getMonitor(String dateStr, Long deviceId) {
        return deviceValueService.getMonitor(dateStr, deviceId, "甲烷浓度");
    }

    @Override
    public Map<String, Object> getInspection(Long monitorId) {
        Map<String, Object> resultMap = new HashMap<>();

        resultMap.put("length", 20);
        resultMap.put("leakDetection", 20);
        resultMap.put("safetyMonitor", 20);
        resultMap.put("safetySignMonitor", 20);
        resultMap.put("time", "2024-09-10 20:00:00");

        return resultMap;
    }

    @Override
    public List<DeviceMonitor> getMonitorObject(String monitorType) {
        return deviceMonitorMapper.listByType(Lists.newArrayList(monitorType));
    }

    @Override
    public String importData(Map<String, List<Map<String, Map<String, String>>>> params) {
        List<Map<String, Map<String, String>>> featureList = params.get("features");

        List<DeviceMonitorRanqijing> list = Lists.newArrayList();
        for (Map<String, Map<String, String>> map : featureList) {
            Map<String, String> attributesMap = map.get("attributes");
            Map<String, String> geometryMap = map.get("geometry");

            DeviceMonitorRanqijing deviceMonitorRanqijing = new DeviceMonitorRanqijing();
            deviceMonitorRanqijing.setId(attributesMap.get("Id"));
            deviceMonitorRanqijing.setFid(attributesMap.get("FID"));
            deviceMonitorRanqijing.setBianhao(attributesMap.get("bianhao"));
            deviceMonitorRanqijing.setGuanjing(attributesMap.get("guanjing"));
            deviceMonitorRanqijing.setLayer(attributesMap.get("layer"));
            deviceMonitorRanqijing.setLon(geometryMap.get("x"));
            deviceMonitorRanqijing.setLat(geometryMap.get("y"));

            list.add(deviceMonitorRanqijing);
        }

        deviceMonitorRanqijingMapper.batchInsert(list);

        return "ok";
    }

    @Override
    public List<DeviceMonitorRanqijing> listGasWell() {
        return deviceMonitorRanqijingMapper.listGasWell();
    }
}
