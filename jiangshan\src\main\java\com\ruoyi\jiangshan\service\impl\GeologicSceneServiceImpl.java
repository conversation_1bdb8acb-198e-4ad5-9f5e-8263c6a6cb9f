package com.ruoyi.jiangshan.service.impl;

import com.google.common.collect.Lists;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.service.ComprehensiveSceneService;
import com.ruoyi.jiangshan.service.GeologicSceneService;
import com.ruoyi.common.dto.DateDTO;
import com.ruoyi.jiangshan.service.IDeviceValueService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.vo.*;
import com.ruoyi.jiangshan.vo.cityrisk.CityRiskAvgLineVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GeologicSceneServiceImpl implements GeologicSceneService {
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Autowired
    private DeviceValueMapper deviceValueMapper;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private IWarningEventService warningEventService;
    @Autowired
    private IDeviceValueService deviceValueService;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private ComprehensiveSceneService comprehensiveSceneService;
    @Autowired
    private DeviceMonitorFxffqMapper deviceMonitorFxffqMapper;

    private final static String TYPE = "风险防范区";
    private final static String SCENE = MonitorSceneType.SCENE_03.getDesc();
    private static List<String> DEVICE_TYPE_LIST = Lists.newArrayList("雨量站");

    @Autowired
    private DeviceInfoTypeMapper deviceInfoTypeMapper;
    @PostConstruct
    public void init() {
        DEVICE_TYPE_LIST = deviceInfoTypeMapper.listByScene(SCENE);
    }

    @Override
    public Map<String, Object> getRainFall(Long id) {
        Map<String, Object> resultMap = new HashMap<>();

        DeviceInfo deviceInfo = deviceInfoMapper.getById(id);

        CityRiskAvgLineVO cityRiskAvgLineVO = comprehensiveSceneService.getSafetyLine(deviceInfo.getDeviceThirdId(), "日降雨量");

        resultMap.put("lineMap", cityRiskAvgLineVO.getCountVOList());

        return resultMap;
    }

    @Override
    public WarningCountVO getSafetyCount(String dateStr) {
        return warningEventService.getSafetyCount(dateStr, SCENE);
    }

    @Override
    public List<WarningEvent> getEvent(String eventName, Integer status, Integer warningLevel) {
        return warningEventMapper.listByConditionLimit(SCENE, eventName, status, warningLevel, 3);
    }

    @Override
    public List<WarningInfoVO> getWarning(Integer warningLevel) {
        // TODO: 2024/12/14
        return warningInfoMapper.listByLevelAndScene(warningLevel, SCENE);
    }

    @Override
    public Map<String, Object> getGroupPrevent() {
        // TODO: 2024/8/28  群防群控
        Map<String, Object> resultMap = new HashMap<>();

        resultMap.put("totalCount", 0L);
        resultMap.put("noExceptionCount", 0);
        resultMap.put("exceptionCount", 0L);

        resultMap.put("preventList", Lists.newArrayList());

//        resultMap.put("preventList", Lists.newArrayList(new GroupPreventVO(new Date(), "先锋村黄根友屋后滑坡隐患风险防范区",
//                "郑珊", "晴", "巡查排查")));

        return resultMap;
    }

    @Override
    public List<RiskPreventVO> getRiskPrevention(RiskPreventVO riskPreventVO) {
        List<DeviceMonitorFxffq> deviceMonitorFxffqList = deviceMonitorFxffqMapper.listAll(riskPreventVO);

        List<DeviceMonitor> deviceMonitorList = deviceMonitorMapper.listAll(null, "地质灾害场景", null);

        Map<String, List<DeviceMonitor>> deviceMonitorMap = deviceMonitorList.stream()
                .collect(Collectors.groupingBy(DeviceMonitor::getMonitorName));

        List<RiskPreventVO> preventVOList = Lists.newArrayList();
        for (DeviceMonitorFxffq deviceMonitorFxffq : deviceMonitorFxffqList) {
            RiskPreventVO resultVO = new RiskPreventVO();
            resultVO.setMonitorName(deviceMonitorFxffq.getFxffqmc());
            resultVO.setMonitorArea(deviceMonitorFxffq.getSzdq());
            resultVO.setWarningLevel(deviceMonitorFxffq.getFxdj());

            List<DeviceMonitor> monitorOneList = deviceMonitorMap.get(deviceMonitorFxffq.getFxffqmc());
            if (CollectionUtils.isNotEmpty(monitorOneList)) {
                DeviceMonitor deviceMonitor = monitorOneList.get(0);
                resultVO.setMonitorId(deviceMonitor.getId());
            }

            preventVOList.add(resultVO);
        }

        return preventVOList;
    }

    @Override
    public List<DeviceMonitor> getAllMonitor() {
        return deviceMonitorMapper.listByType(Lists.newArrayList(TYPE));
    }

    @Override
    public List<DeviceInfo> getAllDevice() {
        return deviceInfoMapper.listByDeviceTypeAndScene("雨量站", SCENE, false);
    }

}
