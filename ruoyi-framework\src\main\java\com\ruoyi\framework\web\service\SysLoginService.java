package com.ruoyi.framework.web.service;

import javax.annotation.Resource;
import javax.xml.transform.Source;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.framework.dtalk.UserResp;
import com.ruoyi.framework.vo.DtalkAppUserVO;
import com.ruoyi.framework.web.service.dtalk.DeptDtalkService;
import com.ruoyi.framework.web.service.dtalk.UserDtalkService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.user.BlackListException;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.exception.user.UserNotExistsException;
import com.ruoyi.common.exception.user.UserPasswordNotMatchException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.framework.security.context.AuthenticationContextHolder;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SysLoginService
{
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private UserDtalkService userDtalkFreeService;

    @Autowired
    private DeptDtalkService deptDtalkService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid)
    {
        // 验证码校验
        validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled)
        {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            redisCache.deleteObject(verifyKey);
            if (captcha == null)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            if (!code.equalsIgnoreCase(captcha))
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId)
    {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    public Map<String, Object> loginV2(String code) throws ExecutionException {
        Map<String, Object> map = new HashMap<>();

        UserResp userResp = userDtalkFreeService.getDingtalkAppUser(code);
//        String organizationCode = deptDtalkService.getOrganizationCode(userResp.getEmployeeCode());

//        String organizationName = "";
//        if (StringUtils.isNotBlank(organizationCode)) {
//            organizationName = deptDtalkService.getOrganizationName(organizationCode);
//        }

        SysUser sysUser = userService.selectByEmployeeCode(userResp.getEmployeeCode());
        if (Objects.nonNull(sysUser) && "1".equals(sysUser.getStatus())) {
            throw new RuntimeException("用户已被停用，请联系管理员开通权限");
        }

        if (Objects.isNull(sysUser)) {
            //新增账号
            sysUser = new SysUser();

            sysUser.setUserName(userResp.getLastName());
            sysUser.setNickName(userResp.getLastName());
            sysUser.setRealName(userResp.getLastName());
            sysUser.setEmployeeCode(userResp.getEmployeeCode());
//            sysUser.setOrganizationCode(organizationCode);
//            sysUser.setOrganizationName(organizationName);
            sysUser.setAccountId(userResp.getAccountId());
            sysUser.setPassword("$2a$10$w8B3HFDFoT13.LklwJJj8.SWmp2SSzX1cbXnxHLiaUXEGFYeTqM72");
            sysUser.setPhonenumber(userResp.getPhone());
            sysUser.setDeptId(1L);
            sysUser.setStatus("1");   // 新用户默认停用
//            sysUser.setStatus("0");

            Long[] roleIds = new Long[1];
            roleIds[0] = 2L;

            sysUser.setRoleIds(roleIds);

            userService.insertUser(sysUser);

            sysUser = userService.selectUserById(sysUser.getUserId());

            throw new RuntimeException("用户已被停用，请联系管理员开通权限");
        }

        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(),
                sysUser, permissionService.getMenuPermission(sysUser));
        loginUser.setUser(sysUser);
        loginUser.setUserId(sysUser.getUserId());

        try {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_SUCCESS,
                    MessageUtils.message("user.login.success")));
        } catch (Exception e) {

        }


        // 返回参数
        map.put(Constants.TOKEN, tokenService.createToken(loginUser));
        map.put("status", sysUser.getStatus());
        return map;
    }

    public void loginTest() throws ExecutionException {
        String organizationCode = deptDtalkService.getOrganizationCode("GE_7d5b84a5b6614573a0435c7ce23f160e");
        System.out.println(organizationCode);
    }

    public Map<String, Object> loginByOperateUserId(String operateUserId) {
        if (StringUtils.isBlank(operateUserId)) {
            operateUserId = "********";
        }

        Map<String, Object> map = new HashMap<>();

        //首先通过浙政钉id查找
        SysUser sysUser = sysUserMapper.getByAccountId(operateUserId);
        if (Objects.isNull(sysUser)) {
            sysUser = sysUserMapper.getByPhone(operateUserId);
        }

        if (Objects.isNull(sysUser)) {
            sysUser = sysUserMapper.getByAccountId("********");
        }

        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(),
                sysUser, permissionService.getMenuPermission(sysUser));
        loginUser.setUser(sysUser);
        loginUser.setUserId(sysUser.getUserId());

        try {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_SUCCESS,
                    MessageUtils.message("user.login.success")));
        } catch (Exception e) {

        }

        // 返回参数
        map.put(Constants.TOKEN, tokenService.createToken(loginUser));
        map.put("status", sysUser.getStatus());

        return map;
    }

    public Map<String, Object> loginFree(String code) {
        log.info("loginFree, code:{}", code);

        Map<String, Object> map = new HashMap<>();

        DtalkAppUserVO dtalkAppUserVO = userDtalkFreeService.getDingtalkAppUserByFree(code);

        log.info("loginFree, userService:{}", userService);

        SysUser sysUser = userService.selectByEmployeeCode(dtalkAppUserVO.getEmployeeCode());

        log.info("loginFree, user:{}", JSON.toJSONString(sysUser));

        if (Objects.nonNull(sysUser) && "1".equals(sysUser.getStatus())) {
            throw new RuntimeException("请联系管理员开通账号");
        }

        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(),
                sysUser, permissionService.getMenuPermission(sysUser));
        loginUser.setUser(sysUser);
        loginUser.setUserId(sysUser.getUserId());

        try {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_SUCCESS,
                    MessageUtils.message("user.login.success")));
        } catch (Exception e) {

        }

        // 返回参数
        map.put(Constants.TOKEN, tokenService.createToken(loginUser));
        map.put("status", sysUser.getStatus());

        log.info("loginFree, result:{}", JSON.toJSONString(map));

        return map;
    }

    public Map<String, Object> loginThird(String employeeCode) {
        Map<String, Object> map = new HashMap<>();

        SysUser sysUser = userService.selectByEmployeeCode(employeeCode);

        if (Objects.nonNull(sysUser) && "1".equals(sysUser.getStatus())) {
            throw new RuntimeException("账号不存在");
        }

        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(),
                sysUser, permissionService.getMenuPermission(sysUser));
        loginUser.setUser(sysUser);
        loginUser.setUserId(sysUser.getUserId());

        try {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_SUCCESS,
                    MessageUtils.message("user.login.success")));
        } catch (Exception e) {

        }

        // 返回参数
        map.put(Constants.TOKEN, tokenService.createToken(loginUser));
        map.put("status", sysUser.getStatus());

        return map;
    }
}
