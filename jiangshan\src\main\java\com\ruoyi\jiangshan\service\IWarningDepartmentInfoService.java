package com.ruoyi.jiangshan.service;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.framework.vo.DtalkEmployeeVo;
import com.ruoyi.framework.vo.EmployeeInfo;
import com.ruoyi.framework.vo.OrganizationNodeInfo;
import com.ruoyi.jiangshan.domain.SysArea;
import com.ruoyi.jiangshan.domain.WarningDepartmentInfo;

/**
 * 联动处置中心部门Service接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface IWarningDepartmentInfoService
{
    /**
     * 查询联动处置中心部门
     *
     * @param id 联动处置中心部门主键
     * @return 联动处置中心部门
     */
    public WarningDepartmentInfo selectWarningDepartmentInfoById(Long id);

    /**
     * 查询联动处置中心部门列表
     *
     * @param warningDepartmentInfo 联动处置中心部门
     * @return 联动处置中心部门集合
     */
    public List<WarningDepartmentInfo> selectWarningDepartmentInfoList(WarningDepartmentInfo warningDepartmentInfo);

    /**
     * 新增联动处置中心部门
     *
     * @param warningDepartmentInfo 联动处置中心部门
     * @return 结果
     */
    public int insertWarningDepartmentInfo(WarningDepartmentInfo warningDepartmentInfo);

    /**
     * 修改联动处置中心部门
     *
     * @param warningDepartmentInfo 联动处置中心部门
     * @return 结果
     */
    public int updateWarningDepartmentInfo(WarningDepartmentInfo warningDepartmentInfo);

    /**
     * 批量删除联动处置中心部门
     *
     * @param ids 需要删除的联动处置中心部门主键集合
     * @return 结果
     */
    public int deleteWarningDepartmentInfoByIds(Long[] ids);

    /**
     * 删除联动处置中心部门信息
     *
     * @param id 联动处置中心部门主键
     * @return 结果
     */
    public int deleteWarningDepartmentInfoById(Long id);

    List<OrganizationNodeInfo> getDtalkParentDept();

    List<OrganizationNodeInfo> getDtalkSubDept(String organizationCode);

    List<EmployeeInfo> getDtalkUserV2(String organizationCode, Integer pageNum, Integer pageSize);

    List<DtalkEmployeeVo> getDtalkUser(String organizationCode, Integer pageNum, Integer pageSize, String keywords);

    List<WarningDepartmentInfo> getDeptTree(Long parentId);

    List<SysArea> getAreaTree(Long parentId);

    List<DtalkEmployeeVo> listAllUser(String keywords, String deptId);

    List<WarningDepartmentInfo> listCopyDept(String keywords);
}
