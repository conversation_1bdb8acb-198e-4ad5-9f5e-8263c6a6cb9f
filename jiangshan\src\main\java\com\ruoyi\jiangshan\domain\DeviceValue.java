package com.ruoyi.jiangshan.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 设备实时数据对象 t_device_value
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Data
public class DeviceValue extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 设备id */
    private Long deviceId;

    /** 设备第三方id */
    @Excel(name = "设备ID")
    private String deviceThirdId;

    /** 监测对象 */
    private Long monitorId;

    /** 监测对象 */
    @Excel(name = "监测对象")
    private String monitorObject;

    @Excel(name = "所属场景")
    private String deviceScene;

    @Excel(name = "设备名称")
    private String deviceName;

    @Excel(name = "设备类型")
    private String deviceType;

    /** 监测数据 */
    @Excel(name = "监测数据")
    private String monitorValue;

    /** 监测项-中文名称 */
//    @Excel(name = "监测项-中文名称")
    private String monitorItem;

    /** 监测项-英文名称 */
//    @Excel(name = "监测项-英文名称")
    private String monitorItemEnglish;

    /** 监测单位 */
    @Excel(name = "监测数据单位")
    private String monitorUnit;

    /** 监测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "采集时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date monitorTime;

    /** 靶标 */
//    @Excel(name = "靶标")
    private String target;

    private List<BusinessFile> fileList;

    private Date startTime;

    private Date endTime;

    private String warningContent;
}
