package com.ruoyi.jiangshan.service;

import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.DeviceMonitorRanqijing;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.vo.WarningCountVO;
import com.ruoyi.jiangshan.vo.WarningInfoVO;

import java.util.List;
import java.util.Map;

public interface GasSceneService {
    Map<String, Object> getPiplineCount();

    WarningCountVO getSafetyCount(String dateStr);

    List<WarningEvent> getEvent(String eventName, Integer status, Integer warningLevel);

    List<WarningInfoVO> getWarning(Integer warningLevel);

    Map<String, Object> getMonitor(String dateStr, Long deviceId);

    Map<String, Object> getInspection(Long monitorId);

    List<DeviceMonitor> getMonitorObject(String monitorType);

    String importData(Map<String, List<Map<String, Map<String, String>>>> params);

    List<DeviceMonitorRanqijing> listGasWell();

}
