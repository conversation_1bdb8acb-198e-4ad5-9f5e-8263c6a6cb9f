package com.ruoyi.jiangshan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
    * 设备预警事件-文件
    */
public class BusinessFile {
    /**
    * 主键
    */
    private Long id;

    /**
    * 业务id
    */
    private Long businessId;

    /**
    * 业务类型 1-设备数据 2-事件 3-风险核实 4-隐患处置
    */
    private Integer businessType;

    /**
    * 文件名
    */
    private String fileName;

    /**
    * 文件路径
    */
    private String filePath;

    /**
    * 文件url
    */
    private String fileUrl;

    /**
    * 创建时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
    * 创建人
    */
    private String createBy;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
}
