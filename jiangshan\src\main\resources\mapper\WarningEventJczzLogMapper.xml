<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningEventJczzLogMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningEventJczzLog">
    <!--@mbg.generated-->
    <!--@Table t_warning_event_jczz_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="event_number" jdbcType="VARCHAR" property="eventNumber" />
    <result column="source_way_code" jdbcType="VARCHAR" property="sourceWayCode" />
    <result column="source_way_name" jdbcType="VARCHAR" property="sourceWayName" />
    <result column="ding_flag" jdbcType="BOOLEAN" property="dingFlag" />
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="report_name" jdbcType="VARCHAR" property="reportName" />
    <result column="report_phone" jdbcType="VARCHAR" property="reportPhone" />
    <result column="depart_code" jdbcType="VARCHAR" property="departCode" />
    <result column="depart_name" jdbcType="VARCHAR" property="departName" />
    <result column="event_type_one" jdbcType="VARCHAR" property="eventTypeOne" />
    <result column="event_type_two" jdbcType="VARCHAR" property="eventTypeTwo" />
    <result column="event_type_three" jdbcType="VARCHAR" property="eventTypeThree" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
    <result column="event_level" jdbcType="VARCHAR" property="eventLevel" />
    <result column="emergency_flag" jdbcType="BOOLEAN" property="emergencyFlag" />
    <result column="emergency_degree" jdbcType="VARCHAR" property="emergencyDegree" />
    <result column="great_flag" jdbcType="BOOLEAN" property="greatFlag" />
    <result column="great_degree" jdbcType="VARCHAR" property="greatDegree" />
    <result column="place_flag" jdbcType="BOOLEAN" property="placeFlag" />
    <result column="event_scale" jdbcType="VARCHAR" property="eventScale" />
    <result column="event_num" jdbcType="INTEGER" property="eventNum" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="county_code" jdbcType="VARCHAR" property="countyCode" />
    <result column="county_name" jdbcType="VARCHAR" property="countyName" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="street_name" jdbcType="VARCHAR" property="streetName" />
    <result column="village_code" jdbcType="VARCHAR" property="villageCode" />
    <result column="village_name" jdbcType="VARCHAR" property="villageName" />
    <result column="gridding_code" jdbcType="VARCHAR" property="griddingCode" />
    <result column="gridding_name" jdbcType="VARCHAR" property="griddingName" />
    <result column="event_time" jdbcType="VARCHAR" property="eventTime" />
    <result column="report_time" jdbcType="VARCHAR" property="reportTime" />
    <result column="closing_time" jdbcType="VARCHAR" property="closingTime" />
    <result column="accomplish_time" jdbcType="VARCHAR" property="accomplishTime" />
    <result column="overdue_flag" jdbcType="BOOLEAN" property="overdueFlag" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="app_code" jdbcType="VARCHAR" property="appCode" />
    <result column="hang_flag" jdbcType="BOOLEAN" property="hangFlag" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="attachments" jdbcType="LONGVARCHAR" property="attachments" />
    <result column="event_flow_list" jdbcType="LONGVARCHAR" property="eventFlowList" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, event_number, source_way_code, source_way_name, ding_flag, report_code, report_name, 
    report_phone, depart_code, depart_name, event_type_one, event_type_two, event_type_three, 
    title, content, address, longitude, latitude, event_level, emergency_flag, emergency_degree, 
    great_flag, great_degree, place_flag, event_scale, event_num, city_code, county_code, 
    county_name, street_code, street_name, village_code, village_name, gridding_code, 
    gridding_name, event_time, report_time, closing_time, accomplish_time, overdue_flag, 
    `state`, app_code, hang_flag, memo, attachments, event_flow_list
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_warning_event_jczz_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_event_jczz_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventJczzLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_jczz_log (event_number, source_way_code, source_way_name, 
      ding_flag, report_code, report_name, 
      report_phone, depart_code, depart_name, 
      event_type_one, event_type_two, event_type_three, 
      title, content, address, 
      longitude, latitude, event_level, 
      emergency_flag, emergency_degree, great_flag, 
      great_degree, place_flag, event_scale, 
      event_num, city_code, county_code, 
      county_name, street_code, street_name, 
      village_code, village_name, gridding_code, 
      gridding_name, event_time, report_time, 
      closing_time, accomplish_time, overdue_flag, 
      `state`, app_code, hang_flag, 
      memo, attachments, event_flow_list
      )
    values (#{eventNumber,jdbcType=VARCHAR}, #{sourceWayCode,jdbcType=VARCHAR}, #{sourceWayName,jdbcType=VARCHAR}, 
      #{dingFlag,jdbcType=BOOLEAN}, #{reportCode,jdbcType=VARCHAR}, #{reportName,jdbcType=VARCHAR}, 
      #{reportPhone,jdbcType=VARCHAR}, #{departCode,jdbcType=VARCHAR}, #{departName,jdbcType=VARCHAR}, 
      #{eventTypeOne,jdbcType=VARCHAR}, #{eventTypeTwo,jdbcType=VARCHAR}, #{eventTypeThree,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}, #{eventLevel,jdbcType=VARCHAR}, 
      #{emergencyFlag,jdbcType=BOOLEAN}, #{emergencyDegree,jdbcType=VARCHAR}, #{greatFlag,jdbcType=BOOLEAN}, 
      #{greatDegree,jdbcType=VARCHAR}, #{placeFlag,jdbcType=BOOLEAN}, #{eventScale,jdbcType=VARCHAR}, 
      #{eventNum,jdbcType=INTEGER}, #{cityCode,jdbcType=VARCHAR}, #{countyCode,jdbcType=VARCHAR}, 
      #{countyName,jdbcType=VARCHAR}, #{streetCode,jdbcType=VARCHAR}, #{streetName,jdbcType=VARCHAR}, 
      #{villageCode,jdbcType=VARCHAR}, #{villageName,jdbcType=VARCHAR}, #{griddingCode,jdbcType=VARCHAR}, 
      #{griddingName,jdbcType=VARCHAR}, #{eventTime,jdbcType=VARCHAR}, #{reportTime,jdbcType=VARCHAR}, 
      #{closingTime,jdbcType=VARCHAR}, #{accomplishTime,jdbcType=VARCHAR}, #{overdueFlag,jdbcType=BOOLEAN}, 
      #{state,jdbcType=VARCHAR}, #{appCode,jdbcType=VARCHAR}, #{hangFlag,jdbcType=BOOLEAN}, 
      #{memo,jdbcType=VARCHAR}, #{attachments,jdbcType=LONGVARCHAR}, #{eventFlowList,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventJczzLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_jczz_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="eventNumber != null">
        event_number,
      </if>
      <if test="sourceWayCode != null">
        source_way_code,
      </if>
      <if test="sourceWayName != null">
        source_way_name,
      </if>
      <if test="dingFlag != null">
        ding_flag,
      </if>
      <if test="reportCode != null">
        report_code,
      </if>
      <if test="reportName != null">
        report_name,
      </if>
      <if test="reportPhone != null">
        report_phone,
      </if>
      <if test="departCode != null">
        depart_code,
      </if>
      <if test="departName != null">
        depart_name,
      </if>
      <if test="eventTypeOne != null">
        event_type_one,
      </if>
      <if test="eventTypeTwo != null">
        event_type_two,
      </if>
      <if test="eventTypeThree != null">
        event_type_three,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="eventLevel != null">
        event_level,
      </if>
      <if test="emergencyFlag != null">
        emergency_flag,
      </if>
      <if test="emergencyDegree != null">
        emergency_degree,
      </if>
      <if test="greatFlag != null">
        great_flag,
      </if>
      <if test="greatDegree != null">
        great_degree,
      </if>
      <if test="placeFlag != null">
        place_flag,
      </if>
      <if test="eventScale != null">
        event_scale,
      </if>
      <if test="eventNum != null">
        event_num,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="countyCode != null">
        county_code,
      </if>
      <if test="countyName != null">
        county_name,
      </if>
      <if test="streetCode != null">
        street_code,
      </if>
      <if test="streetName != null">
        street_name,
      </if>
      <if test="villageCode != null">
        village_code,
      </if>
      <if test="villageName != null">
        village_name,
      </if>
      <if test="griddingCode != null">
        gridding_code,
      </if>
      <if test="griddingName != null">
        gridding_name,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
      <if test="reportTime != null">
        report_time,
      </if>
      <if test="closingTime != null">
        closing_time,
      </if>
      <if test="accomplishTime != null">
        accomplish_time,
      </if>
      <if test="overdueFlag != null">
        overdue_flag,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="appCode != null">
        app_code,
      </if>
      <if test="hangFlag != null">
        hang_flag,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="attachments != null">
        attachments,
      </if>
      <if test="eventFlowList != null">
        event_flow_list,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="eventNumber != null">
        #{eventNumber,jdbcType=VARCHAR},
      </if>
      <if test="sourceWayCode != null">
        #{sourceWayCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceWayName != null">
        #{sourceWayName,jdbcType=VARCHAR},
      </if>
      <if test="dingFlag != null">
        #{dingFlag,jdbcType=BOOLEAN},
      </if>
      <if test="reportCode != null">
        #{reportCode,jdbcType=VARCHAR},
      </if>
      <if test="reportName != null">
        #{reportName,jdbcType=VARCHAR},
      </if>
      <if test="reportPhone != null">
        #{reportPhone,jdbcType=VARCHAR},
      </if>
      <if test="departCode != null">
        #{departCode,jdbcType=VARCHAR},
      </if>
      <if test="departName != null">
        #{departName,jdbcType=VARCHAR},
      </if>
      <if test="eventTypeOne != null">
        #{eventTypeOne,jdbcType=VARCHAR},
      </if>
      <if test="eventTypeTwo != null">
        #{eventTypeTwo,jdbcType=VARCHAR},
      </if>
      <if test="eventTypeThree != null">
        #{eventTypeThree,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="eventLevel != null">
        #{eventLevel,jdbcType=VARCHAR},
      </if>
      <if test="emergencyFlag != null">
        #{emergencyFlag,jdbcType=BOOLEAN},
      </if>
      <if test="emergencyDegree != null">
        #{emergencyDegree,jdbcType=VARCHAR},
      </if>
      <if test="greatFlag != null">
        #{greatFlag,jdbcType=BOOLEAN},
      </if>
      <if test="greatDegree != null">
        #{greatDegree,jdbcType=VARCHAR},
      </if>
      <if test="placeFlag != null">
        #{placeFlag,jdbcType=BOOLEAN},
      </if>
      <if test="eventScale != null">
        #{eventScale,jdbcType=VARCHAR},
      </if>
      <if test="eventNum != null">
        #{eventNum,jdbcType=INTEGER},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="countyCode != null">
        #{countyCode,jdbcType=VARCHAR},
      </if>
      <if test="countyName != null">
        #{countyName,jdbcType=VARCHAR},
      </if>
      <if test="streetCode != null">
        #{streetCode,jdbcType=VARCHAR},
      </if>
      <if test="streetName != null">
        #{streetName,jdbcType=VARCHAR},
      </if>
      <if test="villageCode != null">
        #{villageCode,jdbcType=VARCHAR},
      </if>
      <if test="villageName != null">
        #{villageName,jdbcType=VARCHAR},
      </if>
      <if test="griddingCode != null">
        #{griddingCode,jdbcType=VARCHAR},
      </if>
      <if test="griddingName != null">
        #{griddingName,jdbcType=VARCHAR},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        #{reportTime,jdbcType=VARCHAR},
      </if>
      <if test="closingTime != null">
        #{closingTime,jdbcType=VARCHAR},
      </if>
      <if test="accomplishTime != null">
        #{accomplishTime,jdbcType=VARCHAR},
      </if>
      <if test="overdueFlag != null">
        #{overdueFlag,jdbcType=BOOLEAN},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="appCode != null">
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="hangFlag != null">
        #{hangFlag,jdbcType=BOOLEAN},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="attachments != null">
        #{attachments,jdbcType=LONGVARCHAR},
      </if>
      <if test="eventFlowList != null">
        #{eventFlowList,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningEventJczzLog">
    <!--@mbg.generated-->
    update t_warning_event_jczz_log
    <set>
      <if test="eventNumber != null">
        event_number = #{eventNumber,jdbcType=VARCHAR},
      </if>
      <if test="sourceWayCode != null">
        source_way_code = #{sourceWayCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceWayName != null">
        source_way_name = #{sourceWayName,jdbcType=VARCHAR},
      </if>
      <if test="dingFlag != null">
        ding_flag = #{dingFlag,jdbcType=BOOLEAN},
      </if>
      <if test="reportCode != null">
        report_code = #{reportCode,jdbcType=VARCHAR},
      </if>
      <if test="reportName != null">
        report_name = #{reportName,jdbcType=VARCHAR},
      </if>
      <if test="reportPhone != null">
        report_phone = #{reportPhone,jdbcType=VARCHAR},
      </if>
      <if test="departCode != null">
        depart_code = #{departCode,jdbcType=VARCHAR},
      </if>
      <if test="departName != null">
        depart_name = #{departName,jdbcType=VARCHAR},
      </if>
      <if test="eventTypeOne != null">
        event_type_one = #{eventTypeOne,jdbcType=VARCHAR},
      </if>
      <if test="eventTypeTwo != null">
        event_type_two = #{eventTypeTwo,jdbcType=VARCHAR},
      </if>
      <if test="eventTypeThree != null">
        event_type_three = #{eventTypeThree,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="eventLevel != null">
        event_level = #{eventLevel,jdbcType=VARCHAR},
      </if>
      <if test="emergencyFlag != null">
        emergency_flag = #{emergencyFlag,jdbcType=BOOLEAN},
      </if>
      <if test="emergencyDegree != null">
        emergency_degree = #{emergencyDegree,jdbcType=VARCHAR},
      </if>
      <if test="greatFlag != null">
        great_flag = #{greatFlag,jdbcType=BOOLEAN},
      </if>
      <if test="greatDegree != null">
        great_degree = #{greatDegree,jdbcType=VARCHAR},
      </if>
      <if test="placeFlag != null">
        place_flag = #{placeFlag,jdbcType=BOOLEAN},
      </if>
      <if test="eventScale != null">
        event_scale = #{eventScale,jdbcType=VARCHAR},
      </if>
      <if test="eventNum != null">
        event_num = #{eventNum,jdbcType=INTEGER},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="countyCode != null">
        county_code = #{countyCode,jdbcType=VARCHAR},
      </if>
      <if test="countyName != null">
        county_name = #{countyName,jdbcType=VARCHAR},
      </if>
      <if test="streetCode != null">
        street_code = #{streetCode,jdbcType=VARCHAR},
      </if>
      <if test="streetName != null">
        street_name = #{streetName,jdbcType=VARCHAR},
      </if>
      <if test="villageCode != null">
        village_code = #{villageCode,jdbcType=VARCHAR},
      </if>
      <if test="villageName != null">
        village_name = #{villageName,jdbcType=VARCHAR},
      </if>
      <if test="griddingCode != null">
        gridding_code = #{griddingCode,jdbcType=VARCHAR},
      </if>
      <if test="griddingName != null">
        gridding_name = #{griddingName,jdbcType=VARCHAR},
      </if>
      <if test="eventTime != null">
        event_time = #{eventTime,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        report_time = #{reportTime,jdbcType=VARCHAR},
      </if>
      <if test="closingTime != null">
        closing_time = #{closingTime,jdbcType=VARCHAR},
      </if>
      <if test="accomplishTime != null">
        accomplish_time = #{accomplishTime,jdbcType=VARCHAR},
      </if>
      <if test="overdueFlag != null">
        overdue_flag = #{overdueFlag,jdbcType=BOOLEAN},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=VARCHAR},
      </if>
      <if test="appCode != null">
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="hangFlag != null">
        hang_flag = #{hangFlag,jdbcType=BOOLEAN},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="attachments != null">
        attachments = #{attachments,jdbcType=LONGVARCHAR},
      </if>
      <if test="eventFlowList != null">
        event_flow_list = #{eventFlowList,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningEventJczzLog">
    <!--@mbg.generated-->
    update t_warning_event_jczz_log
    set event_number = #{eventNumber,jdbcType=VARCHAR},
      source_way_code = #{sourceWayCode,jdbcType=VARCHAR},
      source_way_name = #{sourceWayName,jdbcType=VARCHAR},
      ding_flag = #{dingFlag,jdbcType=BOOLEAN},
      report_code = #{reportCode,jdbcType=VARCHAR},
      report_name = #{reportName,jdbcType=VARCHAR},
      report_phone = #{reportPhone,jdbcType=VARCHAR},
      depart_code = #{departCode,jdbcType=VARCHAR},
      depart_name = #{departName,jdbcType=VARCHAR},
      event_type_one = #{eventTypeOne,jdbcType=VARCHAR},
      event_type_two = #{eventTypeTwo,jdbcType=VARCHAR},
      event_type_three = #{eventTypeThree,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      event_level = #{eventLevel,jdbcType=VARCHAR},
      emergency_flag = #{emergencyFlag,jdbcType=BOOLEAN},
      emergency_degree = #{emergencyDegree,jdbcType=VARCHAR},
      great_flag = #{greatFlag,jdbcType=BOOLEAN},
      great_degree = #{greatDegree,jdbcType=VARCHAR},
      place_flag = #{placeFlag,jdbcType=BOOLEAN},
      event_scale = #{eventScale,jdbcType=VARCHAR},
      event_num = #{eventNum,jdbcType=INTEGER},
      city_code = #{cityCode,jdbcType=VARCHAR},
      county_code = #{countyCode,jdbcType=VARCHAR},
      county_name = #{countyName,jdbcType=VARCHAR},
      street_code = #{streetCode,jdbcType=VARCHAR},
      street_name = #{streetName,jdbcType=VARCHAR},
      village_code = #{villageCode,jdbcType=VARCHAR},
      village_name = #{villageName,jdbcType=VARCHAR},
      gridding_code = #{griddingCode,jdbcType=VARCHAR},
      gridding_name = #{griddingName,jdbcType=VARCHAR},
      event_time = #{eventTime,jdbcType=VARCHAR},
      report_time = #{reportTime,jdbcType=VARCHAR},
      closing_time = #{closingTime,jdbcType=VARCHAR},
      accomplish_time = #{accomplishTime,jdbcType=VARCHAR},
      overdue_flag = #{overdueFlag,jdbcType=BOOLEAN},
      `state` = #{state,jdbcType=VARCHAR},
      app_code = #{appCode,jdbcType=VARCHAR},
      hang_flag = #{hangFlag,jdbcType=BOOLEAN},
      memo = #{memo,jdbcType=VARCHAR},
      attachments = #{attachments,jdbcType=LONGVARCHAR},
      event_flow_list = #{eventFlowList,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_event_jczz_log
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="event_number = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="source_way_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sourceWayCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="source_way_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sourceWayName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ding_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dingFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="report_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reportCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="report_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reportName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="report_phone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reportPhone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="depart_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.departCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="depart_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.departName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_type_one = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventTypeOne,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_type_two = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventTypeTwo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_type_three = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventTypeThree,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="title = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.title,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.content,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.address,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="longitude = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.longitude,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="latitude = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.latitude,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_level = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventLevel,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="emergency_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.emergencyFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="emergency_degree = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.emergencyDegree,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="great_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.greatFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="great_degree = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.greatDegree,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="place_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.placeFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="event_scale = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventScale,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="city_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.cityCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="county_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.countyCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="county_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.countyName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="street_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.streetCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="street_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.streetName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="village_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.villageCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="village_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.villageName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="gridding_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.griddingCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="gridding_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.griddingName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="report_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reportTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="closing_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.closingTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="accomplish_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.accomplishTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="overdue_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.overdueFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="`state` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.state,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="app_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="hang_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.hangFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="memo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.memo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="attachments = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.attachments,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_flow_list = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventFlowList,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_jczz_log
    (event_number, source_way_code, source_way_name, ding_flag, report_code, report_name, 
      report_phone, depart_code, depart_name, event_type_one, event_type_two, event_type_three, 
      title, content, address, longitude, latitude, event_level, emergency_flag, emergency_degree, 
      great_flag, great_degree, place_flag, event_scale, event_num, city_code, county_code, 
      county_name, street_code, street_name, village_code, village_name, gridding_code, 
      gridding_name, event_time, report_time, closing_time, accomplish_time, overdue_flag, 
      `state`, app_code, hang_flag, memo, attachments, event_flow_list)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.eventNumber,jdbcType=VARCHAR}, #{item.sourceWayCode,jdbcType=VARCHAR}, #{item.sourceWayName,jdbcType=VARCHAR}, 
        #{item.dingFlag,jdbcType=BOOLEAN}, #{item.reportCode,jdbcType=VARCHAR}, #{item.reportName,jdbcType=VARCHAR}, 
        #{item.reportPhone,jdbcType=VARCHAR}, #{item.departCode,jdbcType=VARCHAR}, #{item.departName,jdbcType=VARCHAR}, 
        #{item.eventTypeOne,jdbcType=VARCHAR}, #{item.eventTypeTwo,jdbcType=VARCHAR}, #{item.eventTypeThree,jdbcType=VARCHAR}, 
        #{item.title,jdbcType=VARCHAR}, #{item.content,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR}, 
        #{item.longitude,jdbcType=VARCHAR}, #{item.latitude,jdbcType=VARCHAR}, #{item.eventLevel,jdbcType=VARCHAR}, 
        #{item.emergencyFlag,jdbcType=BOOLEAN}, #{item.emergencyDegree,jdbcType=VARCHAR}, 
        #{item.greatFlag,jdbcType=BOOLEAN}, #{item.greatDegree,jdbcType=VARCHAR}, #{item.placeFlag,jdbcType=BOOLEAN}, 
        #{item.eventScale,jdbcType=VARCHAR}, #{item.eventNum,jdbcType=INTEGER}, #{item.cityCode,jdbcType=VARCHAR}, 
        #{item.countyCode,jdbcType=VARCHAR}, #{item.countyName,jdbcType=VARCHAR}, #{item.streetCode,jdbcType=VARCHAR}, 
        #{item.streetName,jdbcType=VARCHAR}, #{item.villageCode,jdbcType=VARCHAR}, #{item.villageName,jdbcType=VARCHAR}, 
        #{item.griddingCode,jdbcType=VARCHAR}, #{item.griddingName,jdbcType=VARCHAR}, #{item.eventTime,jdbcType=VARCHAR}, 
        #{item.reportTime,jdbcType=VARCHAR}, #{item.closingTime,jdbcType=VARCHAR}, #{item.accomplishTime,jdbcType=VARCHAR}, 
        #{item.overdueFlag,jdbcType=BOOLEAN}, #{item.state,jdbcType=VARCHAR}, #{item.appCode,jdbcType=VARCHAR}, 
        #{item.hangFlag,jdbcType=BOOLEAN}, #{item.memo,jdbcType=VARCHAR}, #{item.attachments,jdbcType=LONGVARCHAR}, 
        #{item.eventFlowList,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>