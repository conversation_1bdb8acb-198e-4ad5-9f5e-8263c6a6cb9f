package com.ruoyi.jiangshan.vo.compre;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class DeviceMonitorShuikuVO {

    private Long monitorId;

    private String monitorName;

    private String newType;

    /**
     * 实时水位
     */
    private String currentWaterLevel;

    /**
     * 警戒水位
     */
    private String warningWaterLevel;

    /**
     * 差值
     */
    private String subLevel;

    /**
     * 数据上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitorTime;
}
