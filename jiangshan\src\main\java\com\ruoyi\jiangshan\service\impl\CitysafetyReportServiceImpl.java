package com.ruoyi.jiangshan.service.impl;

import java.awt.*;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.NumberFormat;
import java.util.*;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import com.aspose.words.Document;
import com.aspose.words.FontSettings;
import com.aspose.words.SaveFormat;
import com.google.common.collect.Lists;
import com.ruoyi.common.dto.TimeRange;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.enums.WarningCenterGenerateType;
import com.ruoyi.jiangshan.enums.WarningCenterReportType;
import com.ruoyi.jiangshan.enums.WarningLevelType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.util.FileExtendUtil;
import com.ruoyi.jiangshan.util.GenerateChartUtil;
import com.ruoyi.jiangshan.util.JFreeChartUtil;
import com.ruoyi.jiangshan.vo.BusinessCountVO;
import com.ruoyi.jiangshan.vo.CitySafetyLineVO;
import com.ruoyi.jiangshan.vo.WarningReportDeviceVO;
import com.ruoyi.jiangshan.vo.WarningReportVO;
import com.ruoyi.jiangshan.vo.cityrisk.WarningEventReportVO;
import com.ruoyi.jiangshan.vo.street.DeviceStreetVO;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ruoyi.jiangshan.service.ICitysafetyReportService;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

/**
 * 城市安全体检报告管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@Service
@Slf4j
public class CitysafetyReportServiceImpl implements ICitysafetyReportService
{
    @Autowired
    private CitysafetyReportMapper citysafetyReportMapper;
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private DeviceValueMapper deviceValueMapper;
    @Autowired
    private CitysafetyPeriodsMapper citysafetyPeriodsMapper;
    @Autowired
    private Configuration configuration;
    @Autowired
    private DeviceStreetMapper deviceStreetMapper;

    @Value("${file.tmpUrl}")
    private String fileTmpUrl;
    @Value("${file.fontUrl}")
    private String fontUrl;

    /**
     * 查询城市安全体检报告管理
     *
     * @param id 城市安全体检报告管理主键
     * @return 城市安全体检报告管理
     */
    @Override
    public CitysafetyReport selectCitysafetyReportById(Long id)
    {
        return citysafetyReportMapper.selectCitysafetyReportById(id);
    }

    /**
     * 查询城市安全体检报告管理列表
     *
     * @param citysafetyReport 城市安全体检报告管理
     * @return 城市安全体检报告管理
     */
    @Override
    public List<CitysafetyReport> selectCitysafetyReportList(CitysafetyReport citysafetyReport)
    {
        return citysafetyReportMapper.selectCitysafetyReportList(citysafetyReport);
    }

    /**
     * 新增城市安全体检报告管理
     *
     * @param citysafetyReport 城市安全体检报告管理
     * @return 结果
     */
    @Override
    public int insertCitysafetyReport(CitysafetyReport citysafetyReport)
    {
        citysafetyReport.setCreateTime(DateUtils.getNowDate());
        return citysafetyReportMapper.insertCitysafetyReport(citysafetyReport);
    }

    /**
     * 修改城市安全体检报告管理
     *
     * @param citysafetyReport 城市安全体检报告管理
     * @return 结果
     */
    @Override
    public int updateCitysafetyReport(CitysafetyReport citysafetyReport)
    {
        return citysafetyReportMapper.updateCitysafetyReport(citysafetyReport);
    }

    /**
     * 批量删除城市安全体检报告管理
     *
     * @param ids 需要删除的城市安全体检报告管理主键
     * @return 结果
     */
    @Override
    public int deleteCitysafetyReportByIds(Long[] ids)
    {
        return citysafetyReportMapper.deleteCitysafetyReportByIds(ids);
    }

    /**
     * 删除城市安全体检报告管理信息
     *
     * @param id 城市安全体检报告管理主键
     * @return 结果
     */
    @Override
    public int deleteCitysafetyReportById(Long id)
    {
        return citysafetyReportMapper.deleteCitysafetyReportById(id);
    }

    @Override
    public List<WarningReportVO> getInfo(Long id, CitysafetyReport existReport) {
        if (Objects.isNull(existReport)) {
            existReport = citysafetyReportMapper.selectCitysafetyReportById(id);
            if (Objects.isNull(existReport)) {
                throw new RuntimeException("报告不存在");
            }
        }

        String[] sceneList = StringUtils.split(existReport.getReportScene(), ",");

        List<WarningReportVO> reportList = initReportList(sceneList, existReport);

        Map<String, WarningReportVO> sceneReportMap = reportList.stream()
                .collect(Collectors.toMap(WarningReportVO::getScene, Function.identity()));


        Integer reportType = existReport.getReportType();

        List<BusinessCountVO> countVOList = warningInfoMapper
                .countBySceneListAndDate(sceneList, existReport.getMonitorStartTime(), existReport.getMonitorEndTime());

        if (CollectionUtils.isNotEmpty(countVOList)) {
            Map<String, List<BusinessCountVO>> sceneCountMap = countVOList.stream()
                    .collect(Collectors.groupingBy(BusinessCountVO::getTmpKey));

            for (String scene : sceneList) {
                List<BusinessCountVO> sceneCountList = sceneCountMap.get(scene);
                if (CollectionUtils.isEmpty(sceneCountList)) {
                    continue;
                }

                for (BusinessCountVO businessCountVO : sceneCountList) {
                    WarningReportVO reportVO = sceneReportMap.get(scene);

                    if ("1".equals(businessCountVO.getKey())) {
                        reportVO.setWarningLevel01(businessCountVO.getValue());
                    } else if ("2".equals(businessCountVO.getKey())) {
                        reportVO.setWarningLevel02(businessCountVO.getValue());
                    } else if ("3".equals(businessCountVO.getKey())) {
                        reportVO.setWarningLevel03(businessCountVO.getValue());
                    } else if ("4".equals(businessCountVO.getKey())) {
                        reportVO.setWarningLevel04(businessCountVO.getValue());
                    } else if ("5".equals(businessCountVO.getKey())) {
                        reportVO.setWarningLevel05(businessCountVO.getValue());
                    }
                }
            }
        }

        DeviceStreet deviceStreet = deviceStreetMapper.selectByPrimaryKey(existReport.getStreetId());
        String geoStr = null;
        if (Objects.nonNull(deviceStreet)) {
            geoStr = deviceStreet.getGeometry();
        }

        List<WarningReportDeviceVO> reportDeviceVOList = deviceValueMapper.listReport(sceneList,
                existReport.getMonitorStartTime(), existReport.getMonitorEndTime(), geoStr);
        if (CollectionUtils.isNotEmpty(reportDeviceVOList)) {
            Map<String, List<WarningReportDeviceVO>> reportMap = reportDeviceVOList.stream()
                    .collect(Collectors.groupingBy(WarningReportDeviceVO::getDeviceScene));

            for (String scene : sceneList) {
                List<WarningReportDeviceVO> deviceVOList = reportMap.get(scene);
                if (CollectionUtils.isEmpty(deviceVOList)) {
                    continue;
                }

                WarningReportVO reportVO = sceneReportMap.get(scene);
                reportVO.setDeviceList(deviceVOList);
            }
        }


        List<WarningInfo> warningInfoList = warningInfoMapper
                .listReportLine(sceneList, existReport.getMonitorStartTime(), existReport.getMonitorEndTime(), Lists.newArrayList(5));

        Map<String, List<WarningInfo>> warningMap = warningInfoList.stream()
                .collect(Collectors.groupingBy(WarningInfo::getWarningScene));

        //获取坐标轴x轴
        List<TimeRange> timeRangeList = DateUtils.generateDateRanges(existReport.getMonitorStartTime(),
                existReport.getMonitorEndTime(), reportType);

        Map<String, List<CitySafetyLineVO>> resultMap = new HashMap<>();
        for (WarningReportVO reportVO : reportList) {
            List<CitySafetyLineVO> lineList = Lists.newArrayList();

            for (TimeRange timeRange : timeRangeList) {
                CitySafetyLineVO lineVO = new CitySafetyLineVO();
                lineVO.setName(timeRange.getLabel());
                lineVO.setStartTime(timeRange.getStartTime());
                lineVO.setEndTime(timeRange.getEndTime());
                lineList.add(lineVO);
            }

            reportVO.setLineList(lineList);

            resultMap.put(reportVO.getScene(), lineList);

            List<WarningInfo> warningInfoOneList = warningMap.get(reportVO.getScene());

            if (CollectionUtils.isNotEmpty(warningInfoOneList)) {
                List<WarningEventReportVO> reportVOList = warningInfoOneList.stream()
                        .filter(warningInfo -> warningInfo.getWarningLevel() == 5)
                        .map(this::convertWarningEventReportVO)
                        .collect(Collectors.toList());

                List<WarningEventReportVO> modelList = warningInfoOneList.stream()
                        .filter(warningInfo -> Lists.newArrayList(1, 2, 3, 4).contains(warningInfo.getWarningLevel()))
                        .map(this::convertWarningEventReportVO)
                        .collect(Collectors.toList());

                reportVO.setWarningList(reportVOList);
                reportVO.setModelList(modelList);
            }
        }

        for (String scene : sceneList) {
            List<WarningInfo> warningInfoOneList = warningMap.get(scene);

            if (CollectionUtils.isEmpty(warningInfoOneList)) {
                continue;
            }

            List<CitySafetyLineVO> lineVOList = resultMap.get(scene);
            for (CitySafetyLineVO citySafetyLineVO : lineVOList) {
                for (WarningInfo warningInfo : warningInfoOneList) {
                    if (citySafetyLineVO.getStartTime().getTime() <= warningInfo.getWarningTime().getTime()
                            && citySafetyLineVO.getEndTime().getTime() >= warningInfo.getWarningTime().getTime()) {
                        if (Objects.equals(WarningLevelType.WARNING_LEVEL_1.getCode(), warningInfo.getWarningLevel())) {
                            citySafetyLineVO.setWarningLevel01(citySafetyLineVO.getWarningLevel01() + 1);
                        } else if (Objects.equals(WarningLevelType.WARNING_LEVEL_2.getCode(), warningInfo.getWarningLevel())) {
                            citySafetyLineVO.setWarningLevel02(citySafetyLineVO.getWarningLevel02() + 1);
                        } else if (Objects.equals(WarningLevelType.WARNING_LEVEL_3.getCode(), warningInfo.getWarningLevel())) {
                            citySafetyLineVO.setWarningLevel03(citySafetyLineVO.getWarningLevel03() + 1);
                        } else if (Objects.equals(WarningLevelType.WARNING_LEVEL_4.getCode(), warningInfo.getWarningLevel())) {
                            citySafetyLineVO.setWarningLevel04(citySafetyLineVO.getWarningLevel04() + 1);
                        } else if (Objects.equals(WarningLevelType.WARNING_LEVEL_5.getCode(), warningInfo.getWarningLevel())) {
                            citySafetyLineVO.setWarningLevel05(citySafetyLineVO.getWarningLevel05() + 1);
                        }
                    }
                }
            }
        }

        return reportList;
    }

    private WarningEventReportVO convertWarningEventReportVO(WarningInfo warningInfo) {
        WarningEventReportVO warningEventReportVO = new WarningEventReportVO();
        warningEventReportVO.setEventAddress(warningInfo.getWarningAddress());
        warningEventReportVO.setEventLevel(WarningLevelType.getByCode(warningInfo.getWarningLevel()));
        warningEventReportVO.setEventName(warningInfo.getWarningName());
        warningEventReportVO.setEventType(warningInfo.getWarningScene());
        warningEventReportVO.setWarningTime(DateUtils.formatDateSecond(warningInfo.getWarningTime()));
        if (Objects.nonNull(warningInfo.getSuperviseFlag())) {
            warningEventReportVO.setCopyFlag(warningInfo.getSuperviseFlag() == 1 ? "是" : "否");
        } else {
            warningEventReportVO.setCopyFlag("否");
        }
        if (Objects.nonNull(warningInfo.getOverFlag())) {
            warningEventReportVO.setOverFlag(warningInfo.getOverFlag() == 1 ? "是" : "否");
        } else {
            warningEventReportVO.setOverFlag("否");
        }

        return warningEventReportVO;
    }

    @Override
    public int generateReport(CitysafetyReport citysafetyReport) {
        createMomentReport(citysafetyReport);

        citysafetyReportMapper.insertCitysafetyReport(citysafetyReport);

        return 1;
    }

    private CitysafetyReport createMomentReport(CitysafetyReport citysafetyReport) {
        Calendar calendar = Calendar.getInstance();

        Integer generateType = WarningCenterGenerateType.TYPE_01.getCode();

        String sceneStr = citysafetyReport.getReportScene();
        if (StringUtils.isBlank(citysafetyReport.getReportScene())) {
            sceneStr = StringUtils.join(MonitorSceneType.listAllScene(), ",");
        }

        String nameStr = getNameStr(sceneStr);

        CitysafetyPeriods citysafetyPeriods = getCitysafetyPeriods(calendar, generateType, sceneStr);

        citysafetyReport.setReportPeriods(getReportPeriodsStr(citysafetyPeriods));
        citysafetyReport.setReportName("城市安全运行瞬报份-" + nameStr);
        citysafetyReport.setReportScene(sceneStr);
        citysafetyReport.setReportType(WarningCenterReportType.TYPE_00.getCode());
        citysafetyReport.setGenerateType(generateType);
        citysafetyReport.setMonitorEndTime(calendar.getTime());
        citysafetyReport.setCreateTime(calendar.getTime());
        citysafetyReport.setCreateBy(SecurityUtils.getUsername());

        return citysafetyReport;
    }

    private String getNameStr(String sceneStr) {
        String nameStr = sceneStr;
        if (sceneStr.length() > 30) {
            nameStr = "全部场景";
        }

        return nameStr;
    }

    private String getReportPeriodsStr(CitysafetyPeriods citysafetyPeriods) {
        Integer periods = citysafetyPeriods.getNextPeriods();

        NumberFormat formatter = NumberFormat.getInstance();
        formatter.setMinimumIntegerDigits(3);
        formatter.setGroupingUsed(false);
        String formattedNumber = formatter.format(periods);

        return citysafetyPeriods.getYear() + formattedNumber + "期";
    }


    @Override
    public CitysafetyPeriods getCitysafetyPeriods(Calendar calendar, Integer generateType, String scene) {
        Integer length = 1;
        if (generateType == 2) {
            String[] split = StringUtils.split(scene, ",");

            length = split.length;
        }

        int year = calendar.get(Calendar.YEAR);

        CitysafetyPeriods citysafetyPeriods = citysafetyPeriodsMapper.getByYear(year);
        if (Objects.isNull(citysafetyPeriods)) {
            CitysafetyPeriods newPeriod = new CitysafetyPeriods();
            newPeriod.setYear(year + "");
            newPeriod.setPeriods(length);
            newPeriod.setCurrentPeriods(0);

            citysafetyPeriodsMapper.insertSelective(newPeriod);

            return newPeriod;
        } else {
            CitysafetyPeriods exisitPeriods = new CitysafetyPeriods();
            exisitPeriods.setId(citysafetyPeriods.getId());
            exisitPeriods.setPeriods(citysafetyPeriods.getPeriods() + length);

            citysafetyPeriodsMapper.updateByPrimaryKeySelective(exisitPeriods);

            citysafetyPeriods.setCurrentPeriods(citysafetyPeriods.getPeriods());

            return citysafetyPeriods;
        }
    }

    @Override
    public List<WarningReportVO> previewReport(CitysafetyReport citysafetyReport) {
        createMomentReport(citysafetyReport);

        return getInfo(null, citysafetyReport);
    }

    @Override
    public void createPdf(Long id, HttpServletResponse response) {
        BufferedWriter bufferedWriter = null;
//        File file = null;
        String currentFilePath = fileTmpUrl + "/" + IdUtils.fastSimpleUUID() + ".doc";
        log.info("FileUtils.getTempDirectoryPath():" + FileUtils.getTempDirectoryPath());
        try {
            // 获取模板文件
            Template template = configuration.getTemplate("report.ftl", "UTF-8");
            //指定输出流到的位置
//            bufferedWriter = new BufferedWriter(
//                    new OutputStreamWriter(new FileOutputStream(currentFilePath), StandardCharsets.UTF_8));

            Map<String, Object> dataMap = getDataMap(id);

            // 设置响应头，处理中文文件名编码
            String fileName = (String) dataMap.get("fileName");
            fileName = new String((fileName + ".doc").getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
//            response.setContentType("application/pdf");

            bufferedWriter = new BufferedWriter(
                    new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8));

            // 将数据输出到html中
            template.process(dataMap, bufferedWriter);

//            file = new File(currentFilePath);

//            if (FileExtendUtil.getLicense()) {
//                FontSettings.setFontsFolder(fontUrl, true);
//                Document doc = new Document(currentFilePath);
//                doc.save(response.getOutputStream(), SaveFormat.PDF);
//                log.info("转换成功");
//            }
        } catch (Exception e) {
            log.info("pdf生成失败, e:" + e.getMessage());
//            throw new RuntimeException("生成pdf失败:" + e.getMessage());
        } finally {
            if (bufferedWriter != null) {
                try {
                    bufferedWriter.close();
                } catch (IOException e) {
                    log.info("流关闭异常");
                }
            }

//            if (file != null && file.exists()) {
//                file.delete();
//            }
        }
    }

    @Override
    public void createBatchPdf(Long[] idList, HttpServletResponse response) {
        BufferedWriter bufferedWriter = null;
        try {
            // 获取模板文件
            Template template = configuration.getTemplate("report.ftl", "UTF-8");

            response.setHeader("content-disposition", "attachment;filename=" + "城市安全监测报告.zip");
            response.setContentType("application/zip");

            try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
                for (Long id : idList) {
                    //        File file = null;
                    String currentFilePath = fileTmpUrl + "/" + IdUtils.fastSimpleUUID() + ".doc";

                    //指定输出流到的位置
                    bufferedWriter = new BufferedWriter(
                            new OutputStreamWriter(new FileOutputStream(currentFilePath), StandardCharsets.UTF_8));

                    Map<String, Object> dataMap = getDataMap(id);


                    // 将数据输出到html中
                    template.process(dataMap, bufferedWriter);

                    File file = new File(currentFilePath);

                    zipFile(file, dataMap.get("fileName") + ".doc", zos);
                }

            }


        } catch (Exception e) {
            log.info("pdf生成失败, e:" + e.getMessage());
//            throw new RuntimeException("生成pdf失败:" + e.getMessage());
        } finally {
            if (bufferedWriter != null) {
                try {
                    bufferedWriter.close();
                } catch (IOException e) {
                    log.info("流关闭异常");
                }
            }

//            if (file != null && file.exists()) {
//                file.delete();
//            }
        }
    }

    @Override
    public List<DeviceStreetVO> getStreet() {
        List<DeviceStreetVO> list = Lists.newArrayList();

        List<DeviceStreetVO> deviceStreetList = deviceStreetMapper.listAll();

        List<DeviceStreetVO> deviceStreetParentList = deviceStreetList.stream()
                .filter(deviceStreet -> Objects.isNull(deviceStreet.getParentId()))
                .sorted(Comparator.comparingInt(deviceStreet -> Integer.parseInt(deviceStreet.getId())))
                .collect(Collectors.toList());

        list.addAll(deviceStreetParentList);

        Map<String, List<DeviceStreetVO>> map = deviceStreetList.stream()
                .filter(deviceStreet -> Objects.nonNull(deviceStreet.getParentId()))
                .collect(Collectors.groupingBy(DeviceStreetVO::getParentId));

        for (DeviceStreetVO deviceStreet : deviceStreetParentList) {
            List<DeviceStreetVO> childList = map.get(deviceStreet.getId());

            deviceStreet.setChildren(childList);
        }

        return list;
    }

    private void zipFile(File fileToZip, String fileName, ZipOutputStream zos) throws IOException {
        if (fileToZip.isHidden()) {
            return;
        }

        try (FileInputStream fis = new FileInputStream(fileToZip)) {
            ZipEntry zipEntry = new ZipEntry(fileName);
            zos.putNextEntry(zipEntry);
            byte[] bytes = new byte[1024];
            int length;
            while ((length = fis.read(bytes)) >= 0) {
                zos.write(bytes, 0, length);
            }
        }
    }

    private Map<String, Object> getDataMap(Long id) {
        List<WarningReportVO> info = getInfo(id, null);

        Map<String, Object> dataMap = new HashMap<>();

        dataMap.put("reportList", info);

        for (WarningReportVO reportVO : info) {
            List<WarningReportDeviceVO> deviceList = reportVO.getDeviceList();
            if (CollectionUtils.isNotEmpty(deviceList)) {
                for (WarningReportDeviceVO warningReportDeviceVO : deviceList) {
                    warningReportDeviceVO.setWarningLevelStr(WarningLevelType.getByCode(warningReportDeviceVO.getWarningLevel()));
                }
            }

            dataMap.put("fileName", reportVO.getFileName());
        }

        for (WarningReportVO reportVO : info) {
            ByteArrayOutputStream outputStream = null;
            try {
                outputStream = new ByteArrayOutputStream();

                List<CitySafetyLineVO> lineList = reportVO.getLineList();

                //x轴名称列表
                List<String> xAxisNameList = lineList.stream()
                        .map(CitySafetyLineVO::getName)
                        .collect(Collectors.toList());
                //图例名称列表
                List<String> legendNameList = new ArrayList<>(Arrays.asList("红色预警", "橙色预警", "黄色预警", "蓝色预警", "设备告警"));
                //数据列表
                List<List<Object>> dataList = new ArrayList<>();
                dataList.add(lineList.stream().map(CitySafetyLineVO::getWarningLevel01).collect(Collectors.toList()));
                dataList.add(lineList.stream().map(CitySafetyLineVO::getWarningLevel02).collect(Collectors.toList()));
                dataList.add(lineList.stream().map(CitySafetyLineVO::getWarningLevel03).collect(Collectors.toList()));
                dataList.add(lineList.stream().map(CitySafetyLineVO::getWarningLevel04).collect(Collectors.toList()));
                dataList.add(lineList.stream().map(CitySafetyLineVO::getWarningLevel05).collect(Collectors.toList()));

                //图例背景颜色
                List<Color> legendColorList = new ArrayList<>(Arrays.asList(new Color(65, 105, 225)));

                GenerateChartUtil.createLineChart(outputStream, "", legendNameList, xAxisNameList
                        , dataList, JFreeChartUtil.createChartTheme("宋体"), "", ""
                        , 600, 300);

                Base64.Encoder encoder = Base64.getEncoder();
                String base64Image = encoder.encodeToString(outputStream.toByteArray());

                reportVO.setImageUrl(base64Image);

                outputStream.close();

            } catch (Exception e) {
                log.info("error:", e);
            }
        }

        return dataMap;
    }

    private List<WarningReportVO> initReportList(String[] sceneList, CitysafetyReport existReport) {
        List<WarningReportVO> reportList = Lists.newArrayList();

        for (String scene : sceneList) {
            WarningReportVO reportVO = new WarningReportVO();
            reportVO.setScene(scene);
            reportVO.setCreateTime(existReport.getCreateTime());
            reportVO.setFileName(existReport.getReportName());

            reportList.add(reportVO);
        }

        return reportList;
    }
}
