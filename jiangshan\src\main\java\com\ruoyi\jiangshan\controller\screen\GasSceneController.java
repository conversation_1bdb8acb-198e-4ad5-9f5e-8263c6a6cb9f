package com.ruoyi.jiangshan.controller.screen;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.service.GasSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 燃气场景驾驶舱
 */
@RestController
@RequestMapping("/gas")
public class GasSceneController {

    @Autowired
    private GasSceneService gasSceneService;

    /**
     * 燃气管线统计
     * @return
     */
    @GetMapping("/piplineCount")
    public AjaxResult getPiplineCount() {
        return AjaxResult.success(gasSceneService.getPiplineCount());
    }

    /**
     * 安全风险统计
     * @return
     */
    @GetMapping("/safetyCount")
    public AjaxResult getSafetyCount(String dateStr) {
        return AjaxResult.success(gasSceneService.getSafetyCount(dateStr));
    }

    /**
     * 安全风险事件排序
     * @return
     */
    @GetMapping("/event")
    public AjaxResult getEvent(String eventName, Integer status, Integer warningLevel) {
        return AjaxResult.success(gasSceneService.getEvent(eventName, status, warningLevel));
    }

    /**
     * 实时预警
     * @return
     */
    @GetMapping("/warning")
    public AjaxResult getWarning(Integer warningLevel) {
        return AjaxResult.success(gasSceneService.getWarning(warningLevel));
    }

    /**
     * 可燃气体监测
     * @return
     */
    @GetMapping("/monitor")
    public AjaxResult getMonitor(String dateStr, Long deviceId) {
        return AjaxResult.success(gasSceneService.getMonitor(dateStr, deviceId));
    }

    /**
     * 燃气并巡检统计
     * @return
     */
    @GetMapping("/inspection")
    public AjaxResult getInspection(Long monitorId) {
        return AjaxResult.success(gasSceneService.getInspection(monitorId));
    }

    /**
     * 根据类型获得监测对象
     * @return
     */
    @GetMapping("/getMonitorObject")
    public AjaxResult getMonitorObject(String monitorType) {
        return AjaxResult.success(gasSceneService.getMonitorObject(monitorType));
    }

    /**
     * 导入燃气井
     * @return
     */
    @PostMapping("/importData")
    public AjaxResult importData(@RequestBody Map<String, List<Map<String, Map<String, String>>>> params) {
        return AjaxResult.success(gasSceneService.importData(params));
    }

    /**
     * 获取燃气井
     * @return
     */
    @GetMapping("/listGasWell")
    public AjaxResult listGasWell() {
        return AjaxResult.success(gasSceneService.listGasWell());
    }
}
