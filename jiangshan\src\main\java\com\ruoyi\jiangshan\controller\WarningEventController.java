package com.ruoyi.jiangshan.controller;

import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import com.google.common.collect.Lists;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jiangshan.domain.WarningEventJczzLog;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.WarningEventSceneDepartmentMapper;
import com.ruoyi.jiangshan.vo.*;
import com.ruoyi.jiangshan.vo.jczz.WarningEventJczzLogVO;
import com.ruoyi.system.mapper.SysMenuMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 设备预警事件Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/warningEvent")
public class WarningEventController extends BaseController
{
    @Autowired
    private IWarningEventService warningEventService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysMenuMapper sysMenuMapper;
    @Autowired
    private WarningEventSceneDepartmentMapper warningEventSceneDepartmentMapper;

    /**
     * 查询设备预警事件列表
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:list')")
    @GetMapping("/list")
    public TableDataInfo list(WarningEventPageVO pageVO)
    {
        fillEmployeeCode(pageVO);
        checkPermission(pageVO);

        startPage();
        List<WarningEvent> list = warningEventService.selectWarningEventList(pageVO);
        return getDataTable(list);
    }

    private void checkPermission(WarningEventPageVO pageVO) {
        String operateUserId = pageVO.getOperateUserId();
        if (StringUtils.isBlank(operateUserId)) {
            pageVO.setPermissionFlag(0);
            return;
        }

        SysUser sysUser = sysUserMapper.getByAccountId(operateUserId);
        if (Objects.isNull(sysUser)) {
            return;
        }

        List<String> list = sysMenuMapper.selectMenuPermsByUserId(sysUser.getUserId());
        if (list.contains("WARNING_EVENT_LIST_ALL")) {
            pageVO.setPermissionFlag(0);
        }
    }

    private void fillEmployeeCode(WarningEventPageVO pageVO) {
        String operateUserId = pageVO.getOperateUserId();
        if (StringUtils.isBlank(operateUserId)) {
            operateUserId = "1";
        }

        SysUser sysUser = sysUserMapper.getByAccountId(operateUserId);

        //根据用户获得拥有的场景权限
        if (Objects.nonNull(sysUser)) {
            List<String> sceneList = MonitorSceneType.getScenePermissionByUser(sysUser, warningEventSceneDepartmentMapper);

            pageVO.setSceneList(sceneList);

            pageVO.setEmployeeCode(sysUser.getEmployeeCode());
            pageVO.setUserId(sysUser.getUserId());
        }
    }



    /**
     * 联处督办
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:list')")
    @GetMapping("/superviseList")
    public TableDataInfo listSupervise(WarningEventPageVO pageVO)
    {
        fillEmployeeCode(pageVO);

        startPage();
        List<WarningSuperviseVO> list = warningEventService.listSupervise(pageVO);
        return getDataTable(list);
    }

    /**
     * 导出设备预警事件列表
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:export')")
    //@Log(title = "设备预警事件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WarningEventPageVO pageVO)
    {
        fillEmployeeCode(pageVO);
        List<WarningEvent> list = warningEventService.selectWarningEventList(pageVO);
        ExcelUtil<WarningEvent> util = new ExcelUtil<WarningEvent>(WarningEvent.class);
        util.exportExcel(response, list, "设备预警事件数据");
    }

    /**
     * 获取设备预警事件详细信息
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:query')")
    @GetMapping
    public AjaxResult getInfo(Long id, String operateUserId)
    {
        return success(warningEventService.selectWarningEventById(id, operateUserId));
    }

    /**
     * 获取设备预警事件详细信息
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfoV2(@PathVariable Long id)
    {
        return success(warningEventService.selectWarningEventById(id, null));
    }

    /**
     * 新增设备预警事件
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:add')")
//    //@Log(title = "设备预警事件", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody WarningEvent warningEvent)
//    {
//        return toAjax(warningEventService.insertWarningEvent(warningEvent));
//    }

    /**
     * 修改设备预警事件
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "设备预警事件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WarningEvent warningEvent)
    {
        return toAjax(warningEventService.updateWarningEvent(warningEvent));
    }

    /**
     * 删除设备预警事件
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:remove')")
//    //@Log(title = "设备预警事件", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(warningEventService.deleteWarningEventByIds(ids));
//    }

    /**
     * 一键派单
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "一键派单", businessType = BusinessType.UPDATE)
    @PostMapping("/dispatch")
    public AjaxResult dispatch(@RequestBody WarningEventDispatchVO dispatchVO)
    {
        return toAjax(warningEventService.dispatch(dispatchVO, dispatchVO.getAccountId(), dispatchVO.getEmployeeMobile()));
    }

    /**
     * 签收
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "签收", businessType = BusinessType.UPDATE)
    @PostMapping("/receiptV2/{eventId}")
    public AjaxResult receiptV2(@PathVariable Long eventId)
    {
        warningEventService.receiptV2(eventId);
        return AjaxResult.success();
    }

    /**
     * 转派
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "转派", businessType = BusinessType.UPDATE)
    @PostMapping("/transfer")
    public AjaxResult transfer(@RequestBody WarningEventDispatchVO dispatchVO)
    {
        warningEventService.transfer(dispatchVO);
        return AjaxResult.success();
    }

    /**
     * 退回（错报）
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "退回", businessType = BusinessType.UPDATE)
    @PostMapping("/rollback")
    public AjaxResult rollback(@RequestBody WarningEventDispatchVO dispatchVO)
    {
        return toAjax(warningEventService.rollbackV2(dispatchVO));
    }

    /**
     * 退回（退回到派单）
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "退回", businessType = BusinessType.UPDATE)
    @PostMapping("/return2Dispatch")
    public AjaxResult return2Dispatch(@RequestBody WarningEventDispatchVO dispatchVO)
    {
        return toAjax(warningEventService.return2Dispatch(dispatchVO));
    }

    /**
     * 核实
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "核实", businessType = BusinessType.UPDATE)
    @PostMapping("/verify")
    public AjaxResult verify(@RequestBody WarningEventVerifyVO verifyVO)
    {
        return toAjax(warningEventService.verify(verifyVO));
    }

    /**
     * 挂起
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "挂起", businessType = BusinessType.UPDATE)
    @PostMapping("/hangUp")
    public AjaxResult hangUp(@RequestBody WarningEventVerifyVO verifyVO)
    {
        return toAjax(warningEventService.hangUp(verifyVO));
    }

    /**
     * 重启
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "重启", businessType = BusinessType.UPDATE)
    @PostMapping("/restart")
    public AjaxResult restart(@RequestBody WarningEventVerifyVO verifyVO)
    {
        return toAjax(warningEventService.restart(verifyVO));
    }

    /**
     * 签收
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "签收", businessType = BusinessType.UPDATE)
//    @PostMapping("/receipt")
//    public AjaxResult receipt(@RequestBody WarningEventVerifyVO verifyVO)
//    {
//        return toAjax(warningEventService.receipt(verifyVO));
//    }

    /**
     * 处置
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "处置", businessType = BusinessType.UPDATE)
    @PostMapping("/handle")
    public AjaxResult handle(@RequestBody WarningEventVerifyVO verifyVO)
    {
        return toAjax(warningEventService.handle(verifyVO));
    }

    /**
     * 抄送
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "抄送", businessType = BusinessType.UPDATE)
    @PostMapping("/copy")
    public AjaxResult copy(@RequestBody WarningCopyVO copyVO)
    {
        return toAjax(warningEventService.copy(copyVO));
    }

    /**
     * 一键督办
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "一键督办", businessType = BusinessType.UPDATE)
    @PostMapping("/supervise")
    public AjaxResult supervise(@RequestBody WarningBatchSuperviseVO superviseVO)
    {
        return toAjax(warningEventService.supervise(superviseVO));
    }

    /**
     * 通知
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:edit')")
    //@Log(title = "一键督办", businessType = BusinessType.UPDATE)
    @GetMapping("/sendNotify/{id}")
    public AjaxResult sendNotify(@PathVariable Long id)
    {
        return success(warningEventService.sendNotify(id));
    }

    /**
     * 导出word
     */
    @PostMapping("/createWord")
    public void createWord(Long id, HttpServletResponse response)
    {
        warningEventService.createWord(id, response);
    }

    /**
     * 基层智治事件
     */
    @PostMapping("/createJczzWarningEvent")
    public AjaxResult createJczzWarningEvent(@RequestBody WarningEventJczzLogVO warningEventJczzLogVO)
    {
        warningEventService.createJczzWarningEvent(null, warningEventJczzLogVO);
        return AjaxResult.success();
    }

    /**
     * 刷141事件状态
     */
    @PostMapping("/flushEventStatus")
    public AjaxResult flushEventStatus()
    {
        warningEventService.flushEventStatus();
        return AjaxResult.success();
    }

    /**
     * 发送消息
     */
    @GetMapping("/sendMobileMsg")
    public AjaxResult sendMobileMsg(String mobile)
    {
        warningEventService.sendMobileMsg(mobile);
        return AjaxResult.success();
    }

    /**
     * 推送141
     */
    @GetMapping("/push141PlatForm/{id}")
    public AjaxResult push141PlatForm(@PathVariable Long id)
    {
        warningEventService.push141PlatForm(id);
        return AjaxResult.success();
    }
}
