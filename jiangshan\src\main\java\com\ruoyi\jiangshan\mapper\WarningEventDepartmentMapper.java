package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningEventDepartment;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface WarningEventDepartmentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningEventDepartment record);

    int insertSelective(WarningEventDepartment record);

    WarningEventDepartment selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningEventDepartment record);

    int updateByPrimaryKey(WarningEventDepartment record);

    int updateBatch(List<WarningEventDepartment> list);

    int batchInsert(@Param("list") List<WarningEventDepartment> list);

    List<WarningEventDepartment> listByEventIdList(@Param("list") List<Long> warningEventIdList);

    void addScore(@Param("departmentId") Long departmentId, @Param("score") int score);

    void deleteByEventIdAndStatus(@Param("id") Long id, @Param("status") Integer status);

    WarningEventDepartment getByEventAndProcessId(@Param("processId") Long processId, @Param("eventId") Long eventId);

    List<WarningEventDepartment> listByEventIdAndStatus(@Param("eventId") Long eventId, @Param("status") Integer status);

    WarningEventDepartment getLastUserMobile(Long id);
}
