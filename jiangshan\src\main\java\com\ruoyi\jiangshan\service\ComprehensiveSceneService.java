package com.ruoyi.jiangshan.service;

import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.vo.*;
import com.ruoyi.jiangshan.vo.cityrisk.CityRiskAvgLineVO;
import com.ruoyi.jiangshan.vo.cityrisk.CityRiskSafetyEventVO;
import com.ruoyi.jiangshan.vo.compre.*;
import com.ruoyi.jiangshan.vo.hk.HkControlVideoVO;
import com.ruoyi.jiangshan.vo.hk.HkPreviewVideoVO;
import com.ruoyi.jiangshan.vo.street.DeviceStreetGeometry;
import com.ruoyi.jiangshan.vo.street.DeviceStreetVO;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ComprehensiveSceneService {

    List<BusinessCountVO> getRiskMonitor();

    List<BusinessCountVO> getControlSubject();

    List<WarningCountVO> getRiskStatistics(Date startTime, Date endTime);

    List<WarningInfo> getRiskList();

    Map<String, Object> getWarningDispose();

    List<BusinessPercentCountVO> getEfficiencyStatistics(String type);

    List<WarningEvent> getLinkDispose();

    List<DeviceInfo> getMonitorLayer();

    List<WarningLayerVO> getWarningLayer(String scene);

    List<WarningControlLayer> getControlLayer();

    List<WarningDiagnosisVO> getDiagnosisLayer(String scene);

    List<DeviceInfo> listDeviceAndData(String deviceType, String scene, Long monitorId, Integer warningFlag);

    CityRiskAvgLineVO getSafetyLine(String deviceThirdId, String monitorItem);

    List<WarningEvent> getLinkDisposeV2(WarningEventPageVO pageVO);

    List<VideoStreetVO> getVideoStreet();

    List<DeviceInfo> getDeviceByStreet(String deviceStreetAreaCode, String scene, String deviceType);

    String getVideoUrl(String deviceThirdId, String protocol);

    void saveStreet(List<DeviceStreetVO> deviceStreetVOList);

    void saveStreetGeometry(List<DeviceStreetGeometry> list);

    List<TDisasterAvoidSite> listAllDisasterAvoid();

    List<EmergencyMaterial> listEmergencyMaterial(String street, String village);

    String flushFxffq(Map<String, List<Map<String, Object>>> params);

    Map<String, Object> getDeviceMonitor(String deviceType, Long monitorId);

    List<String> getDeviceAllType();

    List<DeviceMonitor> getPointByMonitorType(String monitorType);

    List<DeviceInfo> exportDevice(String deviceType, Long monitorId);

    Map<String, String> controlVideo(HkControlVideoVO hkControlVideoVO);

    Map<String, Object> getPreviewUrl(HkPreviewVideoVO videoVO);

    List<WaterLayerVO> getSceneLayer(String scene, String newType);

    List<WaterLayerVO> getWaterloggingLayer();

    String flushGeometry(Map<String, List<Map<String, Object>>> params, String type);

    WaterLayerVO getWaterDetail(Long monitorId);

    String getYuLiangLayer();

    List<DeviceYuLiangVO> getYuLiangLayerV2();

    List<DeviceMonitor> getAllMonitor(String scene);

    List<DeviceInfo> getDeviceList(Long monitorId);

    List<DeviceMonitorShuikuVO> getShuikuList(String type, String keywords);

    DeviceYuLiangStreetAllVO getWaterExtend();

    DeviceYuLiangStreetAllV2VO getWaterExtendV2();

    DeviceYuLiangStreetAllV2VO getWaterExtendByHour(Integer hour);

    List<BusinessDateDoubleCountVO> getWaterExtendLine(String deviceStreet);

    Map getIrsRainFallOneHour(Integer type);

    Map getIrsRainFallThreeHour(Integer type);

    Map getIrsWeatherWarning();

    List<DeviceInfo> pageDeviceAndData(String deviceType, String scene, Long monitorId, Integer warningFlag,
                             Integer pageNum, Integer pageSize);

    DeviceYuLiangStreetAllV2VO getWaterExtendV3(Integer type);

    List<BusinessDateDoubleCountVO> getWaterExtendLineV2(String deviceStreet, Integer hour);

    Map getIrsRainFallByHour(Integer hour);
}
