package com.ruoyi.jiangshan.controller.screen;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.service.ComprehensiveSceneService;
import com.ruoyi.jiangshan.service.WaterSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 水利场景驾驶舱
 */
@RestController
@RequestMapping("/water")
public class WaterSceneController {
    @Autowired
    private WaterSceneService waterSceneService;

    /**
     * 天气预报
     * @return
     */
    @GetMapping("/weather")
    public AjaxResult getWeather() {
        return AjaxResult.success(waterSceneService.getWeather());
    }

    /**
     * 监测对象
     * @return
     */
    @GetMapping("/monitor")
    public AjaxResult getMonitor(String type) {
        return AjaxResult.success(waterSceneService.getMonitor(type));
    }

    /**
     * 监测设备
     * @return
     */
    @GetMapping("/equipment")
    public AjaxResult getEquipment(String type) {
        return AjaxResult.success(waterSceneService.getEquipment(type));
    }

    /**
     * 实时预警
     * @return
     */
    @GetMapping("/warning")
    public AjaxResult getWarning(Integer warningLevel) {
        return AjaxResult.success(waterSceneService.getWarning(warningLevel));
    }

    /**
     * 安全风险事件统计
     * @return
     */
    @GetMapping("/warningEvent")
    public AjaxResult getWarningDispose(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return AjaxResult.success(waterSceneService.getWarningDispose(startTime, endTime));
    }


}
