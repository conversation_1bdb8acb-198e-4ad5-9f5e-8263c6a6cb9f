package com.ruoyi.jiangshan.domain;

import lombok.Data;

import java.util.Date;

/**
    * 设备预警事件-节点超时规则配置
    */
@Data
public class WarningEventTimeoutRule {
    /**
    * 主键
    */
    private Long id;

    /**
    * 状态 0-未派单 1-待签收 2-待核实 3-待处置
    */
    private Integer status;

    /**
    * 超时时间
    */
    private String timeoutDay;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 创建人
    */
    private String createBy;

}
