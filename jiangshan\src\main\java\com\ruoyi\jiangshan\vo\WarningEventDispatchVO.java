package com.ruoyi.jiangshan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class WarningEventDispatchVO {

    private Long id;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 部门name
     */
    private String departmentName;

    /**
     * 组织code
     */
    private String organizationCode;

    /**
     * 浙政钉accountId
     */
    private String accountId;

    /**
     * 浙政钉employeeCode
     */
    private String employeeCode;

    /**
     * 浙政钉员工名称
     */
    private String employeeName;

    /**
     * 手机号
     */
    private String employeeMobile;

    /**
     * 预估完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedCompleteTime;

    /**
     * 派单说明/回退说明
     */
    private String reason;

    /**
     * 当前操作用户id
     */
    private String operateUserId;
}
