package com.ruoyi.jiangshan.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.domain.WarningEventDepartment;
import com.ruoyi.jiangshan.domain.WarningEventJczzLog;
import com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin;
import com.ruoyi.jiangshan.enums.WarningSuperviseEnum;
import com.ruoyi.jiangshan.openapi.vo.WarningEventOpenApiVO;
import com.ruoyi.jiangshan.openapi.vo.WarningEventQueryVO;
import com.ruoyi.jiangshan.openapi.vo.WarningEventThresholdQueryVO;
import com.ruoyi.jiangshan.vo.*;
import com.ruoyi.jiangshan.vo.jczz.WarningEventJczzLogVO;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 设备预警事件Service接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface IWarningEventService
{
    /**
     * 查询设备预警事件
     *
     * @param id 设备预警事件主键
     * @return 设备预警事件
     */
    public WarningEvent selectWarningEventById(Long id, String operateUserId);

    /**
     * 查询设备预警事件列表
     *
     * @param pageVO 设备预警事件
     * @return 设备预警事件集合
     */
    public List<WarningEvent> selectWarningEventList(WarningEventPageVO pageVO);

    /**
     * 新增设备预警事件
     *
     * @param warningEvent 设备预警事件
     * @return 结果
     */
    public int insertWarningEvent(WarningEvent warningEvent);

    /**
     * 修改设备预警事件
     *
     * @param warningEvent 设备预警事件
     * @return 结果
     */
    public int updateWarningEvent(WarningEvent warningEvent);

    /**
     * 批量删除设备预警事件
     *
     * @param ids 需要删除的设备预警事件主键集合
     * @return 结果
     */
    public int deleteWarningEventByIds(Long[] ids);

    /**
     * 删除设备预警事件信息
     *
     * @param id 设备预警事件主键
     * @return 结果
     */
    public int deleteWarningEventById(Long id);

    Map<String, Object> getWarningDispose(String scene, Date startTime, Date endTime);

    WarningCountVO getSafetyCount(String dateStr, String type);

    int dispatch(WarningEventDispatchVO dispatchVO, String accountId, String employeeMobile);

    int rollbackV2(WarningEventDispatchVO dispatchVO);

    int verify(WarningEventVerifyVO verifyVO);

    int hangUp(WarningEventVerifyVO verifyVO);

    int restart(WarningEventVerifyVO verifyVO);

    int handle(WarningEventVerifyVO verifyVO);

//    int receipt(WarningEventVerifyVO verifyVO);

    int copy(WarningCopyVO copyVO);

    List<WarningSuperviseVO> listSupervise(WarningEventPageVO pageVO);

    void superviseWorkNotify(List<Long> updateEventIdList, WarningSuperviseEnum warningSuperviseEnum);

    int supervise(WarningBatchSuperviseVO superviseVO);

    void fillProcessLog(WarningEvent warningEvent);

    void fillProcessList(List<WarningEvent> warningEventList, List<Long> warningEventIdList);

    String saveOpenApiWarningEvent(WarningEventOpenApiVO apiVO);

    WarningRuleConditionShuxin getThreshold(WarningEventThresholdQueryVO apiVO);

    void receiptV2(Long eventId);

    void transfer(WarningEventDispatchVO dispatchVO);

    String sendNotify(Long id);

    void createWord(Long id, HttpServletResponse response);

    List<WarningEvent> listNonCompleteEvent();

    void updateStatusByJczz();

    void createJczzWarningEvent(WarningEvent warningEvent, WarningEventJczzLogVO warningEventJczzLogVO);

    void handleHkWarningEvent(String fireEscapeDetection, HttpServletRequest request, HttpServletResponse response,
                              MultipartFile file1, MultipartFile file2);

    void fillFileList(List<WarningEvent> warningEventList, List<Long> warningEventIdList);

    void fillEventBizCode(List<WarningEvent> warningEventList, String deviceScene);

    List<WarningEvent> listNonCompleteOrangeEvent(WarningEventQueryVO queryVO);

    WarningEventJczzLogVO get141Detail(String bizCode);

    void flushEventStatus();

    WarningEvent getByEventThirdId(String eventThirdId);

    void sendMobileMsg(String mobile);

    int return2Dispatch(WarningEventDispatchVO dispatchVO);

    Map push141PlatForm(Long id);

    WarningEventDepartment getLastUserMobile(Long id);

    Map eventReport(WarningEvent warningEvent, String state);
}
