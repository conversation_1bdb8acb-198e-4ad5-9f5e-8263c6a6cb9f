package com.ruoyi.jiangshan.enums;

public enum WarningLevelType {
    WARNING_LEVEL_1(1, "红色告警"),
    WARNING_LEVEL_2(2, "橙色告警"),
    WARNING_LEVEL_3(3, "黄色告警"),
    WARNING_LEVEL_4(4, "蓝色告警"),
    WARNING_LEVEL_5(5, "设备告警"),
    ;

    private final Integer code;
    private final String desc;

    WarningLevelType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getByCode(Integer code) {
        for (WarningLevelType value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }

        return "";
    }
}
