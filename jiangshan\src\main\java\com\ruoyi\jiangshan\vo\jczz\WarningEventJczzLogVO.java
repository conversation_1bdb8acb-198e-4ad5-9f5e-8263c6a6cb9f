package com.ruoyi.jiangshan.vo.jczz;

import lombok.Data;

import java.util.List;

@Data
public class WarningEventJczzLogVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 事件编号
     */
    private String eventNumber;

    /**
     * 来源方式编号 不限于以下内容：1巡检录入；2人工录入；3微信录入；4短信录入；5网站录入；6热线电话；7网格员上报；8公众爆料；9厅局部门系统下发；99其他
     */
    private String sourceWayCode;

    /**
     * 来源方式名称
     */
    private String sourceWayName;

    /**
     * 是否浙政钉用户
     */
    private Boolean dingFlag;

    /**
     * 上报人编号 1、如果是浙政钉用户，则录入浙政钉用户唯一编号。
     2、如果非浙政钉用户，则录入原系统用户编号。
     */
    private String reportCode;

    /**
     * 上报人姓名
     */
    private String reportName;

    /**
     * 上报人联系电话
     */
    private String reportPhone;

    /**
     * 上报人组织机构编码（国标）
     */
    private String departCode;

    /**
     * 上报人组织机构名称
     */
    private String departName;

    /**
     * 事件类型一级代码
     */
    private String eventTypeOne;

    /**
     * 事件类型二级代码
     */
    private String eventTypeTwo;

    /**
     * 事件类型三级代码
     */
    private String eventTypeThree;

    /**
     * 事件标题
     */
    private String title;

    /**
     * 事件内容
     */
    private String content;

    /**
     * 事发地点
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 事件等级 1-5级
     */
    private String eventLevel;

    /**
     * 是否紧急事件 0否，1是
     */
    private Boolean emergencyFlag;

    /**
     * 紧急程度
     */
    private String emergencyDegree;

    /**
     * 是否重大事件0否，1是
     */
    private Boolean greatFlag;

    /**
     * 重大程度 0一般,1较大,2重大,3特重大
     */
    private String greatDegree;

    /**
     * 是否重点场所 0否，1是
     */
    private Boolean placeFlag;

    /**
     * 事件规模 0特大、1重大、2一般
     */
    private String eventScale;

    /**
     * 涉事人数
     */
    private Integer eventNum;

    /**
     * 市级编号, 事件所属区域定位
     */
    private String cityCode;

    /**
     * 区县编号
     */
    private String countyCode;

    /**
     * 区县名称
     */
    private String countyName;

    /**
     * 镇街编号
     */
    private String streetCode;

    /**
     * 镇街名称
     */
    private String streetName;

    /**
     * 村社编号
     */
    private String villageCode;

    /**
     * 村社名称
     */
    private String villageName;

    /**
     * 网格编号
     */
    private String griddingCode;

    /**
     * 网格名称
     */
    private String griddingName;

    /**
     * 事发时间
     */
    private String eventTime;

    /**
     * 上报时间
     */
    private String reportTime;

    /**
     * 截止时间
     */
    private String closingTime;

    /**
     * 事件办结/结案时间
     */
    private String accomplishTime;

    /**
     * 是否超期 0否，1是
     */
    private Boolean overdueFlag;

    /**
     * 事件状态 状态值：10 上报、20受理、30办理、32 分流、36 回退、37、协同审核、38协同反馈、39 督办、40结案、50反馈
     */
    private String state;

    /**
     * 事件上报源编码 事件上报源编码，通过2.5获取协同对象，匹配相关数据源内容
     */
    private String appCode;

    /**
     * 0未挂起，1已挂起
     */
    private Boolean hangFlag;

    /**
     * 事件备注
     */
    private String memo;

    /**
     * 事件附件
     */
    private List<WarningEventAttachmentVO> attachments;

    /**
     * 处理流程扩展属性
     */
    private List<WarningEventJczzFlowVO> eventFlowList;
}
