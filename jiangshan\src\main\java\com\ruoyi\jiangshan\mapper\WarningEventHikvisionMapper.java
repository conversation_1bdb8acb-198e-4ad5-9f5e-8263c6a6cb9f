package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningEventHikvision;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WarningEventHikvisionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningEventHikvision record);

    int insertSelective(WarningEventHikvision record);

    WarningEventHikvision selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningEventHikvision record);

    int updateByPrimaryKey(WarningEventHikvision record);

    int updateBatch(List<WarningEventHikvision> list);

    int batchInsert(@Param("list") List<WarningEventHikvision> list);
}