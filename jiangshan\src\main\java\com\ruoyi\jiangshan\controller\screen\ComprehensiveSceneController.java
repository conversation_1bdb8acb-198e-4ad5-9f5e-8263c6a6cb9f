package com.ruoyi.jiangshan.controller.screen;

import com.google.common.collect.Lists;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.enums.WarningSuperviseEnum;
import com.ruoyi.jiangshan.mapper.JiangshanRainfallMapper;
import com.ruoyi.jiangshan.service.ComprehensiveSceneService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.vo.compre.DeviceMonitorQueryLayerVO;
import com.ruoyi.jiangshan.vo.hk.HkControlVideoVO;
import com.ruoyi.jiangshan.vo.hk.HkPreviewVideoVO;
import com.ruoyi.jiangshan.vo.street.DeviceStreetGeometry;
import com.ruoyi.jiangshan.vo.street.DeviceStreetVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 综合场景驾驶舱
 */
@RestController
@RequestMapping("/comprehensive")
public class ComprehensiveSceneController extends BaseController {
    @Autowired
    private ComprehensiveSceneService comprehensiveSceneService;
    @Autowired
    private IWarningEventService warningEventService;

    /**
     * 风险监控
     * @return
     */
    @GetMapping("/riskMonitor")
    public AjaxResult getRiskMonitor() {
        return AjaxResult.success(comprehensiveSceneService.getRiskMonitor());
    }

    /**
     * 管控主体
     * @return
     */
    @GetMapping("/controlSubject")
    public AjaxResult getControlSubject() {
        return AjaxResult.success(comprehensiveSceneService.getControlSubject());
    }

    /**
     * 根据管控主体类型获得经纬度
     * @return
     */
    @GetMapping("/getPoint")
    public AjaxResult getPointByMonitorType(String monitorType) {
        return AjaxResult.success(comprehensiveSceneService.getPointByMonitorType(monitorType));
    }

    /**
     * 风险监控（新）
     * @return
     */
    @GetMapping("/deviceMonitor")
    public AjaxResult getRiskMonitor(String deviceType, Long monitorId) {
        return AjaxResult.success(comprehensiveSceneService.getDeviceMonitor(deviceType, monitorId));
    }

    /**
     * 风险监控（新）-导出
     * @return
     */
    @GetMapping("/exportDevice")
    public void export(HttpServletResponse response, String deviceType, Long monitorId)
    {
        if ("全部".equals(deviceType)) {
            deviceType = null;
        }

        List<DeviceInfo> list = comprehensiveSceneService.exportDevice(deviceType, monitorId);
        ExcelUtil<DeviceInfo> util = new ExcelUtil<DeviceInfo>(DeviceInfo.class);
        util.exportExcel(response, list, "设备信息数据");
    }

    /**
     * 获取所有设备类型
     * @return
     */
    @GetMapping("/getAllType")
    public AjaxResult getDeviceAllType() {
        return AjaxResult.success(comprehensiveSceneService.getDeviceAllType());
    }

    /**
     * 风险统计
     * @return
     */
    @GetMapping("/riskStatistics")
    public AjaxResult getRiskStatistics(@DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                        @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        return AjaxResult.success(comprehensiveSceneService.getRiskStatistics(startTime, endTime));
    }

    /**
     * 风险列表
     * @return
     */
    @GetMapping("/riskList")
    public AjaxResult getRiskList() {
        return AjaxResult.success(comprehensiveSceneService.getRiskList());
    }

    /**
     * 预警处置概览
     * @return
     */
    @GetMapping("/warningDispose")
    public AjaxResult getWarningDispose() {
        return AjaxResult.success(comprehensiveSceneService.getWarningDispose());
    }

    /**
     * 执行效率统计
     * @return
     */
    @GetMapping("/efficiencyStatistics")
    public AjaxResult getEfficiencyStatistics(String type) {
        return AjaxResult.success(comprehensiveSceneService.getEfficiencyStatistics(type));
    }

    /**
     * 联动处置详情
     * @return
     */
    @GetMapping("/linkDispose")
    public AjaxResult getLinkDispose() {
        return AjaxResult.success(comprehensiveSceneService.getLinkDispose());
    }

    /**
     * 监测一张图
     * @return
     */
    @GetMapping("/monitorLayer")
    public AjaxResult getMonitorLayer() {
        return AjaxResult.success(comprehensiveSceneService.getMonitorLayer());
    }

//    /**
//     * 监测一张图
//     * @return
//     */
//    @PostMapping("/monitorLayerV2")
//    public AjaxResult getMonitorLayer(@RequestBody DeviceMonitorQueryLayerVO layerVO) {
//        return AjaxResult.success(comprehensiveSceneService.getMonitorLayer(layerVO));
//    }

    /**
     * 预警一张图
     * @return
     */
    @GetMapping("/warningLayer")
    public AjaxResult getWarningLayer(String scene) {
        return AjaxResult.success(comprehensiveSceneService.getWarningLayer(scene));
    }

    /**
     * 管控一张图
     * @return
     */
    @GetMapping("/controlLayer")
    public AjaxResult getControlLayer() {
        return AjaxResult.success(comprehensiveSceneService.getControlLayer());
    }

    /**
     * 推送诊断一张图
     * @return
     */
    @GetMapping("/diagnosisLayer")
    public AjaxResult getDiagnosisLayer(String scene) {
        return AjaxResult.success(comprehensiveSceneService.getDiagnosisLayer(scene));
    }

    /**
     * 物联设备图层
     * @return
     */
    @GetMapping("/device")
    public AjaxResult listDeviceAndData(String deviceType, String scene, Long monitorId, Integer warningFlag) {
        return AjaxResult.success(comprehensiveSceneService.listDeviceAndData(deviceType, scene, monitorId, warningFlag));
    }

    /**
     * 物联设备图层(分页)
     * @return
     */
    @GetMapping("/devicePage")
    public TableDataInfo pageDeviceAndData(String deviceType, String scene, Long monitorId, Integer warningFlag,
                                           Integer pageNum, Integer pageSize) {
        List<DeviceInfo> list = comprehensiveSceneService.pageDeviceAndData(deviceType, scene, monitorId,
                warningFlag, pageNum, pageSize);
        return getDataTable(list);
    }

    /**
     * 浙政钉责任人推送
     * @return
     */
    @PostMapping("/pushDtalk")
    public AjaxResult pushDtalk(Long eventId) {
        warningEventService.superviseWorkNotify(Lists.newArrayList(eventId), WarningSuperviseEnum.SUPERVISE_3);
        return AjaxResult.success();
    }

    /**
     * 监测一张图监测值折线图
     */
    @GetMapping("/safetyLine")
    public AjaxResult getSafety(String deviceThirdId, String monitorItem) {
        return AjaxResult.success(comprehensiveSceneService.getSafetyLine(deviceThirdId, monitorItem));
    }

    /**
     * 视频监控街道列表
     */
    @GetMapping("/videoStreet")
    public AjaxResult getVideoStreet() {
        return AjaxResult.success(comprehensiveSceneService.getVideoStreet());
    }

    /**
     * 根据街道、场景和设备类型查询设备
     */
    @GetMapping("/getDeviceByStreet")
    public AjaxResult getDeviceByStreet(String deviceStreetAreaCode, String scene, String deviceType) {
        return AjaxResult.success(comprehensiveSceneService.getDeviceByStreet(deviceStreetAreaCode, scene, deviceType));
    }

    /**
     * 根据设备编号查询视频url
     */
    @GetMapping("/getVideoUrl")
    public AjaxResult getVideoUrl(String deviceThirdId, String protocol) {
        return AjaxResult.success(comprehensiveSceneService.getVideoUrl(deviceThirdId, protocol));
    }

    /**
     * 插入街道社区父子关系
     */
    @PostMapping("/saveStreet")
    public AjaxResult saveStreet(@RequestBody List<DeviceStreetVO> deviceStreetVOList) {
        comprehensiveSceneService.saveStreet(deviceStreetVOList);
        return AjaxResult.success();
    }

    /**
     * 插入街道社区经纬度
     */
    @PostMapping("/saveStreetGeometry")
    public AjaxResult saveStreetGeometry(@RequestBody List<DeviceStreetGeometry> list) {
        comprehensiveSceneService.saveStreetGeometry(list);
        return AjaxResult.success();
    }

    /**
     * 避灾点位
     */
    @GetMapping("/disasterAvoid")
    public AjaxResult listAllDisasterAvoid() {
        return AjaxResult.success(comprehensiveSceneService.listAllDisasterAvoid());
    }

    /**
     * 应急物资
     */
    @GetMapping("/emergencyMaterial")
    public AjaxResult listEmergencyMaterial(String street, String village) {
        return AjaxResult.success(comprehensiveSceneService.listEmergencyMaterial(street, village));
    }

    /**
     * 刷风险防范区数据
     */
    @PostMapping("/flushFxffq")
    public AjaxResult flushFxffq(@RequestBody Map<String, List<Map<String, Object>>> params) {
        return AjaxResult.success(comprehensiveSceneService.flushFxffq(params));
    }

    /**
     * 控制云台
     */
    @GetMapping("/controlVideo")
    public AjaxResult controlVideo(@RequestBody HkControlVideoVO hkControlVideoVO) {
        return AjaxResult.success(comprehensiveSceneService.controlVideo(hkControlVideoVO));
    }

    /**
     * 获取历史视频
     */
    @GetMapping("/getPreviewUrl")
    public AjaxResult getPreviewUrl(@RequestBody HkPreviewVideoVO videoVO) {
        return AjaxResult.success(comprehensiveSceneService.getPreviewUrl(videoVO));
    }

    /**
     * 水位/内涝一张图
     * @return
     */
    @GetMapping("/sceneLayer")
    public AjaxResult getSceneLayer(String scene, String newType) {
        return AjaxResult.success(comprehensiveSceneService.getSceneLayer(scene, newType));
    }

    /**
     * 水利/内涝一张图点击详情
     * @return
     */
    @GetMapping("/waterDetail")
    public AjaxResult getWaterDetail(Long monitorId) {
        return AjaxResult.success(comprehensiveSceneService.getWaterDetail(monitorId));
    }

//    /**
//     * 雨量一张图
//     * @return
//     */
//    @GetMapping("/yuliangLayer")
//    public AjaxResult getYuLiangLayer() {
//        return AjaxResult.success(comprehensiveSceneService.getYuLiangLayer());
//    }

    /**
     * 雨量一张图
     * @return
     */
    @GetMapping("/yuliangLayer")
    public AjaxResult getYuLiangLayer() {
        return AjaxResult.success(comprehensiveSceneService.getYuLiangLayerV2());
    }

    /**
     * 刷山塘数据
     */
    @PostMapping("/flushShantang")
    public AjaxResult flushShantang(@RequestBody Map<String, List<Map<String, Object>>> params) {
        return AjaxResult.success(comprehensiveSceneService.flushGeometry(params, "山塘"));
    }

    /**
     * 刷水库数据
     */
    @PostMapping("/flushShuiku")
    public AjaxResult flushShuiku(@RequestBody Map<String, List<Map<String, Object>>> params) {
        return AjaxResult.success(comprehensiveSceneService.flushGeometry(params, "水库"));
    }

    /**
     * 获取所有监测对象
     */
    @GetMapping("/getAllMonitor")
    public AjaxResult getAllMonitor(String scene) {
        return AjaxResult.success(comprehensiveSceneService.getAllMonitor(scene));
    }

    /**
     * 根据监测对象获得设备清单
     * @return
     */
    @GetMapping("/deviceList")
    public AjaxResult getDeviceList(Long monitorId) {
        return AjaxResult.success(comprehensiveSceneService.getDeviceList(monitorId));
    }

    /**
     * 获取水库列表
     * @return
     */
    @GetMapping("/shuikuList")
    public AjaxResult getShuikuList(String type, String keywords) {
        return AjaxResult.success(comprehensiveSceneService.getShuikuList(type, keywords));
    }

    /**
     * 雨量一张图扩展数据
     * @return
     */
    @GetMapping("/waterExtend")
    public AjaxResult getWaterExtend() {
        return AjaxResult.success(comprehensiveSceneService.getWaterExtend());
    }

    /**
     * 雨量一张图扩展数据V2
     * @return
     */
    @GetMapping("/waterExtendV2")
    public AjaxResult getWaterExtendV2() {
        return AjaxResult.success(comprehensiveSceneService.getWaterExtendV2());
    }

    /**
     * 雨量一张图扩展数据V3
     * @return
     */
    @GetMapping("/waterExtendV3")
    public AjaxResult getWaterExtendV3(Integer type) {
        return AjaxResult.success(comprehensiveSceneService.getWaterExtendV3(type));
    }

    /**
     * 雨量一张图折线图V2
     * @return
     */
    @GetMapping("/waterExtendLineV2")
    public AjaxResult getWaterExtendLineV2(String deviceStreet, Integer hour) {
        return AjaxResult.success(comprehensiveSceneService.getWaterExtendLineV2(deviceStreet, hour));
    }

    /**
     * 雨量一张图折线图
     * @return
     */
    @GetMapping("/waterExtendLine")
    public AjaxResult getWaterExtendLine(String deviceStreet) {
        return AjaxResult.success(comprehensiveSceneService.getWaterExtendLine(deviceStreet));
    }

    /**
     * 江山市最近一小时累计实况降水
     * 1-irs 2-自建
     *
     * @return
     */
    @GetMapping("/irsRainFallOneHour")
    public AjaxResult getIrsRainFallOneHour(Integer type) {
        if (Objects.isNull(type)) {
            type = 1;
        }

        return AjaxResult.success(comprehensiveSceneService.getIrsRainFallOneHour(type));
    }

    /**
     * 江山市智能网格预报3小时累计降水
     * 1-irs 2-自建
     *
     * @return
     */
    @GetMapping("/irsRainFallThreeHour")
    public AjaxResult getIrsRainFallThreeHour(Integer type) {
        if (Objects.isNull(type)) {
            type = 1;
        }

        return AjaxResult.success(comprehensiveSceneService.getIrsRainFallThreeHour(type));
    }

    /**
     * 江山市实时气象预警信号查询
     * @return
     */
    @GetMapping("/irsWeatherWarning")
    public AjaxResult getIrsWeatherWarning() {
        return AjaxResult.success(comprehensiveSceneService.getIrsWeatherWarning());
    }

    /**
     * 江山市最近x小时累计实况降水
     *
     * @return
     */
    @GetMapping("/irsRainFallByHour")
    public AjaxResult getIrsRainFallByHour(Integer hour) {
        return AjaxResult.success(comprehensiveSceneService.getIrsRainFallByHour(hour));
    }




}
