package com.ruoyi.jiangshan.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.vo.BusinessCountVO;
import com.ruoyi.jiangshan.vo.BusinessDateCountVO;
import com.ruoyi.jiangshan.vo.WarningReportDeviceVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 设备实时数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@DataSource(DataSourceType.SLAVE)
public interface PushSjbmMapper
{

    void batchInsert(List<DeviceValue> deviceValueList);

    void batchInsertDeviceMonitor(List<DeviceMonitor> deviceMonitorList);

    void batchInsertDeviceInfo(List<DeviceInfo> deviceInfoList);

    void batchInsertWarningEvent(List<WarningEvent> warningEventList);

    void batchInsertWarningEventProcess(List<WarningEventProcess> warningEventProcessList);

}
