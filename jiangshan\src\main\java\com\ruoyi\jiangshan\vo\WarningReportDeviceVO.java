package com.ruoyi.jiangshan.vo;

import lombok.Data;

@Data
public class WarningReportDeviceVO {

    private Long id;

    private String deviceScene = "";

    /**
     * 设备类型
     */
    private String deviceType = "";

    /**
     * 设备名称
     */
    private String deviceName = "";

    /**
     * 设备编号
     */
    private String deviceThirdId = "";

    /**
     * 安装位置
     */
    private String deviceAddress = "";

    /**
     * 监测数据
     */
    private String monitorValue = "";

    /**
     * 监测数据单位
     */
    private String monitorUnit = "";

    /**
     * 状态
     */
    private String deviceStatus = "";

    /**
     * 预警级别
     */
    private Integer warningLevel;

    private String warningLevelStr = "";

    /**
     * 监测项
     */
    private String monitorItem = "";

    /**
     * 监测情况
     */
    private String warningReason = "";

    /**
     * 隐患点核实情况
     */
    private String verifyContent = "";


}
