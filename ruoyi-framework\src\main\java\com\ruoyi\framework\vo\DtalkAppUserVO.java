package com.ruoyi.framework.vo;

import lombok.Data;

@Data
public class DtalkAppUserVO {

    /**
     * 账号名
     */
    private String account;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 应用名
     */
    private String clientId;

    /**
     * 租户下人员编码
     */
    private String employeeCode;

    /**
     * 姓名
     */
    private String lastName;

    /**
     * 账号类型
     */
    private String namespace;

    /**
     * 昵称
     */
    private String nickNameCn;

    /**
     * 租户id
     */
    private String realmId;

    /**
     * 租户名称
     */
    private String realmName;

    /**
     * 租户+用户唯一标识
     */
    private String tenantUserId;

    /**
     * 应用+用户唯一标识
     */
    private String openid;
}
