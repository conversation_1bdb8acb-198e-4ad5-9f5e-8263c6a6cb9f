package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.PatrolDisposalInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡查处置信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface PatrolDisposalInfoMapper
{
    /**
     * 查询巡查处置信息
     *
     * @param id 巡查处置信息主键
     * @return 巡查处置信息
     */
    public PatrolDisposalInfo selectPatrolDisposalInfoById(Long id);

    /**
     * 查询巡查处置信息列表
     *
     * @param patrolDisposalInfo 巡查处置信息
     * @return 巡查处置信息集合
     */
    public List<PatrolDisposalInfo> selectPatrolDisposalInfoList(PatrolDisposalInfo patrolDisposalInfo);

    /**
     * 新增巡查处置信息
     *
     * @param patrolDisposalInfo 巡查处置信息
     * @return 结果
     */
    public int insertPatrolDisposalInfo(PatrolDisposalInfo patrolDisposalInfo);

    /**
     * 修改巡查处置信息
     *
     * @param patrolDisposalInfo 巡查处置信息
     * @return 结果
     */
    public int updatePatrolDisposalInfo(PatrolDisposalInfo patrolDisposalInfo);

    /**
     * 删除巡查处置信息
     *
     * @param id 巡查处置信息主键
     * @return 结果
     */
    public int deletePatrolDisposalInfoById(Long id);

    /**
     * 批量删除巡查处置信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatrolDisposalInfoByIds(Long[] ids);

    /**
     * 根据处置ID查询是否存在
     *
     * @param disposalId 处置ID
     * @return 结果
     */
    public PatrolDisposalInfo selectPatrolDisposalInfoByDisposalId(String disposalId);

    /**
     * 批量插入巡查处置信息数据
     *
     * @param patrolDisposalInfoList 巡查处置信息列表
     * @return 结果
     */
    public int batchInsertPatrolDisposalInfo(List<PatrolDisposalInfo> patrolDisposalInfoList);

    /**
     * 根据处置ID更新数据
     *
     * @param patrolDisposalInfo 巡查处置信息
     * @return 结果
     */
    public int updatePatrolDisposalInfoByDisposalId(PatrolDisposalInfo patrolDisposalInfo);
}
