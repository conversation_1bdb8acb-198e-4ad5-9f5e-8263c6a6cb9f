package com.ruoyi.jiangshan.vo.compre;

import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

@Data
public class DeviceYuLiangStreetAllVO {

    private List<DeviceYuLiangExtendVO> deviceList;

    private List<DeviceYuLiangStreetVO> streetList;

    public static DeviceYuLiangStreetAllVO Init() {
        DeviceYuLiangStreetAllVO vo = new DeviceYuLiangStreetAllVO();
        vo.setDeviceList(Lists.newArrayList());
        vo.setStreetList(Lists.newArrayList());

        return vo;
    }
}
