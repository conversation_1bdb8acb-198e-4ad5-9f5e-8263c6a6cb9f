package com.ruoyi.jiangshan.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 城市安全体检报告管理对象 t_citysafety_report
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@Data
public class CitysafetyReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 序号 */
    private Long id;

    /** 期数 */
    @Excel(name = "期数")
    private String reportPeriods;

    /** 名称 */
    @Excel(name = "名称")
    private String reportName;

    /** 报告场景 */
    @Excel(name = "报告场景")
    private String reportScene;

    /** 报告类型 0-瞬报、1-日报、2-周报、3-月报、4-半年报、5-年报 */
    @Excel(name = "报告类型 0-瞬报、1-日报、2-周报、3-月报、4-半年报、5-年报")
    private Integer reportType;

    /** 生成类型 1-合并生成 2-逐个生成 */
    @Excel(name = "生成类型 1-合并生成 2-逐个生成")
    private Integer generateType;

    /** 监测开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "监测开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date monitorStartTime;

    /** 监测结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "监测结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date monitorEndTime;

    private String streetId;

    private String streetName;
}
