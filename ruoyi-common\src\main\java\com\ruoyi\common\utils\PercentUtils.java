package com.ruoyi.common.utils;

import java.text.DecimalFormat;

public class PercentUtils {
    public static String calculatePercentage(String dividendStr, String divisorStr) {
        try {
            double dividend = Double.parseDouble(dividendStr);
            double divisor = Double.parseDouble(divisorStr);

            if (divisor == 0) {
                return "0.00%";
            }

            double result = (dividend / divisor) * 100;
            DecimalFormat df = new DecimalFormat("#.00%");
            return df.format(result / 100); // 因为DecimalFormat的%会自动乘以100
        } catch (NumberFormatException e) {
            return "0.00%"; // 处理无效数字输入
        } catch (Exception e) {
            return "0.00%"; // 其他异常处理
        }
    }
}
