<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.BusinessFileMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.BusinessFile">
    <!--@mbg.generated-->
    <!--@Table t_business_file-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, business_id, business_type, file_name, file_path, file_url, create_time, create_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_business_file
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_business_file
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.BusinessFile" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_business_file (business_id, business_type, file_name,
      file_path, file_url, create_time,
      create_by)
    values (#{businessId,jdbcType=BIGINT}, #{businessType,jdbcType=TINYINT}, #{fileName,jdbcType=VARCHAR},
      #{filePath,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.BusinessFile" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_business_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.BusinessFile">
    <!--@mbg.generated-->
    update t_business_file
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.BusinessFile">
    <!--@mbg.generated-->
    update t_business_file
    set business_id = #{businessId,jdbcType=BIGINT},
      business_type = #{businessType,jdbcType=TINYINT},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_path = #{filePath,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_business_file
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.businessId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="business_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.businessType,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="file_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fileName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="file_path = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.filePath,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="file_url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fileUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_business_file
    (business_id, business_type, file_name, file_path, file_url, create_time, create_by
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.businessId,jdbcType=BIGINT}, #{item.businessType,jdbcType=TINYINT}, #{item.fileName,jdbcType=VARCHAR},
        #{item.filePath,jdbcType=VARCHAR}, #{item.fileUrl,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.createBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="listByBusinessIdListAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_business_file
    where business_id in
    <foreach collection="idList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and business_type = #{type,jdbcType=INTEGER}
  </select>

  <delete id="deleteByBusinessIdAndTypeList">
    update t_business_file set business_id = null
    where business_id = #{id}
    and business_type in
    <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item}
    </foreach>
  </delete>

  <select id="listByBusinessIdListAndTypeList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_business_file
    where business_id in
    <foreach collection="monitorIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and business_type in
    <foreach collection="typeList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="listByType" resultMap="BaseResultMap">
      select <include refid="Base_Column_List">
  </include>
      from t_business_file
      where business_type = #{type,jdbcType=INTEGER}
      and business_id is not null
  </select>
</mapper>
