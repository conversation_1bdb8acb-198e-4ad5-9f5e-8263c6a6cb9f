package com.ruoyi.jiangshan.client;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.zlb.HmacAuthUtil;
import com.ruoyi.jiangshan.constant.IrsConstants;
import com.ruoyi.jiangshan.domain.PatrolDisposalInfo;
import com.ruoyi.jiangshan.domain.WeatherWarning;
import com.ruoyi.jiangshan.domain.RiskPreventionZone;
import com.ruoyi.jiangshan.mapper.PatrolDisposalInfoMapper;
import com.ruoyi.jiangshan.mapper.WeatherWarningMapper;
import com.ruoyi.jiangshan.mapper.RiskPreventionZoneMapper;
import com.ruoyi.jiangshan.vo.irs.IrsRainFallOneHourDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class GeologicClient {

    @Autowired
    private WeatherWarningMapper weatherWarningMapper;

    @Autowired
    private RiskPreventionZoneMapper riskPreventionZoneMapper;

    @Autowired
    private PatrolDisposalInfoMapper patrolDisposalInfoMapper;

    /**
     * 气象预警+专业监测预警接口地址
     */
    private static final String WEATHER_WARNING_API_URL = "https://csaqyj.jiangshan.gov.cn:8501/dizhi/d479c60d0b4935097476e1ab7869ee21";
    /**
     * 风险防范区接口地址
     */
    private static final String RISK_PREVENTION_ZONE_API_URL = "https://csaqyj.jiangshan.gov.cn:8501/dizhi/7b7c67b1059bfb105fb68b8619bbaae2";
    /**
     * 巡查处置信息接口地址
     */
    private static final String PATROL_DISPOSAL_INFO_API_URL = "https://csaqyj.jiangshan.gov.cn:8501/dizhi/e11594ad8e9d80a471bd3ee15c7f2b28";


    private static final String APP_KEY = "b3XLaUYFScOblaDxdh20Lg==";
    private static final String APP_SECRET = "/dXxX4r682GwIiDqWJnNNdXKMZR5yPlnuodHH2iTWVE=";

    /**
     * 调用第三方巡查处置信息接口并同步数据
     *
     * @param apiPageNum 页码
     * @param apiPageSize 每页条数
     * @return 同步结果
     */
    public Map<String, Object> syncPatrolDisposalInfoData(Integer apiPageNum, Integer apiPageSize) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始同步巡查处置信息数据，参数：apiPageNum={}, apiPageSize={}", apiPageNum, apiPageSize);

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("apiPageNum", apiPageNum != null ? apiPageNum : 1);
            params.put("apiPageSize", apiPageSize != null ? apiPageSize : 1000);

            // 调用第三方接口
            String response = callPatrolDisposalInfoApi(params);
            if (response == null || response.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "调用第三方接口失败，响应为空");
                return result;
            }

            // 解析响应数据
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.getInteger("code") != 0) {
                result.put("success", false);
                result.put("message", "第三方接口返回错误：" + responseJson.getString("message"));
                return result;
            }

            // 获取数据内容
            JSONObject dataObj = responseJson.getJSONObject("data");
            JSONArray data = dataObj.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有获取到巡查处置信息数据");
                return result;
            }

            // 转换数据并插入数据库
            List<PatrolDisposalInfo> patrolDisposalInfoList = convertToPatrolDisposalInfoList(data);
            int insertCount = insertPatrolDisposalInfoData(patrolDisposalInfoList);

            result.put("success", true);
            result.put("message", "同步成功");
            result.put("total", data.size());
            result.put("insertCount", insertCount);
            result.put("page", apiPageNum);
            result.put("size", apiPageSize);

            log.info("巡查处置信息数据同步完成，总数：{}，插入数量：{}", data.size(), insertCount);

        } catch (Exception e) {
            log.error("同步巡查处置信息数据失败", e);
            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 调用第三方巡查处置信息接口
     *
     * @param params 请求参数
     * @return 响应结果
     */
    private String callPatrolDisposalInfoApi(Map<String, Object> params) {
        try {
            // 构建查询字符串
            StringBuilder queryString = new StringBuilder();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (queryString.length() > 0) {
                    queryString.append("&");
                }
                queryString.append(entry.getKey()).append("=").append(entry.getValue());
            }

            String fullUrl = PATROL_DISPOSAL_INFO_API_URL + "?" + queryString.toString();
            log.info("调用巡查处置信息接口：{}", fullUrl);

            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("AppKey", APP_KEY);
            headerMap.put("AppSecret", APP_SECRET);

            // 发送GET请求
            String response = HttpUtil.createGet(fullUrl)
                    .addHeaders(headerMap)
                    .execute()
                    .body();

            log.info("巡查处置信息接口响应：{}", response);

            return response;

        } catch (Exception e) {
            log.error("调用巡查处置信息接口失败", e);
            return null;
        }
    }

    /**
     * 将JSON数组转换为PatrolDisposalInfo对象列表
     *
     * @param data JSON数组
     * @return PatrolDisposalInfo列表
     */
    private List<PatrolDisposalInfo> convertToPatrolDisposalInfoList(JSONArray data) {
        List<PatrolDisposalInfo> patrolDisposalInfoList = new ArrayList<>();

        for (int i = 0; i < data.size(); i++) {
            JSONObject item = data.getJSONObject(i);

            PatrolDisposalInfo patrolDisposalInfo = new PatrolDisposalInfo();
            patrolDisposalInfo.setDisposalId(String.valueOf(item.getLong("id")));
            patrolDisposalInfo.setRiskZoneNum(item.getString("riskZoneNum"));
            patrolDisposalInfo.setRiskZoneName(item.getString("riskZoneName"));
            patrolDisposalInfo.setAreaCode(item.getString("areaCode"));
            patrolDisposalInfo.setWarningLevel(String.valueOf(item.getInteger("warningLevel")));
            patrolDisposalInfo.setWarningType(String.valueOf(item.getInteger("warningType")));
            patrolDisposalInfo.setReadStatus(String.valueOf(item.getInteger("readStatus")));
            patrolDisposalInfo.setDeadStatus(String.valueOf(item.getInteger("deadStatus")));
            patrolDisposalInfo.setCreateTimeXc(DateUtils.parseDate(item.getString("createTimeXc")));
            patrolDisposalInfo.setUserId(String.valueOf(item.getLong("userId")));
            patrolDisposalInfo.setIsPositive(String.valueOf(item.getInteger("isPositive")));
            patrolDisposalInfo.setIsSlid(item.getString("isSlid"));
            patrolDisposalInfo.setType(item.getString("type"));
            patrolDisposalInfo.setContent(item.getString("content"));
            patrolDisposalInfo.setDealPeople(item.getString("dealPeople"));
            patrolDisposalInfo.setDealTime(DateUtils.parseDate(item.getString("dealTime")));
            patrolDisposalInfo.setMonitorPointCode(item.getString("monitorPointCode"));
            patrolDisposalInfo.setStatus(item.getString("status"));
            patrolDisposalInfo.setCreateTimeCz(DateUtils.parseDate(item.getString("createTimeCz")));
            patrolDisposalInfo.setUpdateTimeCz(DateUtils.parseDate(item.getString("updateTimeCz")));
            patrolDisposalInfo.setCreateBy(String.valueOf(item.getLong("createBy")));
            patrolDisposalInfo.setUpdateBy(String.valueOf(item.getLong("updateBy")));
            patrolDisposalInfo.setIsDeleted(String.valueOf(item.getInteger("isDeleted")));
            patrolDisposalInfo.setIsCallNotify(item.getString("isCallNotify"));
            patrolDisposalInfo.setIsTriggerBroadcast(item.getString("isTriggerBroadcast"));
            patrolDisposalInfo.setMonitorPointName(item.getString("monitorPointName"));
            patrolDisposalInfo.setSyncTime(new Date());

            patrolDisposalInfoList.add(patrolDisposalInfo);
        }

        return patrolDisposalInfoList;
    }

    /**
     * 插入巡查处置信息数据到数据库
     *
     * @param patrolDisposalInfoList 巡查处置信息列表
     * @return 插入数量
     */
    private int insertPatrolDisposalInfoData(List<PatrolDisposalInfo> patrolDisposalInfoList) {
        if (patrolDisposalInfoList == null || patrolDisposalInfoList.isEmpty()) {
            return 0;
        }

        int insertCount = 0;

        for (PatrolDisposalInfo patrolDisposalInfo : patrolDisposalInfoList) {
            try {
                // 检查是否已存在（根据disposalId）
                PatrolDisposalInfo existingInfo = patrolDisposalInfoMapper.selectPatrolDisposalInfoByDisposalId(patrolDisposalInfo.getDisposalId());

                if (existingInfo != null) {
                    // 更新现有数据
                    patrolDisposalInfo.setId(existingInfo.getId());
                    int updateResult = patrolDisposalInfoMapper.updatePatrolDisposalInfoByDisposalId(patrolDisposalInfo);
                    if (updateResult > 0) {
                        insertCount++;
                    }
                } else {
                    // 插入新数据
                    int insertResult = patrolDisposalInfoMapper.insertPatrolDisposalInfo(patrolDisposalInfo);
                    if (insertResult > 0) {
                        insertCount++;
                    }
                }

            } catch (Exception e) {
                log.error("插入/更新巡查处置信息数据失败：{}", patrolDisposalInfo.getDisposalId(), e);
            }
        }

        return insertCount;
    }

    /**
     * 分页同步所有巡查处置信息数据
     *
     * @param pageSize 每页大小
     * @return 同步结果
     */
    public Map<String, Object> syncAllPatrolDisposalInfoData(Integer pageSize) {
        Map<String, Object> result = new HashMap<>();
        int totalInsertCount = 0;
        int page = 1;
        boolean hasMore = true;

        try {
            while (hasMore) {
                Map<String, Object> pageResult = syncPatrolDisposalInfoData(page, pageSize);

                if (!(Boolean) pageResult.get("success")) {
                    result.put("success", false);
                    result.put("message", "第" + page + "页同步失败：" + pageResult.get("message"));
                    return result;
                }

                Integer insertCount = (Integer) pageResult.get("insertCount");
                totalInsertCount += insertCount;

                // 判断是否还有更多数据
                Integer total = (Integer) pageResult.get("total");
                if (total < pageSize) {
                    hasMore = false;
                } else {
                    page++;
                }

                // 避免请求过于频繁
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            result.put("success", true);
            result.put("message", "所有数据同步完成");
            result.put("totalInsertCount", totalInsertCount);
            result.put("totalPages", page);

        } catch (Exception e) {
            log.error("分页同步巡查处置信息数据失败", e);
            result.put("success", false);
            result.put("message", "分页同步失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 调用第三方风险防范区接口并同步数据
     *
     * @param startTime 开始时间，格式：2023-01-01
     * @param endTime 结束时间，格式：2023-01-01
     * @param page 分页页码，默认1
     * @param size 每页条数，默认3000
     * @return 同步结果
     */
    public Map<String, Object> syncRiskPreventionZoneData(String startTime, String endTime, Integer page, Integer size) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始同步风险防范区数据，参数：startTime={}, endTime={}, page={}, size={}", startTime, endTime, page, size);

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            if (startTime != null && !startTime.trim().isEmpty()) {
                params.put("startTime", startTime);
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                params.put("endTime", endTime);
            }
            params.put("page", page != null ? page : 1);
            params.put("size", size != null ? size : 3000);

            // 调用第三方接口
            String response = callRiskPreventionZoneApi(params);
            if (response == null || response.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "调用第三方接口失败，响应为空");
                return result;
            }

            // 解析响应数据
            JSONObject responseJson = JSON.parseObject(response);
            if (!"0000".equals(responseJson.getString("code"))) {
                result.put("success", false);
                result.put("message", "第三方接口返回错误：" + responseJson.getString("msg"));
                return result;
            }

            // 获取数据内容
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                result.put("success", false);
                result.put("message", "第三方接口返回数据为空");
                return result;
            }

            Integer total = data.getInteger("total");
            JSONArray content = data.getJSONArray("content");

            if (content == null || content.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有获取到风险防范区数据");
                return result;
            }

            // 转换数据并插入数据库
            List<RiskPreventionZone> riskPreventionZoneList = convertToRiskPreventionZoneList(content);
            int insertCount = insertRiskPreventionZoneData(riskPreventionZoneList);

            result.put("success", true);
            result.put("message", "同步成功");
            result.put("total", total);
            result.put("insertCount", insertCount);
            result.put("page", page);
            result.put("size", size);

            log.info("风险防范区数据同步完成，总数：{}，插入数量：{}", total, insertCount);

        } catch (Exception e) {
            log.error("同步风险防范区数据失败", e);
            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 调用第三方风险防范区接口
     *
     * @param params 请求参数
     * @return 响应结果
     */
    private String callRiskPreventionZoneApi(Map<String, Object> params) {
        try {
            // 构建查询字符串
            StringBuilder queryString = new StringBuilder();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (queryString.length() > 0) {
                    queryString.append("&");
                }
                queryString.append(entry.getKey()).append("=").append(entry.getValue());
            }

            String fullUrl = RISK_PREVENTION_ZONE_API_URL + "?" + queryString.toString();
            log.info("调用风险防范区接口：{}", fullUrl);

            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("AppKey", APP_KEY);
            headerMap.put("AppSecret", APP_SECRET);

            // 发送GET请求
            String response = HttpUtil.createGet(fullUrl)
                    .addHeaders(headerMap)
                    .execute()
                    .body();

            log.info("风险防范区接口响应：{}", response);

            return response;

        } catch (Exception e) {
            log.error("调用风险防范区接口失败", e);
            return null;
        }
    }

    /**
     * 将JSON数组转换为RiskPreventionZone对象列表
     *
     * @param content JSON数组
     * @return RiskPreventionZone列表
     */
    private List<RiskPreventionZone> convertToRiskPreventionZoneList(JSONArray content) {
        List<RiskPreventionZone> riskPreventionZoneList = new ArrayList<>();

        for (int i = 0; i < content.size(); i++) {
            JSONObject item = content.getJSONObject(i);

            RiskPreventionZone riskPreventionZone = new RiskPreventionZone();
            riskPreventionZone.setNum(item.getString("num"));
            riskPreventionZone.setName(item.getString("name"));
            riskPreventionZone.setAreaCode(item.getString("areaCode"));
            riskPreventionZone.setSzs(item.getString("szs"));
            riskPreventionZone.setSzqx(item.getString("szqx"));
            riskPreventionZone.setSzz(item.getString("szz"));
            riskPreventionZone.setRiskLevel(item.getString("risk_level"));
            riskPreventionZone.setStability(item.getString("stability"));
            riskPreventionZone.setDatatype(item.getString("datatype"));
            riskPreventionZone.setStatus(item.getString("status"));
            riskPreventionZone.setAffectedPeople(item.getInteger("affected_people"));
            riskPreventionZone.setAffectedFamily(item.getInteger("affected_family"));
            riskPreventionZone.setAffectedAsset(parseBigDecimal(item.get("affected_asset")));
            riskPreventionZone.setPermanentResidents(item.getInteger("permanent_residents"));
            riskPreventionZone.setHazardAffectedBody(item.getString("hazard_affected_body"));
            riskPreventionZone.setPlaceLeader(item.getString("place_leader"));
            riskPreventionZone.setPlaceLeaderTel(item.getString("place_leader_tel"));
            riskPreventionZone.setCountyLeader(item.getString("county_leader"));
            riskPreventionZone.setCountyLeaderTel(item.getString("county_leader_tel"));
            riskPreventionZone.setVillagesLeader(item.getString("villages_leader"));
            riskPreventionZone.setVillageLeaderTel(item.getString("village_leader_tel"));
            riskPreventionZone.setGrassRoots(item.getString("grass_roots"));
            riskPreventionZone.setGrassRootsTel(item.getString("grass_roots_tel"));
            riskPreventionZone.setHandUnit(item.getString("hand_unit"));
            riskPreventionZone.setCancelReason(item.getString("cancel_reason"));
            riskPreventionZone.setCancelReasonOther(item.getString("cancel_reason_other"));
            riskPreventionZone.setRevokeDate(parseDateTime(item.getString("revoke_date")));
            riskPreventionZone.setCommitDate(parseDateTime(item.getString("commit_date")));
            riskPreventionZone.setCreateTime(parseDateTime(item.getString("create_time")));
            riskPreventionZone.setUpdateTime(parseDateTime(item.getString("update_time")));
            riskPreventionZone.setGeo(item.getString("geo"));
            riskPreventionZone.setPointGeo(item.getString("point_geo"));
            riskPreventionZone.setOneHour1(parseBigDecimal(item.get("one_hour_1")));
            riskPreventionZone.setOneHour2(parseBigDecimal(item.get("one_hour_2")));
            riskPreventionZone.setOneHour3(parseBigDecimal(item.get("one_hour_3")));
            riskPreventionZone.setThreeHour1(parseBigDecimal(item.get("three_hour_1")));
            riskPreventionZone.setThreeHour2(parseBigDecimal(item.get("three_hour_2")));
            riskPreventionZone.setThreeHour3(parseBigDecimal(item.get("three_hour_3")));
            riskPreventionZone.setSixHour1(parseBigDecimal(item.get("six_hour_1")));
            riskPreventionZone.setSixHour2(parseBigDecimal(item.get("six_hour_2")));
            riskPreventionZone.setSixHour3(parseBigDecimal(item.get("six_hour_3")));
            riskPreventionZone.setTwelveHour1(parseBigDecimal(item.get("twelve_hour_1")));
            riskPreventionZone.setTwelveHour2(parseBigDecimal(item.get("twelve_hour_2")));
            riskPreventionZone.setTwelveHour3(parseBigDecimal(item.get("twelve_hour_3")));
            riskPreventionZone.setTwentyFourHour1(parseBigDecimal(item.get("twenty_four_hour_1")));
            riskPreventionZone.setTwentyFourHour2(parseBigDecimal(item.get("twenty_four_hour_2")));
            riskPreventionZone.setTwentyFourHour3(parseBigDecimal(item.get("twenty_four_hour_3")));
            riskPreventionZone.setSyncTime(new Date());

            riskPreventionZoneList.add(riskPreventionZone);
        }

        return riskPreventionZoneList;
    }

    /**
     * 解析BigDecimal类型数据
     *
     * @param value 原始值
     * @return BigDecimal对象
     */
    private BigDecimal parseBigDecimal(Object value) {
        if (value == null) {
            return null;
        }

        try {
            if (value instanceof Number) {
                return new BigDecimal(value.toString());
            } else if (value instanceof String) {
                String strValue = (String) value;
                if (strValue.trim().isEmpty()) {
                    return null;
                }
                return new BigDecimal(strValue);
            }
        } catch (Exception e) {
            log.warn("解析BigDecimal失败：{}", value, e);
        }

        return null;
    }

    /**
     * 插入风险防范区数据到数据库
     *
     * @param riskPreventionZoneList 风险防范区列表
     * @return 插入数量
     */
    private int insertRiskPreventionZoneData(List<RiskPreventionZone> riskPreventionZoneList) {
        if (riskPreventionZoneList == null || riskPreventionZoneList.isEmpty()) {
            return 0;
        }

        int insertCount = 0;

        for (RiskPreventionZone riskPreventionZone : riskPreventionZoneList) {
            try {
                // 检查是否已存在（根据num编号）
                RiskPreventionZone existingZone = riskPreventionZoneMapper.selectRiskPreventionZoneByNum(riskPreventionZone.getNum());

                if (existingZone != null) {
                    // 更新现有数据
                    riskPreventionZone.setId(existingZone.getId());
                    int updateResult = riskPreventionZoneMapper.updateRiskPreventionZoneByNum(riskPreventionZone);
                    if (updateResult > 0) {
                        insertCount++;
                    }
                } else {
                    // 插入新数据
                    int insertResult = riskPreventionZoneMapper.insertRiskPreventionZone(riskPreventionZone);
                    if (insertResult > 0) {
                        insertCount++;
                    }
                }

            } catch (Exception e) {
                log.error("插入/更新风险防范区数据失败：{}", riskPreventionZone.getNum(), e);
            }
        }

        return insertCount;
    }

    /**
     * 分页同步所有风险防范区数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageSize 每页大小
     * @return 同步结果
     */
    public Map<String, Object> syncAllRiskPreventionZoneData(String startTime, String endTime, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();
        int totalInsertCount = 0;
        int page = 1;
        boolean hasMore = true;

        try {
            while (hasMore) {
                Map<String, Object> pageResult = syncRiskPreventionZoneData(startTime, endTime, page, pageSize);

                if (!(Boolean) pageResult.get("success")) {
                    result.put("success", false);
                    result.put("message", "第" + page + "页同步失败：" + pageResult.get("message"));
                    return result;
                }

                Integer insertCount = (Integer) pageResult.get("insertCount");
                totalInsertCount += insertCount;

                // 判断是否还有更多数据
                Integer total = (Integer) pageResult.get("total");
                if (page * pageSize >= total) {
                    hasMore = false;
                } else {
                    page++;
                }

                // 避免请求过于频繁
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            result.put("success", true);
            result.put("message", "所有数据同步完成");
            result.put("totalInsertCount", totalInsertCount);
            result.put("totalPages", page);

        } catch (Exception e) {
            log.error("分页同步风险防范区数据失败", e);
            result.put("success", false);
            result.put("message", "分页同步失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 调用第三方气象预警接口并同步数据
     *
     * @param startTime 开始时间，格式：2023-01-01
     * @param endTime 结束时间，格式：2023-01-01
     * @param page 分页页码，默认1
     * @param size 每页条数，默认3000
     * @return 同步结果
     */
    public Map<String, Object> syncWeatherWarningData(String startTime, String endTime, Integer page, Integer size) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始同步气象预警数据，参数：startTime={}, endTime={}, page={}, size={}", startTime, endTime, page, size);

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            if (startTime != null && !startTime.trim().isEmpty()) {
                params.put("startTime", startTime);
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                params.put("endTime", endTime);
            }
            params.put("page", page != null ? page : 1);
            params.put("size", size != null ? size : 3000);

            // 调用第三方接口
            String response = callWeatherWarningApi(params);
            if (response == null || response.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "调用第三方接口失败，响应为空");
                return result;
            }

            // 解析响应数据
            JSONObject responseJson = JSON.parseObject(response);
            if (!"0000".equals(responseJson.getString("code"))) {
                result.put("success", false);
                result.put("message", "第三方接口返回错误：" + responseJson.getString("msg"));
                return result;
            }

            // 获取数据内容
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                result.put("success", false);
                result.put("message", "第三方接口返回数据为空");
                return result;
            }

            Integer total = data.getInteger("total");
            JSONArray content = data.getJSONArray("content");

            if (content == null || content.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有获取到预警数据");
                return result;
            }

            // 转换数据并插入数据库
            List<WeatherWarning> weatherWarningList = convertToWeatherWarningList(content);
            int insertCount = insertWeatherWarningData(weatherWarningList);

            result.put("success", true);
            result.put("message", "同步成功");
            result.put("total", total);
            result.put("insertCount", insertCount);
            result.put("page", page);
            result.put("size", size);

            log.info("气象预警数据同步完成，总数：{}，插入数量：{}", total, insertCount);

        } catch (Exception e) {
            log.error("同步气象预警数据失败", e);
            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 调用第三方气象预警接口
     *
     * @param params 请求参数
     * @return 响应结果
     */
    private String callWeatherWarningApi(Map<String, Object> params) {
        try {
            // 构建查询字符串
            StringBuilder queryString = new StringBuilder();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (queryString.length() > 0) {
                    queryString.append("&");
                }
                queryString.append(entry.getKey()).append("=").append(entry.getValue());
            }

            String fullUrl = WEATHER_WARNING_API_URL + "?" + queryString.toString();
            log.info("调用气象预警接口：{}", fullUrl);

            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("AppKey", APP_KEY);
            headerMap.put("AppSecret", APP_SECRET);

            // 发送GET请求
            String response = HttpUtil.createGet(fullUrl)
                    .addHeaders(headerMap)
                    .execute()
                    .body();

            log.info("气象预警接口响应：{}", response);

            return response;

        } catch (Exception e) {
            log.error("调用气象预警接口失败", e);
            return null;
        }
    }

    /**
     * 将JSON数组转换为WeatherWarning对象列表
     *
     * @param content JSON数组
     * @return WeatherWarning列表
     */
    private List<WeatherWarning> convertToWeatherWarningList(JSONArray content) {
        List<WeatherWarning> weatherWarningList = new ArrayList<>();

        for (int i = 0; i < content.size(); i++) {
            JSONObject item = content.getJSONObject(i);

            WeatherWarning weatherWarning = new WeatherWarning();
            weatherWarning.setWarningId(item.getString("warning_id"));
            weatherWarning.setRiskZoneNum(item.getString("risk_zone_num"));
            weatherWarning.setRiskZoneId(item.getString("risk_zone_id"));
            weatherWarning.setAreaCode(item.getString("area_code"));
            weatherWarning.setWarningLevel(item.getInteger("warning_level"));
            weatherWarning.setStationCode(item.getString("station_code"));
            weatherWarning.setRemark(item.getString("remark"));
            weatherWarning.setWarningType(item.getInteger("warning_type"));

            // 转换时间格式
            weatherWarning.setWarningTime(parseDateTime(item.getString("warning_time")));
            weatherWarning.setCreateTime(parseDateTime(item.getString("create_time")));
            weatherWarning.setUpdateTime(parseDateTime(item.getString("update_time")));
            weatherWarning.setSyncTime(new Date());

            weatherWarningList.add(weatherWarning);
        }

        return weatherWarningList;
    }

    /**
     * 解析ISO 8601格式的时间字符串
     *
     * @param dateTimeStr 时间字符串，格式：2022-06-15T20:03:00+08:00
     * @return Date对象
     */
    private Date parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 解析ISO 8601格式的时间
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateTimeStr);
            return Date.from(zonedDateTime.toInstant());
        } catch (Exception e) {
            log.warn("解析时间字符串失败：{}", dateTimeStr, e);
            return null;
        }
    }

    /**
     * 插入气象预警数据到数据库
     *
     * @param weatherWarningList 气象预警列表
     * @return 插入数量
     */
    private int insertWeatherWarningData(List<WeatherWarning> weatherWarningList) {
        if (weatherWarningList == null || weatherWarningList.isEmpty()) {
            return 0;
        }

        int insertCount = 0;

        for (WeatherWarning weatherWarning : weatherWarningList) {
            try {
                // 检查是否已存在（根据warningId）
                WeatherWarning existingWarning = weatherWarningMapper.selectWeatherWarningByWarningId(weatherWarning.getWarningId());

                if (existingWarning != null) {
                    // 更新现有数据
                    weatherWarning.setId(existingWarning.getId());
                    int updateResult = weatherWarningMapper.updateWeatherWarningByWarningId(weatherWarning);
                    if (updateResult > 0) {
                        insertCount++;
                    }
                } else {
                    // 插入新数据
                    int insertResult = weatherWarningMapper.insertWeatherWarning(weatherWarning);
                    if (insertResult > 0) {
                        insertCount++;
                    }
                }

            } catch (Exception e) {
                log.error("插入/更新气象预警数据失败：{}", weatherWarning.getWarningId(), e);
            }
        }

        return insertCount;
    }

    /**
     * 分页同步所有气象预警数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageSize 每页大小
     * @return 同步结果
     */
    public Map<String, Object> syncAllWeatherWarningData(String startTime, String endTime, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();
        int totalInsertCount = 0;
        int page = 1;
        boolean hasMore = true;

        try {
            while (hasMore) {
                Map<String, Object> pageResult = syncWeatherWarningData(startTime, endTime, page, pageSize);

                if (!(Boolean) pageResult.get("success")) {
                    result.put("success", false);
                    result.put("message", "第" + page + "页同步失败：" + pageResult.get("message"));
                    return result;
                }

                Integer insertCount = (Integer) pageResult.get("insertCount");
                totalInsertCount += insertCount;

                // 判断是否还有更多数据
                Integer total = (Integer) pageResult.get("total");
                if (page * pageSize >= total) {
                    hasMore = false;
                } else {
                    page++;
                }

                // 避免请求过于频繁
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            result.put("success", true);
            result.put("message", "所有数据同步完成");
            result.put("totalInsertCount", totalInsertCount);
            result.put("totalPages", page);

        } catch (Exception e) {
            log.error("分页同步气象预警数据失败", e);
            result.put("success", false);
            result.put("message", "分页同步失败：" + e.getMessage());
        }

        return result;
    }

}
