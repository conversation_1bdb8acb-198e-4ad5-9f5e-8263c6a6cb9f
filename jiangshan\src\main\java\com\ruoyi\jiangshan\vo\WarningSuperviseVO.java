package com.ruoyi.jiangshan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class WarningSuperviseVO {

    private Long id;

    private String eventName;

    private String eventType;

    private String superviseType;

    private Integer warningLevel;

    private Long departmentId;

    private String departmentName;

    private String departmentOrganizationCode;

    private String eventAddress;

    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date warningTime;

    private String bizCode;
}
