package com.ruoyi.jiangshan.domain;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
    * 设备规则
    */
@Data
public class WarningRuleShuxin {
    /**
    * 主键
    */
    private Long id;

    /**
    * 规则名称
    */
    private String ruleName;

    /**
    * 适用场景
    */
    private String modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
    * 发布渠道 1浙政钉推送
    */
    private Integer publishWay;

    /**
    * 详细描述
    */
    private String ruleDetail;

    /**
    * 推送预警的用户id
    */
    private Long warningUserId;

    /**
    * 推送预警的用户名称
    */
    private String warningUserName;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 创建人
    */
    private String createBy;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 更新人
    */
    private String updateBy;

    /**
    * 备注
    */
    private String remark;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 阈值列表
     */
    private List<WarningRuleConditionShuxin> conditionList;

    private String otherUserId;

    private List<String> otherUserIdList;

    private String allAccountId;

    private String allMobile;

}
