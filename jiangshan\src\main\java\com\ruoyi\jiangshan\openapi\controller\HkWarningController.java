package com.ruoyi.jiangshan.openapi.controller;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.jiangshan.domain.WarningEventHikvision;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.vo.hk.WarningEventHikvisionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.fileupload.servlet.ServletRequestContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.servlet.ServletException;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 海康威视监听地址
 */
@RestController
@RequestMapping("/openapi/hk")
@Slf4j
public class HkWarningController {
    @Autowired
    private IWarningEventService warningEventService;

    /**
     * 监听接口事件解析，设备监听页面设置URL为“/test”
     * https://csaq.jiangshan.gov.cn:8501/prod-api/openapi/hk/warningEvent
     * @param request
     * @param response
     * @throws ServletException
     * @throws IOException
     */
    @PostMapping("/warningEvent")
    public void doPost(@RequestPart(value = "fireEscapeDetection", required = false) String fireEscapeDetection,
                       @RequestPart(value = "targetImage.jpg", required = false) MultipartFile file1,
                       @RequestPart(value = "backgroundImage.jpg", required = false) MultipartFile file2,
                       HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        log.info("HkWarningController, fireEscapeDetection:{}", fireEscapeDetection);

        if (Objects.nonNull(file1)) {
            log.info("HkWarningController, file1:{}", file1.getName());
        }
        if (Objects.nonNull(file2)) {
            log.info("HkWarningController, file2:{}", file2.getName());
        }

        try {
            warningEventService.handleHkWarningEvent(fireEscapeDetection, request, response, file1, file2);

            //正常接收平台响应回复
            response.getWriter().write("success");
        } catch (Exception e) {
            e.printStackTrace();
            response.getWriter().write("false");
        }
    }

    /**
     * 监听接口事件解析，设备监听页面设置URL为“/test”
     * https://csaq.jiangshan.gov.cn:8501/prod-api/openapi/hk/warningEvent
     * @param request
     * @param response
     * @throws ServletException
     * @throws IOException
     */
    @PostMapping("/warningEventV2")
    public void doPostV2(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 打印所有请求头
        Collections.list(request.getHeaderNames()).forEach(headerName -> {
            log.info("HkWarningController, Header - {}: {}", headerName, request.getHeader(headerName));
        });

        // 打印请求参数
        request.getParameterMap().forEach((key, value) -> {
            log.info("HkWarningController, Parameter - {}: {}", key, Arrays.toString(value));
        });

        // 添加日志打印Content-Type
        String contentType = request.getContentType();
        log.info("HkWarningController, Content-Type: {}", contentType);

        //解析multipart/form-data类型报文
        FileItemFactory factory = new DiskFileItemFactory();
        ServletFileUpload sf = new ServletFileUpload(factory);

        try {
            if (ServletFileUpload.isMultipartContent(request)) {
                List<FileItem> formData = sf.parseRequest(new ServletRequestContext(request));

                if (CollectionUtils.isNotEmpty(formData)) {
                    log.info("HkWarningController, formData:{}", JSON.toJSONString(formData));
                } else {
                    log.info("HkWarningController, empty");
                }

                for (FileItem fi : formData) {
                    if (fi.isFormField()) {
                        log.info("");
                        //解析报文中json
                        log.info("HkWarningController, field_name:" + fi.getFieldName());
                        //打印json格式上传的事件报文
                        log.info("HkWarningController, " + fi.getString("UTF-8"));
                        //通过表单name属性进行解析报文
                        switch (fi.getFieldName()) {
                            case "event_log"://门禁、身份证事件解析
                                log.info("HkWarningController, receive Acs event");
                                break;
                            default:
                                log.info("HkWarningController, unknow data");
                        }
                    } else {
                        //保存报文中的图片数据
                        String image_name = fi.getName();
                        log.info("HkWarningController, image_name:" + image_name);
                        if (image_name != "") {
                            //图片保存路径
                            String image_dir_path = "C:\\image\\";

                            File image_dir = new File(image_dir_path);
                            if (!image_dir.exists()) {
                                image_dir.mkdir();
                            }
                            String file_name = UUID.randomUUID().toString();
                            String suffix = image_name.substring(fi.getName().lastIndexOf("."));
                            log.info("HkWarningController, 图片报文路径:" + image_dir_path);
                            log.info("HkWarningController, 图片名称:" + file_name);
                            log.info("HkWarningController, 图片格式:" + suffix);
                            fi.write(new File(image_dir_path, file_name + suffix));
                        } else {
                            throw new Exception("no file receive");
                        }
                    }
                }
            }
            //application/json报文解析
            else {
                //报文解析
                String Content_Type = request.getContentType();
                BufferedReader reader = request.getReader();
                StringBuilder requestData = new StringBuilder();
                String line = null;
                while ((line = reader.readLine()) != null) {
                    requestData.append(line);
                }

                log.info("HkWarningController, requestData:{}", requestData.toString());

                if (Content_Type.contains("application/json"))
                {
                    Object jsonObject = JSON.parse(requestData.toString());
                    // 打印JSON对象
                    System.out.println(jsonObject.toString());
                } else if (Content_Type.contains("application/xml")){
                    try {
                        DocumentBuilderFactory documentFactory = DocumentBuilderFactory.newInstance();
                        DocumentBuilder builder = documentFactory.newDocumentBuilder();
                        Document document = builder.parse(new ByteArrayInputStream(requestData.toString().getBytes("UTF-8")));

                        // 获取根元素
                        Element root = document.getDocumentElement();

                        // 解析关键信息
                        String eventType = getElementValue(root, "eventType");
                        String eventState = getElementValue(root, "eventState");
                        String dateTime = getElementValue(root, "dateTime");
                        String licensePlate = getElementValue(root, "licensePlate");

                        log.info("HkWarningController, 收到停车侦测事件：事件类型={}, 状态={}, 时间={}, 车牌号={}",
                                eventType, eventState, dateTime, licensePlate);

                        // TODO: 根据业务需求处理事件数据
                    } catch (Exception e) {
                        log.error("解析XML数据失败", e);
                        throw e;
                    }
                }
            }

            //正常接收平台响应回复
            response.getWriter().write("success");
        } catch (Exception e) {
            e.printStackTrace();
            response.getWriter().write("false");
        }
    }

    // 添加工具方法
    private String getElementValue(Element parent, String tagName) {
        NodeList nodeList = parent.getElementsByTagName(tagName);
        if (nodeList != null && nodeList.getLength() > 0) {
            Node node = nodeList.item(0);
            if (node.getFirstChild() != null) {
                return node.getFirstChild().getNodeValue();
            }
        }

        return null;
    }
}
