package com.ruoyi.jiangshan.mapper;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.domain.WarningEventDepartment;
import com.ruoyi.jiangshan.domain.WarningInfo;
import com.ruoyi.jiangshan.openapi.vo.WarningEventQueryVO;
import com.ruoyi.jiangshan.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 设备预警事件Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface WarningEventMapper
{
    /**
     * 查询设备预警事件
     *
     * @param id 设备预警事件主键
     * @return 设备预警事件
     */
    public WarningEvent selectWarningEventById(Long id);

    /**
     * 查询设备预警事件列表
     *
     * @param pageVO 设备预警事件
     * @return 设备预警事件集合
     */
    public List<WarningEvent> selectWarningEventList(WarningEventPageVO pageVO);

    /**
     * 新增设备预警事件
     *
     * @param warningEvent 设备预警事件
     * @return 结果
     */
    public int insertWarningEvent(WarningEvent warningEvent);

    /**
     * 修改设备预警事件
     *
     * @param warningEvent 设备预警事件
     * @return 结果
     */
    public int updateWarningEvent(WarningEvent warningEvent);

    /**
     * 删除设备预警事件
     *
     * @param id 设备预警事件主键
     * @return 结果
     */
    public int deleteWarningEventById(Long id);

    /**
     * 批量删除设备预警事件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWarningEventByIds(Long[] ids);

    long countByStatus(@Param("statusList") List<Integer> statusList, @Param("overFlag") Boolean overFlag,
                       @Param("superviseFlag") Boolean superviseFlag);

    List<WarningEvent> listLastFourWarning();

    long countAll();

    List<BusinessPercentCountVO> countByDepartment(@Param("overFlag") Boolean overFlag);

    List<BusinessCountVO> countLevelBySceneAndTime(@Param("scene") String scene,
                                                   @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<WarningEvent> listByConditionLimit(@Param("scene") String scene,
                                            @Param("eventName") String eventName,
                                            @Param("status") Integer status,
                                            @Param("warningLevel") Integer warningLevel,
                                            @Param("limitNum") Integer limitNum);

    void batchInsert(List<WarningEvent> warningEventList);

    List<WarningEvent> listAllNonOvertime();

    List<WarningEvent> listAllWarningOneDayInner(@Param("scene") String scene, @Param("warningLevelList") List<Integer> warningLevelList);

    List<WarningEvent> listByEventType(String scene);

    List<BusinessPercentCountVO> countByScene(@Param("overFlag") Boolean overFlag);

    List<WarningSuperviseVO> listSupervise(WarningEventPageVO pageVO);

    void batchUpdateOverFlag(List<Long> updateEventIdList);

    void batchUpdateSuperviseFlag(@Param("eventIdUpdateList") List<Long> eventIdUpdateList, @Param("superviseType") Integer superviseType);

    void batchUpdateLeaderContent(@Param("employeeIdList") List<Long> employeeIdList, @Param("content") String content);

    List<BusinessCountVO> countByAddress();

    List<BusinessCountVO> countByDate(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<CityRiskEventVO> listByAreaCode(@Param("areaCode") String areaCode);

    List<WarningEvent> listUnCompleteEvent();

    List<WarningEvent> listLastFourWarningV2(WarningEventPageVO pageVO);

    List<WarningEvent> listNonCompleteEvent();

    WarningEvent getByEventThirdId(String eventThirdId);

    WarningEvent checkExist(String deviceThirdId);

    void updateByWarningId(WarningEvent warningEvent);

    List<WarningEvent> listWarningEventThreeDayTask();


    List<WarningEvent> listVideoEvent();

    Long countNumberByDate(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<WarningEvent> listByDate(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    long countStopCount();

    Long getCountByMonthAndScene(@Param("scene") String scene);

    List<WarningEvent> listNonCompleteOrangeEvent(WarningEventQueryVO queryVO);

    WarningEvent getUnCompleteEventByThirdId(@Param("deviceThirdId") String deviceThirdId);

    WarningEvent checkFireExist(@Param("carLicense") String carLicense,
                                @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<WarningEvent> listNonSignEvent();

    List<WarningEvent> listBaiyiWarning();

    List<WarningEvent> listByDateWithOutModel(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    List<WarningEvent> listWarningEventOccupyOverTime();

    List<String> listAllAlarmDevice();

    List<WarningEvent> listPushValue(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
