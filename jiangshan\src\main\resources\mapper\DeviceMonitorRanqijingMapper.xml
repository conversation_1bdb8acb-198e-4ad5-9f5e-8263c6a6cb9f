<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceMonitorRanqijingMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.DeviceMonitorRanqijing">
    <!--@mbg.generated-->
    <!--@Table t_device_monitor_ranqijing-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="fid" jdbcType="VARCHAR" property="fid" />
    <result column="bianhao" jdbcType="VARCHAR" property="bianhao" />
    <result column="guanjing" jdbcType="VARCHAR" property="guanjing" />
    <result column="layer" jdbcType="VARCHAR" property="layer" />
    <result column="lon" jdbcType="VARCHAR" property="lon" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, fid, bianhao, guanjing, layer, lon, lat
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_device_monitor_ranqijing
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from t_device_monitor_ranqijing
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.ruoyi.jiangshan.domain.DeviceMonitorRanqijing">
    <!--@mbg.generated-->
    insert into t_device_monitor_ranqijing (id, fid, bianhao,
      guanjing, layer, lon,
      lat)
    values (#{id,jdbcType=VARCHAR}, #{fid,jdbcType=VARCHAR}, #{bianhao,jdbcType=VARCHAR},
      #{guanjing,jdbcType=VARCHAR}, #{layer,jdbcType=VARCHAR}, #{lon,jdbcType=VARCHAR},
      #{lat,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ruoyi.jiangshan.domain.DeviceMonitorRanqijing">
    <!--@mbg.generated-->
    insert into t_device_monitor_ranqijing
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fid != null">
        fid,
      </if>
      <if test="bianhao != null">
        bianhao,
      </if>
      <if test="guanjing != null">
        guanjing,
      </if>
      <if test="layer != null">
        layer,
      </if>
      <if test="lon != null">
        lon,
      </if>
      <if test="lat != null">
        lat,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="bianhao != null">
        #{bianhao,jdbcType=VARCHAR},
      </if>
      <if test="guanjing != null">
        #{guanjing,jdbcType=VARCHAR},
      </if>
      <if test="layer != null">
        #{layer,jdbcType=VARCHAR},
      </if>
      <if test="lon != null">
        #{lon,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.DeviceMonitorRanqijing">
    <!--@mbg.generated-->
    update t_device_monitor_ranqijing
    <set>
      <if test="fid != null">
        fid = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="bianhao != null">
        bianhao = #{bianhao,jdbcType=VARCHAR},
      </if>
      <if test="guanjing != null">
        guanjing = #{guanjing,jdbcType=VARCHAR},
      </if>
      <if test="layer != null">
        layer = #{layer,jdbcType=VARCHAR},
      </if>
      <if test="lon != null">
        lon = #{lon,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.DeviceMonitorRanqijing">
    <!--@mbg.generated-->
    update t_device_monitor_ranqijing
    set fid = #{fid,jdbcType=VARCHAR},
      bianhao = #{bianhao,jdbcType=VARCHAR},
      guanjing = #{guanjing,jdbcType=VARCHAR},
      layer = #{layer,jdbcType=VARCHAR},
      lon = #{lon,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_device_monitor_ranqijing
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="fid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.fid,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="bianhao = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.bianhao,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="guanjing = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.guanjing,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="layer = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.layer,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="lon = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.lon,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="lat = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.lat,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=VARCHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_device_monitor_ranqijing
    (id, fid, bianhao, guanjing, layer, lon, lat)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.fid,jdbcType=VARCHAR}, #{item.bianhao,jdbcType=VARCHAR},
        #{item.guanjing,jdbcType=VARCHAR}, #{item.layer,jdbcType=VARCHAR}, #{item.lon,jdbcType=VARCHAR},
        #{item.lat,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="listGasWell" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_device_monitor_ranqijing
  </select>
</mapper>
