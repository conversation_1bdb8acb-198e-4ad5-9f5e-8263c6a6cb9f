package com.ruoyi.jiangshan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 设备预警事件-主办和协办部门
 */
@Data
public class WarningEventDepartment {
    /**
     * 主键
     */
    private Long id;

    /**
     * 事件id
     */
    private Long eventId;

    /**
     * 事件流程id
     */
    private Long processId;

    /**
     * 状态 1待签收-核实 2待核实 3待签收-处置 4处置中
     */
    private Integer eventStatus;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 部门name
     */
    private String departmentName;

    /**
     * 组织code
     */
    private String organizationCode;

    /**
     * 浙政钉accountId
     */
    private String accountId;

    /**
     * 浙政钉employeeCode
     */
    private String employeeCode;

    /**
     * 浙政钉员工名称
     */
    private String employeeName;

    /**
     * 手机号
     */
    private String employeeMobile;

    /**
     * 是否主办部门 0-否 1-是
     */
    private Integer mainFlag;

    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

}
