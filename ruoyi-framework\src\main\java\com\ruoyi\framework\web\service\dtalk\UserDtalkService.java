package com.ruoyi.framework.web.service.dtalk;

import com.ruoyi.framework.dtalk.MobileUserResp;
import com.ruoyi.framework.dtalk.UserEmployeeDTO;
import com.ruoyi.framework.dtalk.UserResp;
import com.ruoyi.framework.vo.DtalkAppUserVO;
import com.ruoyi.framework.vo.DtalkEmployeeVo;
import com.ruoyi.framework.vo.EmployeeInfo;

import java.util.List;

/**
 * <AUTHOR> yx-0176
 * @description 浙政钉对接工具类
 * @date : 2021/10/18
 */
public interface UserDtalkService {

    /**
     * 获取accesstoken
     *
     * @return
     */
    String getAccessToken();

    /**
     * 通过临时授权码获取用户信息
     *
     * @param code 临时授权码
     * @return
     */
    UserResp getDingtalkAppUser(String code);

    /**
     * 根据手机号获取浙政钉用户信息
     */
    MobileUserResp getDingtalkAppUserByMobile(String phone) throws Exception;

    /**
     * 根据accountId获取employeeCode
     */
    String getUserByAccountId(String accountId);

    /**
     * 根据employeeCode获取用户信息
     * @param employeeCode
     */
    UserEmployeeDTO getUserByEmployeeCode(String employeeCode);

    /**
     * 发起工作通知
     */
    boolean workNotification(String receiverIds, String bizMsgId,
                             String title, String content, String singleUrl, String singlePcUrl);

    List<String> listAccountByIds(List<String> warningUserIdList);

    /**
     * 分页查询部门下用户列表
     *
     * @return
     */
    List<EmployeeInfo> getOrganizationUserList(String organizationCode, Integer pageSize, Integer pageNum);

    /**
     * 查询当前部门人员列表
     *
     * @param pageNum          页码数
     * @param pageSize         每页数量
     * @param organizationCode 机构id
     * @param nameKeyword      姓名关键字
     * @return
     */
    List<DtalkEmployeeVo> pageSearchEmployee(Integer pageNum, Integer pageSize, String organizationCode, String nameKeyword);

    /**
     * 免登
     * @param authCode
     * @return
     */
    DtalkAppUserVO getDingtalkAppUserByFree(String authCode);
}
