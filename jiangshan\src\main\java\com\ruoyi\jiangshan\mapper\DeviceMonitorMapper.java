package com.ruoyi.jiangshan.mapper;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.vo.BusinessCountVO;
import com.ruoyi.jiangshan.vo.RiskPreventVO;
import org.apache.ibatis.annotations.Param;

/**
 * 设备监测对象Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface DeviceMonitorMapper
{
    /**
     * 查询设备监测对象
     *
     * @param id 设备监测对象主键
     * @return 设备监测对象
     */
    public DeviceMonitor selectDeviceMonitorById(Long id);

    /**
     * 查询设备监测对象列表
     *
     * @param deviceMonitor 设备监测对象
     * @return 设备监测对象集合
     */
    public List<DeviceMonitor> selectDeviceMonitorList(DeviceMonitor deviceMonitor);

    /**
     * 新增设备监测对象
     *
     * @param deviceMonitor 设备监测对象
     * @return 结果
     */
    public int insertDeviceMonitor(DeviceMonitor deviceMonitor);

    /**
     * 修改设备监测对象
     *
     * @param deviceMonitor 设备监测对象
     * @return 结果
     */
    public int updateDeviceMonitor(DeviceMonitor deviceMonitor);

    /**
     * 删除设备监测对象
     *
     * @param id 设备监测对象主键
     * @return 结果
     */
    public int deleteDeviceMonitorById(Long id);

    /**
     * 批量删除设备监测对象
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceMonitorByIds(Long[] ids);

    List<BusinessCountVO> countByType(@Param("list") List<String> list);

    List<BusinessCountVO> countScaleByType(@Param("list") List<String> list);

    List<DeviceMonitor> listByType(@Param("list") List<String> typeList);

    List<RiskPreventVO> getRiskPrevention(@Param("riskPreventVO") RiskPreventVO riskPreventVO, @Param("type") String type);

    List<DeviceMonitor> listAll(@Param("keyword") String keyword, @Param("monitorScene") String monitorScene,
                                @Param("excludeList") List<String> excludeList);

    void batchInsert(List<DeviceMonitor> list);

    List<DeviceMonitor> listByIdList(@Param("modelIdList") List<String> modelIdList);

    DeviceMonitor getByName(@Param("monitorName") String monitorName);

    List<DeviceMonitor> listByTypeAndNewType(@Param("list") List<String> typeList, @Param("newType") String newType);

    List<DeviceMonitor> listByTypeAndKeywords(@Param("list") List<String> typeList, @Param("keywords") String keywords);

    List<DeviceMonitor> listPushValue(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

}
