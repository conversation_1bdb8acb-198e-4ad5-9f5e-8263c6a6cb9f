package com.ruoyi.jiangshan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.jiangshan.domain.BusinessFile;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WarningDiagnosisVO {

    private Long id;

    private Long warningId;

    private String warningName;

    private String warningAddress;

    /**
     * 预警位置
     */
    private String monitorName;

    /**
     * 风险类型
     */
    private String warningType;

    /**
     * 风险等级
     */
    private Integer warningLevel;

    /**
     * 预警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date warningTime;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 预警原因
     */
    private String warningReason;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 状态
     */
    private Integer status;

    private List<BusinessFile> fileList;

}
