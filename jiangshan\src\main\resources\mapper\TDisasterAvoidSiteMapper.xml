<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.TDisasterAvoidSiteMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.TDisasterAvoidSite">
    <!--@mbg.generated-->
    <!--@Table t_disaster_avoid_site-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="year" jdbcType="VARCHAR" property="year" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="town" jdbcType="VARCHAR" property="town" />
    <result column="place_name" jdbcType="VARCHAR" property="placeName" />
    <result column="place_address" jdbcType="VARCHAR" property="placeAddress" />
    <result column="lon" jdbcType="VARCHAR" property="lon" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `year`, city, county, `level`, town, place_name, place_address, lon, lat
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_disaster_avoid_site
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from t_disaster_avoid_site
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.ruoyi.jiangshan.domain.TDisasterAvoidSite">
    <!--@mbg.generated-->
    insert into t_disaster_avoid_site (id, `year`, city,
      county, `level`, town,
      place_name, place_address, lon,
      lat)
    values (#{id,jdbcType=VARCHAR}, #{year,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR},
      #{county,jdbcType=VARCHAR}, #{level,jdbcType=VARCHAR}, #{town,jdbcType=VARCHAR},
      #{placeName,jdbcType=VARCHAR}, #{placeAddress,jdbcType=VARCHAR}, #{lon,jdbcType=VARCHAR},
      #{lat,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ruoyi.jiangshan.domain.TDisasterAvoidSite">
    <!--@mbg.generated-->
    insert into t_disaster_avoid_site
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="year != null">
        `year`,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="county != null">
        county,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="town != null">
        town,
      </if>
      <if test="placeName != null">
        place_name,
      </if>
      <if test="placeAddress != null">
        place_address,
      </if>
      <if test="lon != null">
        lon,
      </if>
      <if test="lat != null">
        lat,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        #{county,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="town != null">
        #{town,jdbcType=VARCHAR},
      </if>
      <if test="placeName != null">
        #{placeName,jdbcType=VARCHAR},
      </if>
      <if test="placeAddress != null">
        #{placeAddress,jdbcType=VARCHAR},
      </if>
      <if test="lon != null">
        #{lon,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.TDisasterAvoidSite">
    <!--@mbg.generated-->
    update t_disaster_avoid_site
    <set>
      <if test="year != null">
        `year` = #{year,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        county = #{county,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=VARCHAR},
      </if>
      <if test="town != null">
        town = #{town,jdbcType=VARCHAR},
      </if>
      <if test="placeName != null">
        place_name = #{placeName,jdbcType=VARCHAR},
      </if>
      <if test="placeAddress != null">
        place_address = #{placeAddress,jdbcType=VARCHAR},
      </if>
      <if test="lon != null">
        lon = #{lon,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.TDisasterAvoidSite">
    <!--@mbg.generated-->
    update t_disaster_avoid_site
    set `year` = #{year,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      county = #{county,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=VARCHAR},
      town = #{town,jdbcType=VARCHAR},
      place_name = #{placeName,jdbcType=VARCHAR},
      place_address = #{placeAddress,jdbcType=VARCHAR},
      lon = #{lon,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_disaster_avoid_site
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`year` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.year,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="city = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.city,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="county = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.county,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`level` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.level,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="town = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.town,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="place_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.placeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="place_address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.placeAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="lon = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.lon,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="lat = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.lat,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=VARCHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_disaster_avoid_site
    (id, `year`, city, county, `level`, town, place_name, place_address, lon, lat)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.year,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR},
        #{item.county,jdbcType=VARCHAR}, #{item.level,jdbcType=VARCHAR}, #{item.town,jdbcType=VARCHAR},
        #{item.placeName,jdbcType=VARCHAR}, #{item.placeAddress,jdbcType=VARCHAR}, #{item.lon,jdbcType=VARCHAR},
        #{item.lat,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="listAllDisasterAvoid" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_disaster_avoid_site
  </select>
</mapper>
