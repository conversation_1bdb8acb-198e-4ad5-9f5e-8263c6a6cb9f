<?xml version="1.0" encoding="UTF-8"?>

<EventNotificationAlert xmlns="http://www.isapi.org/ver20/XMLSchema" version="2.0">
  <!--ro, opt, object, 停车侦测报警, attr:version{opt, string, 协议版本}-->
  <ipAddress>
    <!--ro, req, string, 报警设备IPv4地址-->**********
  </ipAddress>
  <ipv6Address>
    <!--ro, opt, string, 报警设备IPv6地址-->1080:0:0:0:8:800:200C:417A
  </ipv6Address>
  <portNo>
    <!--ro, opt, int, 报警设备端口号-->80
  </portNo>
  <protocol>
    <!--ro, opt, string, 协议类型, range:[0,64], desc:[HTTP#HTTP,HTTPS#HTTPS,EHome#EHome,OTAP#OTAP], 该字段无实际意义-->HTTP
  </protocol>
  <macAddress>
    <!--ro, opt, string, MAC地址-->01:17:24:45:D9:F4
  </macAddress>
  <dynChannelID>
    <!--ro, opt, string, 数字通道号-->test
  </dynChannelID>
  <channelID>
    <!--ro, opt, int, 触发报警的设备通道号, desc:触发的视频通道号-->1
  </channelID>
  <releatedChannelList>
    <!--ro, opt, string, 报警关联(同源)通道列表(与channelID在同一个相机上的通道), desc:用于平台收到报警时展示预览或回放,多个通道号用逗号隔开-->1,2,3
  </releatedChannelList>
  <dateTime>
    <!--ro, req, datetime, 报警触发时间-->2004-05-03T17:30:08+08:00
  </dateTime>
  <activePostCount>
    <!--ro, opt, int, 事件触发次数, desc:事件触发频次-->1
  </activePostCount>
  <eventType>
    <!--ro, req, enum, 事件类型, subType:string, [parking#停车侦测]-->parking
  </eventType>
  <eventState>
    <!--ro, req, enum, 事件状态, subType:string, [active#有效事件,inactive#无效事件], desc:针对持续性事件-->active
  </eventState>
  <eventDescription>
    <!--ro, req, string, 事件描述-->test
  </eventDescription>
  <deviceID>
    <!--ro, opt, string, 设备ID, desc:EHome报警中需返回，例如test0123（EHome2.0、EHome4.0、ISUP5.0）-->12345
  </deviceID>
  <DetectionRegionList>
    <!--ro, opt, array, 检测区域列表, subType:object-->
    <DetectionRegionEntry>
      <!--ro, opt, object, 检测区域-->
      <regionID>
        <!--ro, req, string, 区域ID-->test
      </regionID>
      <sensitivityLevel>
        <!--ro, opt, int, 灵敏度, range:[0,100]-->1
      </sensitivityLevel>
      <RegionCoordinatesList>
        <!--ro, opt, array, 检测区域（规则框）坐标列表, subType:object, range:[0,4], 
desc:检测区域为矩形，
        注意：
        1.前端和超脑设备，本参数表示的是提交检测的规则框区域，
        2.数据中心异常行为检测服务器从3.2.5版本至4.1.0版本，当本事件由视频异常行为检测任务产生时，本节点实际坐标值按照目标框(即"TargetRect"表示的框)返回
        3.通过能力集(/ISAPI/SDT/Management/Task/Video/capabilities?format=json返回smartRule节点)判断设备是否支持异常行为检测视频任务，不支持的设备RegionCoordinatesList都是表示检测的规则框，支持能力设备再结合isRegionCoordinatesListChange参数，isRegionCoordinatesListChange不传或者为false，RegionCoordinatesList表示目标框（兼容4.1.0及之前版本异常行为检测服务器对接过的集成用户），isRegionCoordinatesListChange值为true，RegionCoordinatesList表示检测的规则框-->
        <RegionCoordinates>
          <!--ro, opt, object, 区域点坐标, desc:屏幕左上角为坐标原点-->
          <positionX>
            <!--ro, req, int, 区域点X坐标, range:[0,1000]-->50
          </positionX>
          <positionY>
            <!--ro, req, int, 区域点Y坐标, range:[0,1000]-->50
          </positionY>
        </RegionCoordinates>
      </RegionCoordinatesList>
      <detectionTarget>
        <!--ro, opt, enum, 目标类型, subType:string, [vehicle#车辆]-->vehicle
      </detectionTarget>
      <TargetRect>
        <!--ro, opt, object, 目标区域坐标, 
desc:屏幕左上角为坐标原点，表示算法识别出来的目标，在检测区域（规则框）范围之内，
        注意：
        数据中心异常行为检测服务器从3.2.5版本至4.1.0版本，当本事件由异常行为检测任务产生时，本节点未返回，规划在4.2.0版本，增加返回本节点，新集成用户对接4.2.0及之后的版本，按照本节点解析目标框坐标值（保持与前端和超脑设备逻辑统一）-->
        <X>
          <!--ro, req, float, 目标X坐标, range:[0.000,1.000]-->0.000
        </X>
        <Y>
          <!--ro, req, float, 目标Y坐标, range:[0.000,1.000]-->0.000
        </Y>
        <width>
          <!--ro, req, float, 目标宽度, range:[0.000,1.000]-->1.000
        </width>
        <height>
          <!--ro, req, float, 目标高度, range:[0.000,1.000]-->1.000
        </height>
      </TargetRect>
      <sceneID>
        <!--ro, opt, int, 场景ID-->1
      </sceneID>
      <licensePlate>
        <!--ro, opt, string, 车牌号码-->浙A12345
      </licensePlate>
      <retentionTotalTime>
        <!--ro, opt, int, 实际停留的时间, range:[1,**********], unit:s, unitType:时间, desc:从目标进入区域开始计算-->5
      </retentionTotalTime>
      <vehicleState>
        <!--ro, opt, enum, 车辆状态类型, subType:string, [stay#停留,leave#离开], desc:stay表示车辆停留在区域内，leave表示车辆停留一定时长后，离开了。没有该节点默认为停留,-->stay
      </vehicleState>
      <isRegionCoordinatesListChange>
        <!--ro, opt, bool, 区域框含义是否改变, desc:对于异常行为检测服务器，不返回本参数或者值为false，RegionCoordinatesList内容继续按照目标框返回，异常行为检测服务器4.2.0及之后的版本，当返回本参数且值为true，RegionCoordinatesList内容改为检测区域（规则框）坐标，本事件中本参数是否返回以及返回内容与视频异常行为检测任务(/ISAPI/SDT/Management/Task/Video?format=json)中的isRegionCoordinatesListChange保持一致-->true
      </isRegionCoordinatesListChange>
    </DetectionRegionEntry>
  </DetectionRegionList>
  <channelName>
    <!--ro, opt, string, 通道名称, range:[1,64]-->test
  </channelName>
  <Extensions>
    <!--ro, opt, object, 扩展信息-->
    <serialNumber>
      <!--ro, opt, string, 序列号-->test
    </serialNumber>
    <eventPush>
      <!--ro, opt, string, 事件推送内容-->test
    </eventPush>
  </Extensions>
  <detectionPictureTransType>
    <!--ro, opt, enum, 图片传输方式, subType:string, [url#url,binary#二机制,base64#base64图片数据]-->url
  </detectionPictureTransType>
  <detectionPicturesNumber>
    <!--ro, opt, int, 表示布防接收报警中包含的图片数量, desc:有图片返回,没有图片不返回-->1
  </detectionPicturesNumber>
  <bkgUrl>
    <!--ro, opt, string, 背景侦测图片的URL, desc:默认情况下是云存储协议”,detectionPictureTransType为url和base64时,数据放入此节点-->test
  </bkgUrl>
  <URLCertificationType>
    <!--ro, opt, enum, 图片url认证方式, subType:string, [no#无（这个是针对武汉云存储协议）,digest#摘要认证], desc:digest-摘要认证（这个针对设备本地存储返回URL的方式,设备例如NVR/DVR）-->no
  </URLCertificationType>
  <pId>
    <!--ro, opt, string, 图片ID, desc:detectionPictureTransType为binary时表单中图片的Content-ID填入此节点-->test
  </pId>
  <visibleLightURL>
    <!--ro, opt, string, 可见光图片URL-->test
  </visibleLightURL>
  <thermalURL>
    <!--ro, opt, string, 热成像图片URL-->test
  </thermalURL>
  <targetAttrs>
    <!--ro, opt, object, 透传字段-->{ /*可选,透传字段,推荐使用以下三个字段*/ "deviceId": "1", /*可选, 设备ID, string类型, 最大长度为64*/ "deviceChannel": 1, /*可选, 设备通道号, integer32类型*/ "deviceName": "shebei", /*可选, 设备名称, string类型, 最大长度为128*/ }
  </targetAttrs>
  <TaskInfo>
    <!--ro, opt, object, 数据中心产品提交的任务信息-->
    <taskID>
      <!--ro, req, string, 任务ID, range:[0,64]-->test
    </taskID>
    <algorithmType>
      <!--ro, opt, enum, 算法类型, subType:int, [1#hightQualityVehicle(高质量车),2#hightPerformanceVehicle（高性能车）,3#personStructModel（人员结构化建模）,4#vehicleAndPersonStructModel(人和车结构化建模),5#videoFaceDetect(视频人脸检测),6#videoFaceDetectModel(视频人脸检测和建模),7#smart(人脸侦测（输出坐标框]-->1
    </algorithmType>
    <taskName>
      <!--ro, opt, string, 任务名称-->test
    </taskName>
    <algorithmID>
      <!--ro, opt, string, 算法包ID-->test
    </algorithmID>
    <monitorPointID>
      <!--ro, opt, string, 监控点编号-->test
    </monitorPointID>
    <monitorPointName>
      <!--ro, opt, string, 监控点名称-->test
    </monitorPointName>
    <streamType>
      <!--ro, opt, enum, 流类型, subType:string, [realtime#实时流,historyvideo#历史流,localvideo#本地流]-->realtime
    </streamType>
    <ruleID>
      <!--ro, opt, string, 规则ID, desc:用于区分相同名称的不同任务,事件上报中也有此ruleID,表示事件和此任务相关-->test
    </ruleID>
    <ruleCustomName>
      <!--ro, req, string, 用户自定义事件名称, range:[0,128], desc:调用者生成,报警时会携带此字段,长度不超过128字节-->test
    </ruleCustomName>
    <algorithmInfo>
      <!--ro, opt, object, 算法版本信息, desc:二次分析数据必填-->
      <algorithmId>
        <!--ro, opt, string, 算法包Id-->test
      </algorithmId>
      <algoVendorId>
        <!--ro, opt, string, 厂商id-->test
      </algoVendorId>
      <algoVersionId>
        <!--ro, opt, string, 算法版本号id-->test
      </algoVersionId>
      <algoChipType>
        <!--ro, opt, string, 芯片类型-->test
      </algoChipType>
      <algoModelVersion>
        <!--ro, opt, string, 模型版本-->test
      </algoModelVersion>
    </algorithmInfo>
  </TaskInfo>
  <timeStamp>
    <!--ro, opt, string, 触发报警的帧的时间戳, desc:时间格式:2019-12-30T15:29:23+08:00-->test
  </timeStamp>
  <targetSpeed>
    <!--ro, opt, datetime, 雷达检测到目标速度-->2019-12-30T15:29:23+08:00
  </targetSpeed>
  <targetDistance>
    <!--ro, opt, int, 雷达检测到目标距离, unit:m, unitType:长度-->2
  </targetDistance>
  <triggerType>
    <!--ro, opt, enum, 事件触发源类型, subType:string, [video#视频,radar#雷达], desc:不传该字段默认是video-->video
  </triggerType>
  <uid>
    <!--ro, opt, string, 事件上报的唯一标识, range:[0,64], desc:可以使用时间（精确到毫秒）加上随即数的方式组成-->test
  </uid>
  <isDataRetransmission>
    <!--ro, opt, bool, 重传数据标记（由于网络异常等因素;导致的实时检测的数据上传失败;后设备异常因素恢复后重新上传当时的采集分析数据）-->true
  </isDataRetransmission>
  <ruleID>
    <!--ro, opt, int, 规则序号, range:[0,7], desc:对应通道视频异常行为检测规则参数中的规则序号,对应接口为/ISAPI/Intelligent/channels/<channelID>/behaviorRule/<SID>/rule/<ruleID>-->0
  </ruleID>
  <ruleName>
    <!--ro, opt, string, 规则名称, range:[0,32], desc:对应通道视频异常行为检测规则参数中的规则名称,对应接口为/ISAPI/Intelligent/channels/<channelID>/behaviorRule/<SID>/rule/<ruleID>-->test
  </ruleName>
  <parkingBackgroundImageResolution>
    <!--ro, opt, object, 停车侦测可见光图片的分辨率, desc:归一化坐标转换成实际分辨率坐标时,需要依赖此节点-->
    <height>
      <!--ro, req, int, 分辨率高, unit:px, unitType:图像显示-->1
    </height>
    <width>
      <!--ro, req, int, 分辨率宽, unit:px, unitType:图像显示-->1
    </width>
  </parkingBackgroundImageResolution>
  <targetID>
    <!--ro, opt, int, 目标ID, range:[1,**********], desc:算法对视频报警帧画面中的每个目标生成一个目标ID，默认对目标ID进行累加计数区分，重启设备后ID重置，且与码流私有帧中携带的目标ID一致-->1
  </targetID>
  <cameraIndexCode>
    <!--ro, opt, string, 监控点编号, range:[0,64], desc:来源于任务中/ISAPI/System/AlgoPackageScheduling/AddTask?format=json&taskType=<taskType>&security=<security>&iv=<iv>中DataSource中监控点编号（cameraIndexCode）或者offlineVideoFile中recordID-->test
  </cameraIndexCode>
</EventNotificationAlert>
