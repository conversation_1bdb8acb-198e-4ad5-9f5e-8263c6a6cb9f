package com.ruoyi.quartz.task;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.framework.dtalk.MobileUserResp;
import com.ruoyi.framework.vo.OrganizationNodeInfo;
import com.ruoyi.framework.web.service.dtalk.DeptDtalkService;
import com.ruoyi.framework.web.service.dtalk.UserDtalkService;
import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.DeviceStreet;
import com.ruoyi.jiangshan.domain.WarningDepartmentInfo;
import com.ruoyi.jiangshan.mapper.DeviceInfoMapper;
import com.ruoyi.jiangshan.mapper.DeviceStreetMapper;
import com.ruoyi.jiangshan.mapper.WarningDepartmentInfoMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Component("businessDeptTask")
public class BusinessDeptTask {

    /**
     * 拉取浙政钉部门
     */
    public void pullDtalkDept() throws ExecutionException {
        DeptDtalkService deptDtalkService = SpringUtils.getBean(DeptDtalkService.class);
        WarningDepartmentInfoMapper warningDepartmentInfoMapper = SpringUtils.getBean(WarningDepartmentInfoMapper.class);

        List<OrganizationNodeInfo> organization = deptDtalkService.getOrganization();

        int orderNum = 0;

        for (OrganizationNodeInfo organizationNodeInfo : organization) {
            WarningDepartmentInfo departmentInfo = new WarningDepartmentInfo();

            departmentInfo.setParentId(0L);
            departmentInfo.setAncestors("0");
            departmentInfo.setDepartmentName(organizationNodeInfo.getOrganizationName());
            departmentInfo.setOrderNum(orderNum ++);
            departmentInfo.setOrganizationCode(organizationNodeInfo.getOrganizationCode());

            warningDepartmentInfoMapper.insertSelective(departmentInfo);

            nextOrg(organizationNodeInfo, deptDtalkService, warningDepartmentInfoMapper, departmentInfo.getId(), "0");

            orderNum++;
        }
    }

    public void callOrganization() throws ExecutionException {
        DeptDtalkService deptDtalkService = SpringUtils.getBean(DeptDtalkService.class);

        String orgCode = deptDtalkService.getOrganizationName("GO_42573b4366a84e468d3999564a8a9a73");
    }

    private void nextOrg(OrganizationNodeInfo organizationNodeInfo,
                         DeptDtalkService deptDtalkService,
                         WarningDepartmentInfoMapper warningDepartmentInfoMapper,
                         Long i, String ancestors) throws ExecutionException {
        List<OrganizationNodeInfo> pageOrganization = deptDtalkService.getPageOrganization(organizationNodeInfo.getOrganizationCode());

        if (CollectionUtils.isEmpty(pageOrganization)) {
            return;
        }

        int orderNum = 0;

        for (OrganizationNodeInfo nodeInfo : pageOrganization) {
            String currentAncestor = ancestors + "," + i;

            WarningDepartmentInfo departmentInfo = new WarningDepartmentInfo();
            departmentInfo.setParentId(i);
            departmentInfo.setAncestors(ancestors + "," + i);
            departmentInfo.setDepartmentName(nodeInfo.getOrganizationName());
            departmentInfo.setOrderNum(orderNum ++);
            departmentInfo.setOrganizationCode(nodeInfo.getOrganizationCode());

            warningDepartmentInfoMapper.insertSelective(departmentInfo);

            nextOrg(nodeInfo, deptDtalkService, warningDepartmentInfoMapper, departmentInfo.getId(), currentAncestor);
        }
    }

    /**
     * 拉取浙政钉部门-组织架构
     */
    public void pullDtalkDeptV2() throws ExecutionException {
        DeptDtalkService deptDtalkService = SpringUtils.getBean(DeptDtalkService.class);
        SysDeptMapper sysDeptMapper = SpringUtils.getBean(SysDeptMapper.class);

        List<OrganizationNodeInfo> organization = deptDtalkService.getOrganization();

        int orderNum = 0;

        for (OrganizationNodeInfo organizationNodeInfo : organization) {
            SysDept sysDept = new SysDept();
            sysDept.setParentId(0L);
            sysDept.setAncestors("0");
            sysDept.setDeptName(organizationNodeInfo.getOrganizationName());
            sysDept.setOrderNum(orderNum ++);
            sysDept.setCreateBy(organizationNodeInfo.getOrganizationCode());

            sysDeptMapper.insertDept(sysDept);

            nextOrgV2(organizationNodeInfo, deptDtalkService, sysDeptMapper, sysDept.getDeptId(), "0");

            orderNum++;
        }
    }

    private void nextOrgV2(OrganizationNodeInfo organizationNodeInfo,
                         DeptDtalkService deptDtalkService,
                         SysDeptMapper sysDeptMapper,
                         Long i, String ancestors) throws ExecutionException {
        List<OrganizationNodeInfo> pageOrganization = deptDtalkService.getPageOrganization(organizationNodeInfo.getOrganizationCode());

        if (CollectionUtils.isEmpty(pageOrganization)) {
            return;
        }

        int orderNum = 0;

        for (OrganizationNodeInfo nodeInfo : pageOrganization) {
            String currentAncestor = ancestors + "," + i;

            SysDept sysDept = new SysDept();
            sysDept.setParentId(i);
            sysDept.setAncestors(ancestors + "," + i);
            sysDept.setDeptName(nodeInfo.getOrganizationName());
            sysDept.setOrderNum(orderNum ++);
            sysDept.setCreateBy(nodeInfo.getOrganizationCode());

            sysDeptMapper.insertDept(sysDept);

            nextOrgV2(nodeInfo, deptDtalkService, sysDeptMapper, sysDept.getDeptId(), currentAncestor);
        }
    }

    /**
     * 刷设备详细地址
     */
    public void flushDeviceAddress() {
        DeviceStreetMapper deviceStreetMapper = SpringUtils.getBean(DeviceStreetMapper.class);
        DeviceInfoMapper deviceInfoMapper = SpringUtils.getBean(DeviceInfoMapper.class);

        List<DeviceStreet> deviceStreetList = deviceStreetMapper.listWithOutParentId();

        for (DeviceStreet deviceStreet : deviceStreetList) {
            List<DeviceInfo> deviceInfoList =
                    deviceInfoMapper.listDeviceInfoByGem(deviceStreet.getGeometry());
            if (CollectionUtils.isNotEmpty(deviceInfoList)) {
                DeviceStreet parentStreet = deviceStreetMapper.getByParentId(deviceStreet.getParentId());

                String address = "江山市" + parentStreet.getLabel() + deviceStreet.getLabel();

                List<Long> deviceIdList = deviceInfoList.stream()
                        .map(DeviceInfo::getId)
                        .collect(Collectors.toList());

                deviceInfoMapper.updateAddressByIdList(address, parentStreet.getLabel(), parentStreet.getBsm(), deviceIdList);
            }
        }
    }

    /**
     * 刷老用户的浙政钉信息
     */
    public void flushDingTalkInfo() throws Exception {
        SysUserMapper sysUserMapper = SpringUtils.getBean(SysUserMapper.class);
        UserDtalkService userDtalkService = SpringUtils.getBean(UserDtalkService.class);

        List<SysUser> sysUserList = sysUserMapper.listNonAccountId();
        if (CollectionUtils.isEmpty(sysUserList)) {
            return;
        }

        for (SysUser sysUser : sysUserList) {
            try {
                MobileUserResp userResp = userDtalkService.getDingtalkAppUserByMobile(sysUser.getPhonenumber());

                SysUser updateUser = new SysUser();
                updateUser.setUserId(sysUser.getUserId());
                updateUser.setAccountId(userResp.getAccountId() + "");
                updateUser.setEmployeeCode(userResp.getEmployeeCode() + "");

                sysUserMapper.updateUser(updateUser);
            } catch (Exception e) {

            }
        }
    }
}
