package com.ruoyi.jiangshan.openapi.accept;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.jiangshan.constant.CommonConstant;
import com.ruoyi.jiangshan.enums.RedisChannelMapping;
import com.ruoyi.jiangshan.openapi.vo.DeviceValueOpenApiVO;
import com.ruoyi.jiangshan.service.IDeviceValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.validation.Valid;

@Component
@Slf4j
public class RedisMessageListener implements MessageListener {
    @Autowired
    private RedisMessageListenerContainer redisMessageListenerContainer;
    @Autowired
    private IDeviceValueService deviceValueService;
    @Value("${server.port}")
    private String serverPort;

    @PostConstruct
    public void subscribleToChannel() {
        String redisChannel = RedisChannelMapping.getByPort(serverPort);

        redisMessageListenerContainer.addMessageListener(this, new ChannelTopic(redisChannel));
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String redisChannel = RedisChannelMapping.getByPort(serverPort);

        String channel = new String(message.getChannel());
        String msg = new String(message.getBody());

        log.info("onMessage, channel={}, msg={}, pattern={} ", channel, msg, pattern);

        if (redisChannel.equals(channel)) {
            deviceValueService.saveDeviceValue(JSON.parseObject(msg, DeviceValueOpenApiVO.class));
        }
    }
}
