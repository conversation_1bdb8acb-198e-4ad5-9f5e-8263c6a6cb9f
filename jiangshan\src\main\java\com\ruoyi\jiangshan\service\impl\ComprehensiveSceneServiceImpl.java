package com.ruoyi.jiangshan.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.ruoyi.common.dto.DateDTO;
import com.ruoyi.common.dto.TimeRange;
import com.ruoyi.common.dto.TimeRangeLocalDate;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.PercentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jiangshan.client.IrsWeatherClient;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.enums.*;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.service.ComprehensiveSceneService;
import com.ruoyi.jiangshan.service.irs.BaseIrsService;
import com.ruoyi.jiangshan.vo.*;
import com.ruoyi.jiangshan.vo.cityrisk.CityRiskAvgLineVO;
import com.ruoyi.jiangshan.vo.cityrisk.CityRiskTimeLineVO;
import com.ruoyi.jiangshan.vo.compre.*;
import com.ruoyi.jiangshan.vo.hk.HkControlVideoVO;
import com.ruoyi.jiangshan.vo.hk.HkPreviewVideoVO;
import com.ruoyi.jiangshan.vo.street.DeviceStreetGeoDetailVO;
import com.ruoyi.jiangshan.vo.street.DeviceStreetGeometry;
import com.ruoyi.jiangshan.vo.street.DeviceStreetVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ComprehensiveSceneServiceImpl implements ComprehensiveSceneService {
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private WarningEventSuperviseMapper warningEventSuperviseMapper;
    @Autowired
    private DeviceValueMapper deviceValueMapper;
    @Autowired
    private WarningEventDepartmentMapper warningEventDepartmentMapper;
    @Autowired
    private WarningRuleConditionMapper warningRuleConditionMapper;
    @Autowired
    private CitysafetyReportMapper citysafetyReportMapper;
    @Autowired
    private DeviceStreetMapper deviceStreetMapper;
    @Autowired
    private TDisasterAvoidSiteMapper disasterAvoidSiteMapper;
    @Autowired
    private EmergencyMaterialMapper emergencyMaterialMapper;
    @Autowired
    private BusinessFileMapper businessFileMapper;
    @Autowired
    private DeviceMonitorFxffqMapper deviceMonitorFxffqMapper;
    @Autowired
    private DeviceMonitorShuikuMapper deviceMonitorShuikuMapper;
    @Autowired
    private DeviceMonitorShantangMapper deviceMonitorShantangMapper;
    @Autowired
    private DeviceMonitorHedaoMapper deviceMonitorHedaoMapper;
    @Autowired
    private DeviceMonitorGeometryMapper deviceMonitorGeometryMapper;
    @Autowired
    private BaseIrsService baseIrsService;
    @Autowired
    private IrsWeatherClient irsWeatherClient;
    @Autowired
    private JiangshanRainfallMapper jiangshanRainfallMapper;

//    config.setHost("*************:1443"); // 代理API网关nginx服务器ip端口
//        config.setAppKey("28285666");  // 秘钥appkey
//        config.setAppSecret("C5WAACEkmZRRVyONgTND");// 秘钥appSecret
    public static String ARTEMIS_PATH = "/artemis";
    public static String ARTEMIS_HOST = "*************:1443";
    public static String ARTEMIS_APPKEY = "28285666";
    public static String ARTEMIS_APPSECRET = "C5WAACEkmZRRVyONgTND";

    private static DecimalFormat df = new DecimalFormat("0.00");

    @Override
    public List<BusinessCountVO> getRiskMonitor() {
        List<BusinessCountVO> countList = Lists.newArrayList();



//        List<BusinessCountVO> businessCountVOS = deviceInfoMapper.countByType(Lists.newArrayList("视频监控", "传感器"));
//
//        Map<String, BusinessCountVO> map = countList.stream()
//                .collect(Collectors.toMap(BusinessCountVO::getKey, Function.identity()));
//        if (CollectionUtils.isNotEmpty(businessCountVOS)) {
//            for (BusinessCountVO businessCountVO : businessCountVOS) {
//                BusinessCountVO oneBusiness = map.get(businessCountVO.getKey());
//
//                if (Objects.nonNull(oneBusiness)) {
//                    oneBusiness.setValue(businessCountVO.getValue());
//                }
//            }
//        }
        List<String> videoList = Lists.newArrayList("视频监控");

        Long videoMonitorCount = deviceInfoMapper.sumByType(videoList);
        Long sensorCount = deviceInfoMapper.sumExcludeType(videoList);

        countList.add(new BusinessCountVO("视频监控", Objects.isNull(videoMonitorCount) ? 0L : videoMonitorCount));
        countList.add(new BusinessCountVO("传感器", Objects.isNull(sensorCount) ? 0L : sensorCount));

        long reportCount = citysafetyReportMapper.countAll();
        countList.add(new BusinessCountVO("检查报告", reportCount));

        // TODO: 2024/8/23 人工检查
        countList.add(new BusinessCountVO("人工检查", 17L));

        // TODO: 2024/8/23 其他
        countList.add(new BusinessCountVO("其他", 24L));


        return countList;
    }

    @Override
    public List<BusinessCountVO> getControlSubject() {
        List<String> sceneType01List = Lists.newArrayList();
        sceneType01List.addAll(MonitorSceneType.SCENE_01.getTypeList());
        sceneType01List.addAll(MonitorSceneType.SCENE_02.getTypeList());
        sceneType01List.addAll(MonitorSceneType.SCENE_03.getTypeList());
        sceneType01List.addAll(MonitorSceneType.SCENE_04.getTypeList());
        sceneType01List.addAll(MonitorSceneType.SCENE_05.getTypeList());
        sceneType01List.addAll(MonitorSceneType.SCENE_06.getTypeList());
        sceneType01List.addAll(MonitorSceneType.SCENE_07.getTypeList());
        sceneType01List.add("城市易涝点");

        List<BusinessCountVO> countList = deviceMonitorMapper.countByType(sceneType01List);

        for (BusinessCountVO businessCountVO : countList) {
            if ("污水管".equals(businessCountVO.getKey())) {
                businessCountVO.setKey("污水管网");
            }
            if ("燃气阀井".equals(businessCountVO.getKey())) {
                businessCountVO.setKey("燃气窨井");
            }
        }

//        List<String> sceneType02List = Lists.newArrayList();
//        sceneType02List.addAll(MonitorSceneType.SCENE_05.getTypeList());
//
//        List<String> scene06List = MonitorSceneType.SCENE_06.getTypeList();
//        scene06List.remove("工商业个体户");
//
//        sceneType02List.addAll(scene06List);
//
//        List<BusinessCountVO> countScaleList = deviceMonitorMapper.countScaleByType(sceneType02List);

//        countList.addAll(countScaleList);

        return countList;
    }

    @Override
    public List<WarningCountVO> getRiskStatistics(Date startTime, Date endTime) {
        List<BusinessCountVO> countList = warningInfoMapper.countByScene(startTime, endTime);
        if (CollectionUtils.isEmpty(countList)) {
            return Lists.newArrayList();
        }

        Map<String, List<BusinessCountVO>> countMap = countList.stream()
                .collect(Collectors.groupingBy(BusinessCountVO::getTmpKey));

        List<WarningCountVO> warningCountList = Lists.newArrayList();

        for (Map.Entry<String, List<BusinessCountVO>> entry : countMap.entrySet()) {
            WarningCountVO warningCountVO = new WarningCountVO();

            String warningName = entry.getKey();

            List<BusinessCountVO> valueList = entry.getValue();

            Map<String, Long> valueMap = valueList.stream()
                    .collect(Collectors.toMap(BusinessCountVO::getKey, BusinessCountVO::getValue));

            warningCountVO.setWarningName(warningName);
            warningCountVO.setWarningLevel1(Objects.isNull(valueMap.get("1")) ? 0L : valueMap.get("1"));
            warningCountVO.setWarningLevel2(Objects.isNull(valueMap.get("2")) ? 0L : valueMap.get("2"));
            warningCountVO.setWarningLevel3(Objects.isNull(valueMap.get("3")) ? 0L : valueMap.get("3"));
            warningCountVO.setWarningLevel4(Objects.isNull(valueMap.get("4")) ? 0L : valueMap.get("4"));
            warningCountVO.setWarningLevel5(Objects.isNull(valueMap.get("5")) ? 0L : valueMap.get("5"));

            warningCountList.add(warningCountVO);
        }

        return warningCountList;
    }

    @Override
    public List<WarningInfo> getRiskList() {
        return warningInfoMapper.listLastFourWarning();
    }

    @Override
    public Map<String, Object> getWarningDispose() {
        Map<String, Object> resultMap = new HashMap<>();

        long warningAllCount = warningEventMapper.countAll();
        resultMap.put("预警数量", warningAllCount);

        long submitCount = warningEventMapper.countByStatus(Lists.newArrayList(1, 2, 3, 4, 5, 6), null, null);
        resultMap.put("派单数量", submitCount);

        long completeCount = warningEventMapper.countByStatus(Lists.newArrayList(5, 6), null, null);
        resultMap.put("反馈数量", completeCount);

        long signInCount = warningEventMapper.countByStatus(Lists.newArrayList(2, 4, 5, 6), null, null);
        resultMap.put("签收", signInCount);

        resultMap.put("完结", completeCount);

        long normalSignInCount = warningEventMapper.countByStatus(Lists.newArrayList(2, 4, 5, 6), false, null);
        resultMap.put("正常签收", normalSignInCount);

        long needSignInCount = warningEventMapper.countByStatus(Lists.newArrayList(1, 3), null, null);
        resultMap.put("未签收", needSignInCount);

        long disposeCount = warningEventMapper.countByStatus(Lists.newArrayList(4), null, null);
        resultMap.put("处置中", disposeCount);
        //待核实
        long emergencyDisposeCount = warningEventMapper.countByStatus(Lists.newArrayList(2), null, true);
        resultMap.put("应急处置", emergencyDisposeCount);

        long overSignInCount = warningEventMapper.countByStatus(Lists.newArrayList(2, 4), true, null);
        resultMap.put("超时签收", overSignInCount);

        long unDispatchInCount = warningEventMapper.countByStatus(Lists.newArrayList(0), null, null);
        resultMap.put("未派单", unDispatchInCount);
        //已挂起
        long stopCount = warningEventMapper.countStopCount();
        resultMap.put("持续监测", stopCount);
        //已完成
        resultMap.put("核实正常", completeCount);

        return resultMap;
    }

    @Override
    public List<BusinessPercentCountVO> getEfficiencyStatistics(String type) {
        List<BusinessPercentCountVO> countList = Lists.newArrayList();
        if (Objects.equals(type, "部门")) {
            countList = warningEventMapper
                    .countByDepartment(null);
        } else {
            countList = warningEventMapper
                    .countByScene(null);
        }

        if (CollectionUtils.isEmpty(countList)) {
            return Lists.newArrayList();
        }

        List<BusinessPercentCountVO> noOverCountList = Lists.newArrayList();
        if (Objects.equals(type, "部门")) {
            noOverCountList = warningEventMapper
                    .countByDepartment(false);
        } else {
            noOverCountList = warningEventMapper
                    .countByScene(false);
        }

        Map<String, BusinessPercentCountVO> noOverCountMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(noOverCountList)) {
            noOverCountMap = noOverCountList.stream()
                    .collect(Collectors.toMap(BusinessPercentCountVO::getKey, Function.identity()));
        }

        // 创建DecimalFormat对象，并设置格式为保留两位小数
        DecimalFormat df = new DecimalFormat("#.##");

        for (BusinessPercentCountVO businessCountVO : countList) {
            BusinessPercentCountVO noOverCount = noOverCountMap.get(businessCountVO.getKey());
            if (Objects.nonNull(noOverCount)) {
                businessCountVO.setTmpKey(noOverCount.getValue() + "");

                double percent = noOverCount.getValue() * 100 / businessCountVO.getValue();

                String format = df.format(percent);

                businessCountVO.setValue(Double.parseDouble(format));
            } else {
                businessCountVO.setValue(100.00);
            }
        }

        if (Objects.equals(type, "部门")) {
            Optional<BusinessPercentCountVO> countVO = countList.stream()
                    .filter(businessCountVO -> StringUtils.equals(businessCountVO.getKey(), "江山市自然资源和规划局"))
                    .findFirst();

            if (!countVO.isPresent()) {
                countList.add(new BusinessPercentCountVO("江山市自然资源和规划局", (double) 100, null));
            }
        }

        return countList;
    }

    @Override
    public List<WarningEvent> getLinkDispose() {
        return warningEventMapper.listLastFourWarning();
    }

    @Override
    public List<DeviceInfo> getMonitorLayer() {
        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listAll();

        if (CollectionUtils.isEmpty(deviceInfoList)) {
            return Lists.newArrayList();
        }

        fillMonitorLayer(deviceInfoList);

        return deviceInfoList;
    }

    private void fillMonitorLayer(List<DeviceInfo> deviceInfoList) {
        List<String> deviceThirdIdList = deviceInfoList.stream()
                .map(DeviceInfo::getDeviceThirdId)
                .collect(Collectors.toList());

        List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceThirdIdList(deviceThirdIdList, 7);

        Map<String, List<DeviceValue>> deviceMap = deviceValueList.stream()
                .collect(Collectors.groupingBy(DeviceValue::getDeviceThirdId));

        for (DeviceInfo deviceInfo : deviceInfoList) {
            List<DeviceValue> deviceValueOneList = deviceMap.get(deviceInfo.getDeviceThirdId());
            deviceInfo.setValueList(deviceValueOneList);
        }

        List<BusinessFile> fileList = businessFileMapper.listByType(FileTypeEnum.TYPE_06.getCode());
        if (CollectionUtils.isNotEmpty(fileList)) {
            Map<Long, List<BusinessFile>> fileMap = fileList.stream()
                    .collect(Collectors.groupingBy(BusinessFile::getBusinessId));

            for (DeviceInfo deviceInfo : deviceInfoList) {
                List<BusinessFile> fileListByDeviceId = fileMap.get(deviceInfo.getId());
                if (CollectionUtils.isNotEmpty(fileListByDeviceId)) {
                    deviceInfo.setFileList(fileListByDeviceId);
                }
            }
        }
    }

    @Override
    public List<WarningLayerVO> getWarningLayer(String scene) {
//        DateDTO todayDate = DateUtils.getTimeRange(new Date(), "日");

        List<WarningLayerVO> resultList = warningInfoMapper.listByDate(scene, null, null);

        return resultList;
    }

    @Override
    public List<WarningControlLayer> getControlLayer() {
        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), "日");

        List<WarningControlLayer> warningControlLayerList = Lists.newArrayList();

        List<DeviceMonitor> monitorList = deviceMonitorMapper.listAll(null, null,
                Lists.newArrayList("地质灾害场景"));

        List<Long> monitorIdList = monitorList.stream()
                .map(DeviceMonitor::getId)
                .collect(Collectors.toList());

        List<DeviceValue> deviceValueList = deviceValueMapper.listByMonitorIdListAndDate(monitorIdList,
                null, null);

        List<WarningInfo> warningInfoList = warningInfoMapper.listLastByMonitorIdListAndDate(monitorIdList,
                null, null);

        Map<Long, List<DeviceValue>> deviceValueMap = deviceValueList.stream()
                .collect(Collectors.groupingBy(DeviceValue::getMonitorId));

        Map<Long, WarningInfo> warningMap = warningInfoList.stream()
                .collect(Collectors.toMap(WarningInfo::getMonitorId, Function.identity()));

        for (DeviceMonitor deviceMonitor : monitorList) {
            WarningControlLayer warningControlLayer = new WarningControlLayer();
            warningControlLayer.setName(deviceMonitor.getMonitorName());
            warningControlLayer.setId(deviceMonitor.getId());
            warningControlLayer.setScene(deviceMonitor.getMonitorScene());
            warningControlLayer.setServiceUrl(deviceMonitor.getServiceUrl());
            warningControlLayer.setFileUrl(deviceMonitor.getFileUrl());
            warningControlLayer.setMonitorDepartmentId(deviceMonitor.getMonitorDepartmentId());
            warningControlLayer.setMonitorDepartmentName(deviceMonitor.getMonitorDepartmentName());
            warningControlLayer.setScale(deviceMonitor.getScale());
            warningControlLayer.setScaleUnit(deviceMonitor.getScaleUnit());
            warningControlLayer.setRemark(deviceMonitor.getRemark());

            WarningInfo warningOne = warningMap.get(deviceMonitor.getId());
            if (Objects.nonNull(warningOne)) {
                warningControlLayer.setWarningLevel(warningOne.getWarningLevel());
                warningControlLayer.setHistoryFlag(true);
                warningControlLayer.setMonitorValue(warningOne.getMonitorValue());
                warningControlLayer.setMonitorUnit(warningOne.getMonitorUnit());
            } else {
                warningControlLayer.setWarningLevel(0);
                warningControlLayer.setHistoryFlag(false);
            }

            List<DeviceValue> deviceOneValueList = deviceValueMap.get(deviceMonitor.getId());
            warningControlLayer.setValueList(deviceOneValueList);
            if (CollectionUtils.isNotEmpty(deviceOneValueList)) {
                String deviceThirdId = deviceOneValueList.get(0).getDeviceThirdId();

                List<String> monitorItemList = deviceOneValueList.stream()
                        .map(DeviceValue::getMonitorItemEnglish)
                        .collect(Collectors.toList());

                List<WarningRuleCondition> conditionList = warningRuleConditionMapper
                        .listByMonitorItemAndDevice(deviceThirdId, monitorItemList);

                Map<String, List<WarningRuleCondition>> conditionMap = conditionList.stream()
                        .collect(Collectors.groupingBy(WarningRuleCondition::getMonitorItem));

                for (DeviceValue deviceValue : deviceOneValueList) {
                    List<WarningRuleCondition> ruleConditionList = conditionMap.get(deviceValue.getMonitorItem());

                    deviceValue.setWarningContent(getWarningContent(ruleConditionList));
                }
            }

            warningControlLayer.setLat(deviceMonitor.getLat());
            warningControlLayer.setLon(deviceMonitor.getLon());

            warningControlLayerList.add(warningControlLayer);
        }

//        warningControlLayerList = warningControlLayerList.stream()
//                .filter(controlLayer -> 0 != controlLayer.getWarningLevel())
//                .collect(Collectors.toList());

        warningControlLayerList = warningControlLayerList.stream()
                  .filter(controlLayer -> StringUtils.isNotBlank(controlLayer.getLon())
                          && StringUtils.isNotBlank(controlLayer.getLat()))
                  .collect(Collectors.toList());

        return warningControlLayerList;
    }

    private String getWarningContent(List<WarningRuleCondition> ruleConditionList) {
        if (CollectionUtils.isEmpty(ruleConditionList)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < ruleConditionList.size(); i++) {
            WarningRuleCondition condition = ruleConditionList.get(i);

            sb.append("当");
            sb.append(condition.getMonitorItem());
            sb.append(condition.getRuleCondition());
            sb.append(condition.getMonitorValue());
            sb.append(condition.getMonitorUnit());
            sb.append(",产生");
            sb.append(WarningLevelType.getByCode(condition.getWarningLevel()));

            if (i < ruleConditionList.size() - 1) {
                sb.append(";");
            }
        }


        return sb.toString();
    }

    @Override
    public List<WarningDiagnosisVO> getDiagnosisLayer(String scene) {
        List<WarningEvent> eventList = warningEventMapper.listAllWarningOneDayInner(scene, Lists.newArrayList(5));
        if (CollectionUtils.isEmpty(eventList)) {
            return Lists.newArrayList();
        }

        List<Long> eventIdList = eventList.stream()
                .map(WarningEvent::getId)
                .collect(Collectors.toList());

        List<WarningEventDepartment> departmentList = warningEventDepartmentMapper.listByEventIdList(eventIdList);

        Map<Long, List<WarningEventDepartment>> departmentMap = departmentList.stream()
                .collect(Collectors.groupingBy(WarningEventDepartment::getEventId));

        List<WarningDiagnosisVO> list = eventList.stream()
                .map(event -> convert2Diagnosis(event, departmentMap.get(event.getId())))
                .collect(Collectors.toList());

        //添加文件
        List<BusinessFile> fileList = businessFileMapper.listByBusinessIdListAndType(eventIdList, FileTypeEnum.TYPE_02.getCode());
        if (CollectionUtils.isNotEmpty(fileList)) {
            Map<Long, List<BusinessFile>> fileMap = fileList.stream()
                    .collect(Collectors.groupingBy(BusinessFile::getBusinessId));

            for (WarningDiagnosisVO diagnosisVO : list) {
                List<BusinessFile> fileListByEventId = fileMap.get(diagnosisVO.getId());
                if (CollectionUtils.isNotEmpty(fileListByEventId)) {
                    diagnosisVO.setFileList(fileListByEventId);
                }
            }
        }

        return list;
    }

    @Override
    public List<DeviceInfo> listDeviceAndData(String deviceType, String scene,
                                              Long monitorId, Integer warningFlag) {
        //特殊处理，以下类型不区分场景
        List<String> yuLiangList = Lists.newArrayList("翻斗雨量计", "雨量站", "投入式水位计", "雷达水位计");
        if (yuLiangList.contains(deviceType)) {
            scene = null;
        }

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByDeviceTypeAndSceneAndMonitorId(deviceType, scene,
                monitorId, true, null, null);
        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
            List<String> deviceThirdIdList = deviceInfoList.stream()
                    .map(DeviceInfo::getDeviceThirdId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(deviceThirdIdList)) {
                return deviceInfoList;
            }

            List<WarningInfo> warningInfoList = warningInfoMapper.listByDeviceThird(deviceThirdIdList);

            Map<String, List<WarningInfo>> warningMap = warningInfoList.stream()
                    .filter(warningInfo -> StringUtils.isNotBlank(warningInfo.getDeviceThirdId()))
                    .collect(Collectors.groupingBy(WarningInfo::getDeviceThirdId));

            if (Objects.nonNull(warningFlag)) {
                if (warningFlag == 1) {
                    deviceThirdIdList.removeIf(deviceThirdId -> warningMap.get(deviceThirdId) == null);
                    deviceInfoList.removeIf(deviceInfo -> warningMap.get(deviceInfo.getDeviceThirdId()) == null);
                } else {
                    deviceThirdIdList.removeIf(deviceThirdId -> warningMap.get(deviceThirdId) != null);
                    deviceInfoList.removeIf(deviceInfo -> warningMap.get(deviceInfo.getDeviceThirdId()) != null);
                }
            }

            List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceThirdIdListByHour(deviceThirdIdList, 3);

            Map<String, List<DeviceValue>> deviceMap = deviceValueList.stream()
                    .filter(deviceValue -> StringUtils.isNotBlank(deviceValue.getDeviceThirdId()))
                    .collect(Collectors.groupingBy(DeviceValue::getDeviceThirdId));

            for (DeviceInfo deviceInfo : deviceInfoList) {
                List<DeviceValue> deviceValueOneList = deviceMap.get(deviceInfo.getDeviceThirdId());

                if (CollectionUtils.isNotEmpty(deviceValueOneList)) {
                    if ("雨量站".equals(deviceInfo.getDeviceType())
                            || "翻斗雨量计".equals(deviceInfo.getDeviceType())) {
                        deviceValueOneList.sort(Comparator.comparing(DeviceValue::getMonitorItem));
                    }

                    if ("燃气阀井监测仪".equals(deviceInfo.getDeviceType())) {
                        deviceValueOneList.sort(Comparator.comparing(DeviceValue::getMonitorItem, Comparator.reverseOrder()));
                    }
                }

                deviceInfo.setValueList(deviceValueOneList);

                List<WarningInfo> warningInfoOneList = warningMap.get(deviceInfo.getDeviceThirdId());
                if (CollectionUtils.isNotEmpty(warningInfoOneList)) {
                    deviceInfo.setWarningFlag(true);
                }
            }

            List<Long> deviceIdList = deviceInfoList.stream()
                    .map(DeviceInfo::getId)
                    .collect(Collectors.toList());

            List<BusinessFile> fileList = businessFileMapper.listByBusinessIdListAndType(deviceIdList, FileTypeEnum.TYPE_06.getCode());
            if (CollectionUtils.isNotEmpty(fileList)) {
                Map<Long, List<BusinessFile>> fileMap = fileList.stream()
                        .collect(Collectors.groupingBy(BusinessFile::getBusinessId));

                for (DeviceInfo deviceInfo : deviceInfoList) {
                    List<BusinessFile> fileListByDeviceId = fileMap.get(deviceInfo.getId());
                    if (CollectionUtils.isNotEmpty(fileListByDeviceId)) {
                        deviceInfo.setFileList(fileListByDeviceId);
                    }
                }
            }
        }



        return deviceInfoList;
    }

    @Override
    public CityRiskAvgLineVO getSafetyLine(String deviceThirdId, String monitorItem) {
        CityRiskAvgLineVO cityRiskAvgLineVO = new CityRiskAvgLineVO();

        DeviceInfo deviceInfo = deviceInfoMapper.getByThirdId(deviceThirdId);

        List<DeviceValue> deviceAllValueList = deviceValueMapper.listByDeviceThirdId(deviceThirdId);

        if (CollectionUtils.isNotEmpty(deviceAllValueList)) {
            if ("雨量站".equals(deviceInfo.getDeviceType())
                    || "翻斗雨量计".equals(deviceInfo.getDeviceType())) {
                deviceAllValueList.sort(Comparator.comparing(DeviceValue::getMonitorItem));
            } else {
                deviceAllValueList.sort(Comparator.comparing(DeviceValue::getMonitorItem, Comparator.reverseOrder()));
            }
        }

        cityRiskAvgLineVO.setValueList(deviceAllValueList);

        DateDTO dateDTO = null;
        if ("小时降雨量".equals(monitorItem)) {
            dateDTO = DateUtils.getTimeRange(new Date(), "日");
        } else {
            dateDTO = DateUtils.getTimeRange(new Date(), "近3天");
        }

        List<TimeRange> timeRangeList = Lists.newArrayList();

        List<CityRiskTimeLineVO> countVOList = Lists.newArrayList();

        Long totalCount = deviceValueMapper.getCount(deviceThirdId, monitorItem, dateDTO.getStartTime(), dateDTO.getEndTime());

        List<DeviceValue> deviceValueList = Lists.newArrayList();
        if (totalCount < 100000) {
            deviceValueList = deviceValueMapper.listByDeviceAndMonitorItem(deviceThirdId, monitorItem,
                    dateDTO.getStartTime(), dateDTO.getEndTime());
        } else {
            deviceValueList = deviceValueMapper.listByDeviceAndMonitorItemV2(deviceThirdId, monitorItem,
                    dateDTO.getStartTime(), dateDTO.getEndTime());
        }
        if (CollectionUtils.isNotEmpty(deviceValueList)) {
            DeviceValue deviceValue = deviceValueList.get(0);

            cityRiskAvgLineVO.setUnit(deviceValue.getMonitorUnit());
        }

        if ("小时降雨量".equals(monitorItem)) {
            timeRangeList = DateUtils.generateDateRanges(dateDTO.getStartTime(), dateDTO.getEndTime(),
                    WarningCenterReportType.TYPE_01.getCode());

            for (TimeRange timeRange : timeRangeList) {
                CityRiskTimeLineVO lineVO = new CityRiskTimeLineVO();
                lineVO.setKey(timeRange.getLabel());
                lineVO.setValue(0.0);
                lineVO.setStartTime(timeRange.getStartTime());
                lineVO.setEndTime(timeRange.getEndTime());

                countVOList.add(lineVO);
            }

            WarningRuleCondition condition =
                    warningRuleConditionMapper.getMinConditionByMonitorItem(deviceThirdId, monitorItem);

            if (Objects.nonNull(condition)) {
                cityRiskAvgLineVO.setThreshold(condition.getMonitorValue());
            }

            for (DeviceValue deviceValue : deviceValueList) {
                for (CityRiskTimeLineVO cityRiskTimeLineVO : countVOList) {
                    if (deviceValue.getMonitorTime().after(cityRiskTimeLineVO.getStartTime())
                            && deviceValue.getMonitorTime().before(cityRiskTimeLineVO.getEndTime())) {
                        List<BigDecimal> valueList = cityRiskTimeLineVO.getValueList();

                        BigDecimal valueDecimal = new BigDecimal(deviceValue.getMonitorValue());

                        if (CollectionUtils.isEmpty(valueList)) {
                            cityRiskTimeLineVO.setValueList(Lists.newArrayList(valueDecimal));
                        } else {
                            cityRiskTimeLineVO.getValueList().add(valueDecimal);
                        }
                    }

                    if (CollectionUtils.isNotEmpty(cityRiskTimeLineVO.getValueList())) {
                        cityRiskTimeLineVO.setValue(cityRiskTimeLineVO.getValueList().stream()
                                .mapToDouble(BigDecimal::doubleValue).average().getAsDouble());
                    }
                }
            }
        } else {
            countVOList = deviceValueList.stream()
                    .map(this::convert2CityRiskTimeLineVO)
                    .collect(Collectors.toList());
        }

        cityRiskAvgLineVO.setCountVOList(countVOList);

        return cityRiskAvgLineVO;
    }

    private CityRiskTimeLineVO convert2CityRiskTimeLineVO(DeviceValue value) {
        CityRiskTimeLineVO cityRiskTimeLineVO = new CityRiskTimeLineVO();
        cityRiskTimeLineVO.setKey(DateUtils.formatDateSecond(value.getMonitorTime()));
        cityRiskTimeLineVO.setValue(Double.valueOf(value.getMonitorValue()));

        return cityRiskTimeLineVO;
    }

//    @Override
//    public CityRiskAvgLineVO getSafetyLine(String deviceThirdId, String monitorItem) {
//        CityRiskAvgLineVO cityRiskAvgLineVO = new CityRiskAvgLineVO();
//
//        DeviceInfo deviceInfo = deviceInfoMapper.getByThirdId(deviceThirdId);
//
//        List<DeviceValue> deviceAllValueList = deviceValueMapper.listByDeviceThirdId(deviceThirdId);
//
//        if (CollectionUtils.isNotEmpty(deviceAllValueList)) {
//            if ("雨量站".equals(deviceInfo.getDeviceType())
//                    || "翻斗雨量计".equals(deviceInfo.getDeviceType())) {
//                deviceAllValueList.sort(Comparator.comparing(DeviceValue::getMonitorItem));
//            } else {
//                deviceAllValueList.sort(Comparator.comparing(DeviceValue::getMonitorItem, Comparator.reverseOrder()));
//            }
//        }
//
//        cityRiskAvgLineVO.setValueList(deviceAllValueList);
//
//        DateDTO dateDTO = null;
//        if ("小时降雨量".equals(monitorItem)) {
//            dateDTO = DateUtils.getTimeRange(new Date(), "日");
//        } else {
//            dateDTO = DateUtils.getTimeRange(new Date(), "近7天");
//        }
//
//        List<TimeRange> timeRangeList = Lists.newArrayList();
//        if ("小时降雨量".equals(monitorItem)) {
//            timeRangeList = DateUtils.generateDateRanges(dateDTO.getStartTime(), dateDTO.getEndTime(),
//                    WarningCenterReportType.TYPE_01.getCode());
//        } else {
//            timeRangeList = DateUtils.generateDateRanges(dateDTO.getStartTime(), dateDTO.getEndTime(),
//                    WarningCenterReportType.TYPE_00.getCode());
//        }
//
//        List<CityRiskTimeLineVO> countVOList = Lists.newArrayList();
//        for (TimeRange timeRange : timeRangeList) {
//            CityRiskTimeLineVO lineVO = new CityRiskTimeLineVO();
//            lineVO.setKey(timeRange.getLabel());
//            lineVO.setValue(0.0);
//            lineVO.setStartTime(timeRange.getStartTime());
//            lineVO.setEndTime(timeRange.getEndTime());
//
//            countVOList.add(lineVO);
//        }
//
//        WarningRuleCondition condition =
//                warningRuleConditionMapper.getMinConditionByMonitorItem(deviceThirdId, monitorItem);
//
//        if (Objects.nonNull(condition)) {
//            cityRiskAvgLineVO.setThreshold(condition.getMonitorValue());
//        }
//
//        List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceAndMonitorItem(deviceThirdId, monitorItem,
//                dateDTO.getStartTime(), dateDTO.getEndTime());
//
//        for (DeviceValue deviceValue : deviceValueList) {
//            for (CityRiskTimeLineVO cityRiskTimeLineVO : countVOList) {
//                if (deviceValue.getMonitorTime().after(cityRiskTimeLineVO.getStartTime())
//                        && deviceValue.getMonitorTime().before(cityRiskTimeLineVO.getEndTime())) {
//                    List<BigDecimal> valueList = cityRiskTimeLineVO.getValueList();
//
//                    BigDecimal valueDecimal = new BigDecimal(deviceValue.getMonitorValue());
//
//                    if (CollectionUtils.isEmpty(valueList)) {
//                        cityRiskTimeLineVO.setValueList(Lists.newArrayList(valueDecimal));
//                    } else {
//                        cityRiskTimeLineVO.getValueList().add(valueDecimal);
//                    }
//                }
//
//                if (CollectionUtils.isNotEmpty(cityRiskTimeLineVO.getValueList())) {
//                    cityRiskTimeLineVO.setValue(cityRiskTimeLineVO.getValueList().stream()
//                            .mapToDouble(BigDecimal::doubleValue).average().getAsDouble());
//                }
//            }
//        }
//
//        cityRiskAvgLineVO.setCountVOList(countVOList);
//
//        return cityRiskAvgLineVO;
//    }

    @Override
    public List<WarningEvent> getLinkDisposeV2(WarningEventPageVO pageVO) {
        return warningEventMapper.listLastFourWarningV2(pageVO);
    }

    @Override
    public List<VideoStreetVO> getVideoStreet() {
        List<VideoStreetVO> streetList = deviceInfoMapper.getStreetByDevice("视频监控");

        return streetList;
    }

    @Override
    public List<DeviceInfo> getDeviceByStreet(String deviceStreetAreaCode, String scene, String deviceType) {
        List<DeviceInfo> deviceInfoList = deviceInfoMapper.getDeviceByStreet(deviceStreetAreaCode, scene, deviceType);
//        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
//            List<Long> monitorIdList = deviceInfoList.stream()
//                    .map(DeviceInfo::getMonitorId)
//                    .collect(Collectors.toList());
//
//            List<BusinessFile> fileList = businessFileMapper.listByBusinessIdListAndType(monitorIdList, FileTypeEnum.TYPE_07.getCode());
//            if (CollectionUtils.isNotEmpty(fileList)) {
//                Map<Long, List<BusinessFile>> fileMap = fileList.stream()
//                        .collect(Collectors.groupingBy(BusinessFile::getBusinessId));
//
//                for (DeviceInfo deviceInfo : deviceInfoList) {
//                    List<BusinessFile> fileOneList = fileMap.get(deviceInfo.getMonitorId());
//
//                    if (CollectionUtils.isNotEmpty(fileOneList)) {
//                        BusinessFile businessFile = fileOneList.get(0);
//
//                        deviceInfo.setFileUrl(businessFile.getFileUrl());
//                    }
//                }
//            }
//        }

        return deviceInfoList;
    }

    @Override
    public String getVideoUrl(String deviceThirdId, String protocol) {
//        return callPostApi(deviceThirdId, protocol);

        final String url = ARTEMIS_PATH + "/api/video/v1/cameras/previewURLs";
        Map<String, Object> paramMap = new HashMap<String, Object>();// post请求Form表单参数
        paramMap.put("cameraIndexCode", deviceThirdId);
        paramMap.put("protocol", protocol);

        JSONObject jsonObject = sendHkApi(url, paramMap);

        if (Objects.nonNull(jsonObject)) {
            Map<String, String> dataMap = (Map<String, String>) jsonObject.get("data");

            return dataMap.get("url");
        }

        return null;
    }

    @Override
    public Map<String, String> controlVideo(HkControlVideoVO hkControlVideoVO) {
        final String url = ARTEMIS_PATH + "/api/video/v1/ptzs/controlling";
        Map<String, Object> paramMap = new HashMap<String, Object>();// post请求Form表单参数
        paramMap.put("cameraIndexCode", hkControlVideoVO.getCameraIndexCode());
        paramMap.put("action", hkControlVideoVO.getAction());
        paramMap.put("command", hkControlVideoVO.getCommand());
        if (Objects.nonNull(hkControlVideoVO.getSpeed())) {
            paramMap.put("speed", hkControlVideoVO.getSpeed());
        }
        if (Objects.nonNull(hkControlVideoVO.getPresetIndex())) {
            paramMap.put("presetIndex", hkControlVideoVO.getPresetIndex());
        }

        JSONObject jsonObject = sendHkApi(url, paramMap);

        if (Objects.nonNull(jsonObject)) {
            Map<String, String> dataMap = (Map<String, String>) jsonObject.get("data");

            return dataMap;
        }

        return null;
    }

    @Override
    public Map<String, Object> getPreviewUrl(HkPreviewVideoVO videoVO) {
        final String url = ARTEMIS_PATH + "/api/video/v1/cameras/playbackURLs";
        Map<String, Object> paramMap = new HashMap<String, Object>();// post请求Form表单参数
        paramMap.put("cameraIndexCode", videoVO.getCameraIndexCode());
        paramMap.put("beginTime", DateUtils.formatDateIOS(videoVO.getBeginTime()));
        paramMap.put("endTime", DateUtils.formatDateIOS(videoVO.getEndTime()));
        if (StringUtils.isNotBlank(videoVO.getRecordLocation())) {
            paramMap.put("recordLocation", videoVO.getRecordLocation());
        }
        if (StringUtils.isNotBlank(videoVO.getProtocol())) {
            paramMap.put("protocol", videoVO.getProtocol());
        }
        paramMap.put("needReturnClipInfo", videoVO.getNeedReturnClipInfo());
        if (StringUtils.isNotBlank(videoVO.getUuid())) {
            paramMap.put("uuid", videoVO.getUuid());
        }
        if (StringUtils.isNotBlank(videoVO.getExpand())) {
            paramMap.put("expand", videoVO.getExpand());
        }


        JSONObject jsonObject = sendHkApi(url, paramMap);

        if (Objects.nonNull(jsonObject)) {
            Map<String, Object> dataMap = (Map<String, Object>) jsonObject.get("data");

            return dataMap;
        }

        return null;
    }

    @Override
    public List<WaterLayerVO> getSceneLayer(String scene, String newType) {
        List<WaterLayerVO> list = Lists.newArrayList();

        MonitorSceneType sceneType = MonitorSceneType.getByDesc(scene);
        if (Objects.isNull(sceneType)) {
            return Lists.newArrayList();
        }

        List<DeviceMonitor> monitorList = deviceMonitorMapper.listByTypeAndNewType(sceneType.getTypeList(), newType);
        if (CollectionUtils.isEmpty(monitorList)) {
            return list;
        }

        for (DeviceMonitor deviceMonitor : monitorList) {
            WaterLayerVO waterLayerVO = new WaterLayerVO();
            waterLayerVO.setId(deviceMonitor.getId());
            waterLayerVO.setMonitorName(deviceMonitor.getMonitorName());
            waterLayerVO.setMonitorType(deviceMonitor.getMonitorType());
            waterLayerVO.setLon(deviceMonitor.getLon());
            waterLayerVO.setLat(deviceMonitor.getLat());
            waterLayerVO.setNewType(deviceMonitor.getNewType());

            list.add(waterLayerVO);
        }

        List<Long> monitorIdList = monitorList.stream()
                .map(DeviceMonitor::getId)
                .collect(Collectors.toList());

        List<DeviceInfo> deviceInfoList = Lists.newArrayList();

        if (MonitorSceneType.SCENE_01 == sceneType) {
            deviceInfoList = deviceInfoMapper.listByMonitorIdsAndType(monitorIdList, Lists.newArrayList("投入式水位计"));
        } else if (MonitorSceneType.SCENE_04 == sceneType) {
            deviceInfoList = deviceInfoMapper.listByMonitorIdsAndType(monitorIdList, Lists.newArrayList("雷达水位计"));
        }

        Map<Long, List<DeviceInfo>> deviceMap = deviceInfoList.stream()
                .collect(Collectors.groupingBy(DeviceInfo::getMonitorId));

        for (WaterLayerVO waterLayerVO : list) {
            List<DeviceInfo> oneList = deviceMap.get(waterLayerVO.getId());
            if (CollectionUtils.isEmpty(oneList)) {
                continue;
            }

            DeviceInfo deviceInfo = oneList.get(0);

            //获取预警
            WarningEvent warningEvent = warningEventMapper.getUnCompleteEventByThirdId(deviceInfo.getDeviceThirdId());
            if (Objects.nonNull(warningEvent)) {
                waterLayerVO.setWarningLevel(warningEvent.getWarningLevel());
                waterLayerVO.setWarningDetail(warningEvent.getWarningDetail());
            }
        }

        return list;
    }

    @Override
    public List<WaterLayerVO> getWaterloggingLayer() {
        List<WaterLayerVO> list = Lists.newArrayList();

        List<DeviceMonitor> monitorList = deviceMonitorMapper.listByType(MonitorSceneType.SCENE_04.getTypeList());
        if (CollectionUtils.isEmpty(monitorList)) {
            return list;
        }

        List<Long> monitorIdList = monitorList.stream()
                .map(DeviceMonitor::getId)
                .collect(Collectors.toList());

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByMonitorIdsAndType(monitorIdList, Lists.newArrayList("雷达水位计", "视频监控"));

        Map<Long, List<DeviceInfo>> deviceInfoMap = deviceInfoList.stream()
                .collect(Collectors.groupingBy(DeviceInfo::getMonitorId));

        List<DeviceMonitorHedao> hedaoList = deviceMonitorHedaoMapper.listAll();
        Map<String, String> hedaoMap = hedaoList.stream()
                .collect(Collectors.toMap(DeviceMonitorHedao::getJcmc, DeviceMonitorHedao::getBdyz));

        for (DeviceMonitor deviceMonitor : monitorList) {
            WaterLayerVO waterLayerVO = new WaterLayerVO();
            waterLayerVO.setId(deviceMonitor.getId());
            waterLayerVO.setMonitorName(deviceMonitor.getMonitorName());
            waterLayerVO.setMonitorType(deviceMonitor.getMonitorType());
            waterLayerVO.setLon(deviceMonitor.getLon());
            waterLayerVO.setLat(deviceMonitor.getLat());

            //获取河道阈值
            String bdyz = hedaoMap.get(deviceMonitor.getMonitorName());
            if (Objects.nonNull(bdyz)) {
                waterLayerVO.setThreshold(bdyz);
            }

            List<DeviceInfo> deviceOneList = deviceInfoMap.get(deviceMonitor.getId());
            if (CollectionUtils.isNotEmpty(deviceOneList)) {
                List<DeviceInfo> videoList = deviceOneList.stream()
                        .filter(deviceInfo -> "视频监控".equals(deviceInfo.getDeviceType()))
                        .collect(Collectors.toList());
                waterLayerVO.setVideoList(videoList);

                List<DeviceInfo> oneList = deviceOneList.stream()
                        .filter(deviceInfo -> "雷达水位计".equals(deviceInfo.getDeviceType()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(oneList)) {
                    DeviceInfo deviceInfo = oneList.get(0);
                    waterLayerVO.setDeviceInfo(deviceInfo);
                    //获取最新设备数据
                    List<DeviceValue> lastList = deviceValueMapper.listByDeviceThirdId(deviceInfo.getDeviceThirdId());
                    waterLayerVO.setDeviceValueList(lastList);

                    //计算蓄水率
                    if (CollectionUtils.isNotEmpty(waterLayerVO.getDeviceValueList()) && Objects.nonNull(waterLayerVO.getThreshold())) {
                        Optional<DeviceValue> value = waterLayerVO.getDeviceValueList().stream()
                                .filter(deviceValue -> "液位高".equals(deviceValue.getMonitorItem()))
                                .findFirst();
                        if (value.isPresent()) {
                            String monitorValue = value.get().getMonitorValue();
                            if (StringUtils.isNotBlank(monitorValue)) {
                                String result = PercentUtils.calculatePercentage(monitorValue, waterLayerVO.getThreshold());
                                waterLayerVO.setWaterStorageRate(result);
                            }
                        }
                    }
                    //获取预警
                    WarningEvent warningEvent = warningEventMapper.getUnCompleteEventByThirdId(deviceInfo.getDeviceThirdId());
                    if (Objects.nonNull(warningEvent)) {
                        waterLayerVO.setWarningLevel(warningEvent.getWarningLevel());
                        waterLayerVO.setWarningDetail(warningEvent.getWarningDetail());
                    }
                }


            }

            list.add(waterLayerVO);
        }

        return list;
    }

    @Override
    public String flushGeometry(Map<String, List<Map<String, Object>>> params, String type) {
        List<Map<String, Object>> featureList = params.get("features");

        List<DeviceMonitorGeometry> resultList = Lists.newArrayList();
        for (Map<String, Object> map : featureList) {
            DeviceMonitorGeometry deviceMonitorGeometry = new DeviceMonitorGeometry();

            JSONObject properties = JSON.parseObject(JSON.toJSONString(map.get("properties")));

            String name = properties.get("NAME") + "";

            JSONObject geometry = JSON.parseObject(JSON.toJSONString(map.get("geometry")));

            String coordinates = JSON.toJSONString(geometry.get("coordinates"));

            deviceMonitorGeometry.setMonitorName(name);
            deviceMonitorGeometry.setGeometry(coordinates);
            deviceMonitorGeometry.setType(type);

            resultList.add(deviceMonitorGeometry);
        }

        deviceMonitorGeometryMapper.batchInsert(resultList);

        return "";
    }

    @Override
    public WaterLayerVO getWaterDetail(Long monitorId) {
        DeviceMonitor deviceMonitor = deviceMonitorMapper.selectDeviceMonitorById(monitorId);
        if (Objects.isNull(deviceMonitor)) {
            return null;
        }

        WaterLayerVO resultVO = new WaterLayerVO();

        if ("山塘".equals(deviceMonitor.getMonitorType())) {
            DeviceMonitorShantang deviceMonitorShantang = deviceMonitorShantangMapper.getByMonitorName(deviceMonitor.getMonitorName());

            String threshold = "";
            if (Objects.nonNull(deviceMonitorShantang)) {
                threshold = deviceMonitorShantang.getDsjzwbdgc();
            }

            resultVO = createWaterLayerDetail(threshold, deviceMonitor,
                    Lists.newArrayList("投入式水位计", "水位计", "振弦式渗压计"), Lists.newArrayList("液位高", "水位"));

        } else if ("水库".equals(deviceMonitor.getMonitorType())) {
            DeviceMonitorShuiku deviceMonitorShuiku = deviceMonitorShuikuMapper.getByMonitorName(deviceMonitor.getMonitorName());

            String threshold = "";
            if (Objects.nonNull(deviceMonitorShuiku)) {
                threshold = deviceMonitorShuiku.getF19();
            }

            resultVO = createWaterLayerDetail(threshold, deviceMonitor,
                    Lists.newArrayList("投入式水位计", "水位计", "振弦式渗压计"), Lists.newArrayList("液位高", "水位"));

        } else if ("河道".equals(deviceMonitor.getMonitorType())) {
            DeviceMonitorHedao deviceMonitorHedao = deviceMonitorHedaoMapper.getByMonitorName(deviceMonitor.getMonitorName());

            String threshold = "";
            if (Objects.nonNull(deviceMonitorHedao)) {
                threshold = deviceMonitorHedao.getBdyz();
            }

            resultVO = createWaterLayerDetail(threshold, deviceMonitor,
                    Lists.newArrayList("雷达水位计"), Lists.newArrayList("液位高"));
        }

        DeviceMonitorGeometry deviceMonitorGeometry = deviceMonitorGeometryMapper.getByMonitorName(deviceMonitor.getMonitorName());
        if (Objects.nonNull(deviceMonitorGeometry)) {
            resultVO.setGeometry(deviceMonitorGeometry.getGeometry());
        }

        return resultVO;
    }

    @Override
    public String getYuLiangLayer() {
        TbIrsResult tbIrsResult = new TbIrsResult();
        tbIrsResult.setName("浙江省级区县面雨量小时实况信息查询");
        tbIrsResult.setUrl("https://interface.zjzwfw.gov.cn/gateway/api/001003089/dataSharing/eI61ZTou8Se32Kf9.htm");


        tbIrsResult.setObservtimes("2024-04-01 10:00:00");

        Map map = (Map) baseIrsService.actuator(tbIrsResult, baseIrsService.getRequestMap());

        log.info("getYuLiangLayer, map:{}", JSON.toJSONString(map));

        return "1";
    }

    @Override
    public List<DeviceYuLiangVO> getYuLiangLayerV2() {
        List<DeviceYuLiangVO> list = Lists.newArrayList();

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByYuLiang("雨量");
        if (CollectionUtils.isEmpty(deviceInfoList)) {
            return list;
        }

        List<String> deviceThirdIdList = deviceInfoList.stream()
                .map(DeviceInfo::getDeviceThirdId)
                .collect(Collectors.toList());

        List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceThirdIdListAndMonitorItem(deviceThirdIdList, "小时降雨量");
        if (CollectionUtils.isNotEmpty(deviceValueList)) {
            Map<String, DeviceValue> deviceValueMap = deviceValueList.stream()
                    .collect(Collectors.toMap(DeviceValue::getDeviceThirdId, Function.identity()));

            for (DeviceInfo deviceInfo : deviceInfoList) {
                DeviceYuLiangVO deviceYuLiangVO = new DeviceYuLiangVO();
                deviceYuLiangVO.setId(deviceInfo.getId());
                deviceYuLiangVO.setLnglat(Lists.newArrayList(Double.valueOf(deviceInfo.getLon()), Double.valueOf(deviceInfo.getLat())));

                DeviceValue deviceValue = deviceValueMap.get(deviceInfo.getDeviceThirdId());
                if (Objects.nonNull(deviceValue)) {
                    deviceYuLiangVO.setValue(Double.valueOf(deviceValue.getMonitorValue()));
                }

                list.add(deviceYuLiangVO);
            }
        }

        return list;
    }

    @Override
    public List<DeviceMonitor> getAllMonitor(String scene) {
        List<DeviceMonitor> resultList = deviceMonitorMapper.listAll(null, scene, null);

        return resultList;
    }

    @Override
    public List<DeviceInfo> getDeviceList(Long monitorId) {
        List<DeviceInfo> list = deviceInfoMapper.listByDeviceTypeOrMonitorId(null, monitorId, Lists.newArrayList("视频监控"));

        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        List<WarningEvent> eventList = warningEventMapper.listBaiyiWarning();

        Map<String, List<WarningEvent>> eventMap = eventList.stream()
                .filter(warningEvent -> Objects.nonNull(warningEvent.getDeviceThirdId()))
                .collect(Collectors.groupingBy(WarningEvent::getDeviceThirdId));

        List<DeviceInfo> resultList = Lists.newArrayList();
        for (DeviceInfo deviceInfo : list) {
            if ("白蚁安全观测装置".equals(deviceInfo.getDeviceType())) {
                List<WarningEvent> eventOneList = eventMap.get(deviceInfo.getDeviceThirdId());
                if (CollectionUtils.isNotEmpty(eventOneList)) {
                    resultList.add(deviceInfo);
                }
            } else {
                resultList.add(deviceInfo);
            }
        }

        fillMonitorLayer(resultList);
        fillBaiyiCount(monitorId, resultList);
        fillShenyaValue(resultList);

        return resultList;
    }

    @Override
    public List<DeviceMonitorShuikuVO> getShuikuList(String type, String keywords) {
        if (StringUtils.isBlank(type)) {
            type = "水库";
        }

        List<DeviceMonitor> deviceMonitorList = deviceMonitorMapper
                .listByTypeAndKeywords(Lists.newArrayList(type), keywords);
        if (CollectionUtils.isEmpty(deviceMonitorList)) {
            return Lists.newArrayList();
        }

        List<DeviceMonitorShuikuVO> resultList = Lists.newArrayList();

        List<Long> monitorIdList = deviceMonitorList.stream()
                .map(DeviceMonitor::getId)
                .collect(Collectors.toList());

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -3);

        List<DeviceInfo> deviceInfoList = deviceInfoMapper
                .listByMonitorIdsAndType(monitorIdList, Lists.newArrayList("投入式水位计", "水位计", "雷达水位计"));

        List<String> deviceThirdIdList = deviceInfoList.stream()
                .map(DeviceInfo::getDeviceThirdId)
                .collect(Collectors.toList());

        List<DeviceValue> deviceValueList = deviceValueMapper
                .listByDeviceThirdIdList(deviceThirdIdList, 1);

        Map<Long, List<DeviceValue>> deviceValueMap = deviceValueList.stream()
                .collect(Collectors.groupingBy(DeviceValue::getMonitorId));

        Map<String, String> extendMap = new HashMap<>();
        if ("水库".equals(type)) {
            List<DeviceMonitorShuiku> shuikuExtendList = deviceMonitorShuikuMapper.listAll();

            extendMap = shuikuExtendList.stream()
                    .collect(Collectors.toMap(DeviceMonitorShuiku::getF3, DeviceMonitorShuiku::getF19));
        } else if ("山塘".equals(type)) {
            List<DeviceMonitorShantang> shantangList = deviceMonitorShantangMapper.listAll();

            extendMap = shantangList.stream()
                    .collect(Collectors.toMap(DeviceMonitorShantang::getStmc, DeviceMonitorShantang::getDsjzwbdgc));
        } else if ("河道".equals(type)) {
            List<DeviceMonitorHedao> hedaoList = deviceMonitorHedaoMapper.listAll();

            extendMap = hedaoList.stream()
                    .collect(Collectors.toMap(DeviceMonitorHedao::getJcmc, DeviceMonitorHedao::getBdyz));
        }

        for (DeviceMonitor deviceMonitor : deviceMonitorList) {
            DeviceMonitorShuikuVO shuikuVO = new DeviceMonitorShuikuVO();
            shuikuVO.setMonitorId(deviceMonitor.getId());
            shuikuVO.setMonitorName(deviceMonitor.getMonitorName());
            shuikuVO.setNewType(deviceMonitor.getNewType());

            List<DeviceValue> oneValueList = deviceValueMap.get(deviceMonitor.getId());

            Double currentWaterLevel = null;
            if (CollectionUtils.isNotEmpty(oneValueList)) {
                Optional<DeviceValue> optionalDeviceValue = oneValueList.stream().filter(deviceValue -> "液位高".equals(deviceValue.getMonitorItem())
                        || "水位".equals(deviceValue.getMonitorItem())).findFirst();

                if (optionalDeviceValue.isPresent()) {
                    DeviceValue deviceValue = optionalDeviceValue.get();
                    shuikuVO.setCurrentWaterLevel(deviceValue.getMonitorValue() + deviceValue.getMonitorUnit());
                    currentWaterLevel = Double.valueOf(deviceValue.getMonitorValue());

                    shuikuVO.setMonitorTime(deviceValue.getMonitorTime());
                }
            }

            String warningLevel = extendMap.get(deviceMonitor.getMonitorName());

            Double warningWaterLevel = null;
            if (StringUtils.isNotBlank(warningLevel)) {
                warningWaterLevel = Double.valueOf(warningLevel);

                shuikuVO.setWarningWaterLevel(warningWaterLevel + "m");
            }

            if (Objects.nonNull(currentWaterLevel) && Objects.nonNull(warningWaterLevel)) {
                double sub = currentWaterLevel - warningWaterLevel;
                String format = df.format(sub);
                shuikuVO.setSubLevel(format + "m");
            }

            resultList.add(shuikuVO);
        }

        sortLevelList(resultList, type);

        return resultList;
    }

    private void sortLevelList(List<DeviceMonitorShuikuVO> resultList, String type) {
        if ("水库".equals(type)) {
            resultList.sort(Comparator
                    .comparing((DeviceMonitorShuikuVO vo) -> {
                        // 让"大型"优先，其次"中型"，其余按原顺序
                        if ("大型".equals(vo.getNewType())) {
                            return 0;
                        } else if ("中型".equals(vo.getNewType())) {
                            return 1;
                        } else if ("小（1）型".equals(vo.getNewType())) {
                            return 2;
                        } else if ("小（2）型".equals(vo.getNewType())) {
                            return 3;
                        }
                        return 4;
                    })
                    .thenComparing(vo -> {
                        try {
                            // subLevel 可能为null或带单位，需处理
                            String sub = vo.getSubLevel();
                            if (sub == null) {
                                return -500.00;
                            }
                            // 去掉单位，只保留数字
                            sub = sub.replaceAll("[^\\d.-]", "");
                            return Double.parseDouble(sub);
                        } catch (Exception e) {
                            return Double.MAX_VALUE;
                        }
                    }, Comparator.reverseOrder())
            );
        } else if ("山塘".equals(type)) {
            resultList.sort(Comparator
                    .comparing((DeviceMonitorShuikuVO vo) -> {
                        // 让"大型"优先，其次"中型"，其余按原顺序
                        if ("高坝山塘".equals(vo.getNewType())) {
                            return 0;
                        } else if ("普通山塘".equals(vo.getNewType())) {
                            return 1;
                        } else if ("屋顶山塘".equals(vo.getNewType())) {
                            return 2;
                        }

                        return 3;
                    }).thenComparing(vo -> {
                        // subLevel 可能为null或带单位，需处理
                        String currentWaterLevel = vo.getCurrentWaterLevel();
                        if (currentWaterLevel == null) {
                            return -500.00;
                        }
                        // 去掉单位，只保留数字
                        currentWaterLevel = currentWaterLevel.replaceAll("[^\\d.-]", "");
                        return Double.parseDouble(currentWaterLevel);
                    }, Comparator.reverseOrder()));
        } else {
            resultList.sort(Comparator.comparing(vo -> {
                // subLevel 可能为null或带单位，需处理
                String currentWaterLevel = vo.getCurrentWaterLevel();
                if (currentWaterLevel == null) {
                    return -500.00;
                }
                // 去掉单位，只保留数字
                currentWaterLevel = currentWaterLevel.replaceAll("[^\\d.-]", "");
                return Double.parseDouble(currentWaterLevel);
            }, Comparator.reverseOrder()));
        }
    }

    @Override
    public DeviceYuLiangStreetAllVO getWaterExtend() {
        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByYuLiang("雨量");
        if (CollectionUtils.isEmpty(deviceInfoList)) {
            return DeviceYuLiangStreetAllVO.Init();
        }

        DeviceYuLiangStreetAllVO result = new DeviceYuLiangStreetAllVO();
        List<DeviceYuLiangStreetVO> streetList = Lists.newArrayList();
        List<DeviceYuLiangExtendVO> list = Lists.newArrayList();
        result.setStreetList(streetList);
        result.setDeviceList(list);

        List<String> deviceThirdIdList = deviceInfoList.stream()
                .map(DeviceInfo::getDeviceThirdId)
                .collect(Collectors.toList());

        List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceThirdIdListAndMonitorItem(deviceThirdIdList,
                "小时降雨量");

        Map<String, List<DeviceValue>> deviceValueMap = deviceValueList.stream()
                .collect(Collectors.groupingBy(DeviceValue::getDeviceThirdId));
        for (DeviceInfo deviceInfo : deviceInfoList) {
            DeviceYuLiangExtendVO yuLiangExtendVO = new DeviceYuLiangExtendVO();
            yuLiangExtendVO.setDeviceId(deviceInfo.getId());
            yuLiangExtendVO.setDeviceName(deviceInfo.getDeviceName());
            yuLiangExtendVO.setDeviceStreet(deviceInfo.getDeviceStreet());

            List<DeviceValue> oneList = deviceValueMap.get(deviceInfo.getDeviceThirdId());
            if (CollectionUtils.isNotEmpty(oneList)) {
                DeviceValue deviceValue = oneList.get(0);
                yuLiangExtendVO.setHourRainFall(deviceValue.getMonitorValue() + deviceValue.getMonitorUnit());
                yuLiangExtendVO.setHourRainFallDouble(Double.valueOf(deviceValue.getMonitorValue()));
            }

            list.add(yuLiangExtendVO);
        }

        Map<String, List<DeviceYuLiangExtendVO>> streetMap = list.stream()
                .filter(item -> StringUtils.isNotBlank(item.getDeviceStreet()))
                .collect(Collectors.groupingBy(DeviceYuLiangExtendVO::getDeviceStreet));

        for (Map.Entry<String, List<DeviceYuLiangExtendVO>> entry : streetMap.entrySet()) {
            List<DeviceYuLiangExtendVO> oneExtendList = entry.getValue();

            List<Double> hourRainFallList = oneExtendList.stream()
                    .map(DeviceYuLiangExtendVO::getHourRainFallDouble)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            DeviceYuLiangStreetVO streetVO = new DeviceYuLiangStreetVO();

            if (CollectionUtils.isNotEmpty(hourRainFallList)) {
                Optional<Double> reduce = hourRainFallList.stream()
                        .reduce(Double::sum);

                Optional<Double> max = hourRainFallList.stream()
                        .max(Double::compareTo);

                Optional<Double> min = hourRainFallList.stream()
                        .min(Double::compareTo);

                streetVO.setMaxRainFall(max.get() + "mm/h");
                streetVO.setMinRainFall(min.get() + "mm/h");

                for (DeviceYuLiangExtendVO yuLiangExtendVO : oneExtendList) {
                    double v = reduce.get() / oneExtendList.size();
                    String format = df.format(v);

                    yuLiangExtendVO.setAvgRainFall(format + "mm/h");
                }
            }
            streetVO.setDeviceStreet(entry.getKey());

            streetList.add(streetVO);
        }

        return result;
    }

    @Override
    public DeviceYuLiangStreetAllV2VO getWaterExtendV2() {
        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByYuLiang("雨量");

        DeviceYuLiangStreetAllV2VO deviceYuLiangStreetAllV2VO = new DeviceYuLiangStreetAllV2VO();
        List<DeviceYuLiangExtendV2VO> streetList = Lists.newArrayList();
        deviceYuLiangStreetAllV2VO.setStreetList(streetList);

        String currentDate = DateUtils.formatDateSecond(DateUtils.getNowDate());

        String rainFallAvg = deviceValueMapper.getAvgRainFall(1);
        if (StringUtils.isBlank(rainFallAvg)) {
            rainFallAvg = "0";
        }

        String rainFallAvgScale = df.format(Double.parseDouble(rainFallAvg));

        deviceYuLiangStreetAllV2VO.setRainFallTime(currentDate);
        deviceYuLiangStreetAllV2VO.setRainFallAvg(rainFallAvgScale + "mm/h");
        deviceYuLiangStreetAllV2VO.setLon("118.60164");
        deviceYuLiangStreetAllV2VO.setLat("28.617417");
        deviceYuLiangStreetAllV2VO.setDeviceStreet("江山市");

        List<DeviceValue> deviceValueList = deviceValueMapper.listRainFallInHour();

        Map<String, String> deviceValueMap = deviceValueList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getDeviceThirdId()))
                .collect(Collectors.toMap(DeviceValue::getDeviceThirdId, DeviceValue::getMonitorValue));

        Map<String, List<DeviceInfo>> deviceInfoMap = deviceInfoList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getDeviceStreet()))
                .collect(Collectors.groupingBy(DeviceInfo::getDeviceStreet));

        for (Map.Entry<String, List<DeviceInfo>> entry : deviceInfoMap.entrySet()) {
            String streetName = entry.getKey();
            List<DeviceInfo> deviceOneList = entry.getValue();
            DeviceYuLiangExtendV2VO extendV2VO = new DeviceYuLiangExtendV2VO();

            Double sum = 0.0;
            for (DeviceInfo deviceInfo : deviceOneList) {

                String monitorValue = deviceValueMap.get(deviceInfo.getDeviceThirdId());

                Double oneDouble = 0.0;
                if (Objects.nonNull(monitorValue)) {
                    oneDouble = Double.valueOf(monitorValue);
                }

                sum += oneDouble;
            }

            double avg = sum / deviceOneList.size();
            String format = df.format(avg);

            if (CollectionUtils.isNotEmpty(deviceOneList)) {
                DeviceInfo oneDevice = deviceOneList.get(0);
                extendV2VO.setLat(oneDevice.getLat());
                extendV2VO.setLon(oneDevice.getLon());
            }

            extendV2VO.setRainFallTime(currentDate);
            extendV2VO.setDeviceStreet(streetName);
            extendV2VO.setRainFallAvg(format + "mm/h");
            extendV2VO.setRainFallAvgWithOutUnit(format);

            streetList.add(extendV2VO);
        }

        return deviceYuLiangStreetAllV2VO;
    }

    @Override
    public DeviceYuLiangStreetAllV2VO getWaterExtendByHour(Integer hour) {
        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByYuLiang("雨量");

        DeviceYuLiangStreetAllV2VO deviceYuLiangStreetAllV2VO = new DeviceYuLiangStreetAllV2VO();
        List<DeviceYuLiangExtendV2VO> streetList = Lists.newArrayList();
        deviceYuLiangStreetAllV2VO.setStreetList(streetList);

        String currentDate = DateUtils.formatDateSecond(DateUtils.getNowDate());

        String rainFallAvg = deviceValueMapper.getAvgRainFall(hour);
        if (StringUtils.isBlank(rainFallAvg)) {
            rainFallAvg = "0";
        }

        String rainFallAvgScale = df.format(Double.parseDouble(rainFallAvg));

        deviceYuLiangStreetAllV2VO.setRainFallTime(currentDate);
        deviceYuLiangStreetAllV2VO.setRainFallAvg(rainFallAvgScale + "mm/h");
        deviceYuLiangStreetAllV2VO.setRainFallAvgWithOutUnit(rainFallAvgScale);
        deviceYuLiangStreetAllV2VO.setLon("118.60164");
        deviceYuLiangStreetAllV2VO.setLat("28.617417");
        deviceYuLiangStreetAllV2VO.setDeviceStreet("江山市");

        List<DeviceValue> deviceValueList = deviceValueMapper.listRainFallInHour();

        Map<String, String> deviceValueMap = deviceValueList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getDeviceThirdId()))
                .collect(Collectors.toMap(DeviceValue::getDeviceThirdId, DeviceValue::getMonitorValue));

        Map<String, List<DeviceInfo>> deviceInfoMap = deviceInfoList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getDeviceStreet()))
                .collect(Collectors.groupingBy(DeviceInfo::getDeviceStreet));

        for (Map.Entry<String, List<DeviceInfo>> entry : deviceInfoMap.entrySet()) {
            String streetName = entry.getKey();
            List<DeviceInfo> deviceOneList = entry.getValue();
            DeviceYuLiangExtendV2VO extendV2VO = new DeviceYuLiangExtendV2VO();

            Double sum = 0.0;
            for (DeviceInfo deviceInfo : deviceOneList) {

                String monitorValue = deviceValueMap.get(deviceInfo.getDeviceThirdId());

                Double oneDouble = 0.0;
                if (Objects.nonNull(monitorValue)) {
                    oneDouble = Double.valueOf(monitorValue);
                }

                sum += oneDouble;
            }

            double avg = sum / deviceOneList.size();
            String format = df.format(avg);

            if (CollectionUtils.isNotEmpty(deviceOneList)) {
                DeviceInfo oneDevice = deviceOneList.get(0);
                extendV2VO.setLat(oneDevice.getLat());
                extendV2VO.setLon(oneDevice.getLon());
            }

            extendV2VO.setRainFallTime(currentDate);
            extendV2VO.setDeviceStreet(streetName);
            extendV2VO.setRainFallAvg(format + "mm/h");
            extendV2VO.setRainFallAvgWithOutUnit(format);

            streetList.add(extendV2VO);
        }

        return deviceYuLiangStreetAllV2VO;
    }

    @Override
    public List<BusinessDateDoubleCountVO> getWaterExtendLine(String deviceStreet) {
        List<BusinessDateDoubleCountVO> list = Lists.newArrayList();

        TimeRangeLocalDate beforeOneHour = DateUtils.getBeforeOneHourAnd15Minutes();
        List<TimeRangeLocalDate> splitTimeRange = DateUtils.getSplitTimeRange(beforeOneHour);
        for (TimeRangeLocalDate timeRangeLocalDate : splitTimeRange) {
            Date startTimeDate = DateUtils.localDateTime2Date(timeRangeLocalDate.getStartTime());
            Date endTimeDate = DateUtils.localDateTime2Date(timeRangeLocalDate.getEndTime());

            list.add(new BusinessDateDoubleCountVO(endTimeDate, 0.00,
                    startTimeDate, endTimeDate));
        }

        List<DeviceInfo> deviceInfoList = Lists.newArrayList();
        if ("江山市".equals(deviceStreet)) {
            deviceInfoList = deviceInfoMapper.listByDeviceTypeAndDeviceStreet(null, "雨量");
        } else {
            deviceInfoList = deviceInfoMapper.listByDeviceTypeAndDeviceStreet(deviceStreet, "雨量");
        }

        List<String> deviceThirdIdList = deviceInfoList.stream()
                .map(DeviceInfo::getDeviceThirdId)
                .collect(Collectors.toList());

        List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceThirdIdAndMonitorItemInTimeRange(deviceThirdIdList,
                "小时降雨量", beforeOneHour.getStartTime(), beforeOneHour.getEndTime());

        for (BusinessDateDoubleCountVO businessDateDoubleCountVO : list) {
            Double totalValue = 0.00;
            Integer sum = 0;
            for (DeviceValue deviceValue : deviceValueList) {
                if (deviceValue.getMonitorTime().after(businessDateDoubleCountVO.getStartTime())
                    && deviceValue.getMonitorTime().before(businessDateDoubleCountVO.getEndTime())) {
                    totalValue += Double.parseDouble(deviceValue.getMonitorValue());
                    sum += 1;
                }
            }

            if (sum > 0) {
                String format = df.format(totalValue / sum);
                businessDateDoubleCountVO.setValue(Double.parseDouble(format));
            }
        }

        return list;
    }

    @Override
    public Map getIrsRainFallOneHour(Integer type) {
        Map<String, Object> res = new HashMap();
        if (type == 1) {
            res = irsWeatherClient.getJiangShanRainFallOneHour();
        } else {
            List<Map<String, Object>> resultList = Lists.newArrayList();
            DeviceYuLiangStreetAllV2VO waterExtendV2 = getWaterExtendByHour(1);

            for (DeviceYuLiangExtendV2VO extendV2VO : waterExtendV2.getStreetList()) {
                Map<String, Object> oneRain = new HashMap<>();
                oneRain.put("x", extendV2VO.getLon());
                oneRain.put("y", extendV2VO.getLat());
                oneRain.put("v1", extendV2VO.getRainFallAvgWithOutUnit());
                oneRain.put("name", extendV2VO.getDeviceStreet());

                resultList.add(oneRain);
            }

            res.put("data", resultList);
        }

        return res;
    }

    @Override
    public Map getIrsRainFallThreeHour(Integer type) {
        Map<String, Object> res = new HashMap();
        if (type == 1) {
            res = irsWeatherClient.getJiangShanRainFallThreeHour();
        } else {
            Map<String, Object> points = new HashMap<>();
            List<Map<String, Object>> resultList = Lists.newArrayList();
            points.put("features", resultList);
            res.put("points", points);

            // 分别获取当前小时、前1小时、前2小时的平均降雨量
            CompletableFuture<DeviceYuLiangStreetAllV2VO> join0 = CompletableFuture.supplyAsync(() -> {
                DeviceYuLiangStreetAllV2VO hour0 = getWaterExtendByHour(1);
                return hour0;
            });
            CompletableFuture<DeviceYuLiangStreetAllV2VO> join1 = CompletableFuture.supplyAsync(() -> {
                DeviceYuLiangStreetAllV2VO hour1 = getWaterExtendLineV2ForHourOffset(1);
                return hour1;
            });
            CompletableFuture<DeviceYuLiangStreetAllV2VO> join2 = CompletableFuture.supplyAsync(() -> {
                DeviceYuLiangStreetAllV2VO hour2 = getWaterExtendLineV2ForHourOffset(2);
                return hour2;
            });

            DeviceYuLiangStreetAllV2VO hour0 = join0.join();
            DeviceYuLiangStreetAllV2VO hour1 = join1.join();
            DeviceYuLiangStreetAllV2VO hour2 = join2.join();

            // 以当前小时的街道为主，累加三个小时的平均值
            for (DeviceYuLiangExtendV2VO extendV2VO : hour0.getStreetList()) {
                String street = extendV2VO.getDeviceStreet();
                double sum = 0.0;
                try {
                    sum += Double.parseDouble(extendV2VO.getRainFallAvgWithOutUnit());
                } catch (Exception ignore) {}
                // 查找前1小时、前2小时的同街道
                if (hour1 != null && hour1.getStreetList() != null) {
                    for (DeviceYuLiangExtendV2VO v1 : hour1.getStreetList()) {
                        if (street.equals(v1.getDeviceStreet())) {
                            try {
                                sum += Double.parseDouble(v1.getRainFallAvgWithOutUnit());
                            } catch (Exception ignore) {}
                            break;
                        }
                    }
                }
                if (hour2 != null && hour2.getStreetList() != null) {
                    for (DeviceYuLiangExtendV2VO v2 : hour2.getStreetList()) {
                        if (street.equals(v2.getDeviceStreet())) {
                            try {
                                sum += Double.parseDouble(v2.getRainFallAvgWithOutUnit());
                            } catch (Exception ignore) {}
                            break;
                        }
                    }
                }
                Map<String, Object> oneRain = new HashMap<>();
                Map<String, Object> geometry = new HashMap<>();
                geometry.put("coordinates", Lists.newArrayList(Double.valueOf(extendV2VO.getLon()), Double.valueOf(extendV2VO.getLat())));
                geometry.put("type", "Point");
                oneRain.put("geometry", geometry);
                oneRain.put("type", "Feature");

                Map<String, Object> properties = new HashMap<>();

                String format = df.format(sum);

                properties.put("v", Double.parseDouble(format));
                properties.put("name", extendV2VO.getDeviceStreet());

                oneRain.put("properties", properties);

                resultList.add(oneRain);
            }
        }
        return res;
    }

    // 新增一个辅助方法，获取指定小时偏移的降雨量（1=前1小时，2=前2小时）
    private DeviceYuLiangStreetAllV2VO getWaterExtendLineV2ForHourOffset(int hourOffset) {
        // 1. 计算目标小时的起止时间（带分秒）
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.HOUR_OF_DAY, -hourOffset);
        Date endTime = cal.getTime();
        cal.add(Calendar.HOUR_OF_DAY, -1);
        Date startTime = cal.getTime();
        java.time.LocalDateTime startLdt = startTime.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
        java.time.LocalDateTime endLdt = endTime.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();

        // 2. 获取所有雨量设备
        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByYuLiang("雨量");
        if (deviceInfoList == null || deviceInfoList.isEmpty()) {
            return null;
        }
        List<String> deviceThirdIdList = deviceInfoList.stream()
                .map(DeviceInfo::getDeviceThirdId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        // 3. 查询该小时所有设备的降雨量数据
        List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceThirdIdAndMonitorItemInTimeRange(
                deviceThirdIdList, "小时降雨量", startLdt, endLdt);
        Map<String, List<DeviceValue>> deviceValueMap = deviceValueList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getDeviceThirdId()))
                .collect(Collectors.groupingBy(DeviceValue::getDeviceThirdId));

        // 4. 按街道分组，计算平均值
        Map<String, List<DeviceInfo>> deviceInfoMap = deviceInfoList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getDeviceStreet()))
                .collect(Collectors.groupingBy(DeviceInfo::getDeviceStreet));

        DeviceYuLiangStreetAllV2VO result = new DeviceYuLiangStreetAllV2VO();
        List<DeviceYuLiangExtendV2VO> streetList = Lists.newArrayList();
        result.setStreetList(streetList);
        String currentDate = DateUtils.formatDateSecond(startTime);

        String rainFallAvg = deviceValueMapper.getAvgRainFallByTime(startLdt, endLdt);
        if (StringUtils.isBlank(rainFallAvg)) {
            rainFallAvg = "0";
        }

        String rainFallAvgScale = df.format(Double.parseDouble(rainFallAvg));

        result.setRainFallTime(currentDate);
        result.setRainFallAvg(rainFallAvgScale + "mm/h");
        result.setLon("118.60164");
        result.setLat("28.617417");
        result.setDeviceStreet("江山市");

        for (Map.Entry<String, List<DeviceInfo>> entry : deviceInfoMap.entrySet()) {
            String streetName = entry.getKey();
            List<DeviceInfo> deviceOneList = entry.getValue();
            DeviceYuLiangExtendV2VO extendV2VO = new DeviceYuLiangExtendV2VO();
            Double sum = 0.0;
            int count = 0;
            for (DeviceInfo deviceInfo : deviceOneList) {
                List<DeviceValue> values = deviceValueMap.get(deviceInfo.getDeviceThirdId());
                if (values != null && !values.isEmpty()) {
                    for (DeviceValue v : values) {
                        try {
                            sum += Double.parseDouble(v.getMonitorValue());
                            count++;
                        } catch (Exception ignore) {}
                    }
                }
            }
            double avg = (count > 0) ? sum / count : 0.0;
            String format = df.format(avg);
            if (!deviceOneList.isEmpty()) {
                DeviceInfo oneDevice = deviceOneList.get(0);
                extendV2VO.setLat(oneDevice.getLat());
                extendV2VO.setLon(oneDevice.getLon());
            }
            extendV2VO.setRainFallTime(currentDate);
            extendV2VO.setDeviceStreet(streetName);
            extendV2VO.setRainFallAvg(format + "mm/h");
            extendV2VO.setRainFallAvgWithOutUnit(format);
            streetList.add(extendV2VO);
        }
        return result;
    }

    @Override
    public Map getIrsWeatherWarning() {
        Map res = irsWeatherClient.getQiXiangWarning();

        return res;
    }

    @Override
    public List<DeviceInfo> pageDeviceAndData(String deviceType, String scene, Long monitorId, Integer warningFlag, Integer pageNum, Integer pageSize) {
        //特殊处理，以下类型不区分场景
        List<String> yuLiangList = Lists.newArrayList("翻斗雨量计", "雨量站", "投入式水位计", "雷达水位计");
        if (yuLiangList.contains(deviceType)) {
            scene = null;
        }

        List<String> warningThirdIdList = warningEventMapper.listAllAlarmDevice();
        List<String> includeList = Lists.newArrayList();
        List<String> excludeList = Lists.newArrayList();

        if (Objects.nonNull(warningFlag)) {
            if (warningFlag == 1) {
                includeList = warningThirdIdList;
            } else {
                excludeList = warningThirdIdList;
            }
        }

        PageUtils.startPage();

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByDeviceTypeAndSceneAndMonitorId(deviceType, scene,
                monitorId, true, includeList, excludeList);
        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
            List<String> deviceThirdIdList = deviceInfoList.stream()
                    .map(DeviceInfo::getDeviceThirdId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(deviceThirdIdList)) {
                return deviceInfoList;
            }

            List<WarningInfo> warningInfoList = warningInfoMapper.listByDeviceThird(deviceThirdIdList);

            Map<String, List<WarningInfo>> warningMap = warningInfoList.stream()
                    .filter(warningInfo -> StringUtils.isNotBlank(warningInfo.getDeviceThirdId()))
                    .collect(Collectors.groupingBy(WarningInfo::getDeviceThirdId));

            List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceThirdIdList(deviceThirdIdList, 1);

            Map<String, List<DeviceValue>> deviceMap = deviceValueList.stream()
                    .filter(deviceValue -> StringUtils.isNotBlank(deviceValue.getDeviceThirdId()))
                    .collect(Collectors.groupingBy(DeviceValue::getDeviceThirdId));

            for (DeviceInfo deviceInfo : deviceInfoList) {
                List<DeviceValue> deviceValueOneList = deviceMap.get(deviceInfo.getDeviceThirdId());

                if (CollectionUtils.isNotEmpty(deviceValueOneList)) {
                    if ("雨量站".equals(deviceInfo.getDeviceType())
                            || "翻斗雨量计".equals(deviceInfo.getDeviceType())) {
                        deviceValueOneList.sort(Comparator.comparing(DeviceValue::getMonitorItem));
                    }

                    if ("燃气阀井监测仪".equals(deviceInfo.getDeviceType())) {
                        deviceValueOneList.sort(Comparator.comparing(DeviceValue::getMonitorItem, Comparator.reverseOrder()));
                    }
                }

                deviceInfo.setValueList(deviceValueOneList);

                List<WarningInfo> warningInfoOneList = warningMap.get(deviceInfo.getDeviceThirdId());
                if (CollectionUtils.isNotEmpty(warningInfoOneList)) {
                    deviceInfo.setWarningFlag(true);
                }
            }

            List<Long> deviceIdList = deviceInfoList.stream()
                    .map(DeviceInfo::getId)
                    .collect(Collectors.toList());

            List<BusinessFile> fileList = businessFileMapper.listByBusinessIdListAndType(deviceIdList, FileTypeEnum.TYPE_06.getCode());
            if (CollectionUtils.isNotEmpty(fileList)) {
                Map<Long, List<BusinessFile>> fileMap = fileList.stream()
                        .collect(Collectors.groupingBy(BusinessFile::getBusinessId));

                for (DeviceInfo deviceInfo : deviceInfoList) {
                    List<BusinessFile> fileListByDeviceId = fileMap.get(deviceInfo.getId());
                    if (CollectionUtils.isNotEmpty(fileListByDeviceId)) {
                        deviceInfo.setFileList(fileListByDeviceId);
                    }
                }
            }
        }


        return deviceInfoList;
    }

    @Override
    public DeviceYuLiangStreetAllV2VO getWaterExtendV3(Integer type) {
        if (Objects.isNull(type)) {
            type = 2;
        }

        DeviceYuLiangStreetAllV2VO deviceYuLiangStreetAllV2VO = new DeviceYuLiangStreetAllV2VO();
        Date currentTime = new Date();
        String currentTimeStr = DateUtils.formatDateSecond(currentTime);
        List<DeviceYuLiangExtendV2VO> streetList = Lists.newArrayList();
        deviceYuLiangStreetAllV2VO.setStreetList(streetList);

        Map<String, Object> rainFallMap = new HashMap();
        if (type == 1) {
            rainFallMap = irsWeatherClient.getJiangShanRainFallOneHour();
        } else {
            List<Map<String, Object>> resultList = Lists.newArrayList();
            DeviceYuLiangStreetAllV2VO waterExtendV2 = getWaterExtendByHour(1);

            for (DeviceYuLiangExtendV2VO extendV2VO : waterExtendV2.getStreetList()) {
                Map<String, Object> oneRain = new HashMap<>();
                oneRain.put("x", extendV2VO.getLon());
                oneRain.put("y", extendV2VO.getLat());
                oneRain.put("v1", extendV2VO.getRainFallAvgWithOutUnit());
                oneRain.put("name", extendV2VO.getDeviceStreet());

                resultList.add(oneRain);
            }

            rainFallMap.put("data", resultList);
        }

        List<JiangshanRainfall> jiangshanRainfallList = JSON.parseArray(JSON.toJSONString(rainFallMap.get("data")), JiangshanRainfall.class);

        double avgJiangShan = 0.0;
        double totalJiangShan = 0.0;
        for (JiangshanRainfall jiangshanRainfall : jiangshanRainfallList) {
            jiangshanRainfall.setMonitorTime(currentTime);

            double value = Double.parseDouble(jiangshanRainfall.getV1());

            totalJiangShan += value;

            DeviceYuLiangExtendV2VO deviceYuLiangExtendV2VO = new DeviceYuLiangExtendV2VO();
            deviceYuLiangExtendV2VO.setDeviceStreet(jiangshanRainfall.getName());
            deviceYuLiangExtendV2VO.setLon(jiangshanRainfall.getX());
            deviceYuLiangExtendV2VO.setLat(jiangshanRainfall.getY());
            deviceYuLiangExtendV2VO.setRainFallAvg(jiangshanRainfall.getV1());
            deviceYuLiangExtendV2VO.setRainFallTime(currentTimeStr);
            streetList.add(deviceYuLiangExtendV2VO);
        }

        avgJiangShan = totalJiangShan / jiangshanRainfallList.size();

        deviceYuLiangStreetAllV2VO.setDeviceStreet("江山市");
        deviceYuLiangStreetAllV2VO.setRainFallTime(currentTimeStr);
        deviceYuLiangStreetAllV2VO.setRainFallAvg(df.format(avgJiangShan));
        deviceYuLiangStreetAllV2VO.setLon("118.60164");
        deviceYuLiangStreetAllV2VO.setLat("28.617417");

        return deviceYuLiangStreetAllV2VO;
    }

    @Override
    public List<BusinessDateDoubleCountVO> getWaterExtendLineV2(String deviceStreet, Integer hour) {
        List<BusinessDateDoubleCountVO> resultList = Lists.newArrayList();

        List<JiangshanRainfall> jiangshanRainfallList = jiangshanRainfallMapper.listByNameAndHour(deviceStreet, hour);
        if (CollectionUtils.isNotEmpty(jiangshanRainfallList)) {
            for (JiangshanRainfall jiangshanRainfall : jiangshanRainfallList) {
                BusinessDateDoubleCountVO businessDateDoubleCountVO = new BusinessDateDoubleCountVO();
                businessDateDoubleCountVO.setTime(jiangshanRainfall.getMonitorTime());
                businessDateDoubleCountVO.setValue(Double.parseDouble(jiangshanRainfall.getV1()));

                resultList.add(businessDateDoubleCountVO);
            }
        }

        return resultList;
    }

    @Override
    public Map getIrsRainFallByHour(Integer hour) {
        Map<String, Object> res = new HashMap();

        List<JiangshanRainfall> resultList = Lists.newArrayList();

        List<JiangshanRainfall> jiangshanRainfallList = jiangshanRainfallMapper.listSelfByTime(hour);

        Map<String, List<JiangshanRainfall>> map = jiangshanRainfallList.stream()
                .collect(Collectors.groupingBy(JiangshanRainfall::getName));

        for (Map.Entry<String, List<JiangshanRainfall>> entry : map.entrySet()) {
            List<JiangshanRainfall> valueList = entry.getValue();

            JiangshanRainfall jiangshanRainfall = valueList.get(0);

            Double valueSum = valueList.stream()
                    .map(valueOne -> Double.parseDouble(valueOne.getV1()))
                    .reduce(0.0, Double::sum);

            jiangshanRainfall.setV1(df.format(valueSum));

            resultList.add(jiangshanRainfall);
        }

        res.put("data", resultList);

        return res;
    }

    private void fillShenyaValue(List<DeviceInfo> resultList) {
        List<DeviceInfo> shenyaList = resultList.stream()
                .filter(deviceInfo -> "振弦式渗压计".equals(deviceInfo.getDeviceType()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(shenyaList)) {
            return;
        }

        DeviceInfo deviceInfo = shenyaList.get(0);
        String monitorName = deviceInfo.getMonitorName();

        DeviceMonitorShuiku deviceMonitorShuiku = deviceMonitorShuikuMapper.getByMonitorName(monitorName);

        if (Objects.nonNull(deviceMonitorShuiku)) {
            for (DeviceInfo oneDeviceInfo : shenyaList) {
                DeviceValue deviceValue = new DeviceValue();
                deviceValue.setMonitorValue(deviceMonitorShuiku.getF19());
                deviceValue.setMonitorUnit("m");
                deviceValue.setMonitorItem("警戒水位");
                deviceValue.setMonitorTime(new Date());

                List<DeviceValue> valueList = oneDeviceInfo.getValueList();
                if (CollectionUtils.isNotEmpty(valueList)) {
                    valueList.add(deviceValue);
                } else {
                    oneDeviceInfo.setValueList(Lists.newArrayList(deviceValue));
                }
            }
        }
    }

    private void fillBaiyiCount(Long monitorId, List<DeviceInfo> resultList) {
        List<DeviceInfo> baiyiList = deviceInfoMapper.listByDeviceTypeOrMonitorId("白蚁安全观测装置", monitorId, null);
        if (CollectionUtils.isEmpty(baiyiList)) {
            return;
        }

        DeviceInfo baiyiDevice = baiyiList.get(0);
        resultList.add(baiyiDevice);
        baiyiDevice.setDeviceName("白蚁安全观测装置");

        List<DeviceValue> deviceValueList = Lists.newArrayList();
        DeviceValue inlineValue = new DeviceValue();
        inlineValue.setMonitorItem("离线数量");

        long inlineCount = baiyiList.stream()
                .filter(deviceInfo -> deviceInfo.getDeviceStatus() == 0)
                .count();
        inlineValue.setMonitorValue(inlineCount + "");
        inlineValue.setMonitorUnit("个");
        inlineValue.setMonitorTime(new Date());
        deviceValueList.add(inlineValue);

        DeviceValue totalValue = new DeviceValue();
        totalValue.setMonitorItem("总数");
        totalValue.setMonitorValue(baiyiList.size() + "");
        totalValue.setMonitorUnit("个");
        totalValue.setMonitorTime(new Date());
        deviceValueList.add(totalValue);

        baiyiDevice.setValueList(deviceValueList);
    }

    private WaterLayerVO createWaterLayerDetail(String bdyz, DeviceMonitor deviceMonitor, List<String> deviceTypeList, List<String> monitorItemList) {
        WaterLayerVO resultVO = new WaterLayerVO();

        //获取阈值
        resultVO.setThreshold(bdyz);
        resultVO.setId(deviceMonitor.getId());
        resultVO.setMonitorName(deviceMonitor.getMonitorName());
        resultVO.setMonitorType(deviceMonitor.getMonitorType());
        resultVO.setNewType(deviceMonitor.getNewType());

        deviceTypeList.add("视频监控");

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByMonitorIdsAndType(Lists.newArrayList(deviceMonitor.getId()), deviceTypeList);

        Map<Long, List<DeviceInfo>> deviceInfoMap = deviceInfoList.stream()
                .collect(Collectors.groupingBy(DeviceInfo::getMonitorId));

        List<DeviceInfo> deviceOneList = deviceInfoMap.get(deviceMonitor.getId());
        if (CollectionUtils.isNotEmpty(deviceOneList)) {
            List<DeviceInfo> videoList = deviceOneList.stream()
                    .filter(deviceInfo -> "视频监控".equals(deviceInfo.getDeviceType()))
                    .collect(Collectors.toList());
            resultVO.setVideoList(videoList);

            List<DeviceInfo> oneList = deviceOneList.stream()
                    .filter(deviceInfo -> !"视频监控".equals(deviceInfo.getDeviceType()))
                    .filter(deviceInfo -> deviceTypeList.contains(deviceInfo.getDeviceType()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(oneList)) {
                DeviceInfo deviceInfo = oneList.get(0);
                resultVO.setDeviceInfo(deviceInfo);
                //获取最新设备数据
                List<String> deviceThirdIdList = oneList.stream()
                        .map(DeviceInfo::getDeviceThirdId)
                        .collect(Collectors.toList());

                List<DeviceValue> lastList = deviceValueMapper.listByDeviceThirdIdList(deviceThirdIdList, 7);
                resultVO.setDeviceValueList(lastList);

                //计算蓄水率
                if (CollectionUtils.isNotEmpty(resultVO.getDeviceValueList()) && Objects.nonNull(resultVO.getThreshold())) {
                    Optional<DeviceValue> value = resultVO.getDeviceValueList().stream()
                            .filter(deviceValue -> monitorItemList.contains(deviceValue.getMonitorItem()))
                            .findFirst();
                    if (value.isPresent()) {
                        String monitorValue = value.get().getMonitorValue();
                        if (StringUtils.isNotBlank(monitorValue)) {
                            String result = PercentUtils.calculatePercentage(monitorValue, resultVO.getThreshold());
                            resultVO.setWaterStorageRate(result);
                        }
                    }
                }
                //获取预警
                WarningEvent warningEvent = warningEventMapper.getUnCompleteEventByThirdId(deviceInfo.getDeviceThirdId());
                if (Objects.nonNull(warningEvent)) {
                    resultVO.setWarningLevel(warningEvent.getWarningLevel());
                    resultVO.setWarningDetail(warningEvent.getWarningDetail());
                }
            }
        }

        return resultVO;
    }

    private JSONObject sendHkApi(String url, Map<String, Object> paramMap) {
        /**
         * https://ip:port/artemis/api/resource/v1/org/orgList
         * 通过查阅AI Cloud开放平台文档或网关门户的文档可以看到获取组织列表的接口定义,该接口为POST请求的Rest接口, 入参为JSON字符串，接口协议为https。
         * ArtemisHttpUtil工具类提供了doPostStringArtemis调用POST请求的方法，入参可传JSON字符串, 请阅读开发指南了解方法入参，没有的参数可传null
         */
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(ARTEMIS_HOST); // 代理API网关nginx服务器ip端口
        config.setAppKey(ARTEMIS_APPKEY);  // 秘钥appkey
        config.setAppSecret(ARTEMIS_APPSECRET);// 秘钥appSecret
//        final String getCamsApi = ARTEMIS_PATH + "/api/video/v1/cameras/previewURLs";
//        Map<String, String> paramMap = new HashMap<String, String>();// post请求Form表单参数
//        paramMap.put("cameraIndexCode", deviceThirdId);
//        paramMap.put("protocol", protocol);
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", url);
            }
        };

        String response = null;
        try {
            log.info("sendHkApi, url:{}, request:{}", url, body);

            response = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json;charset=utf-8");

            log.info("sendHkApi, url:{}, response:{}", url, response);

            if (StringUtils.isNotBlank(response)) {
                JSONObject jsonObject = JSON.parseObject(response);

                return jsonObject;
            }
        } catch (Exception e) {
            log.info("sendHkApi, url:{}, error:{}", url, e.toString());
        }

        return null;
    }


    private String callPostApi(String deviceThirdId, String protocol) {
        String ARTEMIS_PATH = "/artemis";

        /**
         * https://ip:port/artemis/api/resource/v1/org/orgList
         * 通过查阅AI Cloud开放平台文档或网关门户的文档可以看到获取组织列表的接口定义,该接口为POST请求的Rest接口, 入参为JSON字符串，接口协议为https。
         * ArtemisHttpUtil工具类提供了doPostStringArtemis调用POST请求的方法，入参可传JSON字符串, 请阅读开发指南了解方法入参，没有的参数可传null
         */
        ArtemisConfig config = new ArtemisConfig();
        config.setHost("*************:1443"); // 代理API网关nginx服务器ip端口
        config.setAppKey("28285666");  // 秘钥appkey
        config.setAppSecret("C5WAACEkmZRRVyONgTND");// 秘钥appSecret
        final String getCamsApi = ARTEMIS_PATH + "/api/video/v1/cameras/previewURLs";
        Map<String, String> paramMap = new HashMap<String, String>();// post请求Form表单参数
        paramMap.put("cameraIndexCode", deviceThirdId);
        paramMap.put("protocol", protocol);
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };

        String response = null;
        try {
            response = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");

            log.info("调用视频监控url, response:{}", response);

            if (StringUtils.isNotBlank(response)) {
                JSONObject jsonObject = JSON.parseObject(response);

                Map<String, String> dataMap = (Map<String, String>) jsonObject.get("data");

                return dataMap.get("url");
            }
        } catch (Exception e) {
            log.info("调用视频监控url失败, error:{}", e.toString());
        }

        return null;
    }

    @Override
    public void saveStreet(List<DeviceStreetVO> deviceStreetVOList) {
        List<DeviceStreet> list = Lists.newArrayList();

        for (DeviceStreetVO deviceStreetVO : deviceStreetVOList) {
            DeviceStreet deviceStreet = new DeviceStreet();
            deviceStreet.setId(deviceStreetVO.getId());
            deviceStreet.setLabel(deviceStreetVO.getLabel());
            deviceStreet.setName(deviceStreetVO.getName());

            list.add(deviceStreet);

            for (DeviceStreetVO child : deviceStreetVO.getChildren()) {
                DeviceStreet childStreet = new DeviceStreet();
                childStreet.setId(child.getId());
                childStreet.setLabel(child.getLabel());
                childStreet.setName(child.getName());
                childStreet.setParentId(deviceStreetVO.getId());

                list.add(childStreet);
            }
        }

        deviceStreetMapper.batchInsert(list);
    }

    @Override
    public void saveStreetGeometry(List<DeviceStreetGeometry> list) {
        for (DeviceStreetGeometry deviceStreetGeometry : list) {
            StringBuilder polygon = new StringBuilder("POLYGON((");

            DeviceStreet deviceStreet = new DeviceStreet();

            DeviceStreetGeoDetailVO geometry = deviceStreetGeometry.getGeometry();
            List<List<List<Double>>> coordinates = geometry.getCoordinates();
            List<List<Double>> lists = coordinates.get(0);
            for (int i = 0; i < lists.size(); i++) {
                List<Double> doubleList = lists.get(i);
                for (Double aDouble : doubleList) {
                    polygon.append(aDouble);
                    polygon.append(" ");
                }

                if (i < lists.size() - 1) {
                    polygon.append(",");
                } else {
                    polygon.append("))");
                }
            }

            deviceStreet.setGeometry(polygon.toString());

            Map<String, Object> properties = deviceStreetGeometry.getProperties();
            deviceStreet.setBsm(properties.get("BSM") + "");
            deviceStreet.setYsdm(properties.get("YSDM") + "");
            deviceStreet.setZldwdm(properties.get("XZQDM") + "");
            deviceStreet.setZldwmc(properties.get("XZQMC") + "");
            deviceStreet.setDcmj(properties.get("DCMJ") + "");
            deviceStreet.setJsmj(properties.get("JSMJ") + "");
            deviceStreet.setMssm(properties.get("MSSM") + "");
            deviceStreet.setHdmc(properties.get("HDMC") + "");
            deviceStreet.setBz(properties.get("BZ") + "");
            deviceStreet.setShapeLength(properties.get("SHAPE_Length") + "");
            deviceStreet.setShapeArea(properties.get("SHAPE_Area") + "");
            deviceStreet.setLabel(deviceStreet.getZldwmc());

            deviceStreetMapper.updateByName(deviceStreet);
        }
    }

    @Override
    public List<TDisasterAvoidSite> listAllDisasterAvoid() {
        List<TDisasterAvoidSite> siteList = disasterAvoidSiteMapper.listAllDisasterAvoid();

        return siteList;
    }

    @Override
    public List<EmergencyMaterial> listEmergencyMaterial(String street, String village) {
        List<EmergencyMaterial> materialList =
                emergencyMaterialMapper.listByStreetAndVillage(street, village);

        return materialList;
    }

    @Override
    public String flushFxffq(Map<String, List<Map<String, Object>>> params) {
        List<Map<String, Object>> featureList = params.get("features");

        List<DeviceMonitorFxffq> resultList = Lists.newArrayList();
        for (Map<String, Object> map : featureList) {
            DeviceMonitorFxffq properties = JSON.parseObject(JSON.toJSONString(map.get("properties")), DeviceMonitorFxffq.class);
            resultList.add(properties);
        }

        deviceMonitorFxffqMapper.batchInsert(resultList);

        return "";
    }

    @Override
    public Map<String, Object> getDeviceMonitor(String deviceType, Long monitorId) {
        Map<String, Object> resultMap = new HashMap<>();

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByDeviceTypeOrMonitorId(deviceType, monitorId, null);
        if (CollectionUtils.isEmpty(deviceInfoList)) {
            resultMap.put("totalCount", "0");
            resultMap.put("onlineCount", "0");
            resultMap.put("inlineCount", "0");
            resultMap.put("onlinePercent", "0%");
            resultMap.put("inlinePercent", "0%");
            resultMap.put("onlineList", Lists.newArrayList());
            resultMap.put("inlineList", Lists.newArrayList());

            return resultMap;
        }

        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
            for (DeviceInfo deviceInfo : deviceInfoList) {
                if (Objects.isNull(deviceInfo.getDeviceStatus())) {
                    deviceInfo.setDeviceStatus(1);
                }
            }
        }

        List<DeviceInfo> onlineList = deviceInfoList.stream()
                .filter(deviceInfo -> deviceInfo.getDeviceStatus() == 1)
                .collect(Collectors.toList());

        int totalCount = deviceInfoList.size();
        int onlineCount = onlineList.size();
        int inlineCount = totalCount - onlineCount;

        double onlineRate = (double) onlineCount / totalCount;
        double inlineRate = (double) inlineCount / totalCount;

        DecimalFormat df = new DecimalFormat("0.00%");

        resultMap.put("totalCount", totalCount + "");
        resultMap.put("onlineCount", onlineCount + "");
        resultMap.put("inlineCount", inlineCount + "");
        resultMap.put("onlinePercent", df.format(onlineRate));
        resultMap.put("inlinePercent", df.format(inlineRate));
        resultMap.put("onlineList", onlineList);

        List<DeviceInfo> inlineList = deviceInfoList.stream()
                .filter(deviceInfo -> deviceInfo.getDeviceStatus() == 0)
                .collect(Collectors.toList());
        resultMap.put("inlineList", inlineList);

        return resultMap;
    }

    @Override
    public List<String> getDeviceAllType() {
        List<String> deviceTypeList = deviceInfoMapper.listAllDeviceType();

        return deviceTypeList;
    }

    @Override
    public List<DeviceMonitor> getPointByMonitorType(String monitorType) {
        return deviceMonitorMapper.listByType(Lists.newArrayList(monitorType));
    }

    @Override
    public List<DeviceInfo> exportDevice(String deviceType, Long monitorId) {
        return deviceInfoMapper.listInlineByDeviceType(deviceType, monitorId);
    }

    private WarningDiagnosisVO convert2Diagnosis(WarningEvent event, List<WarningEventDepartment> departmentList) {
        WarningDiagnosisVO warningDiagnosisVO = new WarningDiagnosisVO();
        warningDiagnosisVO.setId(event.getId());
        warningDiagnosisVO.setWarningId(event.getWarningId());
        warningDiagnosisVO.setMonitorName(event.getEventAddress());
        warningDiagnosisVO.setWarningLevel(event.getWarningLevel());
        warningDiagnosisVO.setWarningReason(event.getWarningDetail());
        warningDiagnosisVO.setWarningTime(event.getWarningTime());
        warningDiagnosisVO.setWarningType(event.getEventType());
        warningDiagnosisVO.setWarningName(event.getEventName());
        warningDiagnosisVO.setWarningAddress(event.getEventAddress());
        warningDiagnosisVO.setStatus(event.getStatus());

        warningDiagnosisVO.setLat(event.getLat());
        warningDiagnosisVO.setLon(event.getLon());

        if (CollectionUtils.isNotEmpty(departmentList)) {
            Map<Integer, List<WarningEventDepartment>> departmentMap = departmentList.stream()
                    .collect(Collectors.groupingBy(WarningEventDepartment::getEventStatus));

            if (StringUtils.isBlank(event.getEventThirdId())) {
                //预警
                List<WarningEventDepartment> departmentOneList = departmentMap.get(WarningEventStepEnum.STATUS_01.getCode());

                if (CollectionUtils.isNotEmpty(departmentOneList)) {
                    Optional<WarningEventDepartment> departmentOptional = departmentOneList.stream()
                            .filter(department -> department.getMainFlag() == 1)
                            .findFirst();

                    if (departmentOptional.isPresent()) {
                        WarningEventDepartment warningEventDepartment = departmentOptional.get();

                        warningDiagnosisVO.setDepartmentId(warningEventDepartment.getDepartmentId());
                        warningDiagnosisVO.setDepartmentName(warningEventDepartment.getDepartmentName());
                    }
                }
            } else {
                //异常
                List<WarningEventDepartment> departmentOneList = departmentMap.get(WarningEventStepEnum.STATUS_01.getCode());

                if (CollectionUtils.isNotEmpty(departmentOneList)) {
                    WarningEventDepartment warningEventDepartment = departmentOneList.get(0);

                    warningDiagnosisVO.setDepartmentId(warningEventDepartment.getDepartmentId());
                    warningDiagnosisVO.setDepartmentName(warningEventDepartment.getDepartmentName());
                }
            }
        }

        return warningDiagnosisVO;
    }

}
