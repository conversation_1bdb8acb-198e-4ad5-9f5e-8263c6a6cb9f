package com.ruoyi.jiangshan.mapper;

import java.util.List;
import com.ruoyi.jiangshan.domain.DeviceMonitorGeometry;

/**
 * 空间范围坐标Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface DeviceMonitorGeometryMapper
{
    /**
     * 查询空间范围坐标
     *
     * @param id 空间范围坐标主键
     * @return 空间范围坐标
     */
    public DeviceMonitorGeometry selectDeviceMonitorGeometryById(Long id);

    /**
     * 查询空间范围坐标列表
     *
     * @param deviceMonitorGeometry 空间范围坐标
     * @return 空间范围坐标集合
     */
    public List<DeviceMonitorGeometry> selectDeviceMonitorGeometryList(DeviceMonitorGeometry deviceMonitorGeometry);

    /**
     * 新增空间范围坐标
     *
     * @param deviceMonitorGeometry 空间范围坐标
     * @return 结果
     */
    public int insertDeviceMonitorGeometry(DeviceMonitorGeometry deviceMonitorGeometry);

    /**
     * 修改空间范围坐标
     *
     * @param deviceMonitorGeometry 空间范围坐标
     * @return 结果
     */
    public int updateDeviceMonitorGeometry(DeviceMonitorGeometry deviceMonitorGeometry);

    /**
     * 删除空间范围坐标
     *
     * @param id 空间范围坐标主键
     * @return 结果
     */
    public int deleteDeviceMonitorGeometryById(Long id);

    /**
     * 批量删除空间范围坐标
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceMonitorGeometryByIds(Long[] ids);

    public void batchInsert(List<DeviceMonitorGeometry> list);

    DeviceMonitorGeometry getByMonitorName(String monitorName);
}
