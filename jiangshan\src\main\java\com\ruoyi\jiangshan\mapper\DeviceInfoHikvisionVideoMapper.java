package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.DeviceInfoHikvisionVideo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeviceInfoHikvisionVideoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeviceInfoHikvisionVideo record);

    int insertSelective(DeviceInfoHikvisionVideo record);

    DeviceInfoHikvisionVideo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeviceInfoHikvisionVideo record);

    int updateByPrimaryKey(DeviceInfoHikvisionVideo record);

    int updateBatch(List<DeviceInfoHikvisionVideo> list);

    int batchInsert(@Param("list") List<DeviceInfoHikvisionVideo> list);

    List<DeviceInfoHikvisionVideo> listAll();

}
