package com.ruoyi.jiangshan.domain;

import lombok.Data;

import java.util.Date;

/**
 * 设备信息-监测项
 */
@Data
public class DeviceInfoItem {
    /**
     * 主键
     */
    private Long id;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 监测项
     */
    private String monitorItem;

    /**
     * 监测项-中文名称
     */
    private String monitorItemEnglish;

    /**
     * 监测单位
     */
    private String monitorUnit;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;
}
