package com.ruoyi.common.utils.zlb;

/**
 * <AUTHOR>
 * @date 2022-03-30 15:24
 */
public interface Constants {

    /**
     * 单点登录 ticketId换token的地址
     */
    //String ACCESS_TOKEN_URL = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220329000007/uc/sso/access_token";
    String ACCESS_TOKEN_URL = "https://ibcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220329000007/uc/sso/access_token";
    /**
     * 单点登录 token获取用户信息地址
     */
    //String GET_USER_INFO_URL = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220329000008/uc/sso/getUserInfo";
    String GET_USER_INFO_URL = "https://ibcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220329000008/uc/sso/getUserInfo";

    /**
     * IRS请求携带的请求头
     */
    String X_BG_HMAC_ACCESS_KEY = "X-BG-HMAC-ACCESS-KEY";
    String X_BG_HMAC_SIGNATURE = "X-BG-HMAC-SIGNATURE";
    String X_BG_HMAC_ALGORITHM = "X-BG-HMAC-ALGORITHM";
    String X_BG_DATE_TIME = "X-BG-DATE-TIME";

    /**
     * IRS签名算法
     */
    String DEFAULT_HMAC_SIGNATURE = "hmac-sha256";

    /**
     * 应用ID
     */
    String APP_ID = "2001832175";
    /**
     * 微信端固定值为weixin
     */
    String WEIXIN_ENDPOINT_TYPE = "weixin";


    /**
     * IRS 申请组件生成的AK
     */
    String IRS_AK = "d1a057159f744cc4a463fe74497ecf78";
    /**
     * IRS 申请组件生成的SK
     */
    String IRS_SK = "70abe14a7f264e53a9b160f8ea950f08";


    String TOKEN_SESSION_KEY = "sessionAccessToken";

    String XIE_TONG_WORD_ZZD = "XIE_TONG_WORD_ZZD";

    String NOTICE_ZZD = "NOTICE_ZZD";

}
