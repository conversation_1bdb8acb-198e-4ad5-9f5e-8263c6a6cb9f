<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningEventDepartmentMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningEventDepartment">
    <!--@mbg.generated-->
    <!--@Table t_warning_event_department-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="event_id" jdbcType="BIGINT" property="eventId" />
    <result column="process_id" jdbcType="BIGINT" property="processId" />
    <result column="event_status" jdbcType="BOOLEAN" property="eventStatus" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="organization_code" jdbcType="VARCHAR" property="organizationCode" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="employee_code" jdbcType="VARCHAR" property="employeeCode" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="main_flag" jdbcType="BOOLEAN" property="mainFlag" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="employee_mobile" jdbcType="VARCHAR" property="employeeMobile" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, event_id, process_id, event_status, department_id, department_name, organization_code,
    account_id, employee_code, employee_name, main_flag, delivery_time, create_time,
    create_by, employee_mobile
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_event_department
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_event_department
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventDepartment" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_department (event_id, process_id, event_status,
      department_id, department_name, organization_code,
      account_id, employee_code, employee_name,
      main_flag, delivery_time, create_time,
      create_by, employee_mobile)
    values (#{eventId,jdbcType=BIGINT}, #{processId,jdbcType=BIGINT}, #{eventStatus,jdbcType=BOOLEAN},
      #{departmentId,jdbcType=BIGINT}, #{departmentName,jdbcType=VARCHAR}, #{organizationCode,jdbcType=VARCHAR},
      #{accountId,jdbcType=VARCHAR}, #{employeeCode,jdbcType=VARCHAR}, #{employeeName,jdbcType=VARCHAR},
      #{mainFlag,jdbcType=BOOLEAN}, #{deliveryTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR}, #{employeeMobile,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventDepartment" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_department
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="eventId != null">
        event_id,
      </if>
      <if test="processId != null">
        process_id,
      </if>
      <if test="eventStatus != null">
        event_status,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="departmentName != null">
        department_name,
      </if>
      <if test="organizationCode != null">
        organization_code,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="employeeCode != null">
        employee_code,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="mainFlag != null">
        main_flag,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="employeeMobile != null">
        employee_mobile,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="eventId != null">
        #{eventId,jdbcType=BIGINT},
      </if>
      <if test="processId != null">
        #{processId,jdbcType=BIGINT},
      </if>
      <if test="eventStatus != null">
        #{eventStatus,jdbcType=BOOLEAN},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="organizationCode != null">
        #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="employeeCode != null">
        #{employeeCode,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="mainFlag != null">
        #{mainFlag,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="employeeMobile != null">
        #{employeeMobile,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningEventDepartment">
    <!--@mbg.generated-->
    update t_warning_event_department
    <set>
      <if test="eventId != null">
        event_id = #{eventId,jdbcType=BIGINT},
      </if>
      <if test="processId != null">
        process_id = #{processId,jdbcType=BIGINT},
      </if>
      <if test="eventStatus != null">
        event_status = #{eventStatus,jdbcType=BOOLEAN},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="departmentName != null">
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="organizationCode != null">
        organization_code = #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="employeeCode != null">
        employee_code = #{employeeCode,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        employee_name = #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="mainFlag != null">
        main_flag = #{mainFlag,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="employeeMobile != null">
        employee_mobile = #{employeeMobile,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningEventDepartment">
    <!--@mbg.generated-->
    update t_warning_event_department
    set event_id = #{eventId,jdbcType=BIGINT},
      process_id = #{processId,jdbcType=BIGINT},
      event_status = #{eventStatus,jdbcType=BOOLEAN},
      department_id = #{departmentId,jdbcType=BIGINT},
      department_name = #{departmentName,jdbcType=VARCHAR},
      organization_code = #{organizationCode,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=VARCHAR},
      employee_code = #{employeeCode,jdbcType=VARCHAR},
      employee_name = #{employeeName,jdbcType=VARCHAR},
      main_flag = #{mainFlag,jdbcType=BOOLEAN},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      employee_mobile = #{employeeMobile,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_event_department
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="event_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="process_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.processId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="event_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.departmentId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="department_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.departmentName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="organization_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.organizationCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="account_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.accountId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="employee_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.employeeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="employee_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.employeeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="main_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.mainFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="delivery_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deliveryTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="employee_mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.employeeMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_department
    (event_id, process_id, event_status, department_id, department_name, organization_code,
      account_id, employee_code, employee_name, main_flag, delivery_time, create_time,
      create_by, employee_mobile)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.eventId,jdbcType=BIGINT}, #{item.processId,jdbcType=BIGINT}, #{item.eventStatus,jdbcType=BOOLEAN},
        #{item.departmentId,jdbcType=BIGINT}, #{item.departmentName,jdbcType=VARCHAR},
        #{item.organizationCode,jdbcType=VARCHAR}, #{item.accountId,jdbcType=VARCHAR},
        #{item.employeeCode,jdbcType=VARCHAR}, #{item.employeeName,jdbcType=VARCHAR}, #{item.mainFlag,jdbcType=BOOLEAN},
        #{item.deliveryTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.createBy,jdbcType=VARCHAR}, #{item.employeeMobile,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="listByEventIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_warning_event_department
    where event_id in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and event_status in (1, 2)
    order by event_status desc, main_flag desc, create_time desc
  </select>

  <update id="addScore">
    update t_warning_department_info
    set score = score + #{score}
    where id = #{departmentId,jdbcType=BIGINT}
  </update>

  <delete id="deleteByEventIdAndStatus">
    delete from t_warning_event_department
    where event_id = #{id,jdbcType=BIGINT} and event_status = #{status,jdbcType=INTEGER}
    </delete>

  <select id="getByEventAndProcessId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_department
    where event_id = #{eventId,jdbcType=BIGINT} and process_id = #{processId}
    order by create_time desc
    limit 1
  </select>

  <select id="listByEventIdAndStatus" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_department
    where event_id = #{eventId,jdbcType=BIGINT} and event_status = #{status,jdbcType=INTEGER}
    order by create_time desc
  </select>

  <select id="getLastUserMobile" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
    </include>
    from t_warning_event_department
    where event_id = #{id,jdbcType=BIGINT}
    and main_flag = 1
    order by create_time desc
    limit 1
  </select>
</mapper>
