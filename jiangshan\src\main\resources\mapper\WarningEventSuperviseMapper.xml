<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningEventSuperviseMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningEventSupervise">
    <!--@mbg.generated-->
    <!--@Table t_warning_event_supervise-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="event_id" jdbcType="BIGINT" property="eventId" />
    <result column="supervise_type" jdbcType="VARCHAR" property="superviseType" />
    <result column="supervise_department_id" jdbcType="BIGINT" property="superviseDepartmentId" />
    <result column="supervise_department_name" jdbcType="VARCHAR" property="superviseDepartmentName" />
    <result column="supervise_user_id" jdbcType="BIGINT" property="superviseUserId" />
    <result column="supervise_user_account_id" jdbcType="VARCHAR" property="superviseUserAccountId" />
    <result column="supervise_user_employee_code" jdbcType="VARCHAR" property="superviseUserEmployeeCode" />
    <result column="supervise_user_name" jdbcType="VARCHAR" property="superviseUserName" />
    <result column="supervise_content" jdbcType="VARCHAR" property="superviseContent" />
    <result column="supervise_push_user_id" jdbcType="BIGINT" property="supervisePushUserId" />
    <result column="supervise_push_user_name" jdbcType="VARCHAR" property="supervisePushUserName" />
    <result column="supervise_account_id" jdbcType="VARCHAR" property="superviseAccountId" />
    <result column="supervice_employee_code" jdbcType="VARCHAR" property="superviceEmployeeCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, event_id, supervise_type, supervise_user_id, supervise_user_account_id, supervise_user_employee_code,
    supervise_user_name, supervise_content, supervise_push_user_id, supervise_push_user_name,
    supervise_account_id, supervice_employee_code, create_time, create_by, supervise_department_id, supervise_department_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_event_supervise
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_event_supervise
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventSupervise" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_supervise (event_id, supervise_type, supervise_user_id,
      supervise_user_account_id, supervise_user_employee_code,
      supervise_user_name, supervise_content, supervise_push_user_id,
      supervise_push_user_name, supervise_account_id,
      supervice_employee_code, create_time, create_by, supervise_department_id, supervise_department_name
      )
    values (#{eventId,jdbcType=BIGINT}, #{superviseType,jdbcType=VARCHAR}, #{superviseUserId,jdbcType=BIGINT},
      #{superviseUserAccountId,jdbcType=VARCHAR}, #{superviseUserEmployeeCode,jdbcType=VARCHAR},
      #{superviseUserName,jdbcType=VARCHAR}, #{superviseContent,jdbcType=VARCHAR}, #{supervisePushUserId,jdbcType=BIGINT},
      #{supervisePushUserName,jdbcType=VARCHAR}, #{superviseAccountId,jdbcType=VARCHAR},
      #{superviceEmployeeCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
      #{superviceDepartmentId,jdbcType=BIGINT}, #{superviceDepartmentName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventSupervise" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_supervise
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="eventId != null">
        event_id,
      </if>
      <if test="superviseType != null">
        supervise_type,
      </if>
      <if test="superviseUserId != null">
        supervise_user_id,
      </if>
      <if test="superviseUserAccountId != null">
        supervise_user_account_id,
      </if>
      <if test="superviseUserEmployeeCode != null">
        supervise_user_employee_code,
      </if>
      <if test="superviseUserName != null">
        supervise_user_name,
      </if>
      <if test="superviseContent != null">
        supervise_content,
      </if>
      <if test="supervisePushUserId != null">
        supervise_push_user_id,
      </if>
      <if test="supervisePushUserName != null">
        supervise_push_user_name,
      </if>
      <if test="superviseAccountId != null">
        supervise_account_id,
      </if>
      <if test="superviceEmployeeCode != null">
        supervice_employee_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="superviceDepartmentId != null">
        supervice_department_id,
      </if>
      <if test="superviceDepartmentName != null">
        supervice_department_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="eventId != null">
        #{eventId,jdbcType=BIGINT},
      </if>
      <if test="superviseType != null">
        #{superviseType,jdbcType=VARCHAR},
      </if>
      <if test="superviseUserId != null">
        #{superviseUserId,jdbcType=BIGINT},
      </if>
      <if test="superviseUserAccountId != null">
        #{superviseUserAccountId,jdbcType=VARCHAR},
      </if>
      <if test="superviseUserEmployeeCode != null">
        #{superviseUserEmployeeCode,jdbcType=VARCHAR},
      </if>
      <if test="superviseUserName != null">
        #{superviseUserName,jdbcType=VARCHAR},
      </if>
      <if test="superviseContent != null">
        #{superviseContent,jdbcType=VARCHAR},
      </if>
      <if test="supervisePushUserId != null">
        #{supervisePushUserId,jdbcType=BIGINT},
      </if>
      <if test="supervisePushUserName != null">
        #{supervisePushUserName,jdbcType=VARCHAR},
      </if>
      <if test="superviseAccountId != null">
        #{superviseAccountId,jdbcType=VARCHAR},
      </if>
      <if test="superviceEmployeeCode != null">
        #{superviceEmployeeCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="superviceDepartmentId != null">
        #{superviceDepartmentId},
      </if>
      <if test="superviceDepartmentName != null">
        #{superviceDepartmentName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningEventSupervise">
    <!--@mbg.generated-->
    update t_warning_event_supervise
    <set>
      <if test="eventId != null">
        event_id = #{eventId,jdbcType=BIGINT},
      </if>
      <if test="superviseType != null">
        supervise_type = #{superviseType,jdbcType=VARCHAR},
      </if>
      <if test="superviseUserId != null">
        supervise_user_id = #{superviseUserId,jdbcType=BIGINT},
      </if>
      <if test="superviseUserAccountId != null">
        supervise_user_account_id = #{superviseUserAccountId,jdbcType=VARCHAR},
      </if>
      <if test="superviseUserEmployeeCode != null">
        supervise_user_employee_code = #{superviseUserEmployeeCode,jdbcType=VARCHAR},
      </if>
      <if test="superviseUserName != null">
        supervise_user_name = #{superviseUserName,jdbcType=VARCHAR},
      </if>
      <if test="superviseContent != null">
        supervise_content = #{superviseContent,jdbcType=VARCHAR},
      </if>
      <if test="supervisePushUserId != null">
        supervise_push_user_id = #{supervisePushUserId,jdbcType=BIGINT},
      </if>
      <if test="supervisePushUserName != null">
        supervise_push_user_name = #{supervisePushUserName,jdbcType=VARCHAR},
      </if>
      <if test="superviseAccountId != null">
        supervise_account_id = #{superviseAccountId,jdbcType=VARCHAR},
      </if>
      <if test="superviceEmployeeCode != null">
        supervice_employee_code = #{superviceEmployeeCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningEventSupervise">
    <!--@mbg.generated-->
    update t_warning_event_supervise
    set event_id = #{eventId,jdbcType=BIGINT},
      supervise_type = #{superviseType,jdbcType=VARCHAR},
      supervise_user_id = #{superviseUserId,jdbcType=BIGINT},
      supervise_user_account_id = #{superviseUserAccountId,jdbcType=VARCHAR},
      supervise_user_employee_code = #{superviseUserEmployeeCode,jdbcType=VARCHAR},
      supervise_user_name = #{superviseUserName,jdbcType=VARCHAR},
      supervise_content = #{superviseContent,jdbcType=VARCHAR},
      supervise_push_user_id = #{supervisePushUserId,jdbcType=BIGINT},
      supervise_push_user_name = #{supervisePushUserName,jdbcType=VARCHAR},
      supervise_account_id = #{superviseAccountId,jdbcType=VARCHAR},
      supervice_employee_code = #{superviceEmployeeCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
    supervise_department_id = #{superviseDepartmentId},
    supervise_department_name = #{superviseDepartmentName}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatchByEventId" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_event_supervise
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="supervise_department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when event_id = #{item.eventId,jdbcType=BIGINT} then #{item.superviseDepartmentId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="supervise_department_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when event_id = #{item.eventId,jdbcType=BIGINT} then #{item.superviseDepartmentName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervise_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when event_id = #{item.eventId,jdbcType=BIGINT} then #{item.superviseUserId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="supervise_user_account_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when event_id = #{item.eventId,jdbcType=BIGINT} then #{item.superviseUserAccountId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervise_user_employee_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when event_id = #{item.eventId,jdbcType=BIGINT} then #{item.superviseUserEmployeeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervise_user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when event_id = #{item.eventId,jdbcType=BIGINT} then #{item.superviseUserName,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where event_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.eventId,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_event_supervise
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="event_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="supervise_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviseType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervise_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviseUserId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="supervise_user_account_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviseUserAccountId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervise_user_employee_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviseUserEmployeeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervise_user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviseUserName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervise_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviseContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervise_push_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.supervisePushUserId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="supervise_push_user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.supervisePushUserName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervise_account_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviseAccountId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supervice_employee_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.superviceEmployeeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_supervise
    (event_id, supervise_type, supervise_user_id, supervise_user_account_id, supervise_user_employee_code,
      supervise_user_name, supervise_content, supervise_push_user_id, supervise_push_user_name,
      supervise_account_id, supervice_employee_code, create_time, create_by, supervise_department_id, supervise_department_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.eventId,jdbcType=BIGINT}, #{item.superviseType,jdbcType=VARCHAR}, #{item.superviseUserId,jdbcType=BIGINT},
        #{item.superviseUserAccountId,jdbcType=VARCHAR}, #{item.superviseUserEmployeeCode,jdbcType=VARCHAR},
        #{item.superviseUserName,jdbcType=VARCHAR}, #{item.superviseContent,jdbcType=VARCHAR},
        #{item.supervisePushUserId,jdbcType=BIGINT}, #{item.supervisePushUserName,jdbcType=VARCHAR},
        #{item.superviseAccountId,jdbcType=VARCHAR}, #{item.superviceEmployeeCode,jdbcType=VARCHAR},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.superviseDepartmentId},
      #{item.superviseDepartmentName})
    </foreach>
  </insert>

  <select id="getByEventId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_warning_event_supervise
    where event_id = #{eventId}
  </select>
</mapper>
