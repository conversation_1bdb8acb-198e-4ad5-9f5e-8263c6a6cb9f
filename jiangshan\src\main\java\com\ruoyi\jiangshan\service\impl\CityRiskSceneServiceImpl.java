package com.ruoyi.jiangshan.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.ruoyi.common.dto.DateDTO;
import com.ruoyi.common.dto.TimeRange;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.framework.web.service.dtalk.UserDtalkService;
import com.ruoyi.jiangshan.constant.CommonConstant;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.enums.WarningCenterReportType;
import com.ruoyi.jiangshan.enums.WarningEventStepEnum;
import com.ruoyi.jiangshan.enums.WarningLevelType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.service.CityRiskSceneService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.vo.*;
import com.ruoyi.jiangshan.vo.cityrisk.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CityRiskSceneServiceImpl implements CityRiskSceneService {

    @Autowired
    private WarningModelMapper warningModelMapper;
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private DeviceValueMapper deviceValueMapper;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private WarningRuleConditionMapper warningRuleConditionMapper;
    @Autowired
    private IWarningEventService warningEventService;
    @Autowired
    private UserDtalkService userDtalkService;

    @Value("${single.url}")
    private String singleUrl;
    @Value("${single.pcUrl}")
    private String singlePcUrl;

    @Override
    public List<WarningModel> getWarningModel() {
        return warningModelMapper.listAll();
    }

    @Override
    public List<CityRiskWarningLevelVO> getWarningAnalysis() {
        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), "年");

        LocalDate nextMonthFirstDay = LocalDate.now().plusMonths(1).withDayOfMonth(1);
        Date date = DateUtils.toDate(nextMonthFirstDay);

        List<TimeRange> rangeList = DateUtils.generateDateRanges(dateDTO.getStartTime(), date, 5);

        List<CityRiskWarningLevelVO> countVOList = Lists.newArrayList();

        List<WarningEvent> warningEventList = warningEventMapper.listByDate(dateDTO.getStartTime(), dateDTO.getEndTime());

        for (TimeRange timeRange : rangeList) {
            CityRiskWarningLevelVO cityRiskWarningLevelVO = new CityRiskWarningLevelVO();
            cityRiskWarningLevelVO.setKey(timeRange.getLabel());
            cityRiskWarningLevelVO.setLevel01Value(0L);
            cityRiskWarningLevelVO.setLevel02Value(0L);
            cityRiskWarningLevelVO.setLevel03Value(0L);
            cityRiskWarningLevelVO.setLevel04Value(0L);
            cityRiskWarningLevelVO.setStartTime(timeRange.getStartTime());
            cityRiskWarningLevelVO.setEndTime(timeRange.getEndTime());

            countVOList.add(cityRiskWarningLevelVO);
        }

        if (CollectionUtils.isNotEmpty(warningEventList)) {
            Map<String, List<WarningEvent>> eventMap = warningEventList.stream()
                    .collect(Collectors.groupingBy(item -> DateUtils.formatYearMonthV2(item.getWarningTime())));

            for (CityRiskWarningLevelVO cityRiskWarningLevelVO : countVOList) {
                List<WarningEvent> eventList = eventMap.get(cityRiskWarningLevelVO.getKey());

                if (CollectionUtils.isEmpty(eventList)) {
                    continue;
                }

                for (WarningEvent warningEvent : eventList) {
                    switch (warningEvent.getWarningLevel()) {
                        case 1:
                            cityRiskWarningLevelVO.setLevel01Value(cityRiskWarningLevelVO.getLevel01Value() + 1);
                            break;
                        case 2:
                            cityRiskWarningLevelVO.setLevel02Value(cityRiskWarningLevelVO.getLevel02Value() + 1);
                            break;
                        case 3:
                            cityRiskWarningLevelVO.setLevel03Value(cityRiskWarningLevelVO.getLevel03Value() + 1);
                            break;
                        case 4:
                            cityRiskWarningLevelVO.setLevel04Value(cityRiskWarningLevelVO.getLevel04Value() + 1);
                            break;
                    }
                }
            }
        }

        return countVOList;
    }

    @Override
    public List<BusinessCountVO> getWarningTop5() {
        List<BusinessCountVO> countVOList = warningEventMapper.countByAddress();

        return countVOList;
    }

    @Override
    public CityRiskScreenVO getStreet(String areaCode) {
        CityRiskScreenVO riskScreenVO = new CityRiskScreenVO();

        List<CityRiskEventVO> eventList = warningEventMapper.listByAreaCode(areaCode);

        if (CollectionUtils.isEmpty(eventList)) {
//            if ("330881121000000014".equals(areaCode)) {
//                riskScreenVO.setWarningLevel(4);
//            } else if ("330881121000000018".equals(areaCode)) {
//                riskScreenVO.setWarningLevel(4);
//            } else if ("330881121000000002".equals(areaCode)) {
//                riskScreenVO.setWarningLevel(1);
//            } else if ("330881121000000016".equals(areaCode)) {
//                riskScreenVO.setWarningLevel(1);
//            } else {
//                // 获取字符串的最后一位字符
//                char lastChar = areaCode.charAt(areaCode.length() - 1);
//
//                // 将字符转换为整数
//                int lastCharInt = Character.getNumericValue(lastChar);
//
//                // 计算余数
//                int remainder = lastCharInt % 4;
//
//                riskScreenVO.setWarningLevel(remainder + 1);
//            }
            riskScreenVO.setWarningLevel(0);

            return riskScreenVO;
        }

        Optional<Integer> min = eventList.stream()
                .map(CityRiskEventVO::getWarningLevel)
                .min(Integer::compareTo);

        riskScreenVO.setWarningLevel(min.orElse(4));

        CityRiskEventVO cityRiskEventVO = eventList.get(0);
        riskScreenVO.setWarningTime(cityRiskEventVO.getWarningTime());
        riskScreenVO.setWarningCount(eventList.size());
//        riskScreenVO.setWarningLevel(cityRiskEventVO.getWarningLevel());

        Map<Integer, List<CityRiskEventVO>> monitorMap = eventList.stream()
                .collect(Collectors.groupingBy(CityRiskEventVO::getWarningLevel));

        List<CityRiskWarningCountVO> countList = Lists.newArrayList();
        Map<Integer, CityRiskWarningCountVO> countMap = new HashMap<>();
        countMap.put(1, new CityRiskWarningCountVO(1, 0, 0));
        countMap.put(2, new CityRiskWarningCountVO(2, 0, 0));
        countMap.put(3, new CityRiskWarningCountVO(3, 0, 0));
        countMap.put(4, new CityRiskWarningCountVO(4, 0, 0));
        countMap.put(5, new CityRiskWarningCountVO(5, 0, 0));

        for (Map.Entry<Integer, List<CityRiskEventVO>> entry : monitorMap.entrySet()) {
            List<CityRiskEventVO> valueList = entry.getValue();

            CityRiskEventVO tmpEvent = valueList.get(0);

            CityRiskWarningCountVO cityRiskWarningCountVO = countMap.get(tmpEvent.getWarningLevel());

            cityRiskWarningCountVO.setWarningCount(valueList.size());

            List<CityRiskEventVO> completeList = valueList.stream()
                    .filter(tmp -> tmp.getStatus().equals(WarningEventStepEnum.STATUS_04.getCode()))
                    .collect(Collectors.toList());

            cityRiskWarningCountVO.setHandleWarningCount(completeList.size());
        }

        riskScreenVO.setWarningIndicator(100L);

        for (Map.Entry<Integer, CityRiskWarningCountVO> entry : countMap.entrySet()) {
            countList.add(entry.getValue());
        }
        riskScreenVO.setCountList(countList);

        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), "近7天");
        List<TimeRange> timeRangeList = DateUtils.generateDateRanges(dateDTO.getStartTime(), dateDTO.getEndTime(),
                WarningCenterReportType.TYPE_02.getCode());

        //初始化
        List<CityRiskLineVO> lineList = Lists.newArrayList();
        for (TimeRange timeRange : timeRangeList) {
            CityRiskLineVO cityRiskLineVO = new CityRiskLineVO();
            cityRiskLineVO.setName(timeRange.getLabel());
            cityRiskLineVO.setStartTime(timeRange.getStartTime());
            cityRiskLineVO.setEndTime(timeRange.getEndTime());

            List<CityRiskStreetResultVO> valueList = Lists.newArrayList();

            valueList.add(new CityRiskStreetResultVO(WarningLevelType.WARNING_LEVEL_1.getCode(), WarningLevelType.WARNING_LEVEL_1.getDesc(), 0L, 0L, 0.00));
            valueList.add(new CityRiskStreetResultVO(WarningLevelType.WARNING_LEVEL_2.getCode(), WarningLevelType.WARNING_LEVEL_2.getDesc(), 0L, 0L, 0.00));
            valueList.add(new CityRiskStreetResultVO(WarningLevelType.WARNING_LEVEL_3.getCode(), WarningLevelType.WARNING_LEVEL_3.getDesc(), 0L, 0L, 0.00));
            valueList.add(new CityRiskStreetResultVO(WarningLevelType.WARNING_LEVEL_4.getCode(), WarningLevelType.WARNING_LEVEL_4.getDesc(), 0L, 0L, 0.00));
            valueList.add(new CityRiskStreetResultVO(WarningLevelType.WARNING_LEVEL_5.getCode(), WarningLevelType.WARNING_LEVEL_5.getDesc(), 0L, 0L, 0.00));

            cityRiskLineVO.setValueList(valueList);

            lineList.add(cityRiskLineVO);
        }

        riskScreenVO.setLineList(lineList);

        for (CityRiskLineVO cityRiskLineVO : lineList) {
            List<CityRiskStreetResultVO> valueList = cityRiskLineVO.getValueList();

            Map<Integer, CityRiskStreetResultVO> valueMap = valueList.stream()
                    .collect(Collectors.toMap(CityRiskStreetResultVO::getWarningLevel, Function.identity()));

            for (CityRiskEventVO eventVO : eventList) {
                if (cityRiskLineVO.getStartTime().before(eventVO.getWarningTime()) &&
                        cityRiskLineVO.getEndTime().after(eventVO.getWarningTime())) {
                    CityRiskStreetResultVO resultVO = valueMap.get(eventVO.getWarningLevel());
                    resultVO.setValue(resultVO.getValue() + 1);
                    if (Objects.equals(eventVO.getStatus(), WarningEventStepEnum.STATUS_04.getCode())) {
                        resultVO.setCompleteValue(resultVO.getCompleteValue() + 1);
                    }
                }
            }

            Long total = 0L;
            for (CityRiskStreetResultVO resultVO : valueList) {
                if (resultVO.getValue() == 0L || resultVO.getCompleteValue() == 0L) {
                    continue;
                }

                double percent = resultVO.getCompleteValue() * 1.0 / resultVO.getValue();

                resultVO.setPercent(percent);

                total += resultVO.getValue();
            }

            cityRiskLineVO.setTotal(total);
        }



        return riskScreenVO;
    }

    @Override
    public List<DeviceDepartmentVO> getDepartmentDevice(List<String> sceneList) {
        List<DeviceDepartmentVO> departmentVOList = Lists.newArrayList();

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listBySceneList(sceneList);
        if (CollectionUtils.isEmpty(deviceInfoList)) {
            return departmentVOList;
        }

        List<String> deviceIdList = deviceInfoList.stream()
                .map(DeviceInfo::getDeviceThirdId)
                .collect(Collectors.toList());

        List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceThirdIdList(deviceIdList, 7);

        Map<Long, List<DeviceValue>> deviceValueMap = deviceValueList.stream()
                .collect(Collectors.groupingBy(DeviceValue::getDeviceId));

        for (DeviceInfo deviceInfo : deviceInfoList) {
            DeviceDepartmentVO deviceDepartmentVO = new DeviceDepartmentVO();
            deviceDepartmentVO.setId(deviceInfo.getId());
            deviceDepartmentVO.setDeviceName(deviceInfo.getDeviceName());
            deviceDepartmentVO.setMonitorId(deviceInfo.getMonitorId());
            deviceDepartmentVO.setMonitorName(deviceInfo.getMonitorName());

            List<DeviceValue> deviceOneValueList = deviceValueMap.get(deviceInfo.getId());
            deviceDepartmentVO.setValueList(deviceOneValueList);
            if (CollectionUtils.isNotEmpty(deviceOneValueList)) {
                String deviceThirdId = deviceOneValueList.get(0).getDeviceThirdId();

                List<String> monitorItemList = deviceOneValueList.stream()
                        .map(DeviceValue::getMonitorItemEnglish)
                        .collect(Collectors.toList());

                List<WarningRuleCondition> conditionList = warningRuleConditionMapper
                        .listByMonitorItemAndDevice(deviceThirdId, monitorItemList);

                Map<String, List<WarningRuleCondition>> conditionMap = conditionList.stream()
                        .collect(Collectors.groupingBy(WarningRuleCondition::getMonitorItem));

                for (DeviceValue deviceValue : deviceOneValueList) {
                    List<WarningRuleCondition> ruleConditionList = conditionMap.get(deviceValue.getMonitorItem());

                    deviceValue.setWarningContent(getWarningContent(ruleConditionList));
                }
            }

            deviceDepartmentVO.setLat(deviceInfo.getLat());
            deviceDepartmentVO.setLon(deviceInfo.getLon());
            deviceDepartmentVO.setDeviceThirdId(deviceInfo.getDeviceThirdId());

            departmentVOList.add(deviceDepartmentVO);
        }

        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), "周");

        List<WarningInfo> warningInfoList = warningInfoMapper
                .listReportLine(sceneList.toArray(new String[sceneList.size()]), dateDTO.getStartTime(), dateDTO.getEndTime(), Lists.newArrayList(1, 2, 3, 4));

        Map<String, List<WarningInfo>> warningMap = warningInfoList.stream()
                .collect(Collectors.groupingBy(WarningInfo::getDeviceThirdId));

        //获取坐标轴x轴
        List<TimeRange> timeRangeList = DateUtils.generateDateRanges(dateDTO.getStartTime(),
                dateDTO.getEndTime(), WarningCenterReportType.TYPE_02.getCode());

        Map<String, List<CitySafetyLineVO>> resultMap = new HashMap<>();
        for (DeviceDepartmentVO deviceDepartmentVO : departmentVOList) {
            List<CitySafetyLineVO> lineList = Lists.newArrayList();

            for (TimeRange timeRange : timeRangeList) {
                CitySafetyLineVO lineVO = new CitySafetyLineVO();
                lineVO.setName(timeRange.getLabel());
                lineVO.setStartTime(timeRange.getStartTime());
                lineVO.setEndTime(timeRange.getEndTime());
                lineList.add(lineVO);
            }

            deviceDepartmentVO.setLineList(lineList);

            resultMap.put(deviceDepartmentVO.getDeviceThirdId(), lineList);
        }

        for (DeviceDepartmentVO deviceDepartmentVO : departmentVOList) {
            List<WarningInfo> warningInfoOneList = warningMap.get(deviceDepartmentVO.getDeviceThirdId());

            if (CollectionUtils.isEmpty(warningInfoOneList)) {
                continue;
            }

            List<CitySafetyLineVO> lineVOList = resultMap.get(deviceDepartmentVO.getDeviceThirdId());
            for (CitySafetyLineVO citySafetyLineVO : lineVOList) {
                for (WarningInfo warningInfo : warningInfoOneList) {
                    if (citySafetyLineVO.getStartTime().getTime() <= warningInfo.getWarningTime().getTime()
                            && citySafetyLineVO.getEndTime().getTime() >= warningInfo.getWarningTime().getTime()) {
                        if (Objects.equals(WarningLevelType.WARNING_LEVEL_1.getCode(), warningInfo.getWarningLevel())) {
                            citySafetyLineVO.setWarningLevel01(citySafetyLineVO.getWarningLevel01() + 1);
                        } else if (Objects.equals(WarningLevelType.WARNING_LEVEL_2.getCode(), warningInfo.getWarningLevel())) {
                            citySafetyLineVO.setWarningLevel02(citySafetyLineVO.getWarningLevel02() + 1);
                        } else if (Objects.equals(WarningLevelType.WARNING_LEVEL_3.getCode(), warningInfo.getWarningLevel())) {
                            citySafetyLineVO.setWarningLevel03(citySafetyLineVO.getWarningLevel03() + 1);
                        } else if (Objects.equals(WarningLevelType.WARNING_LEVEL_4.getCode(), warningInfo.getWarningLevel())) {
                            citySafetyLineVO.setWarningLevel04(citySafetyLineVO.getWarningLevel04() + 1);
                        } else if (Objects.equals(WarningLevelType.WARNING_LEVEL_5.getCode(), warningInfo.getWarningLevel())) {
                            citySafetyLineVO.setWarningLevel05(citySafetyLineVO.getWarningLevel05() + 1);
                        }
                    }
                }
            }
        }

        return departmentVOList;
    }

    @Override
    public List<WarningEvent> getEvent() {
//        PageHelper.startPage(1, 999);
//
//        WarningEventPageVO warningEventPageVO = new WarningEventPageVO();
//        warningEventPageVO.setStatusList(Lists.newArrayList(
//                WarningEventStepEnum.STATUS_01.getCode(),
//                WarningEventStepEnum.STATUS_02.getCode(),
//                WarningEventStepEnum.STATUS_03.getCode(),
//                WarningEventStepEnum.STATUS_04.getCode(),
//                WarningEventStepEnum.STATUS_05.getCode()
//        ));
//
//        warningEventPageVO.setPermissionFlag(0);
//
//        List<WarningEvent> eventList = warningEventService.selectWarningEventList(warningEventPageVO);
//
//        PageInfo<WarningEvent> pageInfo = new PageInfo<>(eventList);

        List<WarningEvent> eventList = warningEventMapper.listAllWarningOneDayInner(null, Lists.newArrayList(1, 2, 3, 4));
        if (CollectionUtils.isEmpty(eventList)) {
            return Lists.newArrayList();
        }

        List<Long> eventIdList = eventList.stream()
                .map(WarningEvent::getId)
                .collect(Collectors.toList());

        warningEventService.fillProcessList(eventList, eventIdList);

        for (WarningEvent warningEvent : eventList) {


            warningEventService.fillProcessLog(warningEvent);
        }

        warningEventService.fillFileList(eventList, eventIdList);

        return eventList;
    }

    @Override
    public void notifyMsg(String accountId) {
        userDtalkService.workNotification(accountId,
                CommonConstant.CITY_RISK_NITIFY_ID + IdUtils.simpleUUID(),
                "您有一条新的关注提醒，请及时处理！", "您有一条新的关注提醒，请及时处理！", singleUrl, singlePcUrl);
    }

    private String getWarningContent(List<WarningRuleCondition> ruleConditionList) {
        if (CollectionUtils.isEmpty(ruleConditionList)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < ruleConditionList.size(); i++) {
            WarningRuleCondition condition = ruleConditionList.get(i);

            sb.append("当");
            sb.append(condition.getMonitorItem());
            sb.append(condition.getRuleCondition());
            sb.append(condition.getMonitorValue());
            sb.append(condition.getMonitorUnit());
            sb.append(",产生");
            sb.append(WarningLevelType.getByCode(condition.getWarningLevel()));

            if (i < ruleConditionList.size() - 1) {
                sb.append(";");
            }
        }


        return sb.toString();
    }

    @Override
    public List<CityRiskSafetyEventVO> getSafety() {
        List<CityRiskSafetyEventVO> resultList = Lists.newArrayList();

        List<WarningEvent> eventList = warningEventMapper.listUnCompleteEvent();

        if (CollectionUtils.isEmpty(eventList)) {
            return Lists.newArrayList();
        }

        List<Long> warningIdList = eventList.stream()
                .map(WarningEvent::getWarningId)
                .collect(Collectors.toList());

        List<WarningInfo> warningInfoList = warningInfoMapper.listByWarningIdList(warningIdList);

        Map<Long, WarningInfo> warningMap = warningInfoList.stream()
                .collect(Collectors.toMap(WarningInfo::getId, Function.identity()));

        List<String> deviceThirdIdList = warningInfoList.stream().map(WarningInfo::getDeviceThirdId)
                .collect(Collectors.toList());

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByDeviceThirdIdList(deviceThirdIdList);

        Map<String, DeviceInfo> deviceInfoMap = deviceInfoList.stream()
                .collect(Collectors.toMap(DeviceInfo::getDeviceThirdId, Function.identity()));

        List<Long> eventIdList = eventList.stream()
                .map(WarningEvent::getId)
                .collect(Collectors.toList());

        warningEventService.fillProcessList(eventList, eventIdList);

        for (WarningEvent warningEvent : eventList) {
            CityRiskSafetyEventVO eventVO = new CityRiskSafetyEventVO();
            eventVO.setEventId(warningEvent.getId());
            eventVO.setDeviceCount(1);
            eventVO.setExpectedImpactRange("23.51");
            eventVO.setExpectedImpactRangeUnit("平方米");
            eventVO.setDeviceCount(1);

            WarningInfo warningInfo = warningMap.get(warningEvent.getWarningId());
            if (Objects.nonNull(warningInfo)) {
                DeviceInfo deviceInfo = deviceInfoMap.get(warningInfo.getDeviceThirdId());

                if (Objects.nonNull(deviceInfo)) {
                    //获取设备数据
                    List<DeviceValue> valueList = deviceValueMapper.listByDeviceThirdId(warningInfo.getDeviceThirdId());

                    deviceInfo.setValueList(valueList);

                    eventVO.setDeviceInfoList(Lists.newArrayList(deviceInfo));
                }
            }

            warningEventService.fillProcessLog(warningEvent);

            eventVO.setLogList(warningEvent.getLogList());

            resultList.add(eventVO);
        }

        return resultList;
    }

    @Override
    public CityRiskAvgLineVO getSafetyLine(String deviceThirdId, String monitorItem) {
        CityRiskAvgLineVO cityRiskAvgLineVO = new CityRiskAvgLineVO();

        // 获取今天的日期
        Calendar today = Calendar.getInstance();
        // 设置时间为0时0分0秒
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);
        today.set(Calendar.MILLISECOND, 0);

        List<CityRiskTimeLineVO> countVOList = Lists.newArrayList();
        cityRiskAvgLineVO.setCountVOList(countVOList);
        Date startTime = today.getTime();
        CityRiskTimeLineVO line01 = new CityRiskTimeLineVO("4:00", 0.0, startTime);
        today.set(Calendar.HOUR_OF_DAY, 4);
        line01.setEndTime(today.getTime());
        countVOList.add(line01);

        CityRiskTimeLineVO line02 = new CityRiskTimeLineVO("8:00", 0.0, today.getTime());
        today.set(Calendar.HOUR_OF_DAY, 8);
        line02.setEndTime(today.getTime());
        countVOList.add(line02);

        CityRiskTimeLineVO line03 = new CityRiskTimeLineVO("12:00", 0.0, today.getTime());
        today.set(Calendar.HOUR_OF_DAY, 12);
        line03.setEndTime(today.getTime());
        countVOList.add(line03);

        CityRiskTimeLineVO line04 = new CityRiskTimeLineVO("16:00", 0.0, today.getTime());
        today.set(Calendar.HOUR_OF_DAY, 16);
        line04.setEndTime(today.getTime());
        countVOList.add(line04);

        CityRiskTimeLineVO line05 = new CityRiskTimeLineVO("20:00", 0.0, today.getTime());
        today.set(Calendar.HOUR_OF_DAY, 20);
        line05.setEndTime(today.getTime());
        countVOList.add(line05);

        CityRiskTimeLineVO line06 = new CityRiskTimeLineVO("24:00", 0.0, today.getTime());
        today.set(Calendar.HOUR_OF_DAY, 23);
        today.set(Calendar.MINUTE, 59);
        today.set(Calendar.SECOND, 59);
        today.set(Calendar.MILLISECOND, 0);

        Date endTime = today.getTime();
        line06.setEndTime(endTime);
        countVOList.add(line06);

        WarningRuleCondition condition =
                warningRuleConditionMapper.getMinConditionByMonitorItem(deviceThirdId, monitorItem);

        if (Objects.nonNull(condition)) {
            cityRiskAvgLineVO.setThreshold(condition.getMonitorValue());
        }

        Long totalCount = deviceValueMapper.getCount(deviceThirdId, monitorItem, startTime, endTime);

        List<DeviceValue> deviceValueList = Lists.newArrayList();
        if (totalCount < 100000) {
            deviceValueList = deviceValueMapper.listByDeviceAndMonitorItem(deviceThirdId, monitorItem,
                    startTime, endTime);
        } else {
            deviceValueList = deviceValueMapper.listByDeviceAndMonitorItemV2(deviceThirdId, monitorItem,
                    startTime, endTime);
        }

        for (DeviceValue deviceValue : deviceValueList) {
            for (CityRiskTimeLineVO cityRiskTimeLineVO : countVOList) {
                if (deviceValue.getMonitorTime().after(cityRiskTimeLineVO.getStartTime())
                        && deviceValue.getMonitorTime().before(cityRiskTimeLineVO.getEndTime())) {
                    List<BigDecimal> valueList = cityRiskTimeLineVO.getValueList();

                    BigDecimal valueDecimal = new BigDecimal(deviceValue.getMonitorValue());

                    if (CollectionUtils.isEmpty(valueList)) {
                        cityRiskTimeLineVO.setValueList(Lists.newArrayList(valueDecimal));
                    } else {
                        cityRiskTimeLineVO.getValueList().add(valueDecimal);
                    }
                }

                if (CollectionUtils.isNotEmpty(cityRiskTimeLineVO.getValueList())) {
                    cityRiskTimeLineVO.setValue(cityRiskTimeLineVO.getValueList().stream()
                            .mapToDouble(BigDecimal::doubleValue).average().getAsDouble());
                }
            }
        }



        return cityRiskAvgLineVO;
    }


}
