package com.ruoyi.jiangshan.service.impl;

import com.google.common.collect.Lists;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.domain.WarningInfo;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.service.IDeviceValueService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.service.WaterloggingService;
import com.ruoyi.jiangshan.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class WaterloggingServiceImpl implements WaterloggingService {
    @Autowired
    private IWarningEventService warningEventService;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private DeviceValueMapper deviceValueMapper;
    @Autowired
    private IDeviceValueService deviceValueService;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;

    private final static String TYPE = "河道";
    private final static String SCENE = MonitorSceneType.SCENE_04.getDesc();
    private static List<String> DEVICE_TYPE_LIST = Lists.newArrayList();

    @Autowired
    private DeviceInfoTypeMapper deviceInfoTypeMapper;
    @PostConstruct
    public void init() {
        DEVICE_TYPE_LIST = deviceInfoTypeMapper.listByScene(SCENE);
    }

    @Override
    public WarningCountVO getSafetyCount(String dateStr) {
        return warningEventService.getSafetyCount(dateStr, SCENE);
    }

    @Override
    public List<FloorCountVO> getFloodCount() {
        List<FloorCountVO> list = Lists.newArrayList();

        List<DeviceMonitor> monitorList = deviceMonitorMapper.listByType(Lists.newArrayList("城市易涝点"));

        if (CollectionUtils.isNotEmpty(monitorList)) {
            for (DeviceMonitor deviceMonitor : monitorList) {
                FloorCountVO floorCountVO = new FloorCountVO();
                floorCountVO.setAddress(deviceMonitor.getRemark());
                floorCountVO.setFloorName(deviceMonitor.getMonitorName());
                floorCountVO.setWarningLevel(null);
                floorCountVO.setRainFall(null);

                list.add(floorCountVO);
            }
        }
//        List<WarningInfoVO> warningInfoList = warningInfoMapper.listByMonitorType(TYPE);
//
//        for (WarningInfoVO warningInfoVO : warningInfoList) {
//            FloorCountVO floorCountVO = new FloorCountVO();
//            floorCountVO.setFloorName(warningInfoVO.getMonitorName());
//            floorCountVO.setWarningLevel(warningInfoVO.getWarningLevel());
//            floorCountVO.setRainFall(warningInfoVO.getWarningData());
//            floorCountVO.setAddress(warningInfoVO.getWarningAddress());
//
//            list.add(floorCountVO);
//        }
        /**
         * 达岭溪-学府街  低  3.0mm  虎山街道城北社区学府街
         * 杨梅溪-江东大道 低 2.0mm  双塔街道赵家村江东大道
         * 封门溪-西山路桥底  低 1.7mm  虎山街道安泰社区西山路桥底
         */
//        list.add(new FloorCountVO("达岭溪-学府街", 4, "3.0mm", "虎山街道城北社区学府街"));
//        list.add(new FloorCountVO("杨梅溪-江东大道", 4, "2.0mm", "双塔街道赵家村江东大道"));
//        list.add(new FloorCountVO("封门溪-西山路桥底", 4, "1.7mm", "虎山街道安泰社区西山路桥底"));

        return list;
    }

    @Override
    public List<WarningInfoVO> getWarning(Integer warningLevel) {
        // TODO: 2024/12/14
        return warningInfoMapper.listByLevelAndScene(warningLevel, SCENE);
    }

    @Override
    public Map<String, List<BusinessCountVO>> getRainfallCount(Long monitorId) {
        Map<String, List<BusinessCountVO>> resultMap = new HashMap<>();

        // TODO: 2024/8/29 修改监测项
        List<BusinessCountVO> resultRainList = deviceValueService.getOneLineByMonitorIdAndItem(monitorId, "日降雨量");
        List<BusinessCountVO> flowList = deviceValueService.getOneLineByMonitorIdAndItem(monitorId, "水位");

        resultMap.put("resultRainList", resultRainList);
        resultMap.put("flowList", flowList);

        return resultMap;
    }

    @Override
    public DeviceOnlineStatusVO getOnlineStatus() {
        DeviceOnlineStatusVO deviceOnlineStatusVO = new DeviceOnlineStatusVO();

        List<BusinessCountVO> countVOList = deviceInfoMapper.countByMonitorSceneAndStatus(SCENE);

        Map<String, Long> countMap = countVOList.stream()
                .collect(Collectors.toMap(BusinessCountVO::getKey, BusinessCountVO::getValue));

        deviceOnlineStatusVO.setOffline(Objects.isNull(countMap.get("0")) ? 0L : countMap.get("0"));
        deviceOnlineStatusVO.setOnline(Objects.isNull(countMap.get("1")) ? 0L : countMap.get("1"));
        deviceOnlineStatusVO.setMaintain(Objects.isNull(countMap.get("2")) ? 0L : countMap.get("2"));
        deviceOnlineStatusVO.setRemove(Objects.isNull(countMap.get("6")) ? 0L : countMap.get("6"));
        deviceOnlineStatusVO.setTotal(deviceOnlineStatusVO.sumTotalOnlineOffline());

        return deviceOnlineStatusVO;
    }
}
