package com.ruoyi.jiangshan.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jiangshan.domain.CitysafetyRule;
import com.ruoyi.jiangshan.service.ICitysafetyRuleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 城市安全体检规则Controller
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@RestController
@RequestMapping("/citysafety/rule")
public class CitysafetyRuleController extends BaseController
{
    @Autowired
    private ICitysafetyRuleService citysafetyRuleService;

    /**
     * 查询报告生成管理列表
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyRule:list')")
    @GetMapping("/list")
    public TableDataInfo list(CitysafetyRule citysafetyRule)
    {
        startPage();
        List<CitysafetyRule> list = citysafetyRuleService.selectCitysafetyRuleList(citysafetyRule);
        return getDataTable(list);
    }

    /**
     * 导出报告生成管理列表
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyRule:export')")
    @Log(title = "报告生成管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CitysafetyRule citysafetyRule)
    {
        List<CitysafetyRule> list = citysafetyRuleService.selectCitysafetyRuleList(citysafetyRule);
        ExcelUtil<CitysafetyRule> util = new ExcelUtil<CitysafetyRule>(CitysafetyRule.class);
        util.exportExcel(response, list, "报告生成管理数据");
    }

    /**
     * 获取报告生成管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyReport:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(citysafetyRuleService.selectCitysafetyRuleById(id));
    }

    /**
     * 新增报告生成管理
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyReport:add')")
    @Log(title = "报告生成管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CitysafetyRule citysafetyRule)
    {
        return toAjax(citysafetyRuleService.insertCitysafetyRule(citysafetyRule));
    }

    /**
     * 修改报告生成管理
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyReport:edit')")
    @Log(title = "报告生成管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CitysafetyRule citysafetyRule)
    {
        return toAjax(citysafetyRuleService.updateCitysafetyRule(citysafetyRule));
    }

    /**
     * 删除报告生成管理
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyReport:remove')")
    @Log(title = "报告生成管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(citysafetyRuleService.deleteCitysafetyRuleByIds(ids));
    }
}
