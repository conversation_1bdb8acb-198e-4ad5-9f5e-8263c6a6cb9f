package com.ruoyi.jiangshan.domain;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
    * 设备规则条件
    */
@Data
public class WarningRuleCondition {
    /**
    * 主键
    */
    private Long id;

    /**
    * 规则id
    */
    private Long ruleId;

    /**
    * 设备id
    */
    private String deviceId;

    private List<Long> deviceIdList;

    /**
    * 设备名称
    */
    private String deviceName;

    /**
    * 设备第三方id
    */
    private String deviceThirdId;

    /**
    * 比较符号条件
    */
    private String ruleCondition;

    /**
    * 监测项
    */
    private String monitorItem;

    /**
     * 监测项英文名称
     */
    private String monitorItemEnglish;

    /**
    * 比较值
    */
    private String monitorValue;

    /**
     * 监测数据单位
     */
    private String monitorUnit;

    /**
    * 告警等级 1-4
    */
    private Integer warningLevel;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 创建人
    */
    private String createBy;

    /**
     * 推送预警的用户id
     */
    private Long warningUserId;

    /**
     * 推送预警的用户名称
     */
    private String warningUserName;
}
