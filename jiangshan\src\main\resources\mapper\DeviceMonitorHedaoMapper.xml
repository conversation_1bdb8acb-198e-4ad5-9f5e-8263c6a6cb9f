<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceMonitorHedaoMapper">

    <resultMap type="DeviceMonitorHedao" id="DeviceMonitorHedaoResult">
        <result property="jcmc"    column="jcmc"    />
        <result property="bdyz"    column="bdyz"    />
    </resultMap>

    <sql id="selectDeviceMonitorHedaoVo">
        select jcmc, bdyz from t_device_monitor_hedao
    </sql>

    <select id="selectDeviceMonitorHedaoList" parameterType="DeviceMonitorHedao" resultMap="DeviceMonitorHedaoResult">
        <include refid="selectDeviceMonitorHedaoVo"/>
        <where>
            <if test="jcmc != null  and jcmc != ''"> and jcmc = #{jcmc}</if>
            <if test="bdyz != null  and bdyz != ''"> and bdyz = #{bdyz}</if>
        </where>
    </select>

    <select id="selectDeviceMonitorHedaoByJcmc" parameterType="String" resultMap="DeviceMonitorHedaoResult">
        <include refid="selectDeviceMonitorHedaoVo"/>
        where jcmc = #{jcmc}
    </select>

    <insert id="insertDeviceMonitorHedao" parameterType="DeviceMonitorHedao">
        insert into t_device_monitor_hedao
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jcmc != null">jcmc,</if>
            <if test="bdyz != null">bdyz,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jcmc != null">#{jcmc},</if>
            <if test="bdyz != null">#{bdyz},</if>
         </trim>
    </insert>

    <update id="updateDeviceMonitorHedao" parameterType="DeviceMonitorHedao">
        update t_device_monitor_hedao
        <trim prefix="SET" suffixOverrides=",">
            <if test="bdyz != null">bdyz = #{bdyz},</if>
        </trim>
        where jcmc = #{jcmc}
    </update>

    <delete id="deleteDeviceMonitorHedaoByJcmc" parameterType="String">
        delete from t_device_monitor_hedao where jcmc = #{jcmc}
    </delete>

    <delete id="deleteDeviceMonitorHedaoByJcmcs" parameterType="String">
        delete from t_device_monitor_hedao where jcmc in
        <foreach item="jcmc" collection="array" open="(" separator="," close=")">
            #{jcmc}
        </foreach>
    </delete>

    <select id="listAll" resultMap="DeviceMonitorHedaoResult">
        <include refid="selectDeviceMonitorHedaoVo">
        </include>

    </select>

    <select id="getByMonitorName" resultMap="DeviceMonitorHedaoResult">
        select * from t_device_monitor_hedao
        where jcmc = #{jcmc}
    </select>
</mapper>
