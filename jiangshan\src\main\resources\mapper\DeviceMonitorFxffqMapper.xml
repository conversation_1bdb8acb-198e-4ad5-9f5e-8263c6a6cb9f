<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceMonitorFxffqMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.DeviceMonitorFxffq">
    <!--@mbg.generated-->
    <!--@Table t_device_monitor_fxffq-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fxqbh" jdbcType="VARCHAR" property="fxqbh" />
    <result column="fxffqmc" jdbcType="VARCHAR" property="fxffqmc" />
    <result column="dcbh" jdbcType="VARCHAR" property="dcbh" />
    <result column="szdq" jdbcType="VARCHAR" property="szdq" />
    <result column="xzc" jdbcType="VARCHAR" property="xzc" />
    <result column="fflx" jdbcType="VARCHAR" property="fflx" />
    <result column="zzt" jdbcType="VARCHAR" property="zzt" />
    <result column="wdx" jdbcType="VARCHAR" property="wdx" />
    <result column="fxdj" jdbcType="VARCHAR" property="fxdj" />
    <result column="syxhs" jdbcType="VARCHAR" property="syxhs" />
    <result column="syxrs" jdbcType="VARCHAR" property="syxrs" />
    <result column="czrk" jdbcType="VARCHAR" property="czrk" />
    <result column="syxcc" jdbcType="VARCHAR" property="syxcc" />
    <result column="xjfgld" jdbcType="VARCHAR" property="xjfgld" />
    <result column="xjfglddh" jdbcType="VARCHAR" property="xjfglddh" />
    <result column="xzfgld" jdbcType="VARCHAR" property="xzfgld" />
    <result column="xzfglddh" jdbcType="VARCHAR" property="xzfglddh" />
    <result column="zrzyszrr" jdbcType="VARCHAR" property="zrzyszrr" />
    <result column="zrzyszrrdh" jdbcType="VARCHAR" property="zrzyszrrdh" />
    <result column="qcqfy" jdbcType="VARCHAR" property="qcqfy" />
    <result column="qcqfydh" jdbcType="VARCHAR" property="qcqfydh" />
    <result column="sjly" jdbcType="VARCHAR" property="sjly" />
    <result column="brbqxmz" jdbcType="VARCHAR" property="brbqxmz" />
    <result column="zlgcxmbh" jdbcType="VARCHAR" property="zlgcxmbh" />
    <result column="jcdbm" jdbcType="VARCHAR" property="jcdbm" />
    <result column="tbr" jdbcType="VARCHAR" property="tbr" />
    <result column="ffqcjsj" jdbcType="VARCHAR" property="ffqcjsj" />
    <result column="tjxzsj" jdbcType="VARCHAR" property="tjxzsj" />
    <result column="hjtgsj" jdbcType="VARCHAR" property="hjtgsj" />
    <result column="yzlx" jdbcType="VARCHAR" property="yzlx" />
    <result column="zztgs" jdbcType="VARCHAR" property="zztgs" />
    <result column="czths" jdbcType="VARCHAR" property="czths" />
    <result column="czthjrs" jdbcType="VARCHAR" property="czthjrs" />
    <result column="level1hhong" jdbcType="VARCHAR" property="level1hhong" />
    <result column="level1hcheng" jdbcType="VARCHAR" property="level1hcheng" />
    <result column="level1hhuang" jdbcType="VARCHAR" property="level1hhuang" />
    <result column="level3hhong" jdbcType="VARCHAR" property="level3hhong" />
    <result column="level3hcheng" jdbcType="VARCHAR" property="level3hcheng" />
    <result column="level3hhuang" jdbcType="VARCHAR" property="level3hhuang" />
    <result column="level6hcheng" jdbcType="VARCHAR" property="level6hcheng" />
    <result column="level6hhuang" jdbcType="VARCHAR" property="level6hhuang" />
    <result column="level12hhong" jdbcType="VARCHAR" property="level12hhong" />
    <result column="level12hcheng" jdbcType="VARCHAR" property="level12hcheng" />
    <result column="level12hhuang" jdbcType="VARCHAR" property="level12hhuang" />
    <result column="level24hhong" jdbcType="VARCHAR" property="level24hhong" />
    <result column="level24hcheng" jdbcType="VARCHAR" property="level24hcheng" />
    <result column="level24hhuang" jdbcType="VARCHAR" property="level24hhuang" />
    <result column="hjyy" jdbcType="VARCHAR" property="hjyy" />
    <result column="hjyy_other" jdbcType="VARCHAR" property="hjyyOther" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, fxqbh, fxffqmc, dcbh, szdq, xzc, fflx, zzt, wdx, fxdj, syxhs, syxrs, czrk, syxcc,
    xjfgld, xjfglddh, xzfgld, xzfglddh, zrzyszrr, zrzyszrrdh, qcqfy, qcqfydh, sjly, brbqxmz,
    zlgcxmbh, jcdbm, tbr, ffqcjsj, tjxzsj, hjtgsj, yzlx, zztgs, czths, czthjrs, level1hhong,
    level1hcheng, level1hhuang, level3hhong, level3hcheng, level3hhuang, level6hcheng,
    level6hhuang, level12hhong, level12hcheng, level12hhuang, level24hhong, level24hcheng,
    level24hhuang, hjyy, hjyy_other
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_device_monitor_fxffq
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_device_monitor_fxffq
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.DeviceMonitorFxffq" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_monitor_fxffq (fxqbh, fxffqmc, dcbh,
      szdq, xzc, fflx, zzt,
      wdx, fxdj, syxhs, syxrs,
      czrk, syxcc, xjfgld,
      xjfglddh, xzfgld, xzfglddh,
      zrzyszrr, zrzyszrrdh, qcqfy,
      qcqfydh, sjly, brbqxmz,
      zlgcxmbh, jcdbm, tbr,
      ffqcjsj, tjxzsj, hjtgsj,
      yzlx, zztgs, czths,
      czthjrs, level1hhong, level1hcheng,
      level1hhuang, level3hhong, level3hcheng,
      level3hhuang, level6hcheng, level6hhuang,
      level12hhong, level12hcheng, level12hhuang,
      level24hhong, level24hcheng, level24hhuang,
      hjyy, hjyy_other)
    values (#{fxqbh,jdbcType=VARCHAR}, #{fxffqmc,jdbcType=VARCHAR}, #{dcbh,jdbcType=VARCHAR},
      #{szdq,jdbcType=VARCHAR}, #{xzc,jdbcType=VARCHAR}, #{fflx,jdbcType=VARCHAR}, #{zzt,jdbcType=VARCHAR},
      #{wdx,jdbcType=VARCHAR}, #{fxdj,jdbcType=VARCHAR}, #{syxhs,jdbcType=VARCHAR}, #{syxrs,jdbcType=VARCHAR},
      #{czrk,jdbcType=VARCHAR}, #{syxcc,jdbcType=VARCHAR}, #{xjfgld,jdbcType=VARCHAR},
      #{xjfglddh,jdbcType=VARCHAR}, #{xzfgld,jdbcType=VARCHAR}, #{xzfglddh,jdbcType=VARCHAR},
      #{zrzyszrr,jdbcType=VARCHAR}, #{zrzyszrrdh,jdbcType=VARCHAR}, #{qcqfy,jdbcType=VARCHAR},
      #{qcqfydh,jdbcType=VARCHAR}, #{sjly,jdbcType=VARCHAR}, #{brbqxmz,jdbcType=VARCHAR},
      #{zlgcxmbh,jdbcType=VARCHAR}, #{jcdbm,jdbcType=VARCHAR}, #{tbr,jdbcType=VARCHAR},
      #{ffqcjsj,jdbcType=VARCHAR}, #{tjxzsj,jdbcType=VARCHAR}, #{hjtgsj,jdbcType=VARCHAR},
      #{yzlx,jdbcType=VARCHAR}, #{zztgs,jdbcType=VARCHAR}, #{czths,jdbcType=VARCHAR},
      #{czthjrs,jdbcType=VARCHAR}, #{level1hhong,jdbcType=VARCHAR}, #{level1hcheng,jdbcType=VARCHAR},
      #{level1hhuang,jdbcType=VARCHAR}, #{level3hhong,jdbcType=VARCHAR}, #{level3hcheng,jdbcType=VARCHAR},
      #{level3hhuang,jdbcType=VARCHAR}, #{level6hcheng,jdbcType=VARCHAR}, #{level6hhuang,jdbcType=VARCHAR},
      #{level12hhong,jdbcType=VARCHAR}, #{level12hcheng,jdbcType=VARCHAR}, #{level12hhuang,jdbcType=VARCHAR},
      #{level24hhong,jdbcType=VARCHAR}, #{level24hcheng,jdbcType=VARCHAR}, #{level24hhuang,jdbcType=VARCHAR},
      #{hjyy,jdbcType=VARCHAR}, #{hjyyOther,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.DeviceMonitorFxffq" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_monitor_fxffq
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fxqbh != null">
        fxqbh,
      </if>
      <if test="fxffqmc != null">
        fxffqmc,
      </if>
      <if test="dcbh != null">
        dcbh,
      </if>
      <if test="szdq != null">
        szdq,
      </if>
      <if test="xzc != null">
        xzc,
      </if>
      <if test="fflx != null">
        fflx,
      </if>
      <if test="zzt != null">
        zzt,
      </if>
      <if test="wdx != null">
        wdx,
      </if>
      <if test="fxdj != null">
        fxdj,
      </if>
      <if test="syxhs != null">
        syxhs,
      </if>
      <if test="syxrs != null">
        syxrs,
      </if>
      <if test="czrk != null">
        czrk,
      </if>
      <if test="syxcc != null">
        syxcc,
      </if>
      <if test="xjfgld != null">
        xjfgld,
      </if>
      <if test="xjfglddh != null">
        xjfglddh,
      </if>
      <if test="xzfgld != null">
        xzfgld,
      </if>
      <if test="xzfglddh != null">
        xzfglddh,
      </if>
      <if test="zrzyszrr != null">
        zrzyszrr,
      </if>
      <if test="zrzyszrrdh != null">
        zrzyszrrdh,
      </if>
      <if test="qcqfy != null">
        qcqfy,
      </if>
      <if test="qcqfydh != null">
        qcqfydh,
      </if>
      <if test="sjly != null">
        sjly,
      </if>
      <if test="brbqxmz != null">
        brbqxmz,
      </if>
      <if test="zlgcxmbh != null">
        zlgcxmbh,
      </if>
      <if test="jcdbm != null">
        jcdbm,
      </if>
      <if test="tbr != null">
        tbr,
      </if>
      <if test="ffqcjsj != null">
        ffqcjsj,
      </if>
      <if test="tjxzsj != null">
        tjxzsj,
      </if>
      <if test="hjtgsj != null">
        hjtgsj,
      </if>
      <if test="yzlx != null">
        yzlx,
      </if>
      <if test="zztgs != null">
        zztgs,
      </if>
      <if test="czths != null">
        czths,
      </if>
      <if test="czthjrs != null">
        czthjrs,
      </if>
      <if test="level1hhong != null">
        level1hhong,
      </if>
      <if test="level1hcheng != null">
        level1hcheng,
      </if>
      <if test="level1hhuang != null">
        level1hhuang,
      </if>
      <if test="level3hhong != null">
        level3hhong,
      </if>
      <if test="level3hcheng != null">
        level3hcheng,
      </if>
      <if test="level3hhuang != null">
        level3hhuang,
      </if>
      <if test="level6hcheng != null">
        level6hcheng,
      </if>
      <if test="level6hhuang != null">
        level6hhuang,
      </if>
      <if test="level12hhong != null">
        level12hhong,
      </if>
      <if test="level12hcheng != null">
        level12hcheng,
      </if>
      <if test="level12hhuang != null">
        level12hhuang,
      </if>
      <if test="level24hhong != null">
        level24hhong,
      </if>
      <if test="level24hcheng != null">
        level24hcheng,
      </if>
      <if test="level24hhuang != null">
        level24hhuang,
      </if>
      <if test="hjyy != null">
        hjyy,
      </if>
      <if test="hjyyOther != null">
        hjyy_other,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fxqbh != null">
        #{fxqbh,jdbcType=VARCHAR},
      </if>
      <if test="fxffqmc != null">
        #{fxffqmc,jdbcType=VARCHAR},
      </if>
      <if test="dcbh != null">
        #{dcbh,jdbcType=VARCHAR},
      </if>
      <if test="szdq != null">
        #{szdq,jdbcType=VARCHAR},
      </if>
      <if test="xzc != null">
        #{xzc,jdbcType=VARCHAR},
      </if>
      <if test="fflx != null">
        #{fflx,jdbcType=VARCHAR},
      </if>
      <if test="zzt != null">
        #{zzt,jdbcType=VARCHAR},
      </if>
      <if test="wdx != null">
        #{wdx,jdbcType=VARCHAR},
      </if>
      <if test="fxdj != null">
        #{fxdj,jdbcType=VARCHAR},
      </if>
      <if test="syxhs != null">
        #{syxhs,jdbcType=VARCHAR},
      </if>
      <if test="syxrs != null">
        #{syxrs,jdbcType=VARCHAR},
      </if>
      <if test="czrk != null">
        #{czrk,jdbcType=VARCHAR},
      </if>
      <if test="syxcc != null">
        #{syxcc,jdbcType=VARCHAR},
      </if>
      <if test="xjfgld != null">
        #{xjfgld,jdbcType=VARCHAR},
      </if>
      <if test="xjfglddh != null">
        #{xjfglddh,jdbcType=VARCHAR},
      </if>
      <if test="xzfgld != null">
        #{xzfgld,jdbcType=VARCHAR},
      </if>
      <if test="xzfglddh != null">
        #{xzfglddh,jdbcType=VARCHAR},
      </if>
      <if test="zrzyszrr != null">
        #{zrzyszrr,jdbcType=VARCHAR},
      </if>
      <if test="zrzyszrrdh != null">
        #{zrzyszrrdh,jdbcType=VARCHAR},
      </if>
      <if test="qcqfy != null">
        #{qcqfy,jdbcType=VARCHAR},
      </if>
      <if test="qcqfydh != null">
        #{qcqfydh,jdbcType=VARCHAR},
      </if>
      <if test="sjly != null">
        #{sjly,jdbcType=VARCHAR},
      </if>
      <if test="brbqxmz != null">
        #{brbqxmz,jdbcType=VARCHAR},
      </if>
      <if test="zlgcxmbh != null">
        #{zlgcxmbh,jdbcType=VARCHAR},
      </if>
      <if test="jcdbm != null">
        #{jcdbm,jdbcType=VARCHAR},
      </if>
      <if test="tbr != null">
        #{tbr,jdbcType=VARCHAR},
      </if>
      <if test="ffqcjsj != null">
        #{ffqcjsj,jdbcType=VARCHAR},
      </if>
      <if test="tjxzsj != null">
        #{tjxzsj,jdbcType=VARCHAR},
      </if>
      <if test="hjtgsj != null">
        #{hjtgsj,jdbcType=VARCHAR},
      </if>
      <if test="yzlx != null">
        #{yzlx,jdbcType=VARCHAR},
      </if>
      <if test="zztgs != null">
        #{zztgs,jdbcType=VARCHAR},
      </if>
      <if test="czths != null">
        #{czths,jdbcType=VARCHAR},
      </if>
      <if test="czthjrs != null">
        #{czthjrs,jdbcType=VARCHAR},
      </if>
      <if test="level1hhong != null">
        #{level1hhong,jdbcType=VARCHAR},
      </if>
      <if test="level1hcheng != null">
        #{level1hcheng,jdbcType=VARCHAR},
      </if>
      <if test="level1hhuang != null">
        #{level1hhuang,jdbcType=VARCHAR},
      </if>
      <if test="level3hhong != null">
        #{level3hhong,jdbcType=VARCHAR},
      </if>
      <if test="level3hcheng != null">
        #{level3hcheng,jdbcType=VARCHAR},
      </if>
      <if test="level3hhuang != null">
        #{level3hhuang,jdbcType=VARCHAR},
      </if>
      <if test="level6hcheng != null">
        #{level6hcheng,jdbcType=VARCHAR},
      </if>
      <if test="level6hhuang != null">
        #{level6hhuang,jdbcType=VARCHAR},
      </if>
      <if test="level12hhong != null">
        #{level12hhong,jdbcType=VARCHAR},
      </if>
      <if test="level12hcheng != null">
        #{level12hcheng,jdbcType=VARCHAR},
      </if>
      <if test="level12hhuang != null">
        #{level12hhuang,jdbcType=VARCHAR},
      </if>
      <if test="level24hhong != null">
        #{level24hhong,jdbcType=VARCHAR},
      </if>
      <if test="level24hcheng != null">
        #{level24hcheng,jdbcType=VARCHAR},
      </if>
      <if test="level24hhuang != null">
        #{level24hhuang,jdbcType=VARCHAR},
      </if>
      <if test="hjyy != null">
        #{hjyy,jdbcType=VARCHAR},
      </if>
      <if test="hjyyOther != null">
        #{hjyyOther,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.DeviceMonitorFxffq">
    <!--@mbg.generated-->
    update t_device_monitor_fxffq
    <set>
      <if test="fxqbh != null">
        fxqbh = #{fxqbh,jdbcType=VARCHAR},
      </if>
      <if test="fxffqmc != null">
        fxffqmc = #{fxffqmc,jdbcType=VARCHAR},
      </if>
      <if test="dcbh != null">
        dcbh = #{dcbh,jdbcType=VARCHAR},
      </if>
      <if test="szdq != null">
        szdq = #{szdq,jdbcType=VARCHAR},
      </if>
      <if test="xzc != null">
        xzc = #{xzc,jdbcType=VARCHAR},
      </if>
      <if test="fflx != null">
        fflx = #{fflx,jdbcType=VARCHAR},
      </if>
      <if test="zzt != null">
        zzt = #{zzt,jdbcType=VARCHAR},
      </if>
      <if test="wdx != null">
        wdx = #{wdx,jdbcType=VARCHAR},
      </if>
      <if test="fxdj != null">
        fxdj = #{fxdj,jdbcType=VARCHAR},
      </if>
      <if test="syxhs != null">
        syxhs = #{syxhs,jdbcType=VARCHAR},
      </if>
      <if test="syxrs != null">
        syxrs = #{syxrs,jdbcType=VARCHAR},
      </if>
      <if test="czrk != null">
        czrk = #{czrk,jdbcType=VARCHAR},
      </if>
      <if test="syxcc != null">
        syxcc = #{syxcc,jdbcType=VARCHAR},
      </if>
      <if test="xjfgld != null">
        xjfgld = #{xjfgld,jdbcType=VARCHAR},
      </if>
      <if test="xjfglddh != null">
        xjfglddh = #{xjfglddh,jdbcType=VARCHAR},
      </if>
      <if test="xzfgld != null">
        xzfgld = #{xzfgld,jdbcType=VARCHAR},
      </if>
      <if test="xzfglddh != null">
        xzfglddh = #{xzfglddh,jdbcType=VARCHAR},
      </if>
      <if test="zrzyszrr != null">
        zrzyszrr = #{zrzyszrr,jdbcType=VARCHAR},
      </if>
      <if test="zrzyszrrdh != null">
        zrzyszrrdh = #{zrzyszrrdh,jdbcType=VARCHAR},
      </if>
      <if test="qcqfy != null">
        qcqfy = #{qcqfy,jdbcType=VARCHAR},
      </if>
      <if test="qcqfydh != null">
        qcqfydh = #{qcqfydh,jdbcType=VARCHAR},
      </if>
      <if test="sjly != null">
        sjly = #{sjly,jdbcType=VARCHAR},
      </if>
      <if test="brbqxmz != null">
        brbqxmz = #{brbqxmz,jdbcType=VARCHAR},
      </if>
      <if test="zlgcxmbh != null">
        zlgcxmbh = #{zlgcxmbh,jdbcType=VARCHAR},
      </if>
      <if test="jcdbm != null">
        jcdbm = #{jcdbm,jdbcType=VARCHAR},
      </if>
      <if test="tbr != null">
        tbr = #{tbr,jdbcType=VARCHAR},
      </if>
      <if test="ffqcjsj != null">
        ffqcjsj = #{ffqcjsj,jdbcType=VARCHAR},
      </if>
      <if test="tjxzsj != null">
        tjxzsj = #{tjxzsj,jdbcType=VARCHAR},
      </if>
      <if test="hjtgsj != null">
        hjtgsj = #{hjtgsj,jdbcType=VARCHAR},
      </if>
      <if test="yzlx != null">
        yzlx = #{yzlx,jdbcType=VARCHAR},
      </if>
      <if test="zztgs != null">
        zztgs = #{zztgs,jdbcType=VARCHAR},
      </if>
      <if test="czths != null">
        czths = #{czths,jdbcType=VARCHAR},
      </if>
      <if test="czthjrs != null">
        czthjrs = #{czthjrs,jdbcType=VARCHAR},
      </if>
      <if test="level1hhong != null">
        level1hhong = #{level1hhong,jdbcType=VARCHAR},
      </if>
      <if test="level1hcheng != null">
        level1hcheng = #{level1hcheng,jdbcType=VARCHAR},
      </if>
      <if test="level1hhuang != null">
        level1hhuang = #{level1hhuang,jdbcType=VARCHAR},
      </if>
      <if test="level3hhong != null">
        level3hhong = #{level3hhong,jdbcType=VARCHAR},
      </if>
      <if test="level3hcheng != null">
        level3hcheng = #{level3hcheng,jdbcType=VARCHAR},
      </if>
      <if test="level3hhuang != null">
        level3hhuang = #{level3hhuang,jdbcType=VARCHAR},
      </if>
      <if test="level6hcheng != null">
        level6hcheng = #{level6hcheng,jdbcType=VARCHAR},
      </if>
      <if test="level6hhuang != null">
        level6hhuang = #{level6hhuang,jdbcType=VARCHAR},
      </if>
      <if test="level12hhong != null">
        level12hhong = #{level12hhong,jdbcType=VARCHAR},
      </if>
      <if test="level12hcheng != null">
        level12hcheng = #{level12hcheng,jdbcType=VARCHAR},
      </if>
      <if test="level12hhuang != null">
        level12hhuang = #{level12hhuang,jdbcType=VARCHAR},
      </if>
      <if test="level24hhong != null">
        level24hhong = #{level24hhong,jdbcType=VARCHAR},
      </if>
      <if test="level24hcheng != null">
        level24hcheng = #{level24hcheng,jdbcType=VARCHAR},
      </if>
      <if test="level24hhuang != null">
        level24hhuang = #{level24hhuang,jdbcType=VARCHAR},
      </if>
      <if test="hjyy != null">
        hjyy = #{hjyy,jdbcType=VARCHAR},
      </if>
      <if test="hjyyOther != null">
        hjyy_other = #{hjyyOther,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.DeviceMonitorFxffq">
    <!--@mbg.generated-->
    update t_device_monitor_fxffq
    set fxqbh = #{fxqbh,jdbcType=VARCHAR},
      fxffqmc = #{fxffqmc,jdbcType=VARCHAR},
      dcbh = #{dcbh,jdbcType=VARCHAR},
      szdq = #{szdq,jdbcType=VARCHAR},
      xzc = #{xzc,jdbcType=VARCHAR},
      fflx = #{fflx,jdbcType=VARCHAR},
      zzt = #{zzt,jdbcType=VARCHAR},
      wdx = #{wdx,jdbcType=VARCHAR},
      fxdj = #{fxdj,jdbcType=VARCHAR},
      syxhs = #{syxhs,jdbcType=VARCHAR},
      syxrs = #{syxrs,jdbcType=VARCHAR},
      czrk = #{czrk,jdbcType=VARCHAR},
      syxcc = #{syxcc,jdbcType=VARCHAR},
      xjfgld = #{xjfgld,jdbcType=VARCHAR},
      xjfglddh = #{xjfglddh,jdbcType=VARCHAR},
      xzfgld = #{xzfgld,jdbcType=VARCHAR},
      xzfglddh = #{xzfglddh,jdbcType=VARCHAR},
      zrzyszrr = #{zrzyszrr,jdbcType=VARCHAR},
      zrzyszrrdh = #{zrzyszrrdh,jdbcType=VARCHAR},
      qcqfy = #{qcqfy,jdbcType=VARCHAR},
      qcqfydh = #{qcqfydh,jdbcType=VARCHAR},
      sjly = #{sjly,jdbcType=VARCHAR},
      brbqxmz = #{brbqxmz,jdbcType=VARCHAR},
      zlgcxmbh = #{zlgcxmbh,jdbcType=VARCHAR},
      jcdbm = #{jcdbm,jdbcType=VARCHAR},
      tbr = #{tbr,jdbcType=VARCHAR},
      ffqcjsj = #{ffqcjsj,jdbcType=VARCHAR},
      tjxzsj = #{tjxzsj,jdbcType=VARCHAR},
      hjtgsj = #{hjtgsj,jdbcType=VARCHAR},
      yzlx = #{yzlx,jdbcType=VARCHAR},
      zztgs = #{zztgs,jdbcType=VARCHAR},
      czths = #{czths,jdbcType=VARCHAR},
      czthjrs = #{czthjrs,jdbcType=VARCHAR},
      level1hhong = #{level1hhong,jdbcType=VARCHAR},
      level1hcheng = #{level1hcheng,jdbcType=VARCHAR},
      level1hhuang = #{level1hhuang,jdbcType=VARCHAR},
      level3hhong = #{level3hhong,jdbcType=VARCHAR},
      level3hcheng = #{level3hcheng,jdbcType=VARCHAR},
      level3hhuang = #{level3hhuang,jdbcType=VARCHAR},
      level6hcheng = #{level6hcheng,jdbcType=VARCHAR},
      level6hhuang = #{level6hhuang,jdbcType=VARCHAR},
      level12hhong = #{level12hhong,jdbcType=VARCHAR},
      level12hcheng = #{level12hcheng,jdbcType=VARCHAR},
      level12hhuang = #{level12hhuang,jdbcType=VARCHAR},
      level24hhong = #{level24hhong,jdbcType=VARCHAR},
      level24hcheng = #{level24hcheng,jdbcType=VARCHAR},
      level24hhuang = #{level24hhuang,jdbcType=VARCHAR},
      hjyy = #{hjyy,jdbcType=VARCHAR},
      hjyy_other = #{hjyyOther,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_device_monitor_fxffq
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="fxqbh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.fxqbh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="fxffqmc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.fxffqmc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dcbh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.dcbh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="szdq = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.szdq,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="xzc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.xzc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="fflx = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.fflx,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zzt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.zzt,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="wdx = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.wdx,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="fxdj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.fxdj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="syxhs = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.syxhs,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="syxrs = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.syxrs,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="czrk = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.czrk,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="syxcc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.syxcc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="xjfgld = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.xjfgld,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="xjfglddh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.xjfglddh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="xzfgld = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.xzfgld,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="xzfglddh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.xzfglddh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zrzyszrr = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.zrzyszrr,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zrzyszrrdh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.zrzyszrrdh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="qcqfy = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.qcqfy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="qcqfydh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.qcqfydh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sjly = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.sjly,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="brbqxmz = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.brbqxmz,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zlgcxmbh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.zlgcxmbh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="jcdbm = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.jcdbm,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="tbr = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.tbr,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ffqcjsj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.ffqcjsj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="tjxzsj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.tjxzsj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="hjtgsj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.hjtgsj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="yzlx = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.yzlx,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zztgs = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.zztgs,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="czths = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.czths,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="czthjrs = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.czthjrs,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level1hhong = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level1hhong,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level1hcheng = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level1hcheng,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level1hhuang = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level1hhuang,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level3hhong = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level3hhong,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level3hcheng = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level3hcheng,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level3hhuang = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level3hhuang,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level6hcheng = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level6hcheng,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level6hhuang = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level6hhuang,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level12hhong = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level12hhong,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level12hcheng = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level12hcheng,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level12hhuang = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level12hhuang,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level24hhong = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level24hhong,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level24hcheng = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level24hcheng,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level24hhuang = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.level24hhuang,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="hjyy = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.hjyy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="hjyy_other = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.hjyyOther,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_monitor_fxffq
    (fxqbh, fxffqmc, dcbh, szdq, xzc, fflx, zzt, wdx, fxdj, syxhs, syxrs, czrk, syxcc,
      xjfgld, xjfglddh, xzfgld, xzfglddh, zrzyszrr, zrzyszrrdh, qcqfy, qcqfydh, sjly,
      brbqxmz, zlgcxmbh, jcdbm, tbr, ffqcjsj, tjxzsj, hjtgsj, yzlx, zztgs, czths, czthjrs,
      level1hhong, level1hcheng, level1hhuang, level3hhong, level3hcheng, level3hhuang,
      level6hcheng, level6hhuang, level12hhong, level12hcheng, level12hhuang, level24hhong,
      level24hcheng, level24hhuang, hjyy, hjyy_other)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fxqbh,jdbcType=VARCHAR}, #{item.fxffqmc,jdbcType=VARCHAR}, #{item.dcbh,jdbcType=VARCHAR},
        #{item.szdq,jdbcType=VARCHAR}, #{item.xzc,jdbcType=VARCHAR}, #{item.fflx,jdbcType=VARCHAR},
        #{item.zzt,jdbcType=VARCHAR}, #{item.wdx,jdbcType=VARCHAR}, #{item.fxdj,jdbcType=VARCHAR},
        #{item.syxhs,jdbcType=VARCHAR}, #{item.syxrs,jdbcType=VARCHAR}, #{item.czrk,jdbcType=VARCHAR},
        #{item.syxcc,jdbcType=VARCHAR}, #{item.xjfgld,jdbcType=VARCHAR}, #{item.xjfglddh,jdbcType=VARCHAR},
        #{item.xzfgld,jdbcType=VARCHAR}, #{item.xzfglddh,jdbcType=VARCHAR}, #{item.zrzyszrr,jdbcType=VARCHAR},
        #{item.zrzyszrrdh,jdbcType=VARCHAR}, #{item.qcqfy,jdbcType=VARCHAR}, #{item.qcqfydh,jdbcType=VARCHAR},
        #{item.sjly,jdbcType=VARCHAR}, #{item.brbqxmz,jdbcType=VARCHAR}, #{item.zlgcxmbh,jdbcType=VARCHAR},
        #{item.jcdbm,jdbcType=VARCHAR}, #{item.tbr,jdbcType=VARCHAR}, #{item.ffqcjsj,jdbcType=VARCHAR},
        #{item.tjxzsj,jdbcType=VARCHAR}, #{item.hjtgsj,jdbcType=VARCHAR}, #{item.yzlx,jdbcType=VARCHAR},
        #{item.zztgs,jdbcType=VARCHAR}, #{item.czths,jdbcType=VARCHAR}, #{item.czthjrs,jdbcType=VARCHAR},
        #{item.level1hhong,jdbcType=VARCHAR}, #{item.level1hcheng,jdbcType=VARCHAR}, #{item.level1hhuang,jdbcType=VARCHAR},
        #{item.level3hhong,jdbcType=VARCHAR}, #{item.level3hcheng,jdbcType=VARCHAR}, #{item.level3hhuang,jdbcType=VARCHAR},
        #{item.level6hcheng,jdbcType=VARCHAR}, #{item.level6hhuang,jdbcType=VARCHAR}, #{item.level12hhong,jdbcType=VARCHAR},
        #{item.level12hcheng,jdbcType=VARCHAR}, #{item.level12hhuang,jdbcType=VARCHAR},
        #{item.level24hhong,jdbcType=VARCHAR}, #{item.level24hcheng,jdbcType=VARCHAR},
        #{item.level24hhuang,jdbcType=VARCHAR}, #{item.hjyy,jdbcType=VARCHAR}, #{item.hjyyOther,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

  <select id="listAll" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_device_monitor_fxffq
    where 1=1
    <if test="monitorName != null and monitorName != ''">
      and fxffqmc like concat('%', #{monitorName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="monitorArea != null and monitorArea != ''">
      and szdq like concat('%', #{monitorArea,jdbcType=VARCHAR}, '%')
    </if>
    <if test="warningLevel != null and warningLevel != ''">
      and fxdj like concat('%', #{warningLevel,jdbcType=VARCHAR}, '%')
    </if>
  </select>
</mapper>
