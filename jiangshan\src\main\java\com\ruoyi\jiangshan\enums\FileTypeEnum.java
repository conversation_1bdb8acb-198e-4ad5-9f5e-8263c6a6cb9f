package com.ruoyi.jiangshan.enums;

/**
 * 业务类型 1-设备数据 2-事件 3-风险核实 4-隐患处置 5-监测对象 6-监测设备 20-其他
 */
public enum FileTypeEnum {
    TYPE_01(1, "设备数据"),
    TYPE_02(2, "事件"),
    TYPE_03(3, "风险核实"),
    TYPE_04(4, "隐患处置"),
    TYPE_05(5, "监测对象"),
    TYPE_06(6, "监测设备"),
    TYPE_07(7, "监测对象图片"),
    TYPE_08(8, "监测对象附件"),
    TYPE_09(9, "二维码"),
    OTHER(20, "其他"),
    ;

    private Integer code;
    private String desc;

    FileTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
