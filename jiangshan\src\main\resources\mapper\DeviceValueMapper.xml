<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceValueMapper">

    <resultMap type="DeviceValue" id="DeviceValueResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceThirdId"    column="device_third_id"    />
        <result property="monitorId"    column="monitor_id"    />
        <result property="monitorObject"    column="monitor_object"    />
        <result property="monitorValue"    column="monitor_value"    />
        <result property="monitorItem"    column="monitor_item"    />
        <result property="monitorItemEnglish"    column="monitor_item_english"    />
        <result property="monitorUnit"    column="monitor_unit"    />
        <result property="monitorTime"    column="monitor_time"    />
        <result property="target"    column="target"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceScene"    column="device_scene"    />
        <result property="deviceName"    column="device_name"    />
    </resultMap>

    <sql id="selectDeviceValueVo">
        select id, device_id, device_third_id, monitor_id, monitor_object, monitor_value,
               monitor_item, monitor_item_english, monitor_unit, monitor_time, target,
               create_time, create_by from t_device_value
    </sql>

    <select id="selectDeviceValueList" parameterType="DeviceValue" resultMap="DeviceValueResult">
        select t1.id, device_id, t1.device_third_id, t1.monitor_id, monitor_value,
        monitor_item, monitor_item_english, monitor_unit, monitor_time, target,
        t1.create_time, t1.create_by, t2.device_type, t2.device_scene, t2.device_name,
        t2.monitor_name as monitor_object
        from t_device_value t1
        left join t_device_info t2 on t1.device_third_id = t2.device_third_id
        <where>
            <if test="monitorId != null"> and t1.monitor_id = #{monitorId,jdbcType=BIGINT}</if>
            <if test="deviceType != null and deviceType != ''"> and t2.device_type = #{deviceType,jdbcType=VARCHAR}</if>
            <if test="deviceScene != null and deviceScene != ''"> and t2.device_scene = #{deviceScene,jdbcType=VARCHAR}</if>
            <if test="deviceName != null and deviceName != ''"> and
                (t2.device_name like concat('%', #{deviceName,jdbcType=VARCHAR}, '%') or t2.device_third_id = #{deviceThirdId})
            </if>
            <if test="monitorTime != null "> and t1.monitor_time = #{monitorTime}</if>
            <if test="startTime != null">
                and t1.monitor_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and t1.monitor_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="deviceThirdId != null and deviceThirdId != ''">
                and t1.device_third_id = #{deviceThirdId}
            </if>
        </where>
        order by monitor_time desc
    </select>

    <select id="selectDeviceValueById" parameterType="Long" resultMap="DeviceValueResult">
        select distinct(t1.id), device_id, t1.device_third_id, t1.monitor_id, monitor_object, monitor_value,
        monitor_item, monitor_item_english, monitor_unit, monitor_time, target,
        t1.create_time, t1.create_by, t2.device_type, t2.device_scene, t2.device_name
        from t_device_value t1
        left join t_device_info t2 on t1.device_third_id = t2.device_third_id
        where t1.id = #{id}
    </select>

    <insert id="insertDeviceValue" parameterType="DeviceValue" useGeneratedKeys="true" keyProperty="id">
        insert into t_device_value
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="deviceThirdId != null">device_third_id,</if>
            <if test="monitorId != null">monitor_id,</if>
            <if test="monitorObject != null">monitor_object,</if>
            <if test="monitorValue != null">monitor_value,</if>
            <if test="monitorItem != null">monitor_item,</if>
            <if test="monitorItemEnglish != null">monitor_item_english,</if>
            <if test="monitorUnit != null">monitor_unit,</if>
            <if test="monitorTime != null">monitor_time,</if>
            <if test="target != null">target,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceThirdId != null">#{deviceThirdId},</if>
            <if test="monitorId != null">#{monitorId},</if>
            <if test="monitorObject != null">#{monitorObject},</if>
            <if test="monitorValue != null">#{monitorValue},</if>
            <if test="monitorItem != null">#{monitorItem},</if>
            <if test="monitorItemEnglish != null">#{monitorItemEnglish},</if>
            <if test="monitorUnit != null">#{monitorUnit},</if>
            <if test="monitorTime != null">#{monitorTime},</if>
            <if test="target != null">#{target},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateDeviceValue" parameterType="DeviceValue">
        update t_device_value
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceThirdId != null">device_third_id = #{deviceThirdId},</if>
            <if test="monitorId != null">monitor_id = #{monitorId},</if>
            <if test="monitorObject != null">monitor_object = #{monitorObject},</if>
            <if test="monitorValue != null">monitor_value = #{monitorValue},</if>
            <if test="monitorItem != null">monitor_item = #{monitorItem},</if>
            <if test="monitorItemEnglish != null">monitor_item_english = #{monitorItemEnglish},</if>
            <if test="monitorUnit != null">monitor_unit = #{monitorUnit},</if>
            <if test="monitorTime != null">monitor_time = #{monitorTime},</if>
            <if test="target != null">target = #{target},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceValueById" parameterType="Long">
        delete from t_device_value where id = #{id}
    </delete>

    <delete id="deleteDeviceValueByIds" parameterType="String">
        delete from t_device_value where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="listByMonitorIdAndItem" resultType="com.ruoyi.jiangshan.vo.BusinessDateCountVO">
        select monitor_value as `value`, monitor_time as `key`
        from t_device_value
        where monitor_id = #{id}
        and monitor_item = #{monitorItem}
        and monitor_time between #{startTime} and #{endTime}
        order by monitor_time desc
        limit 6
    </select>

    <select id="getMaxValueByDeviceType" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
        select Max(monitor_value) as `value`, t2.device_type as `key`
        from t_device_value t1 left join t_device_info t2 on t1.device_third_id = t2.device_third_id
        where 1=1
        <if test="deviceTypeList != null and deviceTypeList.size() != 0">
            and t2.device_type in
            <foreach collection="deviceTypeList" item="deviceType" open="(" separator="," close=")">
                #{deviceType}
            </foreach>
        </if>
        group by t2.device_type
    </select>

    <select id="listByDateAndDeviceId" resultType="com.ruoyi.jiangshan.vo.BusinessDateCountVO">
        select monitor_value as `value`, monitor_time as `key`
        from t_device_value
        where device_third_id = #{deviceThirdId}
        and monitor_time between #{startTime} and #{endTime}
        <if test="monitorItem != null and monitorItem != ''">
            and monitor_item = #{monitorItem,jdbcType=VARCHAR}
        </if>
<!--        group by hour(monitor_time)-->
        order by monitor_time desc
    </select>

    <select id="countByDeviceThirdIdAndItem" resultType="com.ruoyi.jiangshan.vo.BusinessDateCountVO">
        select monitor_value as `value`, monitor_time as `key`, monitor_item as tmpKey
        from t_device_value
        where device_third_id = #{deviceThirdId}
        and monitor_item = #{monitorItem}
        and monitor_time >= CURDATE() - INTERVAL 6 DAY
        <!--        group by hour(monitor_time)-->
        order by monitor_item, monitor_time desc
    </select>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into t_device_value
        (device_id, device_third_id, monitor_id, monitor_object, monitor_value, monitor_item,
        monitor_item_english, monitor_unit, monitor_time, target, create_time, create_by
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deviceId,jdbcType=BIGINT}, #{item.deviceThirdId,jdbcType=VARCHAR}, #{item.monitorId,jdbcType=BIGINT},
            #{item.monitorObject,jdbcType=VARCHAR}, #{item.monitorValue,jdbcType=VARCHAR},
            #{item.monitorItem,jdbcType=VARCHAR}, #{item.monitorItemEnglish,jdbcType=VARCHAR},
            #{item.monitorUnit,jdbcType=VARCHAR}, #{item.monitorTime,jdbcType=TIMESTAMP}, #{item.target,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="getMaxValueByMonitorItem" resultType="double">
        select Max(monitor_value)
        from t_device_value
        where monitor_item = #{monitorItem}
        and monitor_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        and monitor_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </select>

    <select id="getAvgValueByMonitorItem" resultType="double">
        select avg(monitor_value)
        from t_device_value
        where monitor_item = #{monitorItem}
          and monitor_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
          and monitor_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </select>

<!--    <select id="listByMonitorIdListAndDate" resultMap="DeviceValueResult">-->
<!--        select * from (-->
<!--            select max(monitor_time) as monitor_time, id, device_id, device_third_id,-->
<!--            monitor_id, monitor_object, monitor_value, monitor_item, monitor_item_english, monitor_unit, target,-->
<!--            create_time, create_by-->
<!--            from (select * from t_device_value order by monitor_time desc) tmp-->
<!--            group by device_third_id, monitor_item-->
<!--        ) t1 left join t_device_info t2-->
<!--        on t1.device_third_id = t2.device_third_id-->
<!--        where 1=1-->
<!--        <if test="list != null and list.size() != 0">-->
<!--            and t1.monitor_id in-->
<!--            <foreach collection="list" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="startTime != null">-->
<!--            and monitor_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}-->
<!--        </if>-->
<!--        <if test="endTime != null">-->
<!--            and monitor_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}-->
<!--        </if>-->
<!--    </select>-->

    <select id="listByMonitorIdListAndDate" resultMap="DeviceValueResult">
        <if test="list != null and list.size() != 0">
            WITH RankedDeviceValues AS (
            SELECT
            id,
            device_id,
            device_third_id,
            monitor_id,
            monitor_object,
            monitor_value,
            monitor_item,
            monitor_item_english,
            monitor_unit,
            monitor_time,
            target,
            create_time,
            create_by,
            ROW_NUMBER() OVER (PARTITION BY device_third_id, monitor_item ORDER BY monitor_time DESC) AS rn
            FROM
            (select * from t_device_value
            where 1=1
            and monitor_id in
            <foreach
                    collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and monitor_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and monitor_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
            </if>
            and monitor_time <![CDATA[ >= ]]> DATE_SUB(CURDATE(), INTERVAL 1 WEEK)
            order by monitor_time desc
            limit 200000
            ) tmp
        )
        SELECT
            *
        FROM
            RankedDeviceValues t1
        left join t_device_info t2
        on t1.device_third_id = t2.device_third_id
        WHERE
            rn = 1;
    </select>

<!--    <select id="listByDeviceIdList" resultMap="DeviceValueResult">-->
<!--        select id, device_id, device_third_id, monitor_id, monitor_object, monitor_value,-->
<!--        monitor_item, monitor_item_english, monitor_unit, max(monitor_time) as monitor_time, target,-->
<!--        create_time, create_by from (select * from t_device_value order by monitor_time desc) t2-->
<!--        where device_id in-->
<!--        <foreach collection="list" item="item" open="(" separator="," close=")">-->
<!--            #{item}-->
<!--        </foreach>-->
<!--        group by device_id, monitor_item-->
<!--    </select>-->

    <select id="listByDeviceIdList" resultMap="DeviceValueResult">
        WITH RankedDeviceValues AS (
        SELECT
        id,
        device_id,
        device_third_id,
        monitor_id,
        monitor_object,
        monitor_value,
        monitor_item,
        monitor_item_english,
        monitor_unit,
        monitor_time,
        target,
        create_time,
        create_by,
        ROW_NUMBER() OVER (PARTITION BY device_third_id, monitor_item ORDER BY monitor_time DESC) AS rn
        FROM
        (select * from t_device_value
        where monitor_time <![CDATA[ >= ]]> DATE_SUB(CURDATE(), INTERVAL 1 WEEK)
        and device_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by monitor_time desc
        limit 200000
        ) tmp
        )
        SELECT
        *
        FROM
        RankedDeviceValues
        WHERE
        rn = 1;
    </select>

    <select id="listReport" resultType="com.ruoyi.jiangshan.vo.WarningReportDeviceVO">
        WITH RankedDeviceValues AS (
        SELECT
        id,
        device_id,
        device_third_id,
        monitor_id,
        monitor_object,
        monitor_value,
        monitor_item,
        monitor_item_english,
        monitor_unit,
        monitor_time,
        target,
        create_time,
        create_by,
        ROW_NUMBER() OVER (PARTITION BY device_third_id, monitor_item ORDER BY monitor_time DESC) AS rn
        FROM
        (select * from t_device_value
        where monitor_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        and monitor_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        order by monitor_time desc
        limit 200000
        ) tmp

        )

        select distinct (t1.id) as id, t1.device_type as deviceType, t1.device_name as deviceName,
        t2.device_third_id as deviceThirdId, t1.device_address as deviceAddress, t2.monitor_value as monitorValue,
        t2.monitor_unit as monitorUnit, if(t3.id is not null, '警告', '离线') as deviceStatus,
        t3.warning_level as warningLevel, t3.warning_reason as warningReason, t5.process_content as verifyContent,
        t1.device_scene as deviceScene, t2.monitor_item as monitorItem
        from t_device_info t1
        left join RankedDeviceValues t2 on t1.device_third_id = t2.device_third_id
        left join (select * from t_warning_info
                    where warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
                    and warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}) t3 on t1.device_third_id = t3.device_third_id
        left join t_warning_event t4 on t3.id = t4.warning_id
        left join (select * from t_warning_event_process where process_step = 1 and status = 3) t5 on t4.id = t5.event_id
        where 1=1
        and (t3.id is not null or t1.device_status = 0)
        and t1.device_scene in
        <foreach collection="sceneList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="geoStr != null and geoStr != ''">
            and t1.lon != '' and t1.lat != ''
            and ST_Contains(ST_GeomFromText(#{geoStr}), ST_PointFromText(CONCAT('POINT(', t1.lon, ' ', t1.lat, ')')))
        </if>
        group by t1.device_third_id
        order by COALESCE(t3.warning_level, 999999)
    </select>

<!--    <select id="listLastMonitorItemByDeviceId" resultMap="DeviceValueResult">-->
<!--        select max(monitor_time) as monitor_time, id, device_id, device_third_id,-->
<!--               monitor_id, monitor_object, monitor_value, monitor_item,-->
<!--               monitor_item_english, monitor_unit, target,-->
<!--               create_time, create_by-->
<!--        from t_device_value-->
<!--        where device_third_id = #{deviceThirdId}-->
<!--        group by device_third_id, monitor_item-->
<!--        order by monitor_time desc-->
<!--    </select>-->

    <select id="listByDeviceAndMonitorItem" resultMap="DeviceValueResult">
        <include refid="selectDeviceValueVo"></include>
        where device_third_id = #{deviceThirdId}
        and monitor_item = #{monitorItem,jdbcType=VARCHAR}
        and monitor_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        and monitor_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </select>

    <select id="listByDeviceAndMonitorItemV2" resultMap="DeviceValueResult">
        SELECT * FROM (
        SELECT
        @row_num := @row_num + 1 AS row_num,
        t.*
        FROM
        (SELECT @row_num := 0) r,
        (
        SELECT /*+ INDEX(t_device_value idx_composite) */ *
        FROM t_device_value
        WHERE device_third_id = #{deviceThirdId}
        AND monitor_item = #{monitorItem,jdbcType=VARCHAR}
        AND monitor_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        AND monitor_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        ORDER BY monitor_time DESC
        LIMIT 400000  -- 保证能抽取到4000条的最大数据量
        ) t
        ) AS numbered
        WHERE row_num %
        CASE
        WHEN (SELECT COUNT(1) FROM t_device_value
        WHERE device_third_id = #{deviceThirdId}
        AND monitor_item = #{monitorItem,jdbcType=VARCHAR}
        AND monitor_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}) > 400000
        THEN CEIL((SELECT COUNT(1) FROM t_device_value
        WHERE device_third_id = #{deviceThirdId}
        AND monitor_item = #{monitorItem,jdbcType=VARCHAR}
        AND monitor_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}) / 4000)
        ELSE 100
        END = 1
        LIMIT 4000
    </select>

    <select id="listByDeviceThirdId" resultMap="DeviceValueResult">
        WITH RankedDeviceValues AS (
            SELECT
                id,
                device_id,
                device_third_id,
                monitor_id,
                monitor_object,
                monitor_value,
                monitor_item,
                monitor_item_english,
                monitor_unit,
                monitor_time,
                target,
                create_time,
                create_by,
                ROW_NUMBER() OVER (PARTITION BY device_third_id, monitor_item ORDER BY monitor_time DESC) AS rn
            FROM
                (select * from t_device_value
            where monitor_item != '设备连接状态'
              and device_third_id = #{deviceThirdId}
              and monitor_time <![CDATA[ >= ]]> DATE_SUB(CURDATE(), INTERVAL 1 WEEK)
            order by monitor_time desc
            limit 1000
        )
            tmp
        )
        SELECT
            *
        FROM
            RankedDeviceValues t1
                left join t_device_info t2
                          on t1.device_third_id = t2.device_third_id
        WHERE
            rn = 1
        order by monitor_time desc;
    </select>

    <select id="listByDeviceThirdIdList" resultMap="DeviceValueResult">
        WITH RankedDeviceValues AS (
            SELECT
                id,
                device_id,
                device_third_id,
                monitor_id,
                monitor_object,
                monitor_value,
                monitor_item,
                monitor_item_english,
                monitor_unit,
                monitor_time,
                target,
                create_time,
                create_by,
                ROW_NUMBER() OVER (PARTITION BY device_third_id, monitor_item ORDER BY monitor_time DESC) AS rn
            FROM
            (select * from t_device_value
            where monitor_item != '设备连接状态'
              and monitor_time <![CDATA[ >= ]]> DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
              and device_third_id in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            limit 200000) tmp
        )
        SELECT
            *
        FROM
            RankedDeviceValues t1
                left join t_device_info t2
                          on t1.device_third_id = t2.device_third_id
        WHERE
            rn = 1
        order by monitor_time desc;
    </select>

    <select id="getLineData" resultMap="DeviceValueResult">
        select monitor_item, monitor_value, monitor_time from t_device_value
        where device_third_id = #{deviceThirdId}
        and monitor_item != '设备连接状态'
        <if test="startTime != null">
            and monitor_time <![CDATA[ >= ]]> #{startTime, jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and monitor_time <![CDATA[ <= ]]> #{endTime, jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="getMaxValueByDeviceTypeAndMonitorItem" resultType="java.lang.String">
        select Max(monitor_value) as `value`
        from t_device_value t1 left join t_device_info t2 on t1.device_third_id = t2.device_third_id
        where 1=1
        <if test="deviceTypeList != null and deviceTypeList.size() != 0">
            and t2.device_type in
            <foreach collection="deviceTypeList" item="deviceType" open="(" separator="," close=")">
                #{deviceType}
            </foreach>
        </if>
        and monitor_item = #{monitorItem}
        and monitor_time <![CDATA[ >= ]]> #{startTime}
        and monitor_time <![CDATA[ <= ]]> #{endTime}
    </select>

    <select id="getLastValueByDeviceThirdId" resultType="java.lang.String">
        select concat(monitor_value, monitor_unit)
        from t_device_value
        where device_third_id = #{deviceThirdId}
        and monitor_item = #{monitorItem}
        order by monitor_time desc
        limit 1
    </select>

    <select id="getCount" resultType="java.lang.Long">
        select count(1) from t_device_value
        where device_third_id = #{deviceThirdId}
        and monitor_item = #{monitorItem,jdbcType=VARCHAR}
        and monitor_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        and monitor_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </select>

    <select id="listByDeviceThirdIdListAndMonitorItem" resultMap="DeviceValueResult">
        WITH RankedDeviceValues AS (
        SELECT
        id,
        device_id,
        device_third_id,
        monitor_id,
        monitor_object,
        monitor_value,
        monitor_item,
        monitor_item_english,
        monitor_unit,
        monitor_time,
        target,
        create_time,
        create_by,
        ROW_NUMBER() OVER (PARTITION BY device_third_id, monitor_item ORDER BY monitor_time DESC) AS rn
        FROM
        (select * from t_device_value
        where monitor_item = #{monitorItem}
        and monitor_time <![CDATA[ >= ]]> DATE_SUB(CURDATE(), INTERVAL 1 WEEK)
        and device_third_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        limit 100000) tmp
        )
        SELECT
        *
        FROM
        RankedDeviceValues t1
        left join t_device_info t2
        on t1.device_third_id = t2.device_third_id
        WHERE
        rn = 1
        order by monitor_time desc;
    </select>

    <select id="countDeviceValueByTimeRange" resultType="int">
        SELECT COUNT(1)
        FROM t_device_value
        WHERE monitor_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="getDeviceValueByTimeRange" resultMap="DeviceValueResult">
        SELECT *
        FROM t_device_value
        WHERE monitor_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY monitor_time
        LIMIT #{offset}, #{limit}
    </select>

    <select id="getAvgRainFall" resultType="java.lang.String">
       select avg(monitor_value) from (
       SELECT * FROM `t_device_value`
       where monitor_time >= NOW() - INTERVAL #{hour} hour
         and monitor_item = '小时降雨量'
       group by device_third_id) tmp
    </select>

    <select id="listRainFallInHour" resultMap="DeviceValueResult">
        SELECT * FROM `t_device_value`
        where monitor_time >= NOW() - INTERVAL 1 hour
          and monitor_item = '小时降雨量'
        group by device_third_id
    </select>

    <select id="listByIdList" resultMap="DeviceValueResult">
        SELECT * FROM `t_device_value`
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listByDeviceThirdIdAndMonitorItemInTimeRange" resultMap="DeviceValueResult">
        WITH RankedDeviceValues AS (
        SELECT
        id,
        device_id,
        device_third_id,
        monitor_id,
        monitor_object,
        monitor_value,
        monitor_item,
        monitor_item_english,
        monitor_unit,
        monitor_time,
        target,
        create_time,
        create_by,
        ROW_NUMBER() OVER (PARTITION BY device_third_id, monitor_item ORDER BY monitor_time DESC) AS rn
        FROM
        (select * from t_device_value
        where monitor_item = #{monitorItem}
        and monitor_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        and monitor_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        and device_third_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        limit 100000) tmp
        )
        SELECT
        *
        FROM
        RankedDeviceValues t1
        left join t_device_info t2
        on t1.device_third_id = t2.device_third_id
        WHERE
        rn = 1
        order by monitor_time desc;
    </select>

    <select id="getAvgRainFallByTime" resultType="java.lang.String">
        select avg(monitor_value) from (
       SELECT * FROM `t_device_value`
       where monitor_time >= #{startLdt,jdbcType=TIMESTAMP}
         and monitor_time <![CDATA[ <= ]]> #{endLdt,jdbcType=TIMESTAMP}
         and monitor_item = '小时降雨量'
       group by device_third_id) tmp
    </select>

    <select id="listByDeviceThirdIdListByHour" resultMap="DeviceValueResult">
        WITH RankedDeviceValues AS (
        SELECT
        id,
        device_id,
        device_third_id,
        monitor_id,
        monitor_object,
        monitor_value,
        monitor_item,
        monitor_item_english,
        monitor_unit,
        monitor_time,
        target,
        create_time,
        create_by,
        ROW_NUMBER() OVER (PARTITION BY device_third_id, monitor_item ORDER BY monitor_time DESC) AS rn
        FROM
        (select * from t_device_value
        where monitor_item != '设备连接状态'
        and monitor_time <![CDATA[ >= ]]> DATE_SUB(CURDATE(), INTERVAL #{hour} hour)
        and device_third_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        limit 200000) tmp
        )
        SELECT
        *
        FROM
        RankedDeviceValues t1
        left join t_device_info t2
        on t1.device_third_id = t2.device_third_id
        WHERE
        rn = 1
        order by monitor_time desc;
    </select>
</mapper>
