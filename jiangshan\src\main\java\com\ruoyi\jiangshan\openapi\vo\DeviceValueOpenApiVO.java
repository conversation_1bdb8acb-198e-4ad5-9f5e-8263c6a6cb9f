package com.ruoyi.jiangshan.openapi.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.jiangshan.vo.DeviceValueItemOpenApiVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class DeviceValueOpenApiVO {

    /**
     * 设备id
     */
    @NotBlank(message = "设备id不能为空")
    private String deviceThirdId;

    @NotEmpty(message = "监测集合不能为空")
    private List<DeviceValueItemOpenApiVO> monitorList;

    /**
     * 监测时间
     */
    @NotNull(message = "监测时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitorTime;

    /**
     * 靶标
     */
    private String target;


}
