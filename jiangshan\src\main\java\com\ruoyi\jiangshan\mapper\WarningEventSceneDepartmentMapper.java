package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningEventSceneDepartment;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface WarningEventSceneDepartmentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningEventSceneDepartment record);

    int insertSelective(WarningEventSceneDepartment record);

    WarningEventSceneDepartment selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningEventSceneDepartment record);

    int updateByPrimaryKey(WarningEventSceneDepartment record);

    int updateBatch(List<WarningEventSceneDepartment> list);

    int batchInsert(@Param("list") List<WarningEventSceneDepartment> list);

    List<WarningEventSceneDepartment> listAll();

    List<WarningEventSceneDepartment> listByDeptId(@Param("deptId") Long deptId);
}
