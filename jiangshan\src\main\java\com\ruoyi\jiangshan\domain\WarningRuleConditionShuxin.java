package com.ruoyi.jiangshan.domain;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class WarningRuleConditionShuxin {
    /**
    * id
    */
    private Long id;

    /**
    * 模型id
    */
    private Long modelId;

    /**
    * 规则id
    */
    private Long ruleId;

    /**
    * 数据状态
    */
    private String sjzt;

    /**
    * 风险区编号
    */
    @NotBlank(message = "fxqbh不能为空")
    private String fxqbh;

    /**
    * 风险防范区名称
    */
    private String fxffqmc;

    /**
    * 调查编号
    */
    private String dcbh;

    /**
    * 所在地区

    */
    private String szdq;

    /**
    * 行政村

    */
    private String xzc;

    /**
    * 防范类型

    */
    private String fflx;

    /**
    * 防范区范围

    */
    private String ffqfw;

    /**
    * 致灾体

    */
    private String zzt;

    /**
    * 稳定性

    */
    private String wdx;

    /**
    * 风险等级

    */
    private String fxdj;

    /**
    * 受影响户数

    */
    private String syxhs;

    /**
    * 受影响人数

    */
    private String syxrs;

    /**
    * 常住人口

    */
    private String czrk;

    /**
    * 受影响财产

    */
    private String syxcc;

    /**
    * 县局分管领导

    */
    private String xjfgld;

    /**
    * 县局分管领导电话

    */
    private String xjfglddh;

    /**
    * 乡镇分管领导

    */
    private String xzfgld;

    /**
    * 乡镇分管领导电话

    */
    private String xzfglddh;

    /**
    * 自然资源所责任人

    */
    private String zrzyszrr;

    /**
    * 自然资源所责任人电话

    */
    private String zrzyszrrdh;

    /**
    * 群测群防（网格）员

    */
    private String qcqfwgy;

    /**
    * 群测群防（网格）员电话

    */
    private String qcqfwgydh;

    /**
    * 数据来源

    */
    private String sjly;

    /**
    * 避让搬迁项目编号

    */
    private String brbqxmbh;

    /**
    * 治理工程项目编号

    */
    private String zlgcxmbh;

    /**
    * 监测点编码

    */
    private String jcdbm;

    /**
    * 填表人

    */
    private String tbr;

    /**
    * 防范区创建时间

    */
    private String ffqcjsj;

    /**
    * 提交现状时间

    */
    private String tjxzsj;

    /**
    * 核减通过时间

    */
    private String hjtgsj;

    /**
    * 阈值类型

    */
    private String yzlx;

    /**
    * 致灾体个数

    */
    private String zztgs;

    /**
    * 承灾体户数

    */
    private String czths;

    /**
    * 承灾体户籍人数

    */
    private String czthjrs;

    /**
    * 1h红

    */
    private String level1hH;

    /**
    * 1h橙

    */
    private String level1hC;

    /**
    * 1h黄

    */
    private String level1hH1;

    /**
    * 3h红

    */
    private String level3hH;

    /**
    * 3h橙

    */
    private String level3hC;

    /**
    * 3h黄

    */
    private String level3hH3;

    /**
    * 6h红

    */
    private String level6hH;

    /**
    * 6h橙

    */
    private String level6hC;

    /**
    * 6h黄

    */
    private String level6hH6;

    /**
    * 12h红

    */
    private String level12hH;

    /**
    * 12h橙

    */
    private String level12hC;

    /**
    * 12h黄

    */
    private String level12hH12;

    /**
    * 24h红

    */
    private String level24hH;

    /**
    * 24h橙

    */
    private String level24hC;

    /**
    * 24h黄

    */
    private String level24hH24;

    /**
    * 核减原因

    */
    private String hjyy;

    /**
    * 核减原因-其它

    */
    private String hjyyQt;

}
