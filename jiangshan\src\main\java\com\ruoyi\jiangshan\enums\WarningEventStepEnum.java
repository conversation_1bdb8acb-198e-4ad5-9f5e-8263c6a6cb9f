package com.ruoyi.jiangshan.enums;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jiangshan.domain.WarningEventProcess;
import com.ruoyi.jiangshan.vo.WarningEventProcessLogVO;

import java.util.Objects;

/**
 * 流程步骤
 */
public enum WarningEventStepEnum {
//    STATUS_00(0, "未派单", "生成预警事件"),
//    STATUS_01(1, "待核实", "风险核实"),
//    STATUS_02(2, "待签收", "工单签收"),
//    STATUS_03(3, "处置中", "隐患处置"),
//    STATUS_04(4, "已完成", "处置完成"),

    STATUS_00(0, "未派单", "生成预警事件"),
    STATUS_01(1, "待签收-核实", "签收-核实"),
    STATUS_02(2, "待核实", "风险核实"),
    STATUS_03(3, "待签收-处置", "签收-处置"),
    STATUS_04(4, "处置中", "隐患处置"),
    STATUS_05(5, "已完成", "处置完成"),
    STATUS_06(6, "错报", "错报"),
    STATUS_10(10, "退回", "退回"),
    ;

    private Integer code;
    private String desc;
    private String processName;

    WarningEventStepEnum(Integer code, String desc, String processName) {
        this.code = code;
        this.desc = desc;
        this.processName = processName;
    }

    public static WarningEventStepEnum getByCode(Integer code) {
        for (WarningEventStepEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }

        return STATUS_05;
    }

    public static WarningEventProcessLogVO getLog(WarningEventProcess warningEventProcess) {
        WarningEventStepEnum processStep = WarningEventStepEnum.getProcessNameByCode(warningEventProcess.getProcessStep());
        if (Objects.isNull(processStep)) {
            return null;
        }

        WarningEventProcessLogVO logVO = new WarningEventProcessLogVO();
        if (warningEventProcess.getStatus().equals(WarningEventHandleStatus.STATUS_00.getCode())
            || warningEventProcess.getStatus().equals(WarningEventHandleStatus.STATUS_02.getCode())) {
            logVO.setTitle(processStep.getProcessName());
            logVO.setProcessTime(warningEventProcess.getCreateTime());
            logVO.setContent(processStep.getDesc());
        } else if (warningEventProcess.getStatus().equals(WarningEventHandleStatus.STATUS_01.getCode())) {
            logVO.setTitle(processStep.getProcessName());
            logVO.setProcessTime(warningEventProcess.getUpdateTime());
            logVO.setContent(warningEventProcess.getRejectContent());
        } else if (warningEventProcess.getStatus().equals(WarningEventHandleStatus.STATUS_03.getCode())) {
            logVO.setTitle(processStep.getProcessName());
            logVO.setProcessTime(warningEventProcess.getUpdateTime());
            if (StringUtils.isNotBlank(warningEventProcess.getProcessContent())) {
                logVO.setContent(warningEventProcess.getDepartmentName() + "已处理, 处理意见：" + warningEventProcess.getProcessContent());
            } else {
                logVO.setContent(warningEventProcess.getDepartmentName() + "已处理");
            }
        }

        return logVO;
    }

    private static WarningEventStepEnum getProcessNameByCode(Integer processStep) {
        for (WarningEventStepEnum value : values()) {
            if (Objects.equals(value.getCode(), processStep)) {
                return value;
            }
        }

        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getProcessName() {
        return processName;
    }
}
