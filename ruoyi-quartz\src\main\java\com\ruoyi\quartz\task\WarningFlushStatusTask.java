package com.ruoyi.quartz.task;

import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.domain.WarningEventProcess;
import com.ruoyi.jiangshan.enums.WarningSuperviseEnum;
import com.ruoyi.jiangshan.mapper.WarningEventMapper;
import com.ruoyi.jiangshan.mapper.WarningEventProcessMapper;
import com.ruoyi.jiangshan.mapper.WarningEventTimeoutRuleMapper;
import com.ruoyi.jiangshan.mapper.WarningInfoMapper;
import com.ruoyi.jiangshan.service.impl.WarningEventServiceImpl;
import lombok.extern.log4j.Log4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Log4j
@Component("warningFlushStatusTask")
public class WarningFlushStatusTask {

    public void flush141Status() {
        WarningEventServiceImpl warningEventService = SpringUtils.getBean(WarningEventServiceImpl.class);

        log.info("flush141Status, start");

        warningEventService.flushEventStatus();

        log.info("flush141Status, end");
    }

}
