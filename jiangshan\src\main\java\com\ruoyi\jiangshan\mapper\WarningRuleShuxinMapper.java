package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningRuleShuxin;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WarningRuleShuxinMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningRuleShuxin record);

    int insertSelective(WarningRuleShuxin record);

    WarningRuleShuxin selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelectiveByModelId(WarningRuleShuxin record);

    int updateByPrimaryKey(WarningRuleShuxin record);

    int updateBatch(List<WarningRuleShuxin> list);

    int batchInsert(@Param("list") List<WarningRuleShuxin> list);

    WarningRuleShuxin getByModelId(Long id);

    WarningRuleShuxin getAccountByScene(String eventType);
}
