package com.ruoyi.jiangshan.enums;

public enum RedisChannelMapping {
    DEVICE_DATA_CHANNEL("DEVICE_DATA_CHANNEL", "8651"),
    DEVICE_DATA_CHANNEL_2("DEVICE_DATA_CHANNEL_2", "8652"),
    ;

    private String channel;
    private String port;

    RedisChannelMapping(String channel, String port) {
        this.channel = channel;
        this.port = port;
    }

    public String getChannel() {
        return channel;
    }

    public String getPort() {
        return port;
    }

    public static String getByPort(String port) {
        for (RedisChannelMapping value : values()) {
            if (value.getPort().equals(port)) {
                return value.getChannel();
            }
        }

        return DEVICE_DATA_CHANNEL_2.getChannel();
    }
}
