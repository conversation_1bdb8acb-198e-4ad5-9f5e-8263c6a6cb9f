package com.ruoyi.jiangshan.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 报告生成管理对象 t_citysafety_rule
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@Data
public class CitysafetyRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /**
     * 规则编号
     */
    private String bizCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /** 场景选择 */
    @Excel(name = "场景选择")
    private String scene;

    /** 生成频率1-每日、2-每周、3-每月、4-每半年、5-每年 */
    @Excel(name = "生成频率1-每日、2-每周、3-每月、4-每半年、5-每年")
    private Integer generateFrequency;

    /** 生成类型 1-合并生成 2-逐个生成 */
    @Excel(name = "生成类型 1-合并生成 2-逐个生成")
    private Integer generateType;

    /** 生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date generateTime;

    /**
     * 状态
     */
    private Integer status;

    private Date startTime;

    private Date endTime;
}
