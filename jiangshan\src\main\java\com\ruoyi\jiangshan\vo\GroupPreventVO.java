package com.ruoyi.jiangshan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GroupPreventVO {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preventTime;

    private String preventName;

    private String preventUserName;

    private String preventWeather;

    private String preventProblem;
}
