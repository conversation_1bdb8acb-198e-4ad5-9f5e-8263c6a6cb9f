package com.ruoyi.jiangshan.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.framework.vo.DtalkEmployeeVo;
import com.ruoyi.framework.vo.EmployeeInfo;
import com.ruoyi.framework.vo.OrganizationNodeInfo;
import com.ruoyi.framework.web.service.dtalk.DeptDtalkService;
import com.ruoyi.framework.web.service.dtalk.UserDtalkService;
import com.ruoyi.jiangshan.domain.SysArea;
import com.ruoyi.jiangshan.mapper.SysAreaMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.jiangshan.mapper.WarningDepartmentInfoMapper;
import com.ruoyi.jiangshan.domain.WarningDepartmentInfo;
import com.ruoyi.jiangshan.service.IWarningDepartmentInfoService;

/**
 * 联动处置中心部门Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class WarningDepartmentInfoServiceImpl implements IWarningDepartmentInfoService
{
    @Autowired
    private WarningDepartmentInfoMapper warningDepartmentInfoMapper;
    @Autowired
    private DeptDtalkService deptDtalkService;
    @Autowired
    private UserDtalkService userDtalkService;
    @Autowired
    private SysAreaMapper sysAreaMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询联动处置中心部门
     *
     * @param id 联动处置中心部门主键
     * @return 联动处置中心部门
     */
    @Override
    public WarningDepartmentInfo selectWarningDepartmentInfoById(Long id)
    {
        return warningDepartmentInfoMapper.selectWarningDepartmentInfoById(id);
    }

    /**
     * 查询联动处置中心部门列表
     *
     * @param warningDepartmentInfo 联动处置中心部门
     * @return 联动处置中心部门
     */
    @Override
    public List<WarningDepartmentInfo> selectWarningDepartmentInfoList(WarningDepartmentInfo warningDepartmentInfo)
    {
        return warningDepartmentInfoMapper.selectWarningDepartmentInfoList(warningDepartmentInfo);
    }

    /**
     * 新增联动处置中心部门
     *
     * @param warningDepartmentInfo 联动处置中心部门
     * @return 结果
     */
    @Override
    public int insertWarningDepartmentInfo(WarningDepartmentInfo warningDepartmentInfo)
    {
        warningDepartmentInfo.setCreateTime(DateUtils.getNowDate());
        return warningDepartmentInfoMapper.insertWarningDepartmentInfo(warningDepartmentInfo);
    }

    /**
     * 修改联动处置中心部门
     *
     * @param warningDepartmentInfo 联动处置中心部门
     * @return 结果
     */
    @Override
    public int updateWarningDepartmentInfo(WarningDepartmentInfo warningDepartmentInfo)
    {
        warningDepartmentInfo.setUpdateTime(DateUtils.getNowDate());
        return warningDepartmentInfoMapper.updateWarningDepartmentInfo(warningDepartmentInfo);
    }

    /**
     * 批量删除联动处置中心部门
     *
     * @param ids 需要删除的联动处置中心部门主键
     * @return 结果
     */
    @Override
    public int deleteWarningDepartmentInfoByIds(Long[] ids)
    {
        return warningDepartmentInfoMapper.deleteWarningDepartmentInfoByIds(ids);
    }

    /**
     * 删除联动处置中心部门信息
     *
     * @param id 联动处置中心部门主键
     * @return 结果
     */
    @Override
    public int deleteWarningDepartmentInfoById(Long id)
    {
        return warningDepartmentInfoMapper.deleteWarningDepartmentInfoById(id);
    }

    @Override
    public List<OrganizationNodeInfo> getDtalkParentDept() {
        try {
            return deptDtalkService.getOrganization();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }

        return Lists.newArrayList();
    }

    @Override
    public List<OrganizationNodeInfo> getDtalkSubDept(String organizationCode) {
        try {
            return deptDtalkService.getPageOrganization(organizationCode);
        } catch (ExecutionException e) {
            e.printStackTrace();
        }

        return Lists.newArrayList();
    }

    @Override
    public List<EmployeeInfo> getDtalkUserV2(String organizationCode, Integer pageNum, Integer pageSize) {
        return userDtalkService.getOrganizationUserList(organizationCode, pageSize, pageNum);
    }

    @Override
    public List<DtalkEmployeeVo> getDtalkUser(String organizationCode, Integer pageNum, Integer pageSize, String keywords) {
        return userDtalkService.pageSearchEmployee(pageNum, pageSize, organizationCode, keywords);
    }

    @Override
    public List<WarningDepartmentInfo> getDeptTree(Long parentId) {
        if (Objects.isNull(parentId)) {
            parentId = 0L;
        }

        return warningDepartmentInfoMapper.listByParentId(parentId);
    }

    @Override
    public List<SysArea> getAreaTree(Long parentId) {
        List<SysArea> sysAreaList = sysAreaMapper.getAreaTree(parentId);

        return sysAreaList;
    }

    @Override
    public List<DtalkEmployeeVo> listAllUser(String keywords, String deptId) {
        List<SysUser> sysUserList = sysUserMapper.listAllUser(keywords, deptId);
        if (CollectionUtils.isEmpty(sysUserList)) {
            return Lists.newArrayList();
        }

        List<DtalkEmployeeVo> resultList = Lists.newArrayList();
        for (SysUser sysUser : sysUserList) {
            DtalkEmployeeVo employeeVo = new DtalkEmployeeVo();
            employeeVo.setEmployeeCode(sysUser.getEmployeeCode());
            employeeVo.setEmployeeName(sysUser.getNickName());
            employeeVo.setAccountId(sysUser.getAccountId());
            employeeVo.setEmployeeMobile(sysUser.getPhonenumber());
            employeeVo.setDeptId(sysUser.getDeptId());

            SysDept dept = sysUser.getDept();
            if (Objects.nonNull(dept)) {
                employeeVo.setDeptName(dept.getDeptName());
                employeeVo.setAncestors(dept.getAncestors());
            }

            resultList.add(employeeVo);
        }

        return resultList;
    }

    @Override
    public List<WarningDepartmentInfo> listCopyDept(String keywords) {
        return warningDepartmentInfoMapper.listCopyDept(keywords);
    }
}
