package com.ruoyi.jiangshan.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.domain.BusinessFile;
import com.ruoyi.jiangshan.enums.FileTypeEnum;
import com.ruoyi.jiangshan.service.BusinessFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件接口
 */
@RestController
@RequestMapping("/file")
public class FileController {
    @Autowired
    private BusinessFileService businessFileService;

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 结果
     */
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(MultipartFile file)
    {
        if (file.isEmpty())
        {
            return AjaxResult.error("上传文件不能为空");
        }

        BusinessFile businessFile = businessFileService.uploadFile(file);

        if (null == businessFile) {
            return AjaxResult.error("上传失败");
        }

        AjaxResult ajax = AjaxResult.success("上传成功");
        ajax.put("data", businessFile);
        ajax.put("fileId", businessFile.getId());
        ajax.put("fileUrl", businessFile.getFileUrl());
        return ajax;
    }
}
