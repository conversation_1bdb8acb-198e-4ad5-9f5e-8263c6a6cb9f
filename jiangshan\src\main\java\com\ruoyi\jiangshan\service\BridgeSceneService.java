package com.ruoyi.jiangshan.service;

import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.domain.WarningInfo;
import com.ruoyi.jiangshan.vo.BridgeMaintanceVO;
import com.ruoyi.jiangshan.vo.WarningCountVO;
import com.ruoyi.jiangshan.vo.WarningInfoVO;

import java.lang.management.MonitorInfo;
import java.util.List;
import java.util.Map;

public interface BridgeSceneService {
    Map<String, Object> getEquipment();

    List<DeviceMonitor> getBridgeMonitor();

    WarningCountVO getSafetyCount(String dateStr);

    List<WarningInfoVO> getWarning(Integer warningLevel);

    List<WarningEvent> getEvent(String eventName, Integer status, Integer warningLevel);

    List<BridgeMaintanceVO> getMaintance(Long monitorId);

    List<DeviceMonitor> getAllBridge();

    List<DeviceInfo> listBridgeVideo(Long monitorId);

    List<DeviceInfo> listBridgeDevice();
}
