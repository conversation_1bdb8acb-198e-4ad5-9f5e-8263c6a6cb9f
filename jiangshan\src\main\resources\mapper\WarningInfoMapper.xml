<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningInfoMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningInfo">
    <!--@mbg.generated-->
    <!--@Table t_warning_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="condition_id" jdbcType="BIGINT" property="conditionId" />
    <result column="monitor_id" jdbcType="BIGINT" property="monitorId" />
    <result column="device_id" jdbcType="BIGINT" property="deviceId" />
    <result column="device_third_id" jdbcType="VARCHAR" property="deviceThirdId" />
    <result column="rule_condition" jdbcType="VARCHAR" property="ruleCondition" />
    <result column="monitor_item" jdbcType="VARCHAR" property="monitorItem" />
    <result column="monitor_value" jdbcType="VARCHAR" property="monitorValue" />
    <result column="monitor_unit" jdbcType="VARCHAR" property="monitorUnit" />
    <result column="warning_reason" jdbcType="VARCHAR" property="warningReason" />
    <result column="warning_level" jdbcType="BOOLEAN" property="warningLevel" />
    <result column="warning_time" jdbcType="TIMESTAMP" property="warningTime" />
    <result column="warning_address" jdbcType="VARCHAR" property="warningAddress" />
    <result column="warning_name" jdbcType="VARCHAR" property="warningName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="device_value_id" jdbcType="BIGINT" property="deviceValueId" />
    <result column="warning_data" jdbcType="VARCHAR" property="warningData" />
    <result column="warning_scene" jdbcType="VARCHAR" property="warningScene" />
    <result column="event_third_id" jdbcType="VARCHAR" property="eventThirdId" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="len_time" jdbcType="VARCHAR" property="lenTime" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="lon" jdbcType="VARCHAR" property="lon" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="event_street_area_code" jdbcType="VARCHAR" property="eventStreetAreaCode" />
    <result column="event_street" jdbcType="VARCHAR" property="eventStreet" />
    <result column="supervise_flag" jdbcType="INTEGER" property="superviseFlag" />
    <result column="over_flag" jdbcType="INTEGER" property="overFlag" />
  </resultMap>

  <resultMap id="BaseResultMap2" type="com.ruoyi.jiangshan.vo.WarningInfoVO">
    <!--@mbg.generated-->
    <!--@Table t_warning_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="condition_id" jdbcType="BIGINT" property="conditionId" />
    <result column="monitor_id" jdbcType="BIGINT" property="monitorId" />
    <result column="device_id" jdbcType="BIGINT" property="deviceId" />
    <result column="device_third_id" jdbcType="VARCHAR" property="deviceThirdId" />
    <result column="rule_condition" jdbcType="VARCHAR" property="ruleCondition" />
    <result column="monitor_item" jdbcType="VARCHAR" property="monitorItem" />
    <result column="monitor_value" jdbcType="VARCHAR" property="monitorValue" />
    <result column="monitor_unit" jdbcType="VARCHAR" property="monitorUnit" />
    <result column="warning_reason" jdbcType="VARCHAR" property="warningReason" />
    <result column="warning_level" jdbcType="BOOLEAN" property="warningLevel" />
    <result column="warning_time" jdbcType="TIMESTAMP" property="warningTime" />
    <result column="warning_address" jdbcType="VARCHAR" property="warningAddress" />
    <result column="warning_name" jdbcType="VARCHAR" property="warningName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="device_value_id" jdbcType="BIGINT" property="deviceValueId" />
    <result column="warning_data" jdbcType="VARCHAR" property="warningData" />
    <result column="warning_scene" jdbcType="VARCHAR" property="warningScene" />
    <result column="monitor_name" jdbcType="VARCHAR" property="monitorName" />
    <result column="monitor_type" jdbcType="VARCHAR" property="monitorType" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="lon" jdbcType="VARCHAR" property="lon" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, condition_id, monitor_id, device_id, device_third_id, device_value_id, rule_condition,
    monitor_item, monitor_value, monitor_unit, warning_reason, warning_level, warning_time,
    warning_address, warning_name, warning_data, warning_scene, create_time, create_by,
    event_third_id, model_name, len_time, start_time, end_time, lon, lat, event_street_area_code,
    event_street, fxqbh
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_info (condition_id, monitor_id, device_id,
    device_third_id, device_value_id, rule_condition,
    monitor_item, monitor_value, monitor_unit,
    warning_reason, warning_level, warning_time,
    warning_address, warning_name, warning_data,
    warning_scene, create_time, create_by,
    event_third_id, model_name, len_time,
    start_time, end_time, lon,
    lat, event_street_area_code, event_street, fxqbh
    )
    values (#{conditionId,jdbcType=BIGINT}, #{monitorId,jdbcType=BIGINT}, #{deviceId,jdbcType=BIGINT},
    #{deviceThirdId,jdbcType=VARCHAR}, #{deviceValueId,jdbcType=BIGINT}, #{ruleCondition,jdbcType=VARCHAR},
    #{monitorItem,jdbcType=VARCHAR}, #{monitorValue,jdbcType=VARCHAR}, #{monitorUnit,jdbcType=VARCHAR},
    #{warningReason,jdbcType=VARCHAR}, #{warningLevel,jdbcType=BOOLEAN}, #{warningTime,jdbcType=TIMESTAMP},
    #{warningAddress,jdbcType=VARCHAR}, #{warningName,jdbcType=VARCHAR}, #{warningData,jdbcType=VARCHAR},
    #{warningScene,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
    #{eventThirdId,jdbcType=VARCHAR}, #{modelName,jdbcType=VARCHAR}, #{lenTime,jdbcType=VARCHAR},
    #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{lon,jdbcType=VARCHAR},
    #{lat,jdbcType=VARCHAR}, #{eventStreetAreaCode,jdbcType=VARCHAR}, #{eventStreet,jdbcType=VARCHAR},
    #{fxqbh, jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="conditionId != null">
        condition_id,
      </if>
      <if test="monitorId != null">
        monitor_id,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="deviceThirdId != null">
        device_third_id,
      </if>
      <if test="deviceValueId != null">
        device_value_id,
      </if>
      <if test="ruleCondition != null">
        rule_condition,
      </if>
      <if test="monitorItem != null">
        monitor_item,
      </if>
      <if test="monitorValue != null">
        monitor_value,
      </if>
      <if test="monitorUnit != null">
        monitor_unit,
      </if>
      <if test="warningReason != null">
        warning_reason,
      </if>
      <if test="warningLevel != null">
        warning_level,
      </if>
      <if test="warningTime != null">
        warning_time,
      </if>
      <if test="warningAddress != null">
        warning_address,
      </if>
      <if test="warningName != null">
        warning_name,
      </if>
      <if test="warningData != null">
        warning_data,
      </if>
      <if test="warningScene != null">
        warning_scene,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="eventThirdId != null">
        event_third_id,
      </if>
      <if test="modelName != null">
        model_name,
      </if>
      <if test="lenTime != null">
        len_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="lon != null">
        lon,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="eventStreetAreaCode != null">
        event_street_area_code,
      </if>
      <if test="eventStreet != null">
        event_street,
      </if>
      <if test="fxqbh != null">
        fxqbh,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="conditionId != null">
        #{conditionId,jdbcType=BIGINT},
      </if>
      <if test="monitorId != null">
        #{monitorId,jdbcType=BIGINT},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=BIGINT},
      </if>
      <if test="deviceThirdId != null">
        #{deviceThirdId,jdbcType=VARCHAR},
      </if>
      <if test="deviceValueId != null">
        #{deviceValueId,jdbcType=BIGINT},
      </if>
      <if test="ruleCondition != null">
        #{ruleCondition,jdbcType=VARCHAR},
      </if>
      <if test="monitorItem != null">
        #{monitorItem,jdbcType=VARCHAR},
      </if>
      <if test="monitorValue != null">
        #{monitorValue,jdbcType=VARCHAR},
      </if>
      <if test="monitorUnit != null">
        #{monitorUnit,jdbcType=VARCHAR},
      </if>
      <if test="warningReason != null">
        #{warningReason,jdbcType=VARCHAR},
      </if>
      <if test="warningLevel != null">
        #{warningLevel,jdbcType=BOOLEAN},
      </if>
      <if test="warningTime != null">
        #{warningTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warningAddress != null">
        #{warningAddress,jdbcType=VARCHAR},
      </if>
      <if test="warningName != null">
        #{warningName,jdbcType=VARCHAR},
      </if>
      <if test="warningData != null">
        #{warningData,jdbcType=VARCHAR},
      </if>
      <if test="warningScene != null">
        #{warningScene,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="eventThirdId != null">
        #{eventThirdId,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="lenTime != null">
        #{lenTime,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lon != null">
        #{lon,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=VARCHAR},
      </if>
      <if test="eventStreetAreaCode != null">
        #{eventStreetAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="eventStreet != null">
        #{eventStreet,jdbcType=VARCHAR},
      </if>
      <if test="fxqbh != null">
        #{fxqbh,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningInfo">
    <!--@mbg.generated-->
    update t_warning_info
    <set>
      <if test="conditionId != null">
        condition_id = #{conditionId,jdbcType=BIGINT},
      </if>
      <if test="monitorId != null">
        monitor_id = #{monitorId,jdbcType=BIGINT},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=BIGINT},
      </if>
      <if test="deviceThirdId != null">
        device_third_id = #{deviceThirdId,jdbcType=VARCHAR},
      </if>
      <if test="deviceValueId != null">
        device_value_id = #{deviceValueId,jdbcType=BIGINT},
      </if>
      <if test="ruleCondition != null">
        rule_condition = #{ruleCondition,jdbcType=VARCHAR},
      </if>
      <if test="monitorItem != null">
        monitor_item = #{monitorItem,jdbcType=VARCHAR},
      </if>
      <if test="monitorValue != null">
        monitor_value = #{monitorValue,jdbcType=VARCHAR},
      </if>
      <if test="monitorUnit != null">
        monitor_unit = #{monitorUnit,jdbcType=VARCHAR},
      </if>
      <if test="warningReason != null">
        warning_reason = #{warningReason,jdbcType=VARCHAR},
      </if>
      <if test="warningLevel != null">
        warning_level = #{warningLevel,jdbcType=BOOLEAN},
      </if>
      <if test="warningTime != null">
        warning_time = #{warningTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warningAddress != null">
        warning_address = #{warningAddress,jdbcType=VARCHAR},
      </if>
      <if test="warningName != null">
        warning_name = #{warningName,jdbcType=VARCHAR},
      </if>
      <if test="warningData != null">
        warning_data = #{warningData,jdbcType=VARCHAR},
      </if>
      <if test="warningScene != null">
        warning_scene = #{warningScene,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="eventThirdId != null">
        event_third_id = #{eventThirdId,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="lenTime != null">
        len_time = #{lenTime,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lon != null">
        lon = #{lon,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=VARCHAR},
      </if>
      <if test="eventStreetAreaCode != null">
        event_street_area_code = #{eventStreetAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="eventStreet != null">
        event_street = #{eventStreet,jdbcType=VARCHAR},
      </if>
      <if test="fxqbh != null">
        fxqbh = #{fxqbh,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningInfo">
    <!--@mbg.generated-->
    update t_warning_info
    set condition_id = #{conditionId,jdbcType=BIGINT},
      monitor_id = #{monitorId,jdbcType=BIGINT},
      device_id = #{deviceId,jdbcType=BIGINT},
      device_third_id = #{deviceThirdId,jdbcType=VARCHAR},
      device_value_id = #{deviceValueId,jdbcType=BIGINT},
      rule_condition = #{ruleCondition,jdbcType=VARCHAR},
      monitor_item = #{monitorItem,jdbcType=VARCHAR},
      monitor_value = #{monitorValue,jdbcType=VARCHAR},
      monitor_unit = #{monitorUnit,jdbcType=VARCHAR},
      warning_reason = #{warningReason,jdbcType=VARCHAR},
      warning_level = #{warningLevel,jdbcType=BOOLEAN},
      warning_time = #{warningTime,jdbcType=TIMESTAMP},
      warning_address = #{warningAddress,jdbcType=VARCHAR},
      warning_name = #{warningName,jdbcType=VARCHAR},
      warning_data = #{warningData,jdbcType=VARCHAR},
      warning_scene = #{warningScene,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      event_third_id = #{eventThirdId,jdbcType=VARCHAR},
      model_name = #{modelName,jdbcType=VARCHAR},
      len_time = #{lenTime,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      lon = #{lon,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=VARCHAR},
      event_street_area_code = #{eventStreetAreaCode,jdbcType=VARCHAR},
      event_street = #{eventStreet,jdbcType=VARCHAR},
      fxqbh = #{fxqbh,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="condition_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.conditionId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="monitor_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="device_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="device_third_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceThirdId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="device_value_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceValueId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="rule_condition = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ruleCondition,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="monitor_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorItem,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="monitor_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorValue,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="monitor_unit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorUnit,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="warning_reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.warningReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="warning_level = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.warningLevel,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="warning_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.warningTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="warning_address = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.warningAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="warning_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.warningName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="warning_data = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.warningData,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="warning_scene = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.warningScene,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_third_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventThirdId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="model_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modelName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="len_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lenTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="start_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.startTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="end_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.endTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="lon = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lon,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="lat = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lat,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_street_area_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventStreetAreaCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="event_street = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.eventStreet,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="fxqbh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fxqbh,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_info
    (condition_id, monitor_id, device_id, device_third_id, device_value_id, rule_condition,
    monitor_item, monitor_value, monitor_unit, warning_reason, warning_level, warning_time,
    warning_address, warning_name, warning_data, warning_scene, create_time, create_by,
    event_third_id, model_name, len_time, start_time, end_time, lon, lat, event_street_area_code,
    event_street, fxqbh)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.conditionId,jdbcType=BIGINT}, #{item.monitorId,jdbcType=BIGINT}, #{item.deviceId,jdbcType=BIGINT},
      #{item.deviceThirdId,jdbcType=VARCHAR}, #{item.deviceValueId,jdbcType=BIGINT},
      #{item.ruleCondition,jdbcType=VARCHAR}, #{item.monitorItem,jdbcType=VARCHAR}, #{item.monitorValue,jdbcType=VARCHAR},
      #{item.monitorUnit,jdbcType=VARCHAR}, #{item.warningReason,jdbcType=VARCHAR}, #{item.warningLevel,jdbcType=BOOLEAN},
      #{item.warningTime,jdbcType=TIMESTAMP}, #{item.warningAddress,jdbcType=VARCHAR},
      #{item.warningName,jdbcType=VARCHAR}, #{item.warningData,jdbcType=VARCHAR}, #{item.warningScene,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.eventThirdId,jdbcType=VARCHAR},
      #{item.modelName,jdbcType=VARCHAR}, #{item.lenTime,jdbcType=VARCHAR}, #{item.startTime,jdbcType=TIMESTAMP},
      #{item.endTime,jdbcType=TIMESTAMP}, #{item.lon,jdbcType=VARCHAR}, #{item.lat,jdbcType=VARCHAR},
      #{item.eventStreetAreaCode,jdbcType=VARCHAR}, #{item.eventStreet,jdbcType=VARCHAR},
      #{item.fxqbh,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <select id="countByMonitorType" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
    select warning_level as `key`, count(1) as value, t2.monitor_type as `tmpKey`
    from t_warning_info t1 left join t_device_monitor t2
    on t1.monitor_id = t2.id
    where 1=1
      and t2.id is not null
      <if test="list != null and list.size() != 0">
        and t2.monitor_type in
        <foreach collection="list" item="item" open="(" separator="," close=")">
           #{item}
        </foreach>
      </if>
    <if test="startTime != null">
      and t1.warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and t1.warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
    group by t2.monitor_type, t1.warning_level;
  </select>

  <select id="listLastFourWarning" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_info
    order by warning_time desc
    limit 4
  </select>

  <select id="countAll" resultType="long">
    select count(1)
    from t_warning_info
  </select>

  <sql id="Base_Column_List_LEFT_JOIN">
    <!--@mbg.generated-->
    select t4.id, t1.condition_id, t1.monitor_id, t1.device_id, t1.device_third_id, t1.rule_condition, t1.monitor_item,
    t1.monitor_value, t1.monitor_unit, t1.warning_reason, t1.warning_level, t1.warning_time, t1.warning_address,
    t1.warning_name, t1.create_time, t1.create_by, t1.device_value_id, t1.warning_data, t1.warning_scene, if(t2.monitor_name is null, t1.warning_name, t2.monitor_name) as monitor_name,
    t2.monitor_type, t3.device_name, t1.lon, t1.lat
    from t_warning_info t1
    left join t_device_monitor t2 on t1.monitor_id = t2.id
    left join t_device_info t3 on t1.device_third_id = t3.device_third_id
    left join t_warning_event t4 on t4.warning_id = t1.id
  </sql>

  <select id="listByLevelAndScene" resultMap="BaseResultMap2">
    <include refid="Base_Column_List_LEFT_JOIN" />
    where 1=1
    <if test="warningLevel != null">
      and t1.warning_level = #{warningLevel}
    </if>
    and warning_scene = #{scene}
    and status not in (5, 6)
    order by t1.warning_time desc
    limit 10
  </select>

  <select id="countByMonitorScene" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
    select warning_level as `key`, count(1) as value, warning_scene as `tmpKey`
    from t_warning_info
    where 1=1
    <if test="scene != null and scene != ''">
      and warning_scene = #{scene}
    </if>
    <if test="startTime != null">
      and warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
    group by warning_level;
  </select>

  <select id="listByDate" resultType="com.ruoyi.jiangshan.vo.WarningLayerVO">
    select t1.id as id, t2.device_third_id as deviceThirdId, t2.device_name as deviceName, t1.warning_time as monitorTime,
    t1.monitor_unit as monitorUnit, t1.warning_data as warningData, t1.monitor_value as monitorValue,
    t1.warning_level as warningLevel, t2.lon as lon, t2.lat as lat,
    t3.department_id as departmentId, t3.department_name as departmentName, t1.warning_reason as warningReason,
    t1.warning_address as warningAddress, t1.warning_scene as warningType
    from t_warning_info t1
    left join  t_device_info t2 on t1.device_third_id = t2.device_third_id
    left join t_warning_event t3 on t1.id = t3.warning_id
    where 1=1
    <if test="startTime != null">
      and t1.warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and t1.warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
    <if test="scene != null and scene != ''">
      and t1.warning_scene = #{scene,jdbcType=VARCHAR}
    </if>
    group by t2.id
  </select>

  <select id="listLastByMonitorIdListAndDate" resultMap="BaseResultMap">
    select *
    from (select id, condition_id, monitor_id, device_id, device_third_id, rule_condition, monitor_item,
    monitor_value, monitor_unit, warning_reason, max(warning_level) as warning_level, warning_time, warning_address,
    warning_name, create_time, create_by, device_value_id, warning_data, warning_scene
    from (select distinct * from t_warning_info order by warning_level desc, warning_time desc) t3
    group by monitor_id) as t1
    where 1=1
    <if test="list != null and list.size() != 0">
    and monitor_id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    </if>
    <if test="startTime != null">
      and warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>
  <select id="countWarningLevelByScene" resultType="com.ruoyi.jiangshan.vo.WarningLevelCountVO">
    SELECT warning_level as `level`, count(warning_level) as `count`
    FROM t_warning_info
      JOIN t_device_info ON t_warning_info.device_third_id = t_device_info.device_third_id
    WHERE t_device_info.device_scene = #{scene}
    GROUP BY warning_level
    ORDER BY warning_level
  </select>

  <select id="countBySceneListAndDate" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
    SELECT warning_scene as tmpKey, warning_level as `key`, count(warning_level) as `value`
    FROM t_warning_info
    WHERE warning_scene in
    <foreach collection="sceneList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    <if test="monitorStartTime != null">
      and warning_time <![CDATA[ >= ]]> #{monitorStartTime,jdbcType=TIMESTAMP}
    </if>
    <if test="monitorEndTime != null">
      and warning_time <![CDATA[ <= ]]> #{monitorEndTime,jdbcType=TIMESTAMP}
    </if>
    GROUP BY warning_scene, warning_level
  </select>

  <select id="listReportLine" resultMap="BaseResultMap">
    SELECT t1.id, condition_id, monitor_id, device_id, t1.device_third_id, device_value_id, rule_condition,
    monitor_item, monitor_value, monitor_unit, warning_reason, t1.warning_level, t1.warning_time,
    warning_address, warning_name, warning_data, warning_scene, t1.create_time, t1.create_by,
    t1.event_third_id, model_name, len_time, start_time, end_time, t1.lon, t1.lat, t1.event_street_area_code,
    t1.event_street, t1.fxqbh, t2.supervise_flag, t2.over_flag
    FROM t_warning_info t1
    left join t_warning_event t2 on t1.id = t2.warning_id
    WHERE warning_scene in
    <foreach collection="sceneList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    <if test="monitorStartTime != null">
      and t1.warning_time <![CDATA[ >= ]]> #{monitorStartTime,jdbcType=TIMESTAMP}
    </if>
    <if test="monitorEndTime != null">
      and t1.warning_time <![CDATA[ <= ]]> #{monitorEndTime,jdbcType=TIMESTAMP}
    </if>
    <if test="warningLevelList != null and warningLevelList.size() > 0">
      and t1.warning_level in
      <foreach collection="warningLevelList" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
<!--    GROUP BY warning_scene, warning_level-->
  </select>

  <select id="listByWarningIdList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_info
    where id in
    <foreach collection="warningIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="getByEventThirdId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_info
    where event_third_id = #{eventThirdId}
  </select>

  <select id="listByDeviceThird" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_info
    where device_third_id in
    <foreach collection="deviceThirdIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="listByDeviceThirdListAndDate" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
    </include>
    from t_warning_info
    where device_third_id in
    <foreach collection="deviceThirdIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
    and warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    group by device_third_id, DATE(warning_time)
  </select>

  <select id="countByScene" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
    select warning_level as `key`, count(1) as value, warning_scene as `tmpKey`
    from t_warning_info
    where 1=1
    <if test="startTime != null">
      and warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
    group by warning_scene, warning_level;
  </select>

  <select id="listWarningOccupation" resultType="com.ruoyi.jiangshan.vo.WarningOccupationVO">
    select t2.id as eventId,
           t1.warning_data as monitorName,
           t1.monitor_value as licenceNumber,
           if(t2.status = 5, 1, 0) as handleFlag,
           t1.warning_address as warningAddress
    from t_warning_info t1
    left join t_warning_event t2
    on t1.id = t2.warning_id
    where t1.warning_scene = '消防场景'
    and t1.warning_name like '%占用事件%'
    and t1.device_value_id is null
    and t1.create_time >= '2025-01-21 15:00:00'
    order by t1.create_time desc
  </select>

  <select id="listByMonitorIdListAndDate" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_info
    where monitor_id in
    <foreach collection="monitorIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
    and warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    and warning_name like '%占用事件%'
  </select>

  <select id="checkExistByTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from t_warning_info
    where device_third_id = #{deviceThirdId}
    and monitor_item = #{monitorItem}
    and warning_time <![CDATA[ >= ]]> now() - INTERVAL ${hour} HOUR
    limit 1
  </select>

  <delete id="deleteByIds">
    delete from t_warning_info where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <select id="listPushValue" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
    </include>
    from t_warning_info
    where warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
    and warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    and device_value_id is not null
  </select>
</mapper>
