package com.ruoyi.jiangshan.service;

import java.util.List;
import com.ruoyi.jiangshan.domain.WarningModel;
import com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin;
import com.ruoyi.jiangshan.domain.WarningRuleShuxin;
import com.ruoyi.jiangshan.openapi.vo.WarningEventThresholdSaveVO;

/**
 * 设备模型Service接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface IWarningModelService
{
    /**
     * 查询设备模型
     *
     * @param id 设备模型主键
     * @return 设备模型
     */
    public WarningModel selectWarningModelById(Long id);

    /**
     * 查询设备模型列表
     *
     * @param warningModel 设备模型
     * @return 设备模型集合
     */
    public List<WarningModel> selectWarningModelList(WarningModel warningModel);

    /**
     * 新增设备模型
     *
     * @param warningModel 设备模型
     * @return 结果
     */
    public int insertWarningModel(WarningModel warningModel);

    /**
     * 修改设备模型
     *
     * @param warningModel 设备模型
     * @return 结果
     */
    public int updateWarningModel(WarningModel warningModel);

    /**
     * 批量删除设备模型
     *
     * @param ids 需要删除的设备模型主键集合
     * @return 结果
     */
    public int deleteWarningModelByIds(Long[] ids);

    /**
     * 删除设备模型信息
     *
     * @param id 设备模型主键
     * @return 结果
     */
    public int deleteWarningModelById(Long id);

    /**
     * 保存数芯模型
     * @param warningRuleShuxin
     * @return
     */
    int saveSxModel(WarningRuleShuxin warningRuleShuxin);

    /**
     * 查询数芯模型
     * @param id
     * @return
     */
    WarningRuleShuxin getSxModel(Long id);

    List<String> getArea();

    String saveThreshold(WarningEventThresholdSaveVO saveVO);
}
