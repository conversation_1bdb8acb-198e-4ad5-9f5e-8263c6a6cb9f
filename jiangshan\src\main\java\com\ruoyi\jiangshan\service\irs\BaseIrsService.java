package com.ruoyi.jiangshan.service.irs;

import com.ruoyi.jiangshan.domain.TbIrsResult;

import java.util.Map;

/**
 * irs请求
 *
 * <AUTHOR>
 * @date 2022年11月18日 15:22
 */
public interface BaseIrsService {

    /**
     * irs接口查询
     *
     * @param tbIrsResult
     * @return
     */
    Object actuator(TbIrsResult tbIrsResult, Map<String, String> keyParams);

    /**
     * 获取token
     *
     * @param appKey
     * @param appSecret
     * @return
     */
    Map<String, String> getProvinceToken(String appKey, String appSecret);

    void refreshRequestToken();

    Map<String, String> getRequestMap();

}
