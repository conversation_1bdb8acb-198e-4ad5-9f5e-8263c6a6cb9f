package com.ruoyi.jiangshan.service;

import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.domain.WarningModel;
import com.ruoyi.jiangshan.vo.*;
import com.ruoyi.jiangshan.vo.cityrisk.CityRiskAvgLineVO;
import com.ruoyi.jiangshan.vo.cityrisk.CityRiskSafetyEventVO;
import com.ruoyi.jiangshan.vo.cityrisk.CityRiskScreenVO;
import com.ruoyi.jiangshan.vo.cityrisk.CityRiskWarningLevelVO;

import java.util.List;

public interface CityRiskSceneService {

    List<WarningModel> getWarningModel();

    List<CityRiskWarningLevelVO> getWarningAnalysis();

    List<BusinessCountVO> getWarningTop5();

    CityRiskScreenVO getStreet(String areaCode);

    List<DeviceDepartmentVO> getDepartmentDevice(List<String> sceneList);

    List<WarningEvent> getEvent();

    void notifyMsg(String accountId);

    List<CityRiskSafetyEventVO> getSafety();

    CityRiskAvgLineVO getSafetyLine(String deviceThirdId, String monitorItem);
}
