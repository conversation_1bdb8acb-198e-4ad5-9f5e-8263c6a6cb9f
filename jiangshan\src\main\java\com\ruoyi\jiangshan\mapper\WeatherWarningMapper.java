package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WeatherWarning;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 气象预警+专业监测预警Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface WeatherWarningMapper
{
    /**
     * 查询气象预警+专业监测预警
     *
     * @param id 气象预警+专业监测预警主键
     * @return 气象预警+专业监测预警
     */
    public WeatherWarning selectWeatherWarningById(Long id);

    /**
     * 查询气象预警+专业监测预警列表
     *
     * @param weatherWarning 气象预警+专业监测预警
     * @return 气象预警+专业监测预警集合
     */
    public List<WeatherWarning> selectWeatherWarningList(WeatherWarning weatherWarning);

    /**
     * 新增气象预警+专业监测预警
     *
     * @param weatherWarning 气象预警+专业监测预警
     * @return 结果
     */
    public int insertWeatherWarning(WeatherWarning weatherWarning);

    /**
     * 修改气象预警+专业监测预警
     *
     * @param weatherWarning 气象预警+专业监测预警
     * @return 结果
     */
    public int updateWeatherWarning(WeatherWarning weatherWarning);

    /**
     * 删除气象预警+专业监测预警
     *
     * @param id 气象预警+专业监测预警主键
     * @return 结果
     */
    public int deleteWeatherWarningById(Long id);

    /**
     * 批量删除气象预警+专业监测预警
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWeatherWarningByIds(Long[] ids);

    /**
     * 根据预警ID查询是否存在
     *
     * @param warningId 预警ID
     * @return 结果
     */
    public WeatherWarning selectWeatherWarningByWarningId(String warningId);

    /**
     * 批量插入气象预警数据
     *
     * @param weatherWarningList 气象预警列表
     * @return 结果
     */
    public int batchInsertWeatherWarning(List<WeatherWarning> weatherWarningList);

    /**
     * 根据预警ID更新数据
     *
     * @param weatherWarning 气象预警
     * @return 结果
     */
    public int updateWeatherWarningByWarningId(WeatherWarning weatherWarning);
}
