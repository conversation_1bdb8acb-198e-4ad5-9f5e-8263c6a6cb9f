<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceInfoTypeMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.DeviceInfoType">
    <!--@mbg.generated-->
    <!--@Table t_device_info_type-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="monitor_type" jdbcType="VARCHAR" property="monitorType" />
    <result column="device_type" jdbcType="VARCHAR" property="deviceType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, monitor_type, device_type, create_time, create_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_device_info_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_device_info_type
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoType" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_type (monitor_type, device_type, create_time,
      create_by)
    values (#{monitorType,jdbcType=VARCHAR}, #{deviceType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoType" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="monitorType != null">
        monitor_type,
      </if>
      <if test="deviceType != null">
        device_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="monitorType != null">
        #{monitorType,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        #{deviceType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoType">
    <!--@mbg.generated-->
    update t_device_info_type
    <set>
      <if test="monitorType != null">
        monitor_type = #{monitorType,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        device_type = #{deviceType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoType">
    <!--@mbg.generated-->
    update t_device_info_type
    set monitor_type = #{monitorType,jdbcType=VARCHAR},
      device_type = #{deviceType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_device_info_type
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="monitor_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="device_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_type
    (monitor_type, device_type, create_time, create_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.monitorType,jdbcType=VARCHAR}, #{item.deviceType,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.createBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="listByScene" resultType="java.lang.String">
    select device_type
    from t_device_info_type
    where monitor_type = #{scene,jdbcType=VARCHAR}
  </select>
</mapper>
