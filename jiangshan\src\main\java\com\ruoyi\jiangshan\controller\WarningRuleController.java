package com.ruoyi.jiangshan.controller;

import java.util.List;

import com.ruoyi.framework.web.service.dtalk.UserDtalkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jiangshan.domain.WarningRule;
import com.ruoyi.jiangshan.service.IWarningRuleService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 设备规则Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/warningRule")
public class WarningRuleController extends BaseController
{
    @Autowired
    private IWarningRuleService warningRuleService;
    @Autowired
    private UserDtalkService userDtalkService;

    /**
     * 查询设备规则列表
     */
//    @PreAuthorize("@ss.hasPermi('warningRule:warningRule:list')")
    @GetMapping("/list")
    public TableDataInfo list(WarningRule warningRule)
    {
        startPage();
        List<WarningRule> list = warningRuleService.selectWarningRuleList(warningRule);
        return getDataTable(list);
    }

//    /**
//     * 导出设备规则列表
//     */
////    @PreAuthorize("@ss.hasPermi('warningRule:warningRule:export')")
//    @Log(title = "设备规则", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, WarningRule warningRule)
//    {
//        List<WarningRule> list = warningRuleService.selectWarningRuleList(warningRule);
//        ExcelUtil<WarningRule> util = new ExcelUtil<WarningRule>(WarningRule.class);
//        util.exportExcel(response, list, "设备规则数据");
//    }

    /**
     * 获取设备规则详细信息
     */
//    @PreAuthorize("@ss.hasPermi('warningRule:warningRule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(warningRuleService.selectWarningRuleById(id));
    }

    /**
     * 新增设备规则
     */
//    @PreAuthorize("@ss.hasPermi('warningRule:warningRule:add')")
    @Log(title = "设备规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WarningRule warningRule)
    {
        return toAjax(warningRuleService.insertWarningRule(warningRule));
    }

    /**
     * 修改设备规则
     */
//    @PreAuthorize("@ss.hasPermi('warningRule:warningRule:edit')")
    @Log(title = "设备规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WarningRule warningRule)
    {
        return toAjax(warningRuleService.updateWarningRule(warningRule));
    }

    /**
     * 删除设备规则
     */
//    @PreAuthorize("@ss.hasPermi('warningRule:warningRule:remove')")
    @Log(title = "设备规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(warningRuleService.deleteWarningRuleByIds(ids));
    }

    /**
     * 根据设备type获取监测项
     */
//    @PreAuthorize("@ss.hasPermi('warningRule:warningRule:query')")
    @GetMapping(value = "/monitorItem")
    public AjaxResult listMonitorItemByType(String type)
    {
        return success(warningRuleService.listMonitorItemByType(type));
    }

    /**
     * 发送工作通知
     */
//    @PreAuthorize("@ss.hasPermi('warningRule:warningRule:query')")
    @GetMapping(value = "/send")
    public AjaxResult sendDtalk(String receiverIds, String bizMsgId,
                                String title, String content, String singleUrl, String singlePcUrl)
    {
        return success(userDtalkService.workNotification(receiverIds, bizMsgId, title, content, singleUrl, singlePcUrl));
    }
}
