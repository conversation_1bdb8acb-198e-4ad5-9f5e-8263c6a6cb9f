package com.ruoyi.jiangshan.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.jiangshan.vo.BusinessDeviceValueLineVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.service.IDeviceValueService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 设备实时数据Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/deviceValue")
public class DeviceValueController extends BaseController
{
    @Autowired
    private IDeviceValueService deviceValueService;

    /**
     * 查询设备实时数据列表
     */
//    @PreAuthorize("@ss.hasPermi('deviceValue:deviceValue:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceValue deviceValue)
    {
        startPage();
        List<DeviceValue> list = deviceValueService.selectDeviceValueList(deviceValue);
        return getDataTable(list);
    }

    /**
     * 查询折线图数据
     */
//    @PreAuthorize("@ss.hasPermi('deviceValue:deviceValue:list')")
    @GetMapping("/getLineData")
    public AjaxResult getLineData(DeviceValue deviceValue)
    {
        List<BusinessDeviceValueLineVO> list = deviceValueService.getLineData(deviceValue);
        return AjaxResult.success(list);
    }

    /**
     * 导出设备实时数据列表
     */
//    @PreAuthorize("@ss.hasPermi('deviceValue:deviceValue:export')")
    @Log(title = "设备实时数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeviceValue deviceValue)
    {
        List<DeviceValue> list = deviceValueService.selectDeviceValueList(deviceValue);
        ExcelUtil<DeviceValue> util = new ExcelUtil<DeviceValue>(DeviceValue.class);
        util.exportExcel(response, list, "设备实时数据数据");
    }

    /**
     * 获取设备实时数据详细信息
     */
//    @PreAuthorize("@ss.hasPermi('deviceValue:deviceValue:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deviceValueService.selectDeviceValueById(id));
    }

//    /**
//     * 新增设备实时数据
//     */
//    @PreAuthorize("@ss.hasPermi('deviceValue:deviceValue:add')")
//    @Log(title = "设备实时数据", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody DeviceValue deviceValue)
//    {
//        return toAjax(deviceValueService.insertDeviceValue(deviceValue));
//    }
//
    /**
     * 修改设备实时数据
     */
//    @PreAuthorize("@ss.hasPermi('deviceValue:deviceValue:edit')")
    @Log(title = "设备实时数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeviceValue deviceValue)
    {
        return toAjax(deviceValueService.updateDeviceValue(deviceValue));
    }

    /**
     * 删除设备实时数据
     */
//    @PreAuthorize("@ss.hasPermi('deviceValue:deviceValue:remove')")
    @Log(title = "设备实时数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(deviceValueService.deleteDeviceValueByIds(ids));
    }
}
