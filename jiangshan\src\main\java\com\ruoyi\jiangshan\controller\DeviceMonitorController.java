package com.ruoyi.jiangshan.controller;

import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.jiangshan.util.DownloadUtil;
import com.ruoyi.jiangshan.vo.DeviceMonitorVO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.service.IDeviceMonitorService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;

/**
 * 设备监测对象Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/deviceMonitor")
public class DeviceMonitorController extends BaseController
{
    @Autowired
    private IDeviceMonitorService deviceMonitorService;

//    /**
//     * 查询设备监测对象列表
//     */
////    @PreAuthorize("@ss.hasPermi('deviceMonitor:deviceMonitor:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(DeviceMonitor deviceMonitor)
//    {
//        startPage();
//        List<DeviceMonitor> list = deviceMonitorService.selectDeviceMonitorList(deviceMonitor);
//        return getDataTable(list);
//    }

    /**
     * 查询所有监测对象
     */
//    @PreAuthorize("@ss.hasPermi('deviceMonitor:deviceMonitor:list')")
    @GetMapping("/listAll")
    public AjaxResult listAll(String keyword, String monitorScene)
    {
        List<DeviceMonitorVO> list = deviceMonitorService.listByScene(keyword, monitorScene);
        return AjaxResult.success(list);
    }

    /**
     * 导出设备监测对象列表
     */
//    @PreAuthorize("@ss.hasPermi('deviceMonitor:deviceMonitor:export')")
    @Log(title = "设备监测对象", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response)
    {
        List<DeviceMonitor> list = deviceMonitorService.listAll();
        ExcelUtil<DeviceMonitor> util = new ExcelUtil<DeviceMonitor>(DeviceMonitor.class);
        util.exportExcel(response, list, "设备监测对象数据");
    }

    /**
     * 导入设备监测对象
     */
    @PostMapping("/import")
    public AjaxResult problemImport(MultipartFile file) throws IOException {
        ExcelUtil<DeviceMonitor> util = new ExcelUtil<>(DeviceMonitor.class);
        List<DeviceMonitor> list = util.importExcel(file.getInputStream());
        deviceMonitorService.excelImport(list);
        return AjaxResult.success();
    }

    /**
     * 设备监测对象-下载模板
     */
    @PostMapping("/template")
    public void problemTemplate(HttpServletResponse response) {
        ExcelUtil<DeviceMonitor> util = new ExcelUtil<DeviceMonitor>(DeviceMonitor.class);
        util.exportExcel(response, Lists.newArrayList(), "设备监测对象模板");
    }

    /**
     * 获取设备监测对象详细信息
     */
//    @PreAuthorize("@ss.hasPermi('deviceMonitor:deviceMonitor:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deviceMonitorService.selectDeviceMonitorById(id));
    }

    /**
     * 新增设备监测对象
     */
//    @PreAuthorize("@ss.hasPermi('deviceMonitor:deviceMonitor:add')")
    @Log(title = "设备监测对象", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DeviceMonitor deviceMonitor)
    {
        return toAjax(deviceMonitorService.insertDeviceMonitor(deviceMonitor));
    }

    /**
     * 修改设备监测对象
     */
//    @PreAuthorize("@ss.hasPermi('deviceMonitor:deviceMonitor:edit')")
    @Log(title = "设备监测对象", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeviceMonitor deviceMonitor)
    {
        return toAjax(deviceMonitorService.updateDeviceMonitor(deviceMonitor));
    }

    /**
     * 删除设备监测对象
     */
//    @PreAuthorize("@ss.hasPermi('deviceMonitor:deviceMonitor:remove')")
    @Log(title = "设备监测对象", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(deviceMonitorService.deleteDeviceMonitorByIds(ids));
    }
}
