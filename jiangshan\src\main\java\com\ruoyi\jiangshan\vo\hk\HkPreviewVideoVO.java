package com.ruoyi.jiangshan.vo.hk;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class HkPreviewVideoVO {

    /**
     * 设备编号
     */
    private String cameraIndexCode;

    /**
     * 开始查询时间（ISO8601格式yyyy-MM-dd'T'HH:mm:ss.SSSXXX）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 结束查询时间（ISO8601格式yyyy-MM-dd'T'HH:mm:ss.SSSXXX）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 录像存储位置（0-中心存储，1-设备存储）,默认中心存储
     */
    private String recordLocation;

    /**
     * 取流协议（应用层协议)，“rtsp”:RTSP协议（标准rtsp协议常用于如开源工具vlc取流播放，非标rtsp协议一般用于对接海康sdk。
     * 标准rtsp协议与非标rtsp协议如何指定请参考expand字段。）；
     * “rtmp”:RTMP协议（RTMP回放要求录像片段连续，需要在URL后自行拼接beginTime=20210902T100303&endTime=20210902T100400，
     * 其中20210902T100303至20210902T100400为查询出有连续录像的时间段。对于不连续的录像，需要分段查询分段播放）；
     * “hls”:HLS协议（一般用于H5页面取流播放，HLS协议只支持海康SDK协议、EHOME协议、ONVIF协议接入的设备；只支持H264视频编码和AAC音频编码；
     * hls协议只支持云存储，不支持设备存储，云存储版本要求v2.2.4及以上的2.x版本，
     * 或v3.0.5及以上的3.x版本需在运管中心-视频联网共享中切换成启动平台外置VOD）；
     * “ws”-Websocket协议（一般用于H5视频播放器取流播放）。此字段未指定默认为rtsp协议
     */
    private String protocol;

    /**
     * 是否返回录像片段详细信息（默认false。当需要展示该时间段的录像片段信息时，需要填写true）
     */
    private Boolean needReturnClipInfo = true;

    /**
     * 上一次查询返回的uuid，用于继续查询剩余片段，默认为空字符串
     */
    private String uuid;

    /**
     * 此字段非必要不建议指定，
     * 当protocol为rtsp时：支持指定streamform=rtp，表示使用标准RTSP协议，典型如使用VLC播放。
     * 当使用海康取流播放工具如视频SDK时，请勿指定streamform=rtp。
     * 支持指定transcode=1，表示将H265编码视频转换成H264编码，典型使用场景如HLS播放。
     * 指定transcode=0表示不转码，默认为transcode=0。
     * 可以同时指定streamform与transcode，但二者必须使用“&”连接起来，如transcode=1&streamform=rtp
     */
    private String expand;
}
