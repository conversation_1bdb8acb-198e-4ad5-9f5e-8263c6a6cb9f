package com.ruoyi.quartz.task;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.time.DayOfWeek;
import java.util.stream.Collectors;

@Slf4j
@Component("deviceValueTask")
public class DeviceValueTask {
    // 每页数据量
    private static final int PAGE_SIZE = 10000;

    public void execute(String param) {
        PushSjbmMapper pushSjbmMapper = SpringUtils.getBean(PushSjbmMapper.class);
        DeviceValueMapper deviceValueMapper = SpringUtils.getBean(DeviceValueMapper.class);

        try {
            log.info("deviceValueTask, 开始执行设备数据周度同步任务");

            // 获取上一周的起止时间
            LocalDate now = LocalDate.now();
            LocalDate lastWeek = now.minusWeeks(1);
            LocalDateTime startTime = LocalDateTime.of(lastWeek.with(DayOfWeek.MONDAY), LocalTime.MIN);
            LocalDateTime endTime = LocalDateTime.of(lastWeek.with(DayOfWeek.SUNDAY), LocalTime.MAX);

            log.info("deviceValueTask, 同步时间范围: {} 至 {}",
                    startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 获取总记录数
            int totalCount = deviceValueMapper.countDeviceValueByTimeRange(startTime, endTime);
            log.info("deviceValueTask, 需要同步的数据总量: {}", totalCount);

            // 计算总页数
            int totalPages = (totalCount + PAGE_SIZE - 1) / PAGE_SIZE;

            log.info("deviceValueTask, totalPages: {}", totalPages);

            int start = 0;
            if (StringUtils.isNotBlank(param)) {
                start  = Integer.parseInt(param);
            }

            // 分页处理
            for (int pageNum = start; pageNum < totalPages; pageNum++) {
                int offset = pageNum * PAGE_SIZE;

                log.info("deviceValueTask, offset: {}", offset);

                // 获取分页数据
                List<DeviceValue> deviceValues = deviceValueMapper.getDeviceValueByTimeRange(
                        startTime,
                        endTime,
                        offset,
                        PAGE_SIZE
                );

                if (deviceValues != null && !deviceValues.isEmpty()) {
                    log.info("deviceValueTask, deviceValues: {}", deviceValues.size());

                    for (DeviceValue deviceValue : deviceValues) {
                        if (StringUtils.isBlank(deviceValue.getTarget())) {
                            deviceValue.setTarget("0");
                        }
                        if (Objects.isNull(deviceValue.getDeviceId())) {
                            deviceValue.setDeviceId(0L);
                        }
                        if (StringUtils.isBlank(deviceValue.getDeviceThirdId())) {
                            deviceValue.setDeviceThirdId("");
                        }
                        if (Objects.isNull(deviceValue.getMonitorId())) {
                            deviceValue.setMonitorId(0L);
                        }
                        if (StringUtils.isBlank(deviceValue.getMonitorObject())) {
                            deviceValue.setMonitorObject("");
                        }
                    }

                    // 批量插入数据
                    pushSjbmMapper.batchInsert(deviceValues);
                    log.info("deviceValueTask, 成功同步第 {} 页数据，数量: {}", pageNum + 1, deviceValues.size());
                }

                // 每处理完一页，暂停一下，避免数据库压力过大
                Thread.sleep(1000);
            }

            log.info("设备数据周度同步任务执行完成");

        } catch (Exception e) {
            log.error("设备数据周度同步任务执行失败", e);
        }
    }

    public void execute2(String param) {
        log.info("deviceValueTask, start");

        PushSjbmMapper pushSjbmMapper = SpringUtils.getBean(PushSjbmMapper.class);
        DeviceValueMapper deviceValueMapper = SpringUtils.getBean(DeviceValueMapper.class);
        WarningInfoMapper warningInfoMapper = SpringUtils.getBean(WarningInfoMapper.class);

        // 获取上一周的起止时间
        LocalDate now = LocalDate.now();
        LocalDate lastWeek = now.minusWeeks(1);
        LocalDateTime startTime = LocalDateTime.of(lastWeek.with(DayOfWeek.MONDAY), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(lastWeek.with(DayOfWeek.SUNDAY), LocalTime.MAX);

        List<WarningInfo> warningInfoList = warningInfoMapper.listPushValue(startTime, endTime);
        if (CollectionUtils.isEmpty(warningInfoList)) {
            return;
        }

        List<Long> deviceValueIdList = warningInfoList.stream()
                .map(WarningInfo::getDeviceValueId)
                .collect(Collectors.toList());

        List<DeviceValue> deviceValueList = deviceValueMapper.listByIdList(deviceValueIdList);
        if (CollectionUtils.isEmpty(deviceValueList)) {
            return;
        }

        for (DeviceValue deviceValue : deviceValueList) {
            if (StringUtils.isBlank(deviceValue.getTarget())) {
                deviceValue.setTarget("0");
            }
            if (Objects.isNull(deviceValue.getDeviceId())) {
                deviceValue.setDeviceId(0L);
            }
            if (StringUtils.isBlank(deviceValue.getDeviceThirdId())) {
                deviceValue.setDeviceThirdId("");
            }
            if (Objects.isNull(deviceValue.getMonitorId())) {
                deviceValue.setMonitorId(0L);
            }
            if (StringUtils.isBlank(deviceValue.getMonitorObject())) {
                deviceValue.setMonitorObject("");
            }
        }

        log.info("deviceValueTask, deviceValueList:{}", JSON.toJSONString(deviceValueList));

        // 批量插入数据
        pushSjbmMapper.batchInsert(deviceValueList);

        log.info("deviceValueTask, end");
    }

    public void pushDeviceMonitor(String param) {
        log.info("pushDeviceMonitor, start");

        PushSjbmMapper pushSjbmMapper = SpringUtils.getBean(PushSjbmMapper.class);
        DeviceMonitorMapper deviceMonitorMapper = SpringUtils.getBean(DeviceMonitorMapper.class);

        // 获取上一周的起止时间
        LocalDate now = LocalDate.now();
        LocalDate lastWeek = now.minusWeeks(1);
        LocalDateTime startTime = LocalDateTime.of(lastWeek.with(DayOfWeek.MONDAY), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(lastWeek.with(DayOfWeek.SUNDAY), LocalTime.MAX);

        if ("all".equals(param)) {
            startTime = null;
            endTime = null;
        }

        List<DeviceMonitor> deviceMonitorList = deviceMonitorMapper.listPushValue(startTime, endTime);
        if (CollectionUtils.isEmpty(deviceMonitorList)) {
            return;
        }

//        for (DeviceMonitor deviceMonitor : deviceMonitorList) {
//
//        }

        log.info("pushDeviceMonitor, deviceMonitorList:{}", JSON.toJSONString(deviceMonitorList));

        // 批量插入数据
        pushSjbmMapper.batchInsertDeviceMonitor(deviceMonitorList);

        log.info("pushDeviceMonitor, end");
    }

    public void pushDeviceInfo(String param) {
        log.info("pushDeviceInfo, start");

        PushSjbmMapper pushSjbmMapper = SpringUtils.getBean(PushSjbmMapper.class);
        DeviceInfoMapper deviceInfoMapper = SpringUtils.getBean(DeviceInfoMapper.class);

        // 获取上一周的起止时间
        LocalDate now = LocalDate.now();
        LocalDate lastWeek = now.minusWeeks(1);
        LocalDateTime startTime = LocalDateTime.of(lastWeek.with(DayOfWeek.MONDAY), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(lastWeek.with(DayOfWeek.SUNDAY), LocalTime.MAX);

        if ("all".equals(param)) {
            startTime = null;
            endTime = null;
        }

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listPushValue(startTime, endTime);
        if (CollectionUtils.isEmpty(deviceInfoList)) {
            return;
        }

//        for (DeviceMonitor deviceMonitor : deviceMonitorList) {
//
//        }

        log.info("pushDeviceInfo, deviceInfoList:{}", JSON.toJSONString(deviceInfoList));

        // 批量插入数据
        pushSjbmMapper.batchInsertDeviceInfo(deviceInfoList);

        log.info("pushDeviceInfo, end");
    }

    public void pushWarningEvent(String param) {
        log.info("pushWarningEvent, start");

        PushSjbmMapper pushSjbmMapper = SpringUtils.getBean(PushSjbmMapper.class);
        WarningEventMapper warningEventMapper = SpringUtils.getBean(WarningEventMapper.class);

        // 获取上一周的起止时间
        LocalDate now = LocalDate.now();
        LocalDate lastWeek = now.minusWeeks(1);
        LocalDateTime startTime = LocalDateTime.of(lastWeek.with(DayOfWeek.MONDAY), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(lastWeek.with(DayOfWeek.SUNDAY), LocalTime.MAX);

        if ("all".equals(param)) {
            startTime = null;
            endTime = null;
        }

        List<WarningEvent> warningEventList = warningEventMapper.listPushValue(startTime, endTime);
        if (CollectionUtils.isEmpty(warningEventList)) {
            return;
        }

//        for (DeviceMonitor deviceMonitor : deviceMonitorList) {
//
//        }

        log.info("pushWarningEvent, warningEventList:{}", JSON.toJSONString(warningEventList));

        // 批量插入数据
        pushSjbmMapper.batchInsertWarningEvent(warningEventList);

        log.info("pushWarningEvent, end");
    }

    public void pushWarningEventProcess(String param) {
        log.info("pushWarningEventProcess, start");

        PushSjbmMapper pushSjbmMapper = SpringUtils.getBean(PushSjbmMapper.class);
        WarningEventProcessMapper warningEventProcessMapper = SpringUtils.getBean(WarningEventProcessMapper.class);

        // 获取上一周的起止时间
        LocalDate now = LocalDate.now();
        LocalDate lastWeek = now.minusWeeks(1);
        LocalDateTime startTime = LocalDateTime.of(lastWeek.with(DayOfWeek.MONDAY), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(lastWeek.with(DayOfWeek.SUNDAY), LocalTime.MAX);

        if ("all".equals(param)) {
            startTime = null;
            endTime = null;
        }

        List<WarningEventProcess> warningEventProcessList = warningEventProcessMapper.listPushValue(startTime, endTime);
        if (CollectionUtils.isEmpty(warningEventProcessList)) {
            return;
        }

//        for (WarningEventProcess deviceMonitor : warningEventProcessList) {
//
//        }

        log.info("pushWarningEventProcess, warningEventProcessList:{}", JSON.toJSONString(warningEventProcessList));

        // 批量插入数据
        pushSjbmMapper.batchInsertWarningEventProcess(warningEventProcessList);

        log.info("pushWarningEventProcess, end");
    }
}
