package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.JiangshanRainfall;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 降雨量Mapper接口
 */
public interface JiangshanRainfallMapper {
    /**
     * 新增降雨量
     *
     * @param jiangshanRainfall 降雨量
     * @return 结果
     */
    int insert(JiangshanRainfall jiangshanRainfall);

    /**
     * 批量新增降雨量
     *
     * @param list 降雨量列表
     * @return 结果
     */
    int batchInsert(List<JiangshanRainfall> list);

    /**
     * 根据村名查询降雨量信息
     *
     * @param name 村名
     * @return 降雨量信息列表
     */
    List<JiangshanRainfall> selectByName(String name);

    List<JiangshanRainfall> listByNameAndHour(@Param("deviceStreet") String deviceStreet, @Param("hour") Integer hour);

    int batchInsertSelf(List<JiangshanRainfall> list);

    List<JiangshanRainfall> listSelfByTime(@Param("hour") Integer hour);
}
