<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.RiskPreventionZoneMapper">

    <resultMap type="RiskPreventionZone" id="RiskPreventionZoneResult">
        <result property="id"    column="id"    />
        <result property="num"    column="num"    />
        <result property="name"    column="name"    />
        <result property="areaCode"    column="area_code"    />
        <result property="szs"    column="szs"    />
        <result property="szqx"    column="szqx"    />
        <result property="szz"    column="szz"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="stability"    column="stability"    />
        <result property="datatype"    column="datatype"    />
        <result property="status"    column="status"    />
        <result property="affectedPeople"    column="affected_people"    />
        <result property="affectedFamily"    column="affected_family"    />
        <result property="affectedAsset"    column="affected_asset"    />
        <result property="permanentResidents"    column="permanent_residents"    />
        <result property="hazardAffectedBody"    column="hazard_affected_body"    />
        <result property="placeLeader"    column="place_leader"    />
        <result property="placeLeaderTel"    column="place_leader_tel"    />
        <result property="countyLeader"    column="county_leader"    />
        <result property="countyLeaderTel"    column="county_leader_tel"    />
        <result property="villagesLeader"    column="villages_leader"    />
        <result property="villageLeaderTel"    column="village_leader_tel"    />
        <result property="grassRoots"    column="grass_roots"    />
        <result property="grassRootsTel"    column="grass_roots_tel"    />
        <result property="handUnit"    column="hand_unit"    />
        <result property="cancelReason"    column="cancel_reason"    />
        <result property="cancelReasonOther"    column="cancel_reason_other"    />
        <result property="revokeDate"    column="revoke_date"    />
        <result property="commitDate"    column="commit_date"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="geo"    column="geo"    />
        <result property="pointGeo"    column="point_geo"    />
        <result property="oneHour1"    column="one_hour_1"    />
        <result property="oneHour2"    column="one_hour_2"    />
        <result property="oneHour3"    column="one_hour_3"    />
        <result property="threeHour1"    column="three_hour_1"    />
        <result property="threeHour2"    column="three_hour_2"    />
        <result property="threeHour3"    column="three_hour_3"    />
        <result property="sixHour1"    column="six_hour_1"    />
        <result property="sixHour2"    column="six_hour_2"    />
        <result property="sixHour3"    column="six_hour_3"    />
        <result property="twelveHour1"    column="twelve_hour_1"    />
        <result property="twelveHour2"    column="twelve_hour_2"    />
        <result property="twelveHour3"    column="twelve_hour_3"    />
        <result property="twentyFourHour1"    column="twenty_four_hour_1"    />
        <result property="twentyFourHour2"    column="twenty_four_hour_2"    />
        <result property="twentyFourHour3"    column="twenty_four_hour_3"    />
        <result property="syncTime"    column="sync_time"    />
    </resultMap>

    <sql id="selectRiskPreventionZoneVo">
        select id, num, name, area_code, szs, szqx, szz, risk_level, stability, datatype, status, affected_people, affected_family, affected_asset, permanent_residents, hazard_affected_body, place_leader, place_leader_tel, county_leader, county_leader_tel, villages_leader, village_leader_tel, grass_roots, grass_roots_tel, hand_unit, cancel_reason, cancel_reason_other, revoke_date, commit_date, create_time, update_time, geo, point_geo, one_hour_1, one_hour_2, one_hour_3, three_hour_1, three_hour_2, three_hour_3, six_hour_1, six_hour_2, six_hour_3, twelve_hour_1, twelve_hour_2, twelve_hour_3, twenty_four_hour_1, twenty_four_hour_2, twenty_four_hour_3, sync_time from t_risk_prevention_zone
    </sql>

    <select id="selectRiskPreventionZoneList" parameterType="RiskPreventionZone" resultMap="RiskPreventionZoneResult">
        <include refid="selectRiskPreventionZoneVo"/>
        <where>
            <if test="num != null  and num != ''"> and num like concat('%', #{num}, '%')</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="szs != null  and szs != ''"> and szs like concat('%', #{szs}, '%')</if>
            <if test="szqx != null  and szqx != ''"> and szqx like concat('%', #{szqx}, '%')</if>
            <if test="szz != null  and szz != ''"> and szz like concat('%', #{szz}, '%')</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="stability != null  and stability != ''"> and stability = #{stability}</if>
            <if test="datatype != null  and datatype != ''"> and datatype = #{datatype}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="hazardAffectedBody != null  and hazardAffectedBody != ''"> and hazard_affected_body = #{hazardAffectedBody}</if>
            <if test="placeLeader != null  and placeLeader != ''"> and place_leader like concat('%', #{placeLeader}, '%')</if>
            <if test="countyLeader != null  and countyLeader != ''"> and county_leader like concat('%', #{countyLeader}, '%')</if>
            <if test="villagesLeader != null  and villagesLeader != ''"> and villages_leader like concat('%', #{villagesLeader}, '%')</if>
            <if test="grassRoots != null  and grassRoots != ''"> and grass_roots like concat('%', #{grassRoots}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectRiskPreventionZoneById" parameterType="Long" resultMap="RiskPreventionZoneResult">
        <include refid="selectRiskPreventionZoneVo"/>
        where id = #{id}
    </select>

    <select id="selectRiskPreventionZoneByNum" parameterType="String" resultMap="RiskPreventionZoneResult">
        <include refid="selectRiskPreventionZoneVo"/>
        where num = #{num}
    </select>

    <insert id="insertRiskPreventionZone" parameterType="RiskPreventionZone" useGeneratedKeys="true" keyProperty="id">
        insert into t_risk_prevention_zone
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="num != null">num,</if>
            <if test="name != null">name,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="szs != null">szs,</if>
            <if test="szqx != null">szqx,</if>
            <if test="szz != null">szz,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="stability != null">stability,</if>
            <if test="datatype != null">datatype,</if>
            <if test="status != null">status,</if>
            <if test="affectedPeople != null">affected_people,</if>
            <if test="affectedFamily != null">affected_family,</if>
            <if test="affectedAsset != null">affected_asset,</if>
            <if test="permanentResidents != null">permanent_residents,</if>
            <if test="hazardAffectedBody != null">hazard_affected_body,</if>
            <if test="placeLeader != null">place_leader,</if>
            <if test="placeLeaderTel != null">place_leader_tel,</if>
            <if test="countyLeader != null">county_leader,</if>
            <if test="countyLeaderTel != null">county_leader_tel,</if>
            <if test="villagesLeader != null">villages_leader,</if>
            <if test="villageLeaderTel != null">village_leader_tel,</if>
            <if test="grassRoots != null">grass_roots,</if>
            <if test="grassRootsTel != null">grass_roots_tel,</if>
            <if test="handUnit != null">hand_unit,</if>
            <if test="cancelReason != null">cancel_reason,</if>
            <if test="cancelReasonOther != null">cancel_reason_other,</if>
            <if test="revokeDate != null">revoke_date,</if>
            <if test="commitDate != null">commit_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="geo != null">geo,</if>
            <if test="pointGeo != null">point_geo,</if>
            <if test="oneHour1 != null">one_hour_1,</if>
            <if test="oneHour2 != null">one_hour_2,</if>
            <if test="oneHour3 != null">one_hour_3,</if>
            <if test="threeHour1 != null">three_hour_1,</if>
            <if test="threeHour2 != null">three_hour_2,</if>
            <if test="threeHour3 != null">three_hour_3,</if>
            <if test="sixHour1 != null">six_hour_1,</if>
            <if test="sixHour2 != null">six_hour_2,</if>
            <if test="sixHour3 != null">six_hour_3,</if>
            <if test="twelveHour1 != null">twelve_hour_1,</if>
            <if test="twelveHour2 != null">twelve_hour_2,</if>
            <if test="twelveHour3 != null">twelve_hour_3,</if>
            <if test="twentyFourHour1 != null">twenty_four_hour_1,</if>
            <if test="twentyFourHour2 != null">twenty_four_hour_2,</if>
            <if test="twentyFourHour3 != null">twenty_four_hour_3,</if>
            <if test="syncTime != null">sync_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="num != null">#{num},</if>
            <if test="name != null">#{name},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="szs != null">#{szs},</if>
            <if test="szqx != null">#{szqx},</if>
            <if test="szz != null">#{szz},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="stability != null">#{stability},</if>
            <if test="datatype != null">#{datatype},</if>
            <if test="status != null">#{status},</if>
            <if test="affectedPeople != null">#{affectedPeople},</if>
            <if test="affectedFamily != null">#{affectedFamily},</if>
            <if test="affectedAsset != null">#{affectedAsset},</if>
            <if test="permanentResidents != null">#{permanentResidents},</if>
            <if test="hazardAffectedBody != null">#{hazardAffectedBody},</if>
            <if test="placeLeader != null">#{placeLeader},</if>
            <if test="placeLeaderTel != null">#{placeLeaderTel},</if>
            <if test="countyLeader != null">#{countyLeader},</if>
            <if test="countyLeaderTel != null">#{countyLeaderTel},</if>
            <if test="villagesLeader != null">#{villagesLeader},</if>
            <if test="villageLeaderTel != null">#{villageLeaderTel},</if>
            <if test="grassRoots != null">#{grassRoots},</if>
            <if test="grassRootsTel != null">#{grassRootsTel},</if>
            <if test="handUnit != null">#{handUnit},</if>
            <if test="cancelReason != null">#{cancelReason},</if>
            <if test="cancelReasonOther != null">#{cancelReasonOther},</if>
            <if test="revokeDate != null">#{revokeDate},</if>
            <if test="commitDate != null">#{commitDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="geo != null">#{geo},</if>
            <if test="pointGeo != null">#{pointGeo},</if>
            <if test="oneHour1 != null">#{oneHour1},</if>
            <if test="oneHour2 != null">#{oneHour2},</if>
            <if test="oneHour3 != null">#{oneHour3},</if>
            <if test="threeHour1 != null">#{threeHour1},</if>
            <if test="threeHour2 != null">#{threeHour2},</if>
            <if test="threeHour3 != null">#{threeHour3},</if>
            <if test="sixHour1 != null">#{sixHour1},</if>
            <if test="sixHour2 != null">#{sixHour2},</if>
            <if test="sixHour3 != null">#{sixHour3},</if>
            <if test="twelveHour1 != null">#{twelveHour1},</if>
            <if test="twelveHour2 != null">#{twelveHour2},</if>
            <if test="twelveHour3 != null">#{twelveHour3},</if>
            <if test="twentyFourHour1 != null">#{twentyFourHour1},</if>
            <if test="twentyFourHour2 != null">#{twentyFourHour2},</if>
            <if test="twentyFourHour3 != null">#{twentyFourHour3},</if>
            <if test="syncTime != null">#{syncTime},</if>
        </trim>
    </insert>

    <insert id="batchInsertRiskPreventionZone" parameterType="java.util.List">
        insert into t_risk_prevention_zone (num, name, area_code, szs, szqx, szz, risk_level, stability, datatype, status, affected_people, affected_family, affected_asset, permanent_residents, hazard_affected_body, place_leader, place_leader_tel, county_leader, county_leader_tel, villages_leader, village_leader_tel, grass_roots, grass_roots_tel, hand_unit, cancel_reason, cancel_reason_other, revoke_date, commit_date, create_time, update_time, geo, point_geo, one_hour_1, one_hour_2, one_hour_3, three_hour_1, three_hour_2, three_hour_3, six_hour_1, six_hour_2, six_hour_3, twelve_hour_1, twelve_hour_2, twelve_hour_3, twenty_four_hour_1, twenty_four_hour_2, twenty_four_hour_3, sync_time) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.num}, #{item.name}, #{item.areaCode}, #{item.szs}, #{item.szqx}, #{item.szz}, #{item.riskLevel}, #{item.stability}, #{item.datatype}, #{item.status}, #{item.affectedPeople}, #{item.affectedFamily}, #{item.affectedAsset}, #{item.permanentResidents}, #{item.hazardAffectedBody}, #{item.placeLeader}, #{item.placeLeaderTel}, #{item.countyLeader}, #{item.countyLeaderTel}, #{item.villagesLeader}, #{item.villageLeaderTel}, #{item.grassRoots}, #{item.grassRootsTel}, #{item.handUnit}, #{item.cancelReason}, #{item.cancelReasonOther}, #{item.revokeDate}, #{item.commitDate}, #{item.createTime}, #{item.updateTime}, #{item.geo}, #{item.pointGeo}, #{item.oneHour1}, #{item.oneHour2}, #{item.oneHour3}, #{item.threeHour1}, #{item.threeHour2}, #{item.threeHour3}, #{item.sixHour1}, #{item.sixHour2}, #{item.sixHour3}, #{item.twelveHour1}, #{item.twelveHour2}, #{item.twelveHour3}, #{item.twentyFourHour1}, #{item.twentyFourHour2}, #{item.twentyFourHour3}, #{item.syncTime})
        </foreach>
    </insert>

    <update id="updateRiskPreventionZone" parameterType="RiskPreventionZone">
        update t_risk_prevention_zone
        <trim prefix="SET" suffixOverrides=",">
            <if test="num != null">num = #{num},</if>
            <if test="name != null">name = #{name},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="szs != null">szs = #{szs},</if>
            <if test="szqx != null">szqx = #{szqx},</if>
            <if test="szz != null">szz = #{szz},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="stability != null">stability = #{stability},</if>
            <if test="datatype != null">datatype = #{datatype},</if>
            <if test="status != null">status = #{status},</if>
            <if test="affectedPeople != null">affected_people = #{affectedPeople},</if>
            <if test="affectedFamily != null">affected_family = #{affectedFamily},</if>
            <if test="affectedAsset != null">affected_asset = #{affectedAsset},</if>
            <if test="permanentResidents != null">permanent_residents = #{permanentResidents},</if>
            <if test="hazardAffectedBody != null">hazard_affected_body = #{hazardAffectedBody},</if>
            <if test="placeLeader != null">place_leader = #{placeLeader},</if>
            <if test="placeLeaderTel != null">place_leader_tel = #{placeLeaderTel},</if>
            <if test="countyLeader != null">county_leader = #{countyLeader},</if>
            <if test="countyLeaderTel != null">county_leader_tel = #{countyLeaderTel},</if>
            <if test="villagesLeader != null">villages_leader = #{villagesLeader},</if>
            <if test="villageLeaderTel != null">village_leader_tel = #{villageLeaderTel},</if>
            <if test="grassRoots != null">grass_roots = #{grassRoots},</if>
            <if test="grassRootsTel != null">grass_roots_tel = #{grassRootsTel},</if>
            <if test="handUnit != null">hand_unit = #{handUnit},</if>
            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>
            <if test="cancelReasonOther != null">cancel_reason_other = #{cancelReasonOther},</if>
            <if test="revokeDate != null">revoke_date = #{revokeDate},</if>
            <if test="commitDate != null">commit_date = #{commitDate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="geo != null">geo = #{geo},</if>
            <if test="pointGeo != null">point_geo = #{pointGeo},</if>
            <if test="oneHour1 != null">one_hour_1 = #{oneHour1},</if>
            <if test="oneHour2 != null">one_hour_2 = #{oneHour2},</if>
            <if test="oneHour3 != null">one_hour_3 = #{oneHour3},</if>
            <if test="threeHour1 != null">three_hour_1 = #{threeHour1},</if>
            <if test="threeHour2 != null">three_hour_2 = #{threeHour2},</if>
            <if test="threeHour3 != null">three_hour_3 = #{threeHour3},</if>
            <if test="sixHour1 != null">six_hour_1 = #{sixHour1},</if>
            <if test="sixHour2 != null">six_hour_2 = #{sixHour2},</if>
            <if test="sixHour3 != null">six_hour_3 = #{sixHour3},</if>
            <if test="twelveHour1 != null">twelve_hour_1 = #{twelveHour1},</if>
            <if test="twelveHour2 != null">twelve_hour_2 = #{twelveHour2},</if>
            <if test="twelveHour3 != null">twelve_hour_3 = #{twelveHour3},</if>
            <if test="twentyFourHour1 != null">twenty_four_hour_1 = #{twentyFourHour1},</if>
            <if test="twentyFourHour2 != null">twenty_four_hour_2 = #{twentyFourHour2},</if>
            <if test="twentyFourHour3 != null">twenty_four_hour_3 = #{twentyFourHour3},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateRiskPreventionZoneByNum" parameterType="RiskPreventionZone">
        update t_risk_prevention_zone
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="szs != null">szs = #{szs},</if>
            <if test="szqx != null">szqx = #{szqx},</if>
            <if test="szz != null">szz = #{szz},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="stability != null">stability = #{stability},</if>
            <if test="datatype != null">datatype = #{datatype},</if>
            <if test="status != null">status = #{status},</if>
            <if test="affectedPeople != null">affected_people = #{affectedPeople},</if>
            <if test="affectedFamily != null">affected_family = #{affectedFamily},</if>
            <if test="affectedAsset != null">affected_asset = #{affectedAsset},</if>
            <if test="permanentResidents != null">permanent_residents = #{permanentResidents},</if>
            <if test="hazardAffectedBody != null">hazard_affected_body = #{hazardAffectedBody},</if>
            <if test="placeLeader != null">place_leader = #{placeLeader},</if>
            <if test="placeLeaderTel != null">place_leader_tel = #{placeLeaderTel},</if>
            <if test="countyLeader != null">county_leader = #{countyLeader},</if>
            <if test="countyLeaderTel != null">county_leader_tel = #{countyLeaderTel},</if>
            <if test="villagesLeader != null">villages_leader = #{villagesLeader},</if>
            <if test="villageLeaderTel != null">village_leader_tel = #{villageLeaderTel},</if>
            <if test="grassRoots != null">grass_roots = #{grassRoots},</if>
            <if test="grassRootsTel != null">grass_roots_tel = #{grassRootsTel},</if>
            <if test="handUnit != null">hand_unit = #{handUnit},</if>
            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>
            <if test="cancelReasonOther != null">cancel_reason_other = #{cancelReasonOther},</if>
            <if test="revokeDate != null">revoke_date = #{revokeDate},</if>
            <if test="commitDate != null">commit_date = #{commitDate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="geo != null">geo = #{geo},</if>
            <if test="pointGeo != null">point_geo = #{pointGeo},</if>
            <if test="oneHour1 != null">one_hour_1 = #{oneHour1},</if>
            <if test="oneHour2 != null">one_hour_2 = #{oneHour2},</if>
            <if test="oneHour3 != null">one_hour_3 = #{oneHour3},</if>
            <if test="threeHour1 != null">three_hour_1 = #{threeHour1},</if>
            <if test="threeHour2 != null">three_hour_2 = #{threeHour2},</if>
            <if test="threeHour3 != null">three_hour_3 = #{threeHour3},</if>
            <if test="sixHour1 != null">six_hour_1 = #{sixHour1},</if>
            <if test="sixHour2 != null">six_hour_2 = #{sixHour2},</if>
            <if test="sixHour3 != null">six_hour_3 = #{sixHour3},</if>
            <if test="twelveHour1 != null">twelve_hour_1 = #{twelveHour1},</if>
            <if test="twelveHour2 != null">twelve_hour_2 = #{twelveHour2},</if>
            <if test="twelveHour3 != null">twelve_hour_3 = #{twelveHour3},</if>
            <if test="twentyFourHour1 != null">twenty_four_hour_1 = #{twentyFourHour1},</if>
            <if test="twentyFourHour2 != null">twenty_four_hour_2 = #{twentyFourHour2},</if>
            <if test="twentyFourHour3 != null">twenty_four_hour_3 = #{twentyFourHour3},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
        </trim>
        where num = #{num}
    </update>

    <delete id="deleteRiskPreventionZoneById" parameterType="Long">
        delete from t_risk_prevention_zone where id = #{id}
    </delete>

    <delete id="deleteRiskPreventionZoneByIds" parameterType="String">
        delete from t_risk_prevention_zone where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
