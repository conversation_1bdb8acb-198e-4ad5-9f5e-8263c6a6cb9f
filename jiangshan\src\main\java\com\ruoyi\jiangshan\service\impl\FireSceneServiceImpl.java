package com.ruoyi.jiangshan.service.impl;

import com.google.common.collect.Lists;
import com.ruoyi.common.dto.DateDTO;
import com.ruoyi.common.dto.TimeRange;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.domain.WarningInfo;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.service.FireSceneService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class FireSceneServiceImpl implements FireSceneService {
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private IWarningEventService warningEventService;
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;

    private static final String SCENE = MonitorSceneType.SCENE_07.getDesc();
    private static List<String> DEVICE_TYPE_LIST = Lists.newArrayList("智能消火栓监测设备");

    @Autowired
    private DeviceInfoTypeMapper deviceInfoTypeMapper;
    @PostConstruct
    public void init() {
        DEVICE_TYPE_LIST = deviceInfoTypeMapper.listByScene(SCENE);
    }

    @Override
    public List<WarningEvent> getMonitor(Long monitorId) {
        // TODO: 2024/8/30
        List<WarningEvent> eventList = warningEventMapper.listByEventType(SCENE);

        return eventList;
    }

    @Override
    public List<WarningOccupationVO> getOccupationList() {
        List<WarningOccupationVO> list = warningInfoMapper.listWarningOccupation();

        return list;
    }

    @Override
    public List<BusinessCountVO> getOccupationCount(String dateStr) {
        List<DeviceMonitor> monitorList = deviceMonitorMapper.listByType(Lists.newArrayList("消防登高面"));
        if (CollectionUtils.isEmpty(monitorList)) {
            return Lists.newArrayList();
        }

        List<Long> monitorIdList = monitorList.stream()
                .map(DeviceMonitor::getId)
                .collect(Collectors.toList());

        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), dateStr);

        List<WarningInfo> warningInfoList = warningInfoMapper.listByMonitorIdListAndDate(monitorIdList,
                dateDTO.getStartTime(), dateDTO.getEndTime());
        if (CollectionUtils.isEmpty(warningInfoList)) {
            return Lists.newArrayList();
        }

        Map<Long, List<WarningInfo>> warningMap = warningInfoList.stream()
                .collect(Collectors.groupingBy(WarningInfo::getMonitorId));

        List<BusinessCountVO> countVOList = Lists.newArrayList();
        for (DeviceMonitor deviceMonitor : monitorList) {
            List<WarningInfo> warningOneList = warningMap.get(deviceMonitor.getId());

            BusinessCountVO businessCountVO = new BusinessCountVO();
            businessCountVO.setKey(deviceMonitor.getMonitorName());
            if (CollectionUtils.isNotEmpty(warningOneList)) {
                businessCountVO.setValue((long) warningOneList.size());
            } else {
                businessCountVO.setValue(0L);
            }

            countVOList.add(businessCountVO);
        }

        return countVOList;
    }

    @Override
    public List<WarningInfoVO> getWarning(Integer warningLevel) {
        return warningInfoMapper.listByLevelAndScene(warningLevel, SCENE);
    }

    @Override
    public WarningCountVO getSafetyCount(String dateStr) {
        return warningEventService.getSafetyCount(dateStr, SCENE);
    }

    @Override
    public List<BusinessMultiLineVO> getStatistics() {
        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByDeviceType("智能消火栓监测设备");

        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), "近7天");
        List<TimeRange> timeRangeList = DateUtils.generateDateRanges(dateDTO.getStartTime(), dateDTO.getEndTime(), 2);

        List<BusinessMultiLineVO> resultList = Lists.newArrayList();

        //查询时间范围内是否存在预警
        List<String> deviceThirdIdList = deviceInfoList.stream()
                .map(DeviceInfo::getDeviceThirdId)
                .collect(Collectors.toList());

        List<WarningInfo> warningInfoList = warningInfoMapper
                .listByDeviceThirdListAndDate(deviceThirdIdList, dateDTO.getStartTime(), dateDTO.getEndTime());

        for (TimeRange timeRange : timeRangeList) {
            long totalCount = deviceInfoList.size();
            long warningCount = 0L;

            if (CollectionUtils.isNotEmpty(warningInfoList)) {
                for (WarningInfo warningInfo : warningInfoList) {
                    if (warningInfo.getWarningTime().after(timeRange.getStartTime())
                            && warningInfo.getWarningTime().before(timeRange.getEndTime())) {
                        warningCount ++;
                    }
                }
            }

            resultList.add(new BusinessMultiLineVO(timeRange.getLabel(),
                    (long) deviceInfoList.size() - warningCount, warningCount));
        }

        return resultList;
    }

    @Override
    public Map<String, List<DeviceInfo>> getDeviceList() {
        Map<String, List<DeviceInfo>> resultMap = new HashMap<>();

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByDeviceType("智能消火栓监测设备");
        if (CollectionUtils.isEmpty(deviceInfoList)) {
            return resultMap;
        }

        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), "日");

        //查询时间范围内是否存在预警
        List<String> deviceThirdIdList = deviceInfoList.stream()
                .map(DeviceInfo::getDeviceThirdId)
                .collect(Collectors.toList());

        List<WarningInfo> warningInfoList = warningInfoMapper
                .listByDeviceThirdListAndDate(deviceThirdIdList, dateDTO.getStartTime(), dateDTO.getEndTime());

        if (CollectionUtils.isNotEmpty(warningInfoList)) {
            Map<String, List<WarningInfo>> warningMap = warningInfoList.stream()
                    .collect(Collectors.groupingBy(WarningInfo::getDeviceThirdId));
            for (DeviceInfo deviceInfo : deviceInfoList) {
                List<WarningInfo> warningInfoOneList = warningMap.get(deviceInfo.getDeviceThirdId());
                if (CollectionUtils.isNotEmpty(warningInfoOneList)) {
                    deviceInfo.setWarningFlag(true);
                }
            }
        }

        List<DeviceInfo> warningList = deviceInfoList.stream()
                .filter(DeviceInfo::getWarningFlag)
                .collect(Collectors.toList());

        deviceInfoList.removeAll(warningList);

        resultMap.put("warningList", warningList);
        resultMap.put("normalList", deviceInfoList);

        return resultMap;
    }
}
