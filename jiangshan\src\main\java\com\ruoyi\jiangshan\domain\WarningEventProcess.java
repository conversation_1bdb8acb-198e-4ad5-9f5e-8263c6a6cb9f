package com.ruoyi.jiangshan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.jiangshan.vo.WarningEventProcessContentVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 设备预警事件-流程
 */
@Data
public class WarningEventProcess {
    /**
     * 主键
     */
    private Long id;

    /**
     * 事件id
     */
    private Long eventId;

    /**
     * 处置部门id
     */
    private Long departmentId;

    /**
     * 处置部门name
     */
    private String departmentName;

    /**
     * 人员id
     */
    private String userId;

    /**
     * 人员name
     */
    private String userName;

    /**
     * 联系方式
     */
    private String userMobile;

    /**
     * 流程步骤 1待签收-核实 2待核实 3待签收-处置 4处置中
     */
    private Integer processStep;

    /**
     * 流程意见
     */
    private String processContent;

    /**
     * 流程完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processTime;

    /**
     * 退回原因
     */
    private String rejectContent;

    /**
     * 挂起原因
     */
    private String stopContent;

    /**
     * 挂起开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stopStartTime;

    /**
     * 挂起结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stopEndTime;

    /**
     * 状态 0-待处置 1-已退回 2-已挂起 3-已完成
     */
    private Integer status;

    /**
     * 超期状态 0-未超期 1-已超期
     */
    private Integer overFlag;

    /**
     * 督办人员id
     */
    private Long superviseUserId;

    /**
     * 督办人员
     */
    private String superviseUser;

    /**
     * 督办意见
     */
    private String superviseContent;

    /**
     * 预期完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedCompleteTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    private List<WarningEventDepartment> departmentList;

    private List<BusinessFile> fileList;

    private List<WarningEventProcessContentVO> contentList;

    private Integer signFlag = 0;

}
