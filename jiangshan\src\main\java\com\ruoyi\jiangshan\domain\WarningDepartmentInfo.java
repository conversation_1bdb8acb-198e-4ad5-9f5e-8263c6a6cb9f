package com.ruoyi.jiangshan.domain;

import java.util.Date;

/**
 * 联动处置中心部门表
 */
public class WarningDepartmentInfo {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 部门树关系
     */
    private String ancestors;

    /**
     * 顺序
     */
    private Integer orderNum;

    /**
     * 浙政钉部门code
     */
    private String organizationCode;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 分管领导id
     */
    private Long leaderId;

    /**
     * 分管领导
     */
    private String leaderName;

    /**
     * 分管领导手机号
     */
    private String leaderMobile;

    /**
     * 分管领导accountId
     */
    private String leaderAccountId;

    /**
     * 分管领导employeeCode
     */
    private String leaderEmployeeCode;

    /**
     * 信息员id
     */
    private Long messengerId;

    /**
     * 信息员
     */
    private String messengerName;

    /**
     * 信息员手机号
     */
    private String messengerMobile;

    /**
     * 信息员accountId
     */
    private String messengerAccountId;

    /**
     * 信息员employeeCode
     */
    private String messengerEmployeeCode;

    /**
     * 行政区划
     */
    private String area;

    /**
     * 行政区划code
     */
    private String areaCode;

    /**
     * 监管行业
     */
    private String regulatedIndustry;

    /**
     * 主要职责
     */
    private String mainDuty;

    /**
     * 综合评分
     */
    private String score;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Long getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(Long leaderId) {
        this.leaderId = leaderId;
    }

    public String getLeaderName() {
        return leaderName;
    }

    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName;
    }

    public String getLeaderMobile() {
        return leaderMobile;
    }

    public void setLeaderMobile(String leaderMobile) {
        this.leaderMobile = leaderMobile;
    }

    public String getLeaderAccountId() {
        return leaderAccountId;
    }

    public void setLeaderAccountId(String leaderAccountId) {
        this.leaderAccountId = leaderAccountId;
    }

    public String getLeaderEmployeeCode() {
        return leaderEmployeeCode;
    }

    public void setLeaderEmployeeCode(String leaderEmployeeCode) {
        this.leaderEmployeeCode = leaderEmployeeCode;
    }

    public Long getMessengerId() {
        return messengerId;
    }

    public void setMessengerId(Long messengerId) {
        this.messengerId = messengerId;
    }

    public String getMessengerName() {
        return messengerName;
    }

    public void setMessengerName(String messengerName) {
        this.messengerName = messengerName;
    }

    public String getMessengerMobile() {
        return messengerMobile;
    }

    public void setMessengerMobile(String messengerMobile) {
        this.messengerMobile = messengerMobile;
    }

    public String getMessengerAccountId() {
        return messengerAccountId;
    }

    public void setMessengerAccountId(String messengerAccountId) {
        this.messengerAccountId = messengerAccountId;
    }

    public String getMessengerEmployeeCode() {
        return messengerEmployeeCode;
    }

    public void setMessengerEmployeeCode(String messengerEmployeeCode) {
        this.messengerEmployeeCode = messengerEmployeeCode;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getRegulatedIndustry() {
        return regulatedIndustry;
    }

    public void setRegulatedIndustry(String regulatedIndustry) {
        this.regulatedIndustry = regulatedIndustry;
    }

    public String getMainDuty() {
        return mainDuty;
    }

    public void setMainDuty(String mainDuty) {
        this.mainDuty = mainDuty;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
}