package com.ruoyi.jiangshan.service.impl;

import com.google.common.collect.Lists;
import com.ruoyi.common.dto.DateDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.service.IDeviceValueService;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.service.PiplineSceneService;
import com.ruoyi.jiangshan.vo.WarningCountVO;
import com.ruoyi.jiangshan.vo.WarningInfoVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

@Service
public class PiplineSceneServiceImpl implements PiplineSceneService {
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Autowired
    private DeviceValueMapper deviceValueMapper;
    @Autowired
    private IWarningEventService warningEventService;
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private IDeviceValueService deviceValueService;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private DeviceInfoTypeMapper deviceInfoTypeMapper;

    private final static List<String> TYPE_LIST = Lists.newArrayList("污水管", "雨水管");
    private final static String SCENE = MonitorSceneType.SCENE_05.getDesc();
    private static List<String> DEVICE_TYPE_LIST = Lists.newArrayList("雷达液位计", "多普勒流量计");

    @PostConstruct
    public void init() {
        DEVICE_TYPE_LIST = deviceInfoTypeMapper.listByScene(SCENE);
    }

    @Override
    public Map<String, Object> getPiplineDetail() {
        Map<String, Object> resultMap = new HashMap<>();

        List<DeviceMonitor> monitorList = deviceMonitorMapper.listByType(TYPE_LIST);
        if (CollectionUtils.isNotEmpty(monitorList)) {
//            BigDecimal totalLength = monitorList.stream()
//                    .peek(monitor -> {
//                        if (StringUtils.isBlank(monitor.getScale())) {
//                            monitor.setScale("0");
//                        }
//                    })
//                    .map(monitor -> new BigDecimal(monitor.getScale()))
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//            resultMap.put("totalLength", totalLength);
            resultMap.put("totalLength", monitorList.size());
        } else {
            resultMap.put("totalLength", 0);
        }

        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), "日");

        String count01 = deviceValueMapper.getMaxValueByDeviceTypeAndMonitorItem(DEVICE_TYPE_LIST,
                "液位高", dateDTO.getStartTime(), dateDTO.getEndTime());
        String count02 = deviceValueMapper.getMaxValueByDeviceTypeAndMonitorItem(DEVICE_TYPE_LIST,
                "瞬时流量", dateDTO.getStartTime(), dateDTO.getEndTime());

        resultMap.put("maxLevel", count01);
        resultMap.put("maxFlow", count02);

        return resultMap;
    }

    @Override
    public WarningCountVO getSafetyCount(String dateStr) {
        return warningEventService.getSafetyCount(dateStr, SCENE);
    }

    @Override
    public List<WarningEvent> getEvent(String eventName, Integer status, Integer warningLevel) {
        return warningEventMapper.listByConditionLimit(SCENE, eventName, status, warningLevel, 3);
    }

    @Override
    public List<WarningInfoVO> getWarning(Integer warningLevel) {
        return warningInfoMapper.listByLevelAndScene(warningLevel, SCENE);
    }

    @Override
    public Map<String, Object> getFlowMonitor(String dateStr, Long deviceId) {
        Map<String, Object> monitorMap = deviceValueService.getMonitor(dateStr, deviceId, null);

        //获取最新的设备数据
        DeviceInfo deviceInfo = deviceInfoMapper.getById(deviceId);

        String deviceThirdId = deviceInfo.getDeviceThirdId();

        String lastValue = deviceValueMapper.getLastValueByDeviceThirdId(deviceThirdId, "瞬时流量");
        if (StringUtils.isBlank(lastValue)) {
            monitorMap.put("value", "0m^3/s");
        } else {
            monitorMap.put("value", lastValue);
        }

        return monitorMap;
    }

    @Override
    public Map<String, Object> getLevelMonitor(String dateStr, Long deviceId) {
        Map<String, Object> monitorMap = deviceValueService.getMonitor(dateStr, deviceId, null);

        //获取最新的设备数据
        DeviceInfo deviceInfo = deviceInfoMapper.getById(deviceId);

        String deviceThirdId = deviceInfo.getDeviceThirdId();

        String lastValue = deviceValueMapper.getLastValueByDeviceThirdId(deviceThirdId, "液位高");
        if (StringUtils.isBlank(lastValue)) {
            monitorMap.put("value", "0m");
        } else {
            monitorMap.put("value", lastValue);
        }

        return monitorMap;
    }

    @Override
    public List<DeviceInfo> getDevice(String deviceType) {
        return deviceInfoMapper.listByDeviceType(deviceType);
    }

    @Override
    public List<DeviceInfo> getDeviceByScene(String scene) {
        List<String> deviceTypeList = deviceInfoTypeMapper.listByScene(scene);

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByDeviceTypeList(deviceTypeList);

        return deviceInfoList;
    }
}
