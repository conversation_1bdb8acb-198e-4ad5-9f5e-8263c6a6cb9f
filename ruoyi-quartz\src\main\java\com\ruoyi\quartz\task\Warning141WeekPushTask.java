package com.ruoyi.quartz.task;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.mapper.DeviceValueMapper;
import com.ruoyi.jiangshan.mapper.PushSjbmMapper;
import com.ruoyi.jiangshan.mapper.WarningEventMapper;
import com.ruoyi.jiangshan.service.impl.WarningEventServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component("warning141WeekPushTask")
public class Warning141WeekPushTask {

    public void execute(String param) {
        WarningEventMapper warningEventMapper = SpringUtils.getBean(WarningEventMapper.class);
        WarningEventServiceImpl warningEventService = SpringUtils.getBean(WarningEventServiceImpl.class);

        // 1. 计算上周的起止时间
        LocalDate today = LocalDate.now();
        LocalDate lastMonday = today.minusWeeks(1).with(java.time.DayOfWeek.MONDAY);
        LocalDate lastSunday = lastMonday.plusDays(6);
        LocalDateTime startTime = lastMonday.atStartOfDay();
        LocalDateTime endTime = lastSunday.atTime(LocalTime.MAX);
        java.util.Date startDate = java.sql.Timestamp.valueOf(startTime);
        java.util.Date endDate = java.sql.Timestamp.valueOf(endTime);

        // 2. 查询本周所有事件（可根据实际业务调整scene等参数）
        List<WarningEvent> weekEvents = warningEventMapper.listByDateWithOutModel(startDate, endDate);

        // 3. 统计各类数据
        // 设备名称+类型分组
        Map<String, List<WarningEvent>> deviceTypeMap = weekEvents.stream().collect(
                java.util.stream.Collectors.groupingBy(e -> (e.getDeviceName() + "-" + e.getEventType())));

        // 4. 生成并推送事件（这里只做演示，实际可根据业务组装事件内容）
        String title = DateUtils.formatDateV2(endDate);

        WarningEvent pushEvent = new WarningEvent();
        pushEvent.setBizCode("JSCSAQ-ZB-" + title);
        pushEvent.setEventName("江山城市安全平台" + title + "周报");
        pushEvent.setEventAddress("江山市");
        pushEvent.setLat("28.755549");
        pushEvent.setLon("118.632804");
        pushEvent.setWarningTime(endDate);

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, List<WarningEvent>> entry : deviceTypeMap.entrySet()) {
            String key = entry.getKey();
            List<WarningEvent> deviceEvents = entry.getValue();
            String[] arr = key.split("-");
            String deviceName = arr.length > 0 ? arr[0] : "";
            String eventType = arr.length > 1 ? arr[1] : "";

            // 新增事件数
            long newCount = deviceEvents.size();
            // 已办结事件数（状态5=已完成）
            long finishedCount = deviceEvents.stream()
                    .filter(e -> e.getStatus() != null && (e.getStatus() == 5 || e.getStatus() == 6))
                    .count();
            // 未办结事件数
            long unfinishedCount = newCount - finishedCount;

            // 高频时段统计（按小时）
            Map<Integer, Long> hourMap = deviceEvents.stream()
                    .filter(e -> e.getWarningTime() != null)
                    .collect(java.util.stream.Collectors.groupingBy(
                            e -> e.getWarningTime().toInstant().atZone(java.time.ZoneId.systemDefault()).getHour(),
                            java.util.stream.Collectors.counting()
                    ));
            int highFreqHour = hourMap.entrySet().stream().max(Map.Entry.comparingByValue()).map(Map.Entry::getKey).orElse(-1);

            String content = "时段:" + DateUtils.formatDate(startDate) + "-" + DateUtils.formatDate(endDate) + "\n"
                    + "告警设备名称:" + deviceName + "\n"
                    + "事件类型:" + eventType + "\n"
                    + "本周新增:" + newCount + ", 已办结:" + finishedCount + ", 未办结:" + unfinishedCount + ", 高频时段:" + (highFreqHour >= 0 ? highFreqHour + "时" : "无") + "\n";

            sb.append(content);
        }

        pushEvent.setWarningDetail(sb.toString());

        // 推送到基层智治平台
        warningEventService.eventReport(pushEvent, "10");
    }
}
