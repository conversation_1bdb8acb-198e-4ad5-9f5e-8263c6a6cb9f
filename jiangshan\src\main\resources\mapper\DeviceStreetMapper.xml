<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceStreetMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.DeviceStreet">
    <!--@mbg.generated-->
    <!--@Table t_device_street-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="bsm" jdbcType="VARCHAR" property="bsm" />
    <result column="ysdm" jdbcType="VARCHAR" property="ysdm" />
    <result column="zldwdm" jdbcType="VARCHAR" property="zldwdm" />
    <result column="zldwmc" jdbcType="VARCHAR" property="zldwmc" />
    <result column="dcmj" jdbcType="VARCHAR" property="dcmj" />
    <result column="jsmj" jdbcType="VARCHAR" property="jsmj" />
    <result column="mssm" jdbcType="VARCHAR" property="mssm" />
    <result column="hdmc" jdbcType="VARCHAR" property="hdmc" />
    <result column="bz" jdbcType="VARCHAR" property="bz" />
    <result column="shape_length" jdbcType="VARCHAR" property="shapeLength" />
    <result column="shape_area" jdbcType="VARCHAR" property="shapeArea" />
    <result column="geometry" jdbcType="LONGVARCHAR" property="geometry" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, parent_id, `label`, `name`, bsm, ysdm, zldwdm, zldwmc, dcmj, jsmj, mssm, hdmc,
    bz, shape_length, shape_area, geometry, create_by, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_device_street
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from t_device_street
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.ruoyi.jiangshan.domain.DeviceStreet">
    <!--@mbg.generated-->
    insert into t_device_street (id, parent_id, `label`,
      `name`, bsm, ysdm, zldwdm,
      zldwmc, dcmj, jsmj,
      mssm, hdmc, bz, shape_length,
      shape_area, geometry, create_by,
      create_time)
    values (#{id,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR}, #{label,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR}, #{bsm,jdbcType=VARCHAR}, #{ysdm,jdbcType=VARCHAR}, #{zldwdm,jdbcType=VARCHAR},
      #{zldwmc,jdbcType=VARCHAR}, #{dcmj,jdbcType=VARCHAR}, #{jsmj,jdbcType=VARCHAR},
      #{mssm,jdbcType=VARCHAR}, #{hdmc,jdbcType=VARCHAR}, #{bz,jdbcType=VARCHAR}, #{shapeLength,jdbcType=VARCHAR},
      #{shapeArea,jdbcType=VARCHAR}, #{geometry,jdbcType=LONGVARCHAR}, #{createBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.ruoyi.jiangshan.domain.DeviceStreet">
    <!--@mbg.generated-->
    insert into t_device_street
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="label != null">
        `label`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="bsm != null">
        bsm,
      </if>
      <if test="ysdm != null">
        ysdm,
      </if>
      <if test="zldwdm != null">
        zldwdm,
      </if>
      <if test="zldwmc != null">
        zldwmc,
      </if>
      <if test="dcmj != null">
        dcmj,
      </if>
      <if test="jsmj != null">
        jsmj,
      </if>
      <if test="mssm != null">
        mssm,
      </if>
      <if test="hdmc != null">
        hdmc,
      </if>
      <if test="bz != null">
        bz,
      </if>
      <if test="shapeLength != null">
        shape_length,
      </if>
      <if test="shapeArea != null">
        shape_area,
      </if>
      <if test="geometry != null">
        geometry,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="bsm != null">
        #{bsm,jdbcType=VARCHAR},
      </if>
      <if test="ysdm != null">
        #{ysdm,jdbcType=VARCHAR},
      </if>
      <if test="zldwdm != null">
        #{zldwdm,jdbcType=VARCHAR},
      </if>
      <if test="zldwmc != null">
        #{zldwmc,jdbcType=VARCHAR},
      </if>
      <if test="dcmj != null">
        #{dcmj,jdbcType=VARCHAR},
      </if>
      <if test="jsmj != null">
        #{jsmj,jdbcType=VARCHAR},
      </if>
      <if test="mssm != null">
        #{mssm,jdbcType=VARCHAR},
      </if>
      <if test="hdmc != null">
        #{hdmc,jdbcType=VARCHAR},
      </if>
      <if test="bz != null">
        #{bz,jdbcType=VARCHAR},
      </if>
      <if test="shapeLength != null">
        #{shapeLength,jdbcType=VARCHAR},
      </if>
      <if test="shapeArea != null">
        #{shapeArea,jdbcType=VARCHAR},
      </if>
      <if test="geometry != null">
        #{geometry,jdbcType=LONGVARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.DeviceStreet">
    <!--@mbg.generated-->
    update t_device_street
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        `label` = #{label,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="bsm != null">
        bsm = #{bsm,jdbcType=VARCHAR},
      </if>
      <if test="ysdm != null">
        ysdm = #{ysdm,jdbcType=VARCHAR},
      </if>
      <if test="zldwdm != null">
        zldwdm = #{zldwdm,jdbcType=VARCHAR},
      </if>
      <if test="zldwmc != null">
        zldwmc = #{zldwmc,jdbcType=VARCHAR},
      </if>
      <if test="dcmj != null">
        dcmj = #{dcmj,jdbcType=VARCHAR},
      </if>
      <if test="jsmj != null">
        jsmj = #{jsmj,jdbcType=VARCHAR},
      </if>
      <if test="mssm != null">
        mssm = #{mssm,jdbcType=VARCHAR},
      </if>
      <if test="hdmc != null">
        hdmc = #{hdmc,jdbcType=VARCHAR},
      </if>
      <if test="bz != null">
        bz = #{bz,jdbcType=VARCHAR},
      </if>
      <if test="shapeLength != null">
        shape_length = #{shapeLength,jdbcType=VARCHAR},
      </if>
      <if test="shapeArea != null">
        shape_area = #{shapeArea,jdbcType=VARCHAR},
      </if>
      <if test="geometry != null">
        geometry = #{geometry,jdbcType=LONGVARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.DeviceStreet">
    <!--@mbg.generated-->
    update t_device_street
    set parent_id = #{parentId,jdbcType=VARCHAR},
      `label` = #{label,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      bsm = #{bsm,jdbcType=VARCHAR},
      ysdm = #{ysdm,jdbcType=VARCHAR},
      zldwdm = #{zldwdm,jdbcType=VARCHAR},
      zldwmc = #{zldwmc,jdbcType=VARCHAR},
      dcmj = #{dcmj,jdbcType=VARCHAR},
      jsmj = #{jsmj,jdbcType=VARCHAR},
      mssm = #{mssm,jdbcType=VARCHAR},
      hdmc = #{hdmc,jdbcType=VARCHAR},
      bz = #{bz,jdbcType=VARCHAR},
      shape_length = #{shapeLength,jdbcType=VARCHAR},
      shape_area = #{shapeArea,jdbcType=VARCHAR},
      geometry = #{geometry,jdbcType=LONGVARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_device_street
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.parentId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`label` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.label,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.name,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="bsm = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.bsm,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ysdm = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.ysdm,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zldwdm = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.zldwdm,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zldwmc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.zldwmc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dcmj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.dcmj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="jsmj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.jsmj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="mssm = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.mssm,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="hdmc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.hdmc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="bz = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.bz,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="shape_length = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.shapeLength,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="shape_area = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.shapeArea,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="geometry = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.geometry,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=VARCHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into t_device_street
    (id, parent_id, `label`, `name`, bsm, ysdm, zldwdm, zldwmc, dcmj, jsmj, mssm, hdmc,
      bz, shape_length, shape_area, geometry, create_by, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.parentId,jdbcType=VARCHAR}, #{item.label,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR}, #{item.bsm,jdbcType=VARCHAR}, #{item.ysdm,jdbcType=VARCHAR},
        #{item.zldwdm,jdbcType=VARCHAR}, #{item.zldwmc,jdbcType=VARCHAR}, #{item.dcmj,jdbcType=VARCHAR},
        #{item.jsmj,jdbcType=VARCHAR}, #{item.mssm,jdbcType=VARCHAR}, #{item.hdmc,jdbcType=VARCHAR},
        #{item.bz,jdbcType=VARCHAR}, #{item.shapeLength,jdbcType=VARCHAR}, #{item.shapeArea,jdbcType=VARCHAR},
        #{item.geometry,jdbcType=LONGVARCHAR}, #{item.createBy,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

  <update id="updateByName">
    update t_device_street
    set geometry = #{geometry,jdbcType=VARCHAR},
        bsm = #{bsm,jdbcType=VARCHAR},
        ysdm = #{ysdm,jdbcType=VARCHAR},
        zldwdm = #{zldwdm,jdbcType=VARCHAR},
        zldwmc = #{zldwmc,jdbcType=VARCHAR},
        dcmj = #{dcmj,jdbcType=VARCHAR},
        jsmj = #{jsmj,jdbcType=VARCHAR},
        mssm = #{mssm,jdbcType=VARCHAR},
        hdmc = #{hdmc,jdbcType=VARCHAR},
        bz = #{bz,jdbcType=VARCHAR},
        shape_length = #{shapeLength,jdbcType=VARCHAR},
        shape_area = #{shapeArea,jdbcType=VARCHAR}
    where label = #{label,jdbcType=VARCHAR}
  </update>

  <select id="listAll" resultType="com.ruoyi.jiangshan.vo.street.DeviceStreetVO">
    select id as id, parent_id as parentId, label as label, name as name
    from t_device_street
  </select>

  <select id="listWithOutParentId" resultMap="BaseResultMap">
      select
    <include refid="Base_Column_List">
    </include>
      from t_device_street
      where parent_id is not null
  </select>

  <select id="getByParentId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from t_device_street
    where id = #{parentId}
  </select>

  <select id="getStreet" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from t_device_street
    where ST_Contains(ST_GeomFromText(geometry), ST_PointFromText(CONCAT('POINT(', #{lon}, ' ', #{lat}, ')')))
    and parent_id is null
  </select>
</mapper>
