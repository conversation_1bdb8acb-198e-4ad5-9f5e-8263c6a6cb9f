<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.PushSjbmMapper">

    <resultMap type="DeviceValue" id="DeviceValueResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceThirdId"    column="device_third_id"    />
        <result property="monitorId"    column="monitor_id"    />
        <result property="monitorObject"    column="monitor_object"    />
        <result property="monitorValue"    column="monitor_value"    />
        <result property="monitorItem"    column="monitor_item"    />
        <result property="monitorItemEnglish"    column="monitor_item_english"    />
        <result property="monitorUnit"    column="monitor_unit"    />
        <result property="monitorTime"    column="monitor_time"    />
        <result property="target"    column="target"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceScene"    column="device_scene"    />
        <result property="deviceName"    column="device_name"    />
    </resultMap>

    <sql id="selectDeviceValueVo">
        select id, device_id, device_third_id, monitor_id, monitor_object, monitor_value,
               monitor_item, monitor_item_english, monitor_unit, monitor_time, target,
               create_time, create_by from t_device_value
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into biz_0800_jsscsaqwlsbjcxx
        (id, device_id, device_third_id, monitor_id, monitor_object, monitor_value, monitor_item,
        monitor_item_english, monitor_unit, monitor_time, target, create_time, create_by
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.deviceId,jdbcType=BIGINT}, #{item.deviceThirdId,jdbcType=VARCHAR}, #{item.monitorId,jdbcType=BIGINT},
            #{item.monitorObject,jdbcType=VARCHAR}, #{item.monitorValue,jdbcType=VARCHAR},
            #{item.monitorItem,jdbcType=VARCHAR}, #{item.monitorItemEnglish,jdbcType=VARCHAR},
            #{item.monitorUnit,jdbcType=VARCHAR}, #{item.monitorTime,jdbcType=TIMESTAMP}, #{item.target,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="batchInsertDeviceMonitor" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into biz_0800_jsscsaqtest1
        (id, monitor_name, monitor_type, monitor_scene, service_url, file_url, lon, lat, monitor_department_id,
        monitor_department_name, create_time, create_by, update_time, update_by, `scale`,
        scale_unit, remark, monitor_third_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.monitorName,jdbcType=VARCHAR}, #{item.monitorType,jdbcType=VARCHAR}, #{item.monitorScene,jdbcType=VARCHAR},
            #{item.serviceUrl,jdbcType=VARCHAR}, #{item.fileUrl,jdbcType=VARCHAR}, #{item.lon,jdbcType=VARCHAR},
            #{item.lat,jdbcType=VARCHAR}, #{item.monitorDepartmentId,jdbcType=BIGINT}, #{item.monitorDepartmentName,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.scale,jdbcType=VARCHAR}, #{item.scaleUnit,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.monitorThirdId,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="batchInsertDeviceInfo" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into biz_0800_jsscsaqtest2
        (id, device_third_id, device_num, device_scene, device_name, device_type, device_address,
        device_department_id, device_department_name, device_status, monitor_id, monitor_name,
        file_url, lon, lat, open_flag, last_connect_time, last_dis_connect_time, create_time,
        create_by, update_time, update_by, device_street, device_street_area_code, remark,
        field01, field02, field03, field04, field05)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.deviceThirdId,jdbcType=VARCHAR}, #{item.deviceNum,jdbcType=VARCHAR}, #{item.deviceScene,jdbcType=VARCHAR},
            #{item.deviceName,jdbcType=VARCHAR}, #{item.deviceType,jdbcType=VARCHAR}, #{item.deviceAddress,jdbcType=VARCHAR},
            #{item.deviceDepartmentId,jdbcType=BIGINT}, #{item.deviceDepartmentName,jdbcType=VARCHAR},
            #{item.deviceStatus,jdbcType=TINYINT}, #{item.monitorId,jdbcType=BIGINT}, #{item.monitorName,jdbcType=VARCHAR},
            #{item.fileUrl,jdbcType=VARCHAR}, #{item.lon,jdbcType=VARCHAR}, #{item.lat,jdbcType=VARCHAR},
            #{item.openFlag,jdbcType=BOOLEAN}, #{item.lastConnectTime,jdbcType=TIMESTAMP},
            #{item.lastDisConnectTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
            #{item.deviceStreet,jdbcType=VARCHAR}, #{item.deviceStreetAreaCode,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.field01,jdbcType=VARCHAR}, #{item.field02,jdbcType=VARCHAR},
            #{item.field03,jdbcType=VARCHAR}, #{item.field04,jdbcType=VARCHAR}, #{item.field05,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="batchInsertWarningEvent" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into biz_0800_jsscsaqtest3
        (id, warning_id, event_third_id, event_name, event_type, department_id, department_name,
        event_address, warning_detail, warning_level, warning_time, delivery_time, reject_reason,
        `status`, stop_flag, over_flag, supervise_flag, supervise_type, create_time, create_by,
        update_time, update_by, lon, lat, leader_content, event_street, event_street_area_code,
        device_third_id, device_name, expected_complete_time, biz_code, first_warning_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.warningId,jdbcType=BIGINT}, #{item.eventThirdId,jdbcType=VARCHAR}, #{item.eventName,jdbcType=VARCHAR},
            #{item.eventType,jdbcType=VARCHAR}, #{item.departmentId,jdbcType=BIGINT}, #{item.departmentName,jdbcType=VARCHAR},
            #{item.eventAddress,jdbcType=VARCHAR}, #{item.warningDetail,jdbcType=VARCHAR},
            #{item.warningLevel,jdbcType=BOOLEAN}, #{item.warningTime,jdbcType=TIMESTAMP},
            #{item.deliveryTime,jdbcType=TIMESTAMP}, #{item.rejectReason,jdbcType=VARCHAR},
            #{item.status,jdbcType=BOOLEAN}, #{item.stopFlag,jdbcType=BOOLEAN}, #{item.overFlag,jdbcType=BOOLEAN},
            #{item.superviseFlag,jdbcType=BOOLEAN}, #{item.superviseType,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.lon,jdbcType=VARCHAR}, #{item.lat,jdbcType=VARCHAR},
            #{item.leaderContent,jdbcType=VARCHAR}, #{item.eventStreet,jdbcType=VARCHAR},
            #{item.eventStreetAreaCode,jdbcType=VARCHAR}, #{item.deviceThirdId}, #{item.deviceName},
            #{item.expectedCompleteTime,jdbcType=TIMESTAMP}, #{item.bizCode,jdbcType=VARCHAR},
            #{item.firstWarningTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <insert id="batchInsertWarningEventProcess" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into biz_0800_jsscsaqtest4
        (id, event_id, department_id, department_name, user_id, user_name, user_mobile, process_step,
        process_content, process_time, reject_content, stop_content, stop_start_time, stop_end_time,
        `status`, over_flag, supervise_user_id, supervise_user, supervise_content, expected_complete_time,
        create_time, create_by, update_time, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.eventId,jdbcType=BIGINT}, #{item.departmentId,jdbcType=BIGINT}, #{item.departmentName,jdbcType=VARCHAR},
            #{item.userId,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR}, #{item.userMobile,jdbcType=VARCHAR},
            #{item.processStep,jdbcType=BOOLEAN}, #{item.processContent,jdbcType=VARCHAR},
            #{item.processTime,jdbcType=TIMESTAMP}, #{item.rejectContent,jdbcType=VARCHAR},
            #{item.stopContent,jdbcType=VARCHAR}, #{item.stopStartTime,jdbcType=TIMESTAMP},
            #{item.stopEndTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=BOOLEAN}, #{item.overFlag,jdbcType=BOOLEAN},
            #{item.superviseUserId,jdbcType=BIGINT}, #{item.superviseUser,jdbcType=VARCHAR},
            #{item.superviseContent,jdbcType=VARCHAR}, #{item.expectedCompleteTime,jdbcType=TIMESTAMP},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>
