<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Word.Document"?>
<pkg:package xmlns:pkg="http://schemas.microsoft.com/office/2006/xmlPackage">
    <pkg:part pkg:name="/_rels/.rels" pkg:contentType="application/vnd.openxmlformats-package.relationships+xml">
        <pkg:xmlData>
            <Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
                <Relationship Id="rId4"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument"
                              Target="word/document.xml"/>
                <Relationship Id="rId2"
                              Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties"
                              Target="docProps/core.xml"/>
                <Relationship Id="rId1"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties"
                              Target="docProps/app.xml"/>
                <Relationship Id="rId3"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties"
                              Target="docProps/custom.xml"/>
            </Relationships>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/_rels/document.xml.rels"
              pkg:contentType="application/vnd.openxmlformats-package.relationships+xml">
        <pkg:xmlData>
            <Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
                <Relationship Id="rId9" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image"
                              Target="media/image3.png"/>
                <Relationship Id="rId8" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image"
                              Target="media/image2.png"/>
                <Relationship Id="rId7" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image"
                              Target="media/image1.png"/>
                <Relationship Id="rId6" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme"
                              Target="theme/theme1.xml"/>
                <Relationship Id="rId5"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/header"
                              Target="header1.xml"/>
                <Relationship Id="rId4"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes"
                              Target="endnotes.xml"/>
                <Relationship Id="rId3"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes"
                              Target="footnotes.xml"/>
                <Relationship Id="rId2"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings"
                              Target="settings.xml"/>
                <Relationship Id="rId12"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable"
                              Target="fontTable.xml"/>
                <#list reportList as item>
                    <Relationship Id="lineId${item_index + 10}"
                                  Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image"
                                  Target="media/image${item_index + 10}.png"/>
                </#list>

                <Relationship Id="rId10"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image"
                              Target="media/image4.png"/>
                <Relationship Id="rId1"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles"
                              Target="styles.xml"/>
            </Relationships>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/document.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml">
        <pkg:xmlData>
            <w:document xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
                        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                        xmlns:o="urn:schemas-microsoft-com:office:office"
                        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                        xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                        xmlns:v="urn:schemas-microsoft-com:vml"
                        xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
                        xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
                        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                        xmlns:w10="urn:schemas-microsoft-com:office:word"
                        xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
                        xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
                        xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
                        xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
                        xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
                        xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData"
                        mc:Ignorable="w14 w15 wp14">
                <w:body>
                    <#list reportList as item>
                        <#if item_index gt 0>
                            <w:p>
                                <w:r>
                                    <w:br w:type="page" />
                                </w:r>
                            </w:p>
                        </#if>

                    <w:p w14:paraId="76DA1FBB">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="center"/>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="44"/>
                                <w:szCs w:val="44"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="44"/>
                                <w:szCs w:val="44"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t>城市安全体检报告-${item.scene}</w:t>
                        </w:r>
                    </w:p>
                    <w:p w14:paraId="38BB3623">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:ind w:left="15000" w:leftChars="0" w:firstLine="500" w:firstLineChars="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t>${item.createTime?string('yyyy-MM-dd HH:mm:ss')}</w:t>
                        </w:r>
                    </w:p>
                    <w:p w14:paraId="73BA682D">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:position w:val="-25"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                            </w:rPr>
                            <w:drawing>
                                <wp:anchor distT="0" distB="0" distL="0" distR="0" simplePos="0"
                                           relativeHeight="251661312" behindDoc="0" locked="0" layoutInCell="1"
                                           allowOverlap="1">
                                    <wp:simplePos x="0" y="0"/>
                                    <wp:positionH relativeFrom="column">
                                        <wp:posOffset>9292590</wp:posOffset>
                                    </wp:positionH>
                                    <wp:positionV relativeFrom="paragraph">
                                        <wp:posOffset>487045</wp:posOffset>
                                    </wp:positionV>
                                    <wp:extent cx="889000" cy="800100"/>
                                    <wp:effectExtent l="0" t="0" r="6350" b="0"/>
                                    <wp:wrapSquare wrapText="bothSides"/>
                                    <wp:docPr id="18" name="IM 18"/>
                                    <wp:cNvGraphicFramePr/>
                                    <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                                        <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                            <pic:pic
                                                    xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                                <pic:nvPicPr>
                                                    <pic:cNvPr id="18" name="IM 18"/>
                                                    <pic:cNvPicPr/>
                                                </pic:nvPicPr>
                                                <pic:blipFill>
                                                    <a:blip r:embed="rId7"/>
                                                    <a:stretch>
                                                        <a:fillRect/>
                                                    </a:stretch>
                                                </pic:blipFill>
                                                <pic:spPr>
                                                    <a:xfrm>
                                                        <a:off x="0" y="0"/>
                                                        <a:ext cx="889000" cy="800100"/>
                                                    </a:xfrm>
                                                    <a:prstGeom prst="rect">
                                                        <a:avLst/>
                                                    </a:prstGeom>
                                                </pic:spPr>
                                            </pic:pic>
                                        </a:graphicData>
                                    </a:graphic>
                                </wp:anchor>
                            </w:drawing>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:position w:val="-25"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                            </w:rPr>
                            <w:drawing>
                                <wp:anchor distT="0" distB="0" distL="0" distR="0" simplePos="0"
                                           relativeHeight="251660288" behindDoc="0" locked="0" layoutInCell="1"
                                           allowOverlap="1">
                                    <wp:simplePos x="0" y="0"/>
                                    <wp:positionH relativeFrom="column">
                                        <wp:posOffset>6106160</wp:posOffset>
                                    </wp:positionH>
                                    <wp:positionV relativeFrom="paragraph">
                                        <wp:posOffset>476250</wp:posOffset>
                                    </wp:positionV>
                                    <wp:extent cx="889000" cy="800100"/>
                                    <wp:effectExtent l="0" t="0" r="6350" b="0"/>
                                    <wp:wrapSquare wrapText="bothSides"/>
                                    <wp:docPr id="16" name="IM 16"/>
                                    <wp:cNvGraphicFramePr/>
                                    <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                                        <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                            <pic:pic
                                                    xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                                <pic:nvPicPr>
                                                    <pic:cNvPr id="16" name="IM 16"/>
                                                    <pic:cNvPicPr/>
                                                </pic:nvPicPr>
                                                <pic:blipFill>
                                                    <a:blip r:embed="rId8"/>
                                                    <a:stretch>
                                                        <a:fillRect/>
                                                    </a:stretch>
                                                </pic:blipFill>
                                                <pic:spPr>
                                                    <a:xfrm>
                                                        <a:off x="0" y="0"/>
                                                        <a:ext cx="889000" cy="800100"/>
                                                    </a:xfrm>
                                                    <a:prstGeom prst="rect">
                                                        <a:avLst/>
                                                    </a:prstGeom>
                                                </pic:spPr>
                                            </pic:pic>
                                        </a:graphicData>
                                    </a:graphic>
                                </wp:anchor>
                            </w:drawing>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:position w:val="-25"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                            </w:rPr>
                            <w:drawing>
                                <wp:anchor distT="0" distB="0" distL="0" distR="0" simplePos="0"
                                           relativeHeight="251659264" behindDoc="0" locked="0" layoutInCell="1"
                                           allowOverlap="1">
                                    <wp:simplePos x="0" y="0"/>
                                    <wp:positionH relativeFrom="column">
                                        <wp:posOffset>3047365</wp:posOffset>
                                    </wp:positionH>
                                    <wp:positionV relativeFrom="paragraph">
                                        <wp:posOffset>465455</wp:posOffset>
                                    </wp:positionV>
                                    <wp:extent cx="889000" cy="800100"/>
                                    <wp:effectExtent l="0" t="0" r="6350" b="0"/>
                                    <wp:wrapSquare wrapText="bothSides"/>
                                    <wp:docPr id="12" name="IM 12"/>
                                    <wp:cNvGraphicFramePr/>
                                    <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                                        <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                            <pic:pic
                                                    xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                                <pic:nvPicPr>
                                                    <pic:cNvPr id="12" name="IM 12"/>
                                                    <pic:cNvPicPr/>
                                                </pic:nvPicPr>
                                                <pic:blipFill>
                                                    <a:blip r:embed="rId9"/>
                                                    <a:stretch>
                                                        <a:fillRect/>
                                                    </a:stretch>
                                                </pic:blipFill>
                                                <pic:spPr>
                                                    <a:xfrm>
                                                        <a:off x="0" y="0"/>
                                                        <a:ext cx="889000" cy="800100"/>
                                                    </a:xfrm>
                                                    <a:prstGeom prst="rect">
                                                        <a:avLst/>
                                                    </a:prstGeom>
                                                </pic:spPr>
                                            </pic:pic>
                                        </a:graphicData>
                                    </a:graphic>
                                </wp:anchor>
                            </w:drawing>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                            </w:rPr>
                            <w:drawing>
                                <wp:anchor distT="0" distB="0" distL="0" distR="0" simplePos="0"
                                           relativeHeight="251662336" behindDoc="0" locked="0" layoutInCell="1"
                                           allowOverlap="1">
                                    <wp:simplePos x="0" y="0"/>
                                    <wp:positionH relativeFrom="column">
                                        <wp:posOffset>82550</wp:posOffset>
                                    </wp:positionH>
                                    <wp:positionV relativeFrom="paragraph">
                                        <wp:posOffset>481965</wp:posOffset>
                                    </wp:positionV>
                                    <wp:extent cx="889000" cy="800100"/>
                                    <wp:effectExtent l="0" t="0" r="6350" b="0"/>
                                    <wp:wrapSquare wrapText="bothSides"/>
                                    <wp:docPr id="4" name="IM 4"/>
                                    <wp:cNvGraphicFramePr/>
                                    <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                                        <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                            <pic:pic
                                                    xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                                <pic:nvPicPr>
                                                    <pic:cNvPr id="4" name="IM 4"/>
                                                    <pic:cNvPicPr/>
                                                </pic:nvPicPr>
                                                <pic:blipFill>
                                                    <a:blip r:embed="rId10"/>
                                                    <a:stretch>
                                                        <a:fillRect/>
                                                    </a:stretch>
                                                </pic:blipFill>
                                                <pic:spPr>
                                                    <a:xfrm>
                                                        <a:off x="0" y="0"/>
                                                        <a:ext cx="889000" cy="800100"/>
                                                    </a:xfrm>
                                                    <a:prstGeom prst="rect">
                                                        <a:avLst/>
                                                    </a:prstGeom>
                                                </pic:spPr>
                                            </pic:pic>
                                        </a:graphicData>
                                    </a:graphic>
                                </wp:anchor>
                            </w:drawing>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t>全市运行总体情况</w:t>
                        </w:r>
                    </w:p>
                    <w:p w14:paraId="69095D00">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                    </w:p>
                    <w:p w14:paraId="5A7E96D0">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t xml:space="preserve">  </w:t>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t>红色预警 ${item.warningLevel01} 个</w:t>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t xml:space="preserve">                                  </w:t>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t xml:space="preserve">                                           橙色预警 ${item.warningLevel02} 个</w:t>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t xml:space="preserve">                                     </w:t>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t xml:space="preserve">  </w:t>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t xml:space="preserve">                                   </w:t>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t xml:space="preserve">                                         黄色预警 ${item.warningLevel03} 个</w:t>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t xml:space="preserve">                                        </w:t>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t xml:space="preserve">                                             蓝色预警 ${item.warningLevel04}个</w:t>
                        </w:r>
                    </w:p>
                    <w:p w14:paraId="04FAB8BD">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                    </w:p>
                    <w:p w14:paraId="27E1E1B1">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                    </w:p>
                    <w:p w14:paraId="6B4321CE">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                    </w:p>
                    <w:p w14:paraId="2E123F99">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t>设备预警信息</w:t>
                        </w:r>
                    </w:p>

                    <w:tbl>
                        <w:tblPr>
                            <w:tblStyle w:val="5"/>
                            <w:tblW w:w="5000" w:type="pct"/>
                            <w:jc w:val="center"/>
                            <w:tblBorders>
                                <w:top w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                <w:left w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                <w:bottom w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                <w:right w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                <w:insideH w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                <w:insideV w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                            </w:tblBorders>
                            <w:tblLayout w:type="autofit"/>
                            <w:tblCellMar>
                                <w:top w:w="0" w:type="dxa"/>
                                <w:left w:w="108" w:type="dxa"/>
                                <w:bottom w:w="0" w:type="dxa"/>
                                <w:right w:w="108" w:type="dxa"/>
                            </w:tblCellMar>
                        </w:tblPr>
                        <w:tblGrid>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                        </w:tblGrid>
                        <w:tr w14:paraId="3E51E9BC">
                            <w:tblPrEx>
                                <w:tblBorders>
                                    <w:top w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:left w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:bottom w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:right w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:insideH w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:insideV w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                </w:tblBorders>
                                <w:tblCellMar>
                                    <w:top w:w="0" w:type="dxa"/>
                                    <w:left w:w="108" w:type="dxa"/>
                                    <w:bottom w:w="0" w:type="dxa"/>
                                    <w:right w:w="108" w:type="dxa"/>
                                </w:tblCellMar>
                            </w:tblPrEx>
                            <w:trPr>
                                <w:jc w:val="center"/>
                            </w:trPr>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="433C593E">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>事件名称</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="41571291">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>事件类型</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="125EEEA0">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>风险等级</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="111FA618">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>处置部门</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="171D3E73">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>事件地点</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="0A3AB27E">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>预警时间</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="0A3AB27E">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>是否抄送</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="0A3AB27E">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>是否超时</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                        </w:tr>
                        <#list item.warningList as warning>
                            <w:tr w14:paraId="47AF44A9">
                                <w:tblPrEx>
                                    <w:tblBorders>
                                        <w:top w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                        <w:left w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                        <w:bottom w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                        <w:right w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                        <w:insideH w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                        <w:insideV w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    </w:tblBorders>
                                    <w:tblCellMar>
                                        <w:top w:w="0" w:type="dxa"/>
                                        <w:left w:w="108" w:type="dxa"/>
                                        <w:bottom w:w="0" w:type="dxa"/>
                                        <w:right w:w="108" w:type="dxa"/>
                                    </w:tblCellMar>
                                </w:tblPrEx>
                                <w:trPr>
                                    <w:jc w:val="center"/>
                                </w:trPr>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="625" w:type="pct"/>
                                    </w:tcPr>
                                    <w:p w14:paraId="3DAD6DA4">
                                        <w:pPr>
                                            <w:widowControl w:val="0"/>
                                            <w:bidi w:val="0"/>
                                            <w:jc w:val="left"/>
                                            <w:rPr>
                                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                            <w:t>${warning.eventName}</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="625" w:type="pct"/>
                                    </w:tcPr>
                                    <w:p w14:paraId="7F494B14">
                                        <w:pPr>
                                            <w:widowControl w:val="0"/>
                                            <w:bidi w:val="0"/>
                                            <w:jc w:val="left"/>
                                            <w:rPr>
                                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                            <w:t>${warning.eventType}</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="625" w:type="pct"/>
                                    </w:tcPr>
                                    <w:p w14:paraId="0EE4CED2">
                                        <w:pPr>
                                            <w:widowControl w:val="0"/>
                                            <w:bidi w:val="0"/>
                                            <w:jc w:val="left"/>
                                            <w:rPr>
                                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                            <w:t>${warning.eventLevel}</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="625" w:type="pct"/>
                                    </w:tcPr>
                                    <w:p w14:paraId="1845D8E1">
                                        <w:pPr>
                                            <w:widowControl w:val="0"/>
                                            <w:bidi w:val="0"/>
                                            <w:jc w:val="left"/>
                                            <w:rPr>
                                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                            <w:t>${warning.departmentName}</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="625" w:type="pct"/>
                                    </w:tcPr>
                                    <w:p w14:paraId="000C8251">
                                        <w:pPr>
                                            <w:widowControl w:val="0"/>
                                            <w:bidi w:val="0"/>
                                            <w:jc w:val="left"/>
                                            <w:rPr>
                                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                            <w:t>${warning.eventAddress}</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="625" w:type="pct"/>
                                    </w:tcPr>
                                    <w:p w14:paraId="54268062">
                                        <w:pPr>
                                            <w:widowControl w:val="0"/>
                                            <w:bidi w:val="0"/>
                                            <w:jc w:val="left"/>
                                            <w:rPr>
                                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                            <w:t>${warning.warningTime}</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="625" w:type="pct"/>
                                    </w:tcPr>
                                    <w:p w14:paraId="54268062">
                                        <w:pPr>
                                            <w:widowControl w:val="0"/>
                                            <w:bidi w:val="0"/>
                                            <w:jc w:val="left"/>
                                            <w:rPr>
                                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                            <w:t>${warning.copyFlag}</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="625" w:type="pct"/>
                                    </w:tcPr>
                                    <w:p w14:paraId="54268062">
                                        <w:pPr>
                                            <w:widowControl w:val="0"/>
                                            <w:bidi w:val="0"/>
                                            <w:jc w:val="left"/>
                                            <w:rPr>
                                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                          w:cs="微软雅黑"/>
                                                <w:sz w:val="28"/>
                                                <w:szCs w:val="28"/>
                                                <w:vertAlign w:val="baseline"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                            <w:t>${warning.overFlag}</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                            </w:tr>
                        </#list>
                    </w:tbl>

                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="360" w:lineRule="auto"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                            </w:rPr>
                        </w:pPr>
                    </w:p>

                    <w:p w14:paraId="2E123F99">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t>功能要件运行情况</w:t>
                        </w:r>
                    </w:p>



                    <w:tbl>
                        <w:tblPr>
                            <w:tblStyle w:val="5"/>
                            <w:tblW w:w="5000" w:type="pct"/>
                            <w:jc w:val="center"/>
                            <w:tblBorders>
                                <w:top w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                <w:left w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                <w:bottom w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                <w:right w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                <w:insideH w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                <w:insideV w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                            </w:tblBorders>
                            <w:tblLayout w:type="autofit"/>
                            <w:tblCellMar>
                                <w:top w:w="0" w:type="dxa"/>
                                <w:left w:w="108" w:type="dxa"/>
                                <w:bottom w:w="0" w:type="dxa"/>
                                <w:right w:w="108" w:type="dxa"/>
                            </w:tblCellMar>
                        </w:tblPr>
                        <w:tblGrid>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                            <w:gridCol w:w="2357"/>
                        </w:tblGrid>
                        <w:tr w14:paraId="3E51E9BC">
                            <w:tblPrEx>
                                <w:tblBorders>
                                    <w:top w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:left w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:bottom w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:right w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:insideH w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:insideV w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                </w:tblBorders>
                                <w:tblCellMar>
                                    <w:top w:w="0" w:type="dxa"/>
                                    <w:left w:w="108" w:type="dxa"/>
                                    <w:bottom w:w="0" w:type="dxa"/>
                                    <w:right w:w="108" w:type="dxa"/>
                                </w:tblCellMar>
                            </w:tblPrEx>
                            <w:trPr>
                                <w:jc w:val="center"/>
                            </w:trPr>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="433C593E">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>设备类型</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="41571291">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>设备名称</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="125EEEA0">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>设备编号</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="111FA618">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>安装位置</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="171D3E73">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>监测数值</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="0A3AB27E">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>状态</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="333E7FB5">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>预警级别</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="287AE5A6">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>形变位移情况</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                        </w:tr>
                        <#list item.deviceList as device>
                        <w:tr w14:paraId="47AF44A9">
                            <w:tblPrEx>
                                <w:tblBorders>
                                    <w:top w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:left w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:bottom w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:right w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:insideH w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                    <w:insideV w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                                </w:tblBorders>
                                <w:tblCellMar>
                                    <w:top w:w="0" w:type="dxa"/>
                                    <w:left w:w="108" w:type="dxa"/>
                                    <w:bottom w:w="0" w:type="dxa"/>
                                    <w:right w:w="108" w:type="dxa"/>
                                </w:tblCellMar>
                            </w:tblPrEx>
                            <w:trPr>
                                <w:jc w:val="center"/>
                            </w:trPr>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="7F494B14">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>${device.deviceType}</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="7F494B14">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>${device.deviceName}</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="0EE4CED2">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>${device.deviceThirdId}</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="1845D8E1">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>${device.deviceAddress}</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="000C8251">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>${device.monitorValue}</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="54268062">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>${device.deviceStatus}</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="570DF33C">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>${device.warningLevelStr}</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                            <w:tc>
                                <w:tcPr>
                                    <w:tcW w:w="625" w:type="pct"/>
                                </w:tcPr>
                                <w:p w14:paraId="7ADAF611">
                                    <w:pPr>
                                        <w:widowControl w:val="0"/>
                                        <w:bidi w:val="0"/>
                                        <w:jc w:val="left"/>
                                        <w:rPr>
                                            <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                    </w:pPr>
                                    <w:r>
                                        <w:rPr>
                                            <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                                      w:cs="微软雅黑"/>
                                            <w:sz w:val="28"/>
                                            <w:szCs w:val="28"/>
                                            <w:vertAlign w:val="baseline"/>
                                            <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                        </w:rPr>
                                        <w:t>${device.verifyContent}</w:t>
                                    </w:r>
                                </w:p>
                            </w:tc>
                        </w:tr>
                        </#list>
                    </w:tbl>


                    <w:p w14:paraId="277ABA12">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                    </w:p>
                    <w:p w14:paraId="453FD253">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="eastAsia" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑"
                                          w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:t>整体安全变化趋势</w:t>
                        </w:r>
                    </w:p>
                    <w:p w14:paraId="4AD3C839">
                        <w:pPr>
                            <w:bidi w:val="0"/>
                            <w:jc w:val="left"/>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                        </w:pPr>
                        <w:bookmarkStart w:id="0" w:name="_GoBack"/>
                        <w:r>
                            <w:rPr>
                                <w:rFonts w:hint="default" w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                                <w:sz w:val="28"/>
                                <w:szCs w:val="28"/>
                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                            </w:rPr>
                            <w:drawing>
                                <wp:inline distT="0" distB="0" distL="114300" distR="114300">
                                    <wp:extent cx="11833860" cy="4523740"/>
                                    <wp:effectExtent l="0" t="0" r="15240" b="10160"/>
                                    <wp:docPr id="1" name="图片 1" descr="image"/>
                                    <wp:cNvGraphicFramePr>
                                        <a:graphicFrameLocks
                                                xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main"
                                                noChangeAspect="1"/>
                                    </wp:cNvGraphicFramePr>
                                    <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                                        <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                            <pic:pic
                                                    xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                                <pic:nvPicPr>
                                                    <pic:cNvPr id="1" name="图片 1" descr="image"/>
                                                    <pic:cNvPicPr>
                                                        <a:picLocks noChangeAspect="1"/>
                                                    </pic:cNvPicPr>
                                                </pic:nvPicPr>
                                                <pic:blipFill>
                                                    <a:blip r:embed="lineId${item_index + 10}"/>
                                                    <a:stretch>
                                                        <a:fillRect/>
                                                    </a:stretch>
                                                </pic:blipFill>
                                                <pic:spPr>
                                                    <a:xfrm>
                                                        <a:off x="0" y="0"/>
                                                        <a:ext cx="11833860" cy="4523740"/>
                                                    </a:xfrm>
                                                    <a:prstGeom prst="rect">
                                                        <a:avLst/>
                                                    </a:prstGeom>
                                                </pic:spPr>
                                            </pic:pic>
                                        </a:graphicData>
                                    </a:graphic>
                                </wp:inline>
                            </w:drawing>
                        </w:r>
                        <w:bookmarkEnd w:id="0"/>
                    </w:p>



                    <w:sectPr>
                        <w:headerReference r:id="rId5" w:type="default"/>
                        <w:type w:val="continuous"/>
                        <w:pgSz w:w="21280" w:h="19340"/>
                        <w:pgMar w:top="400" w:right="1320" w:bottom="0" w:left="1320" w:header="0" w:footer="0"
                                 w:gutter="0"/>
                        <w:cols w:equalWidth="0" w:num="1">
                            <w:col w:w="18640"/>
                        </w:cols>
                    </w:sectPr>


                    </#list>
                </w:body>
            </w:document>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/docProps/app.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.extended-properties+xml">
        <pkg:xmlData>
            <Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties"
                        xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
                <Pages>1</Pages>
                <Words>95</Words>
                <Characters>111</Characters>
                <TotalTime>0</TotalTime>
                <ScaleCrop>false</ScaleCrop>
                <LinksUpToDate>false</LinksUpToDate>
                <CharactersWithSpaces>398</CharactersWithSpaces>
                <Application>WPS Office_12.1.0.18276_F1E327BC-269C-435d-A152-05C5408002CA</Application>
            </Properties>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/docProps/core.xml"
              pkg:contentType="application/vnd.openxmlformats-package.core-properties+xml">
        <pkg:xmlData>
            <cp:coreProperties xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties"
                               xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/"
                               xmlns:dcmitype="http://purl.org/dc/dcmitype/"
                               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <dcterms:created xsi:type="dcterms:W3CDTF">2024-09-20T02:00:00Z</dcterms:created>
                <dc:creator>Administrator</dc:creator>
                <cp:lastModifiedBy>一巷江湖</cp:lastModifiedBy>
                <dcterms:modified xsi:type="dcterms:W3CDTF">2024-09-23T01:09:38Z</dcterms:modified>
            </cp:coreProperties>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/docProps/custom.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.custom-properties+xml">
        <pkg:xmlData>
            <Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties"
                        xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="2" name="CRO">
                    <vt:lpwstr>wqlLaW5nc29mdCBQREYgdG8gV1BTIDEwMA</vt:lpwstr>
                </property>
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="3" name="Created">
                    <vt:filetime>2024-09-20T10:01:28Z</vt:filetime>
                </property>
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="4" name="KSOProductBuildVer">
                    <vt:lpwstr>2052-12.1.0.18276</vt:lpwstr>
                </property>
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="5" name="ICV">
                    <vt:lpwstr>1A35D737567A4AC997866F07E6D951A7_13</vt:lpwstr>
                </property>
            </Properties>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/endnotes.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml">
        <pkg:xmlData>
            <w:endnotes xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
                        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                        xmlns:o="urn:schemas-microsoft-com:office:office"
                        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                        xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                        xmlns:v="urn:schemas-microsoft-com:vml"
                        xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
                        xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
                        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                        xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
                        xmlns:w10="urn:schemas-microsoft-com:office:word"
                        xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
                        xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
                        xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
                        xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
                        xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData"
                        mc:Ignorable="w14 w15 wp14">
                <w:endnote w:type="separator" w:id="0">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:separator/>
                        </w:r>
                    </w:p>
                </w:endnote>
                <w:endnote w:type="continuationSeparator" w:id="1">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:continuationSeparator/>
                        </w:r>
                    </w:p>
                </w:endnote>
            </w:endnotes>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/fontTable.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.fontTable+xml">
        <pkg:xmlData>
            <w:fonts xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                     xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                     xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                     xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" mc:Ignorable="w14">
                <w:font w:name="Times New Roman">
                    <w:panose1 w:val="02020603050405020304"/>
                    <w:charset w:val="00"/>
                    <w:family w:val="roman"/>
                    <w:pitch w:val="variable"/>
                    <w:sig w:usb0="20007A87" w:usb1="80000000" w:usb2="00000008" w:usb3="00000000" w:csb0="000001FF"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="宋体">
                    <w:panose1 w:val="02010600030101010101"/>
                    <w:charset w:val="86"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="00000203" w:usb1="288F0000" w:usb2="00000006" w:usb3="00000000" w:csb0="00040001"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Wingdings">
                    <w:panose1 w:val="05000000000000000000"/>
                    <w:charset w:val="02"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="00000000" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="80000000"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Arial">
                    <w:panose1 w:val="020B0604020202020204"/>
                    <w:charset w:val="01"/>
                    <w:family w:val="swiss"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="E0002EFF" w:usb1="C000785B" w:usb2="00000009" w:usb3="00000000" w:csb0="400001FF"
                           w:csb1="FFFF0000"/>
                </w:font>
                <w:font w:name="黑体">
                    <w:panose1 w:val="02010609060101010101"/>
                    <w:charset w:val="86"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="800002BF" w:usb1="38CF7CFA" w:usb2="00000016" w:usb3="00000000" w:csb0="00040001"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Courier New">
                    <w:panose1 w:val="02070309020205020404"/>
                    <w:charset w:val="01"/>
                    <w:family w:val="modern"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="E0002EFF" w:usb1="C0007843" w:usb2="00000009" w:usb3="00000000" w:csb0="400001FF"
                           w:csb1="FFFF0000"/>
                </w:font>
                <w:font w:name="Symbol">
                    <w:panose1 w:val="05050102010706020507"/>
                    <w:charset w:val="02"/>
                    <w:family w:val="roman"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="00000000" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="80000000"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Calibri">
                    <w:panose1 w:val="020F0502020204030204"/>
                    <w:charset w:val="00"/>
                    <w:family w:val="swiss"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="E4002EFF" w:usb1="C200247B" w:usb2="00000009" w:usb3="00000000" w:csb0="200001FF"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="微软雅黑">
                    <w:panose1 w:val="020B0503020204020204"/>
                    <w:charset w:val="86"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="80000287" w:usb1="2ACF3C50" w:usb2="00000016" w:usb3="00000000" w:csb0="0004001F"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Tahoma">
                    <w:panose1 w:val="020B0604030504040204"/>
                    <w:charset w:val="00"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="E1002EFF" w:usb1="C000605B" w:usb2="00000029" w:usb3="00000000" w:csb0="200101FF"
                           w:csb1="20280000"/>
                </w:font>
            </w:fonts>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/footnotes.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml">
        <pkg:xmlData>
            <w:footnotes xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
                         xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                         xmlns:o="urn:schemas-microsoft-com:office:office"
                         xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                         xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                         xmlns:v="urn:schemas-microsoft-com:vml"
                         xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
                         xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
                         xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                         xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                         xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
                         xmlns:w10="urn:schemas-microsoft-com:office:word"
                         xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
                         xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
                         xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
                         xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
                         xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData"
                         mc:Ignorable="w14 w15 wp14">
                <w:footnote w:type="separator" w:id="0">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:separator/>
                        </w:r>
                    </w:p>
                </w:footnote>
                <w:footnote w:type="continuationSeparator" w:id="1">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:continuationSeparator/>
                        </w:r>
                    </w:p>
                </w:footnote>
            </w:footnotes>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/header1.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml">
        <pkg:xmlData>
            <w:hdr xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
                   xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                   xmlns:o="urn:schemas-microsoft-com:office:office"
                   xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                   xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                   xmlns:v="urn:schemas-microsoft-com:vml"
                   xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
                   xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
                   xmlns:w10="urn:schemas-microsoft-com:office:word"
                   xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                   xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                   xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
                   xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
                   xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
                   xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
                   xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
                   xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData"
                   mc:Ignorable="w14 w15 wp14">
                <w:p w14:paraId="7AAD686B">
                    <w:pPr>
                        <w:pStyle w:val="2"/>
                        <w:spacing w:line="14" w:lineRule="auto"/>
                        <w:rPr>
                            <w:sz w:val="2"/>
                        </w:rPr>
                    </w:pPr>
                </w:p>
            </w:hdr>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/media/image1.png" pkg:contentType="image/png">
        <pkg:binaryData>iVBORw0KGgoAAAANSUhEUgAAAF4AAABVCAYAAADAFVOtAAAHNklEQVR4nO2ca2wVRRSAv4XbIlRB
            kUZDReSlIEREARGUhxHBRySiJESSGgVFQQmgKVpRkYcISEl8GzEmKIkPghI0KIogGgQ1oohGBIRi
            DASkPOVh9a4/Tmtoe9u7szszO/e2XzJ/urNnTk9Pz8ycOTved8d8MpAuQHvAB7ZWtIwi4WeO3XOA
            B4DJQEG1Z6XAHOAVIGlZr1B43/yVEZZvBXwKdE/T7ytgKHDYuEYR8Ta4b/gzgHVAt4D9vwb6AyeN
            aaSBTAg1swhudIDewKPA42bU0YO37qjTlu8CbAISiu8dBzoDu7RrpImE4zPRfNSNDtAUeBq4Xa86
            +vDWHnHW44cAH0WUcSWwXoMu2vE+d9PwjYEfgK4R5axHjO8croaae4hudIA+wEjgLQ2ytOKtOuyc
            xzcHtgH5muSVIhPtCU3ytNDIR/bdDrWpPuRrlNfWh8kO/F5VmrfSLY9vD/wMNNEs9whwIbBHs9zQ
            JJJO2Z256Dc6yO53JjDGgOxQeCsOOWP5q4G1BuX/C/QEvjc4RmBcWtWUGJbfuGKMawyPEwhXQk0h
            4o2mGQQMA5ZZGKtOvGUHY7d8U+Qgo3qO3RRbkT1CuaXxUuKCxxdhz+gAnYD7gQUWx6yBt/RArJZv
            DfwK5Fke9wDyB9hvedz/iXtynY19owOcBUxDjhJjwXunLDaPvxw5LWoU4t3dwHLkiK8AmTCbKcoo
            By4BfgkxfmTi9PgFqBv9GOKlb1B1cmwJPAgUK8jKQfL9NyrqoIVETP5+K7JhUuEkMBz4OMWzMuS4
            bz9izKDcAFwHrFTUJTLe4v3WTZ+L5GM6KL43F5gSoN9y4CYFuZuR6gWr//xxhJoJqBsd4LWA/V5E
            zfDdkPz/y8oaRcBbZNfjWyG59haK7+0E2gXs6yFhKUdB/j6gIxbrcWxvoKajbnSQSTUoPpIGbqnw
            Tj4wFdnMWcFmqLkYuNvSWGHcaQLwErBDsy4psbmqCVuqYYsmyAQ+wsZgtkLN0IrmOrchy9wvTA9k
            I9Q0Rm1tHTclQC/Tg9gw/FgkvmcKPZHzgUUmBzFdtNoCSUZlGk8BS1BbTSlh2uOnoq8+xiYFyNJy
            mqkBTBq+AzGmXTXwEPAq8IcJ4SZDjalSDVvkIecFhSaEm/L4/kgmMdMZBTwLfKtbsAnDe5gv1bBF
            I+TcQDWFnRYTG6hC5HQpW7gK2c2+q1Oobo9vhnyzlG3MQWpx/tYlUHe1cJEPBXFX4hpo7XyYpFOm
            zlBTgCzBspVi4HVgrw5hOkNNXKUatmgOzEBSIJHRZfieyNIr2xkNPA/8GFWQrg1UCeHqYzKNyorj
            wVEF6fD4yhx2feFa5DD9gyhCok6uuchSq74xD/kG95+wAqIuJyf60N6B5Z7t1tmH+6ItJ4P+iWqS
            j1rJXLbxBPAmUnmsTJRQE7ZUI1s4G7khZFKYl8OGmq4+jHHgXz7uNs6HTjZDTQl2SzVyFfurlmyH
            JRd4BikTVyJMqLkeqbC1SRukMqwsQN9uyHdVtrgZ+ZLwM5WXVD0+rlKNJsjO+LkAfccb1iUVJUAP
            JIoEQjXG3+tDl5jiaZEP56fp08+Hwhh06+7DaJV3vAm7Av+RWiCVvq2CvmCArUhF2m8png0C3keS
            WXGwB/mg7WiQziqh5jHiNTrIL7YFeA+pezmMfDk4Gugbo14A5yL7mkB7G298MI/vAPxEZlcN2OAE
            cjdOabqOQVc182gwehBOQ3JXI9N19MaWprX8AGBNdJ3qFf2QS0prJV2Mz6ZSDZssAK6oq0O6UHMH
            cJlGheoLvZF9x+LaOnh37azV8nnIPQOt9esViZ3AC8AKZDJri3y1Nxy1D85M8ztwEXLraw3qCjVT
            cM/oi4A7qfpN6nZku94dWIVkDV2gDVJ1MSPVQ69wR0qPPw9ZL9tKNgVhKXLMWFdw7AF8iTt6H0Uu
            odtd/UGjJOI+1drsJDSr5Vlc7ZEk+Gn6bEzC2w7oWtlOT8KsVM+8UTU9vhdyNaxLVQOrCX6XWG9g
            g0FdVEki5S8bT/1hqlWNi6Uamw31tUFlxfHAU39YfXIdgVTHuoan0Nc1pwHZhN6C5JiAqh8Yu1yq
            obKXuNSYFtGYi9TilEPVUDOJ4Bc12KYvcpvSpgB9xxnWJSwdkc/25wN4w7f7IKUa24gvlx2ET5AK
            rrpq1PtX9FM9o7XFQSS1/WflCdR0H5rHdLIUtA32YYkPTWt5PsCHD33IdUDX2tqZPjzpA96wbX4B
            cmOFS9vtuigDFiIpg+PABUjKwImrawNQDlyQSMpRWqYYHaTaoAiLd8toJgcYkkimSV82YIQ+Cd/P
            KG/PFhKJpMy0DdjlUCLpswaYGLcm9YzVCV/WvfvIzFs2MpG9wKpEUu5kKUZuqmjAPMXAscqUwUJk
            SxvkJtMGwjObiotLT02SPYykDWYC58ShVRazB6nEW1j5A2/glhoJ+TwkJ9KXhrgflX1Ifc1yql2z
            9R8chgfDrrKQzgAAAABJRU5ErkJgghjj
        </pkg:binaryData>
    </pkg:part>
    <pkg:part pkg:name="/word/media/image2.png" pkg:contentType="image/png">
        <pkg:binaryData>iVBORw0KGgoAAAANSUhEUgAAAF4AAABVCAYAAADAFVOtAAAHTElEQVR4nO3deYzdVRXA8c+vfSCl
            bLVaaWRxo2BEWyUF3NhUNLKJiLZ1pWoEqqglIigCKiBQbY1WXAABNyDRqNXQgAruVdFQtAimNCoS
            q0KRblOk7Rv/OLSZGWbmvd/+m+l8k/fPvHvPPb097/7OPefc+0t6N91hBPIUHIIW1uD32FKrRilp
            6W3XrUMajsalOBTj+vx9A76JC/CfGvRKTdLbs6xuHbrls3h/hzaP4Dj8unx18pG0N/6qbh264dM4
            u8u268Uv4w/lqZOfpL3xF3Xr0Ikj8LOUfVZgBrYWr04xNH2NT8QSk5aDcTq+UKw6xZG0N9xetw7D
            cRq+mrHvQ3gO1hanTnEk7fU/qVuHoZiIlZiaQ8ZC3T8bKqVFY5eac+WbdJiHK7EqvzrFkmxdd0vd
            OgzGvvgLJhQg67t4fQFyCqXV0Af/pxQz6XAyjpTeMyqVZOvaH9atw0AOxTL9d6Z5uVOEGHoLlJmL
            Jq7xixQ76fBCvAPXFiw3M8nWtd+rW4e+vAk3liR7NQ7AxpLkp6JJG6gn4bIS5U8VntLHShyja5q0
            1HwQzyh5jLPxFfyj5HE6kmz570116wBTxGZpjwrG+hbeXME4w9KUpeZi1Uw6zMHn8NuKxhuUJiw1
            z8fcisdchJdUPGY/mjDxCzG+4jFfjFnK86A6kmxZc31dY8Px+EFNY/8dB+HROgav0+JbIrOUhcew
            REzerngt9k8pY3/MFzncymn11vdwPQMHZuh3tUhqr+7zt3EiJvNFPDWFrHNxDf6dQY9cJJsfuqrq
            MWGScB8np+x3pQj1DsV03IYnp5B5Nd6dUo/cJJsf/HLVYxJexQdS9rkP03QOdL0NaR5cW0UA7a6U
            +uSijjX+AJyZod+1uosu3oTPiKKnbhgvPKtXZNApM3VsoBZg5wz9bu2y3f/wY+EudssxOFE8sCuh
            aos/Gidl7NuTou0jGeQvwM0qKgWs0uIT8ZOugiwJj2l4r2zlJKlp9VZn8aeJIqMmcwG+hofLHqgq
            i58oAmFNZxIuwlllD1TVGn+e/KUaVXG62C/cW+YgVVj8vmJrPlLYSYQyji9zkCos/jLFlWpUxXF4
            FX5U1gBlT/xhIvEwElkoQhClTFDZQbJFZQovmYNFDKeUmEqZlWSzRMJhJPMJ3IB1RQsu6+FadqlG
            VUzBR/HhogWXNfHzpU9MNJWzRJz/b0UKLePh+jTht48WdsEVeGORQsuY+Iuxe9FCa+ZUvAy/LEpg
            0V7NC0RMZjSyUFQyF0LRFl9HqUZVzMRb8fUihLX0FuZOnqDiLE4NXIpvY1NeQUVZfEskEkY7++Ac
            fDyvoKLcyTNlK9UYiXwIV+GfeYQUYfGTcGFeISOIieKM1tvzCCnC4i+Uro5lNPAWfF5c15KJvKm/
            abKVaox0xgkP7oisAvJa/AKRONgReTneILyc1ORZ47fVouzIXC5qcR5L2zGrxVdZqtFkniVKEa9I
            2zGrxb9TZGeqJE312cTStHgiHxHlhQ+m6ZTF4nfDJ9N2KoAZWN5l25llKjKAPUXC5Iw0ncaFxaf6
            nEd77wz98n7mdtnuGNrPrVi3d9F+Xpo+Sc+q2Wn+o/YT9SZ1VQ3MEam4odgDt+NF1ajTj1vx6m4b
            p11qLldvqcb1oi5ysENjU/B99Uw6HCuOBN3cTeOk575TuxV8uLhVownchS+JdNyuwq2dJXK9dXKP
            OD7aMeSb9Kw8pVuhy8TkjzE878PiTo2SnpUndyNstjiKPkZn1ohL6Iat0e9mjd/F6CjVqIrJotx7
            2HrRVm/n5Wi+8GbG6J55oiRk5VANOln83uIs6Bjp2FkEEF83VINOIYMmlmr04Bu4ThwM3l1ECd8j
            3eHisjlJnPka9EbVZOO9xw7Vcbq4GLlJVQOrcBQeGOS7CeJehCYl3JeLfcUTzmQNZ/FNK9X4lwhF
            DzbpROb/BGFhh1WlVAdmiCthrhn4xVBr/IniH9kkFuP+Dm02iQqArnaPFXGx2Gn3u4Qu2XDPUQMb
            tnC3SOs1hS14uu7ehpCIJemZpWqUjktwft8/DGbx8zRr0olNSbevoOgVW/cmTfx8cQnd9l/swIMJ
            k4Tz3zSSlO2LvjA0LxPEJnT7saSBFn+RZpZqTBanB7u5tnCcOEbTNGaLS+h+Q3+vZpqUWZQKGS/8
            9PM7NRT+8z7lqpOZRR4/npRsWLE9S7ZEuGNN5WG81PAHf/fCz0VotqnMwQ3JhhWHEK5jY1+d0IfV
            eCX+PMh3k7FUtfnWLNyPA7cdTBgptY9T8Ufx67xObKr2xCmirK7K6oKs7Ie5yfo/Td9LvMikSbvU
            0c7SFu2Zxia9ag5v6W3vqLWPdbJTi3aWa6TGyMfalt72HeLtkLvVrc0OxG0t2pvxHTlPOIyRihuT
            dcufTez07lbdHe47MrfgNdtCBg+IrfYSzUv1jSZ+J2I2/YJkPxVptcVG/nUnTWOzyEKdI943K1l3
            56CXbBz5+OegylQbnTwqDqgtxV/7fvF/cHbt1CqQwFMAAAAASUVORK5CYII=
        </pkg:binaryData>
    </pkg:part>
    <pkg:part pkg:name="/word/media/image3.png" pkg:contentType="image/png">
        <pkg:binaryData>iVBORw0KGgoAAAANSUhEUgAAAF4AAABVCAYAAADAFVOtAAAHJklEQVR4nO2da2wVRRSAvy1bKG9F
            FBGNQBQwWkMJDyOI+AJF8C0ERUWDKIIKETGIYiG8oaBEUFTUGCNqBGOQEJUIGsVQVEBARDFqgvwQ
            DC9BaW/v+uNQ6OPe3p3d2Zntbb+EhOydPef03NN5nDkzdby931PLaAj0AE4DSoBNwEGrFgXAxUva
            tsEv7YAZwJ1AkwrPy4CvgEnANxbsCoTj7Sm2bYMf+gMrgaYZ2k0FCiO3RgOuF/+IvxL4CMjz0fY5
            IAFMj9QiDTjJPzbYtqEm8oCdQHuFdxLIGLAlCoN0Efc+fgJqTgdwgeeBfrqN0YmT/O0L2zak42zg
            F6BZwPdvAz7UZ45e4hzxMwjudIC5wMdAqR5z9BJXx3cF7gsp4wLgMaAovDn6ccp2f2bbhlR8Dlyl
            Qc5B5Av4W4MsrcQx4m9Gj9NBVrdTgbGa5GnDKft5jW0bKpIL7AAu1CgzAVyKTEtjQ9wifix6nQ4y
            vSwCBmqWGwqnbOcq2zaU0wrYDZwekfzrgU8ikq1MnCK+kOicDhL1a5GkmnXi4vguwMMR67gYGAW8
            FLEeXziJ7R/YtgFkoXOjAT37kDHkkAFdNRKHiL8WM04HOBN4BnjSkL60OImt79rUn4NkEfMN6jyO
            dDu/GtRZDdsRPxKzTgdohORxbjestxJOYvPbtnQ3R7KPbSzp7wdYS826eNZmV08T3OkbT/xLAgXI
            LpUqC4DugBfQhlDY2vprD4wL8N4W4BGqb2rnAy+gluPphmRA3wxgR2ic0m+X2dD7HjBE8Z2dQF9g
            f5rP84DVwNUKMvcCnYCjiraExsbgejnqTge4h/ROB/gPKf3YAzT2KfMc4ClgSgB7QuGUFi81rXMj
            0FPxnS1IX+6HN4ARCrKPAZ2RL8wYpiP+btSdDvCpQttVqDm+CTAbGK5iUFhcksYcnwfMCvjuvwpt
            g5TzDQMWAcaqu0xG/ATgPAN6gkwPc5DpZR/NtqTF1HSyLTKIxZneyKD/vgllpiI+bKmGKeYg5YLH
            o1ZkwvEFhC/VMEV7YDwy2EaKCccvQPrQ2sIk4HXgryiVRJ2ruYWY1zCmoAVSbTwqSiVRRnwukn6t
            jTwAvAj8EJWCKGc1UZRqmKIB0kVeG5WCqBZQZwDPRiHYINcAg5GVsHai6moKibZUwxTzgTVINZpW
            ohhcuwAP6RZqiU7AGCTXr5UoIn4+MrBmC1OAt4ADOoXqdvx1mCvVMEUrpOt8XKdQ19M3uJYnmrKR
            0cASYJcugToj/kHgEl3CYkYu0oUO1iVQl+ObIwcAsplByLx+rQ5huhw/GXv1MSZZgJzPCu00HY5v
            j+aBJ8bkI9Vvr4QVpMPxc/F33D1bmAYsB46EERLW8b2Rkoq6RBukCm5SGCGuF27lujDMy7WYccBS
            4PegAsJE/HDksoa6SB6yTTg0qICg2cnGwMygSrOEIUhJyNdBXg4a8aZKNeLOQoIVaAVyfFtgYhBl
            IVBJumW6xUknPZAuV/mQQRDHz8R8qUZXhbaBIjAEs4AVqFW7KTu+ALhX5QVNDEAug/szQzsXyRmZ
            5FzkMNs0lZdU91wXYqdUowFy69JQal6uT0cuGDLNROA1pN7eFyoRfyvBjrzo4g7khxtD9V9rF8mZ
            2yoTbIp0wSP8vuAce8fXGYFc4Efk7hfb7EcWL8XI8fgC5FR4O5tGIb+JPYHv/DT2G/GPEg+nA7RG
            sqFxIwfpivv6aexnAZUNpRqmuAI5P7siU0M/VQZTkZuO6vHHXKQWp6SmRplmNV2IuIYwC+mI7E/M
            q6lRpj6+iOwq1TDFZOT87L50DWpyfH9idp3UCdYhBaXbkAGtFxJh3WwaVYWWyIJqdLoGztFl/VM9
            zwG2Eq+qgVLgLiDdBTvjiVd5SQJJdexI9WG6iI9jqcb9pHc6yFSuGYpL9whxkUAYkOpD5+ir1U6g
            t0Bu1TgrWruU2Iq/RFlDJJ/TOlpzlBiIFL5WItWsZjLxcjrIStUPJcigNiE6U5QpQg5IV5q3V+1q
            OiD38caN7QptU/apFrkISWksrviwquPnEM9SDSeitqYoRDZLTl5CV9HxfYhvqUY34EufbVU2TUzR
            Gin3fqL8gfPPksvK/19MfKsGdiGr6Ew0QQbXOKY4SpBL6HbDqYiPe6lGZyQPvzhDu+nE0+kgM655
            yL4GLslkmFs1TLIIWZSkmuHkIE4fb9QidcrP/a53PS85Etk3jDs5wMucivxtyJZgL+Sesg72TFOi
            EOjn4pUNsm2JIvnIF1Bb6Q20dPGSvWxbUsdwge4uXrKhbUvqILkuXvIglf9oYT3Rc8jFS65DLmmr
            xwyHgU0uXnI59Y43yUog4XpecjXytzNS5o3r0coh5P76k+UdwxDnx3n1Wts5AtzEifrP8pTBAeSa
            kHnIJTn1G9x62YDc37O5/IFzePb5VRt1BG5ArvhuZMy07OQnYD0pMqv/A+XLqxL7gExFAAAAAElF
            TkSuQmCC
        </pkg:binaryData>
    </pkg:part>
    <pkg:part pkg:name="/word/media/image4.png" pkg:contentType="image/png">
        <pkg:binaryData>iVBORw0KGgoAAAANSUhEUgAAAF4AAABVCAYAAADAFVOtAAAHK0lEQVR4nO2de2xURRSHvyvbIlRB
            EaKxEp4aECKigIAKYlTwEUWUSDSpEXyCEFFTBFERkApISXwbMCaoiUZSJBhQsGLQoBgDgkhEUMHE
            QFCQR61g6V7/OF0sy7K9c+/M3Nltv6T/7J175vTsdB5nfjP1/K1byUG6A50BH9hW95NTJPD9uH0I
            SgEwHngUKE57thOYDbwBJC37FQrP37Ilbh+C0Bb4FOjVQLmvgGHAQeMeRSRB0vkGcjqwGugZoOwA
            YBUwCDhi0qmoJHz3u5rnCBb0FP2AJ4GnzbijBy+5cWPcPmSjO7AJSCi+9w/QDfhNu0eacH1wnYd6
            0AFaAM8Dd+p1Rx9ecv36uH04GUOBjyPaGAB8rcEX7bg6uDZDWntU5iPBdw5Xu5r7gR4a7PQHRgHv
            abClFa923bq4fUinFbAdaKfJ3k5koD2syZ4WXOxqpqIv6AAdkNXuLI02I+PVrl0btw/16QxsAZpr
            tnsIuADYrdluaFxr8XPQH3SQ1e9M4F4DtkPh1a5ZE7cPKa4ETDpTC/QBvjNYR2BcmtWUG7bfrK6O
            qw3XE4iE70ZXU4K0RtMMAW4BllqoKyve0crKuH1ogWxkpOfYTbENWSPUWKovIy4MrqXYCzrA+cDD
            yKo2NryjK1fGWf+5wE9AkeV6/0K+gL2W6z1G3C2+DPtBBzgTmIZsJcaCV7N8eVx1Xwp8A5wS4t1d
            wDJki68YGTBbKtqoAS4CfgxRf2TinE7ORz3o1UgrfZvjB8c2wGPAFAVbBUgG9EZFH7QQV1dzG7Jg
            UuEIMAL4JMOzfch2317U0sk3ANcB1gc6r2ap9SltIZKP6aL43hxgUoByy4CbFOxuRtQLVltgHF3N
            BNSDDvBmwHKvohb4nkj+/3VljyLg1VRU2KyvLZJrb6343g6gU8CyHtItFSjY/wPoikU9jm15x3TU
            gw4yqAbFR9LAbRTeaYfsA5SqOBUFm4PrhcB9luoK05omAK8Bv2r2JSM2Ax9WqmGL5sgAPtJGZbYG
            12F1P65zOzLN/cJ0RTZavC6phi3Kgb6mK7ER+AeQ/j1X6IPsDywyWYnpWU1rJBmVa8wCFqM2m1LC
            dIvXLdWwRTEytZxmqgKTge9CjGlXDTwOLAB+N2Hc5KzGlFTDFkXIfkGJCeOmWvwgJJOY69wFvAh8
            q9uwicB7mJdq2OIUZN9ANYXdICbkHSXI7lK+cAWymv1Ap1HdfXxL5MxSvjEb0eL8q8ug7q7GtlTD
            Fp2AicgXoAWdgS9GpmD5yhTgLWCPDmM6u5q4pBq2aAXMQFIgkdHV4vsgU698ZwzwMvB9VEO6ZjXl
            hNPH5BopxfG1UQ3p6GpSOezGwjXIZvpHUYxE7WoK0TjS5xBzkTO4R8MaiNriH0HOLTU2ugEPAS+F
            NRClxbdDTTKXbzwDvIMoj5WJEviwUo184SzkhpCJYV4OuwPVA4dO0MXIWES5pnw1V9gWX45dqUah
            YnlVyXZYCoEXEJm4EmECfz2isLVJe0QZti9A2Z7IuSpb3IycJPxM5SXVWU1cUo3myMo4yCxinGFf
            MlEO9EZBwaba4h9Ebk2Kg1IkNZvt1qXLMbRV1wC9kHTCwqAveNVTpwYt2xpR+rZV90sb2xBF2i8Z
            ng0BPkSSWXGwGznQVhWksMqs5iniDTrIL7YVWILoXg4iJwfHAANj9AvgHGRdE2ht4/09eXKQcl2A
            H8ht1YANDiOr2p0NFQw6uM6lKehBOBXJXY1qqGCQwXUwcKsGpxoLdyCSkKwXATUU+HySathkPnBZ
            tgINBf5u4BKdHjUS+iHrjndPViDbDlQRbko1dgCvACuQwawDcmpvBGoHzkxTBlQgt76eQLbBdRIy
            VXOJRcA9HH8m9Wdkud4LqESyhi7QHlFdzMj00Ksan1HQex4yX7aVbApCBbLNmG0a1hv4Enf8rkIu
            oduV/uBkfXwZ7jifYjIN50I2AO8jfxUucBrSXY9Of+BVjR2b/llf5D5el1QDqwl+l1g/wKVbTJOI
            /GVD/Q8ztXgXpRqbDZW1QUpxfFX9D9NzNSMRdaxreAplXWs08P8idEnqg/ot3mWphspa4mJjXkRj
            DqLFqYHjAz+R4Bc12GYgcpvSpgBlTxi0HKErcmx/HoB3aPRoEKnGduLLZQdhFaLgyqZRH1RXTnWP
            1hb7kdT2n6kWPx23gw6iV1yMJKEyrQYHI3/KrgYd4AzgWWCcd6ikpBi5scKl5XY29iFbbCuQL6Aj
            kjJw4uraANQAHRO+7w8jd4IOojYoxeLdMpopAIYmSCazpi+bMEL/BMlkLrX2fCGRwPf3x+1FI+RA
            gmTyc0Ru3YQ9Vifw/VXILXS5eMtGLrIHqEz4yWQ1ogVZELNDjYUpQHVqAbUQWdIGucm0ifCUUXdx
            af2tvyeQtMFM4Ox4/MpbdiNKvGPaSu/A8OHphYqQnMhAmvr9qOxB/qvyMtKu2foP1iKzVwWijq0A
            AAAASUVORK5CYII=
        </pkg:binaryData>
    </pkg:part>
    <#list reportList as item>
        <pkg:part pkg:name="/word/media/image${item_index + 10}.png" pkg:contentType="image/png">
            <pkg:binaryData>${item.imageUrl}</pkg:binaryData>
        </pkg:part>
    </#list>
    <pkg:part pkg:name="/word/settings.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml">
        <pkg:xmlData>
            <w:settings xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                        xmlns:o="urn:schemas-microsoft-com:office:office"
                        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                        xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                        xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w10="urn:schemas-microsoft-com:office:word"
                        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                        xmlns:sl="http://schemas.openxmlformats.org/schemaLibrary/2006/main"
                        xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData" mc:Ignorable="w14">
                <w:zoom w:percent="90"/>
                <w:displayBackgroundShape w:val="1"/>
                <w:bordersDoNotSurroundHeader w:val="0"/>
                <w:bordersDoNotSurroundFooter w:val="0"/>
                <w:documentProtection w:enforcement="0"/>
                <w:displayHorizontalDrawingGridEvery w:val="1"/>
                <w:displayVerticalDrawingGridEvery w:val="1"/>
                <w:noPunctuationKerning w:val="1"/>
                <w:characterSpacingControl w:val="doNotCompress"/>
                <w:footnotePr>
                    <w:footnote w:id="0"/>
                    <w:footnote w:id="1"/>
                </w:footnotePr>
                <w:endnotePr>
                    <w:endnote w:id="0"/>
                    <w:endnote w:id="1"/>
                </w:endnotePr>
                <w:compat>
                    <w:spaceForUL/>
                    <w:ulTrailSpace/>
                    <w:doNotExpandShiftReturn/>
                    <w:doNotWrapTextWithPunct/>
                    <w:doNotUseEastAsianBreakRules/>
                    <w:useFELayout/>
                    <w:doNotUseIndentAsNumberingTabStop/>
                    <w:compatSetting w:name="compatibilityMode" w:uri="http://schemas.microsoft.com/office/word"
                                     w:val="14"/>
                </w:compat>
                <w:docVars>
                    <w:docVar w:name="commondata" w:val="eyJoZGlkIjoiNGZjOTJhNWE5MTlhYzUxNjMzN2ZhZDY5OTQzNjU1ZDcifQ=="/>
                </w:docVars>
                <w:rsids>
                    <w:rsidRoot w:val="00000000"/>
                    <w:rsid w:val="11DA3D05"/>
                    <w:rsid w:val="196F5132"/>
                    <w:rsid w:val="1CBF2402"/>
                    <w:rsid w:val="1CC952A6"/>
                    <w:rsid w:val="2E7D5CEC"/>
                    <w:rsid w:val="366D2511"/>
                    <w:rsid w:val="382A0C93"/>
                    <w:rsid w:val="3963708B"/>
                    <w:rsid w:val="3CF67395"/>
                    <w:rsid w:val="41780CC1"/>
                    <w:rsid w:val="4B217A7B"/>
                    <w:rsid w:val="51024103"/>
                    <w:rsid w:val="528043E7"/>
                    <w:rsid w:val="54DE7BDB"/>
                    <w:rsid w:val="5F8E5854"/>
                    <w:rsid w:val="60151D76"/>
                    <w:rsid w:val="61CB0541"/>
                    <w:rsid w:val="652341F0"/>
                    <w:rsid w:val="65B0249C"/>
                    <w:rsid w:val="69891532"/>
                    <w:rsid w:val="73410663"/>
                    <w:rsid w:val="7487654A"/>
                    <w:rsid w:val="75511CC0"/>
                    <w:rsid w:val="777803CC"/>
                    <w:rsid w:val="7CC729E0"/>
                    <w:rsid w:val="7F3705E3"/>
                </w:rsids>
                <m:mathPr>
                    <m:brkBin m:val="before"/>
                    <m:brkBinSub m:val="--"/>
                    <m:smallFrac m:val="0"/>
                    <m:dispDef/>
                    <m:lMargin m:val="0"/>
                    <m:rMargin m:val="0"/>
                    <m:defJc m:val="centerGroup"/>
                    <m:wrapIndent m:val="1440"/>
                    <m:intLim m:val="subSup"/>
                    <m:naryLim m:val="undOvr"/>
                </m:mathPr>
                <w:themeFontLang w:val="en-US" w:eastAsia="zh-CN"/>
                <w:clrSchemeMapping w:bg1="light1" w:t1="dark1" w:bg2="light2" w:t2="dark2" w:accent1="accent1"
                                    w:accent2="accent2" w:accent3="accent3" w:accent4="accent4" w:accent5="accent5"
                                    w:accent6="accent6" w:hyperlink="hyperlink"
                                    w:followedHyperlink="followedHyperlink"/>
                <w:shapeDefaults>
                    <o:shapedefaults fillcolor="#FFFFFF" fill="t" stroke="t">
                        <v:fill on="t" focussize="0,0"/>
                        <v:stroke color="#000000"/>
                    </o:shapedefaults>
                    <o:shapelayout v:ext="edit">
                        <o:idmap v:ext="edit" data="1"/>
                    </o:shapelayout>
                </w:shapeDefaults>
            </w:settings>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/styles.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml">
        <pkg:xmlData>
            <w:styles xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                      xmlns:o="urn:schemas-microsoft-com:office:office"
                      xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                      xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                      xmlns:v="urn:schemas-microsoft-com:vml"
                      xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                      xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                      xmlns:w10="urn:schemas-microsoft-com:office:word"
                      xmlns:sl="http://schemas.openxmlformats.org/schemaLibrary/2006/main"
                      xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData" mc:Ignorable="w14">
                <w:docDefaults>
                    <w:rPrDefault>
                        <w:rPr>
                            <w:rFonts w:ascii="Times New Roman" w:hAnsi="Times New Roman" w:eastAsia="宋体"
                                      w:cs="Times New Roman"/>
                        </w:rPr>
                    </w:rPrDefault>
                    <w:pPrDefault/>
                </w:docDefaults>
                <w:latentStyles w:count="260" w:defQFormat="0" w:defUnhideWhenUsed="1" w:defSemiHidden="1"
                                w:defUIPriority="99" w:defLockedState="0">
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Normal"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="heading 1"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 2"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 3"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 4"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 5"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 6"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 7"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 8"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 9"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 9"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 9"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Normal Indent"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="footnote text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="annotation text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="header"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="footer"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index heading"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="caption"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="table of figures"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="envelope address"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="envelope return"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="footnote reference"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="annotation reference"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="line number"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="page number"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="endnote reference"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="endnote text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="table of authorities"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="macro"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toa heading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 5"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Title"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Closing"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Signature"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0"
                                    w:name="Default Paragraph Font"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:name="Body Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text Indent"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Message Header"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Subtitle"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Salutation"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Date"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Body Text First Indent"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Body Text First Indent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Note Heading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text Indent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text Indent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Block Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Hyperlink"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="FollowedHyperlink"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Strong"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Emphasis"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Document Map"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Plain Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="E-mail Signature"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Normal (Web)"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Acronym"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Address"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Cite"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Code"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Definition"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Keyboard"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Preformatted"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Sample"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Typewriter"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Variable"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:name="Normal Table"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="annotation subject"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Simple 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Simple 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Simple 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Colorful 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Colorful 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Colorful 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table 3D effects 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table 3D effects 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table 3D effects 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Contemporary"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Elegant"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Professional"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Subtle 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Subtle 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Web 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Web 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Web 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Balloon Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Theme"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0" w:name="Light Shading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0" w:name="Light List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0" w:name="Light Grid"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0" w:name="Medium Shading 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0" w:name="Medium Shading 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0" w:name="Medium List 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0" w:name="Medium List 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0" w:name="Medium Grid 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0" w:name="Medium Grid 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0" w:name="Medium Grid 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0" w:name="Dark List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0" w:name="Colorful Shading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0" w:name="Colorful List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0" w:name="Colorful Grid"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 6"/>
                </w:latentStyles>
                <w:style w:type="paragraph" w:default="1" w:styleId="1">
                    <w:name w:val="Normal"/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:pPr>
                        <w:kinsoku w:val="0"/>
                        <w:autoSpaceDE w:val="0"/>
                        <w:autoSpaceDN w:val="0"/>
                        <w:adjustRightInd w:val="0"/>
                        <w:snapToGrid w:val="0"/>
                        <w:spacing w:line="240" w:lineRule="auto"/>
                        <w:jc w:val="left"/>
                        <w:textAlignment w:val="baseline"/>
                    </w:pPr>
                    <w:rPr>
                        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                        <w:snapToGrid w:val="0"/>
                        <w:color w:val="000000"/>
                        <w:kern w:val="0"/>
                        <w:sz w:val="21"/>
                        <w:szCs w:val="21"/>
                        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA"/>
                    </w:rPr>
                </w:style>
                <w:style w:type="character" w:default="1" w:styleId="6">
                    <w:name w:val="Default Paragraph Font"/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                </w:style>
                <w:style w:type="table" w:default="1" w:styleId="4">
                    <w:name w:val="Normal Table"/>
                    <w:semiHidden/>
                    <w:uiPriority w:val="0"/>
                    <w:tblPr>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="108" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="108" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPr>
                </w:style>
                <w:style w:type="paragraph" w:styleId="2">
                    <w:name w:val="Body Text"/>
                    <w:basedOn w:val="1"/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:rPr>
                        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                        <w:sz w:val="21"/>
                        <w:szCs w:val="21"/>
                        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA"/>
                    </w:rPr>
                </w:style>
                <w:style w:type="paragraph" w:styleId="3">
                    <w:name w:val="HTML Preformatted"/>
                    <w:basedOn w:val="1"/>
                    <w:uiPriority w:val="0"/>
                    <w:pPr>
                        <w:tabs>
                            <w:tab w:val="left" w:pos="916"/>
                            <w:tab w:val="left" w:pos="1832"/>
                            <w:tab w:val="left" w:pos="2748"/>
                            <w:tab w:val="left" w:pos="3664"/>
                            <w:tab w:val="left" w:pos="4580"/>
                            <w:tab w:val="left" w:pos="5496"/>
                            <w:tab w:val="left" w:pos="6412"/>
                            <w:tab w:val="left" w:pos="7328"/>
                            <w:tab w:val="left" w:pos="8244"/>
                            <w:tab w:val="left" w:pos="9160"/>
                            <w:tab w:val="left" w:pos="10076"/>
                            <w:tab w:val="left" w:pos="10992"/>
                            <w:tab w:val="left" w:pos="11908"/>
                            <w:tab w:val="left" w:pos="12824"/>
                            <w:tab w:val="left" w:pos="13740"/>
                            <w:tab w:val="left" w:pos="14656"/>
                        </w:tabs>
                        <w:jc w:val="left"/>
                    </w:pPr>
                    <w:rPr>
                        <w:rFonts w:hint="eastAsia" w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                        <w:kern w:val="0"/>
                        <w:sz w:val="24"/>
                        <w:szCs w:val="24"/>
                        <w:lang w:val="en-US" w:eastAsia="zh-CN" w:bidi="ar"/>
                    </w:rPr>
                </w:style>
                <w:style w:type="table" w:styleId="5">
                    <w:name w:val="Table Grid"/>
                    <w:basedOn w:val="4"/>
                    <w:uiPriority w:val="0"/>
                    <w:pPr>
                        <w:widowControl w:val="0"/>
                        <w:jc w:val="both"/>
                    </w:pPr>
                    <w:tblPr>
                        <w:tblBorders>
                            <w:top w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                            <w:left w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                            <w:bottom w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                            <w:right w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                            <w:insideH w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                            <w:insideV w:val="single" w:color="auto" w:sz="4" w:space="0"/>
                        </w:tblBorders>
                    </w:tblPr>
                </w:style>
                <w:style w:type="table" w:customStyle="1" w:styleId="7">
                    <w:name w:val="Table Normal"/>
                    <w:semiHidden/>
                    <w:unhideWhenUsed/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:tblPr>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="0" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="0" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPr>
                </w:style>
                <w:style w:type="paragraph" w:customStyle="1" w:styleId="8">
                    <w:name w:val="Table Text"/>
                    <w:basedOn w:val="1"/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:rPr>
                        <w:rFonts w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                        <w:sz w:val="25"/>
                        <w:szCs w:val="25"/>
                        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA"/>
                    </w:rPr>
                </w:style>
            </w:styles>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/theme/theme1.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.theme+xml">
        <pkg:xmlData>
            <a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office">
                <a:themeElements>
                    <a:clrScheme name="Office">
                        <a:dk1>
                            <a:sysClr val="windowText" lastClr="000000"/>
                        </a:dk1>
                        <a:lt1>
                            <a:sysClr val="window" lastClr="FFFFFF"/>
                        </a:lt1>
                        <a:dk2>
                            <a:srgbClr val="1F497D"/>
                        </a:dk2>
                        <a:lt2>
                            <a:srgbClr val="EEECE1"/>
                        </a:lt2>
                        <a:accent1>
                            <a:srgbClr val="4F81BD"/>
                        </a:accent1>
                        <a:accent2>
                            <a:srgbClr val="C0504D"/>
                        </a:accent2>
                        <a:accent3>
                            <a:srgbClr val="9BBB59"/>
                        </a:accent3>
                        <a:accent4>
                            <a:srgbClr val="8064A2"/>
                        </a:accent4>
                        <a:accent5>
                            <a:srgbClr val="4BACC6"/>
                        </a:accent5>
                        <a:accent6>
                            <a:srgbClr val="F79646"/>
                        </a:accent6>
                        <a:hlink>
                            <a:srgbClr val="0000FF"/>
                        </a:hlink>
                        <a:folHlink>
                            <a:srgbClr val="800080"/>
                        </a:folHlink>
                    </a:clrScheme>
                    <a:fontScheme name="Office">
                        <a:majorFont>
                            <a:latin typeface="Cambria"/>
                            <a:ea typeface=""/>
                            <a:cs typeface=""/>
                            <a:font script="Jpan" typeface="ＭＳ ゴシック"/>
                            <a:font script="Hang" typeface="맑은 고딕"/>
                            <a:font script="Hans" typeface="宋体"/>
                            <a:font script="Hant" typeface="新細明體"/>
                            <a:font script="Arab" typeface="Times New Roman"/>
                            <a:font script="Hebr" typeface="Times New Roman"/>
                            <a:font script="Thai" typeface="Angsana New"/>
                            <a:font script="Ethi" typeface="Nyala"/>
                            <a:font script="Beng" typeface="Vrinda"/>
                            <a:font script="Gujr" typeface="Shruti"/>
                            <a:font script="Khmr" typeface="MoolBoran"/>
                            <a:font script="Knda" typeface="Tunga"/>
                            <a:font script="Guru" typeface="Raavi"/>
                            <a:font script="Cans" typeface="Euphemia"/>
                            <a:font script="Cher" typeface="Plantagenet Cherokee"/>
                            <a:font script="Yiii" typeface="Microsoft Yi Baiti"/>
                            <a:font script="Tibt" typeface="Microsoft Himalaya"/>
                            <a:font script="Thaa" typeface="MV Boli"/>
                            <a:font script="Deva" typeface="Mangal"/>
                            <a:font script="Telu" typeface="Gautami"/>
                            <a:font script="Taml" typeface="Latha"/>
                            <a:font script="Syrc" typeface="Estrangelo Edessa"/>
                            <a:font script="Orya" typeface="Kalinga"/>
                            <a:font script="Mlym" typeface="Kartika"/>
                            <a:font script="Laoo" typeface="DokChampa"/>
                            <a:font script="Sinh" typeface="Iskoola Pota"/>
                            <a:font script="Mong" typeface="Mongolian Baiti"/>
                            <a:font script="Viet" typeface="Times New Roman"/>
                            <a:font script="Uigh" typeface="Microsoft Uighur"/>
                            <a:font script="Geor" typeface="Sylfaen"/>
                        </a:majorFont>
                        <a:minorFont>
                            <a:latin typeface="Calibri"/>
                            <a:ea typeface=""/>
                            <a:cs typeface=""/>
                            <a:font script="Jpan" typeface="ＭＳ 明朝"/>
                            <a:font script="Hang" typeface="맑은 고딕"/>
                            <a:font script="Hans" typeface="宋体"/>
                            <a:font script="Hant" typeface="新細明體"/>
                            <a:font script="Arab" typeface="Arial"/>
                            <a:font script="Hebr" typeface="Arial"/>
                            <a:font script="Thai" typeface="Cordia New"/>
                            <a:font script="Ethi" typeface="Nyala"/>
                            <a:font script="Beng" typeface="Vrinda"/>
                            <a:font script="Gujr" typeface="Shruti"/>
                            <a:font script="Khmr" typeface="DaunPenh"/>
                            <a:font script="Knda" typeface="Tunga"/>
                            <a:font script="Guru" typeface="Raavi"/>
                            <a:font script="Cans" typeface="Euphemia"/>
                            <a:font script="Cher" typeface="Plantagenet Cherokee"/>
                            <a:font script="Yiii" typeface="Microsoft Yi Baiti"/>
                            <a:font script="Tibt" typeface="Microsoft Himalaya"/>
                            <a:font script="Thaa" typeface="MV Boli"/>
                            <a:font script="Deva" typeface="Mangal"/>
                            <a:font script="Telu" typeface="Gautami"/>
                            <a:font script="Taml" typeface="Latha"/>
                            <a:font script="Syrc" typeface="Estrangelo Edessa"/>
                            <a:font script="Orya" typeface="Kalinga"/>
                            <a:font script="Mlym" typeface="Kartika"/>
                            <a:font script="Laoo" typeface="DokChampa"/>
                            <a:font script="Sinh" typeface="Iskoola Pota"/>
                            <a:font script="Mong" typeface="Mongolian Baiti"/>
                            <a:font script="Viet" typeface="Arial"/>
                            <a:font script="Uigh" typeface="Microsoft Uighur"/>
                            <a:font script="Geor" typeface="Sylfaen"/>
                        </a:minorFont>
                    </a:fontScheme>
                    <a:fmtScheme name="Office">
                        <a:fillStyleLst>
                            <a:solidFill>
                                <a:schemeClr val="phClr"/>
                            </a:solidFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="50000"/>
                                            <a:satMod val="300000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="35000">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="37000"/>
                                            <a:satMod val="300000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="15000"/>
                                            <a:satMod val="350000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:lin ang="16200000" scaled="1"/>
                            </a:gradFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="51000"/>
                                            <a:satMod val="130000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="80000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="93000"/>
                                            <a:satMod val="130000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="94000"/>
                                            <a:satMod val="135000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:lin ang="16200000" scaled="0"/>
                            </a:gradFill>
                        </a:fillStyleLst>
                        <a:lnStyleLst>
                            <a:ln w="9525" cap="flat" cmpd="sng" algn="ctr">
                                <a:solidFill>
                                    <a:schemeClr val="phClr">
                                        <a:shade val="95000"/>
                                        <a:satMod val="105000"/>
                                    </a:schemeClr>
                                </a:solidFill>
                                <a:prstDash val="solid"/>
                            </a:ln>
                            <a:ln w="25400" cap="flat" cmpd="sng" algn="ctr">
                                <a:solidFill>
                                    <a:schemeClr val="phClr"/>
                                </a:solidFill>
                                <a:prstDash val="solid"/>
                            </a:ln>
                            <a:ln w="38100" cap="flat" cmpd="sng" algn="ctr">
                                <a:solidFill>
                                    <a:schemeClr val="phClr"/>
                                </a:solidFill>
                                <a:prstDash val="solid"/>
                            </a:ln>
                        </a:lnStyleLst>
                        <a:effectStyleLst>
                            <a:effectStyle>
                                <a:effectLst>
                                    <a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0">
                                        <a:srgbClr val="000000">
                                            <a:alpha val="38000"/>
                                        </a:srgbClr>
                                    </a:outerShdw>
                                </a:effectLst>
                            </a:effectStyle>
                            <a:effectStyle>
                                <a:effectLst>
                                    <a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0">
                                        <a:srgbClr val="000000">
                                            <a:alpha val="35000"/>
                                        </a:srgbClr>
                                    </a:outerShdw>
                                </a:effectLst>
                            </a:effectStyle>
                            <a:effectStyle>
                                <a:effectLst>
                                    <a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0">
                                        <a:srgbClr val="000000">
                                            <a:alpha val="35000"/>
                                        </a:srgbClr>
                                    </a:outerShdw>
                                </a:effectLst>
                                <a:scene3d>
                                    <a:camera prst="orthographicFront">
                                        <a:rot lat="0" lon="0" rev="0"/>
                                    </a:camera>
                                    <a:lightRig rig="threePt" dir="t">
                                        <a:rot lat="0" lon="0" rev="1200000"/>
                                    </a:lightRig>
                                </a:scene3d>
                                <a:sp3d>
                                    <a:bevelT w="63500" h="25400"/>
                                </a:sp3d>
                            </a:effectStyle>
                        </a:effectStyleLst>
                        <a:bgFillStyleLst>
                            <a:solidFill>
                                <a:schemeClr val="phClr"/>
                            </a:solidFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="40000"/>
                                            <a:satMod val="350000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="40000">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="45000"/>
                                            <a:satMod val="350000"/>
                                            <a:shade val="99000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="20000"/>
                                            <a:satMod val="255000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:path path="circle">
                                    <a:fillToRect l="50000" t="-80000" r="50000" b="180000"/>
                                </a:path>
                            </a:gradFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="80000"/>
                                            <a:satMod val="300000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="30000"/>
                                            <a:satMod val="200000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:path path="circle">
                                    <a:fillToRect l="50000" t="50000" r="50000" b="50000"/>
                                </a:path>
                            </a:gradFill>
                        </a:bgFillStyleLst>
                    </a:fmtScheme>
                </a:themeElements>
                <a:objectDefaults/>
            </a:theme>
        </pkg:xmlData>
    </pkg:part>
</pkg:package>
