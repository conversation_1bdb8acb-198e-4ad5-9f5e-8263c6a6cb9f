package com.ruoyi.jiangshan.mapper;

import java.util.List;
import com.ruoyi.jiangshan.domain.DeviceMonitorShuiku;

/**
 * 监测对象-水库Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface DeviceMonitorShuikuMapper
{
    /**
     * 查询监测对象-水库
     *
     * @param f1 监测对象-水库主键
     * @return 监测对象-水库
     */
    public DeviceMonitorShuiku selectDeviceMonitorShuikuByF1(String f1);

    /**
     * 查询监测对象-水库列表
     *
     * @param deviceMonitorShuiku 监测对象-水库
     * @return 监测对象-水库集合
     */
    public List<DeviceMonitorShuiku> selectDeviceMonitorShuikuList(DeviceMonitorShuiku deviceMonitorShuiku);

    /**
     * 新增监测对象-水库
     *
     * @param deviceMonitorShuiku 监测对象-水库
     * @return 结果
     */
    public int insertDeviceMonitorShuiku(DeviceMonitorShuiku deviceMonitorShuiku);

    /**
     * 修改监测对象-水库
     *
     * @param deviceMonitorShuiku 监测对象-水库
     * @return 结果
     */
    public int updateDeviceMonitorShuiku(DeviceMonitorShuiku deviceMonitorShuiku);

    /**
     * 删除监测对象-水库
     *
     * @param f1 监测对象-水库主键
     * @return 结果
     */
    public int deleteDeviceMonitorShuikuByF1(String f1);

    /**
     * 批量删除监测对象-水库
     *
     * @param f1s 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceMonitorShuikuByF1s(String[] f1s);

    List<DeviceMonitorShuiku> listAll();

    DeviceMonitorShuiku getByMonitorName(String monitorName);
}
