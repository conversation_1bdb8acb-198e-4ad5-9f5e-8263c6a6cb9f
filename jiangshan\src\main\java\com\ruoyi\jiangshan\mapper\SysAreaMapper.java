package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.SysArea;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SysAreaMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SysArea record);

    int insertSelective(SysArea record);

    SysArea selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysArea record);

    int updateByPrimaryKey(SysArea record);

    int updateBatch(List<SysArea> list);

    int batchInsert(@Param("list") List<SysArea> list);

    List<SysArea> getAreaTree(Long parentId);
}
