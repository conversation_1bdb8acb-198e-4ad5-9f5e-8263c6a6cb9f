package com.ruoyi.jiangshan.domain;

import lombok.Data;

import java.util.Date;

/**
    * 城市安全体检报告管理-期数
    */
@Data
public class CitysafetyPeriods {
    /**
    * 主键
    */
    private Long id;

    /**
    * 年份
    */
    private String year;

    /**
    * 期数
    */
    private Integer periods;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 创建人
    */
    private String createBy;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 更新人
    */
    private String updateBy;

    private Integer currentPeriods;

    public Integer getNextPeriods() {
        currentPeriods += 1;

        return currentPeriods;
    }
}
