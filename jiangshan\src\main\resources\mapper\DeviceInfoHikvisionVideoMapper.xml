<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceInfoHikvisionVideoMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.DeviceInfoHikvisionVideo">
    <!--@mbg.generated-->
    <!--@Table t_device_info_hikvision_video-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="camera_index_code" jdbcType="VARCHAR" property="cameraIndexCode" />
    <result column="gb_index_code" jdbcType="VARCHAR" property="gbIndexCode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="device_index_code" jdbcType="VARCHAR" property="deviceIndexCode" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
    <result column="altitude" jdbcType="VARCHAR" property="altitude" />
    <result column="pixel" jdbcType="INTEGER" property="pixel" />
    <result column="camera_type" jdbcType="INTEGER" property="cameraType" />
    <result column="camera_type_name" jdbcType="VARCHAR" property="cameraTypeName" />
    <result column="install_place" jdbcType="VARCHAR" property="installPlace" />
    <result column="matrix_code" jdbcType="VARCHAR" property="matrixCode" />
    <result column="chan_num" jdbcType="VARCHAR" property="chanNum" />
    <result column="viewshed" jdbcType="LONGVARCHAR" property="viewshed" />
    <result column="capability_set" jdbcType="VARCHAR" property="capabilitySet" />
    <result column="capability_set_name" jdbcType="VARCHAR" property="capabilitySetName" />
    <result column="intelligent_set" jdbcType="VARCHAR" property="intelligentSet" />
    <result column="intelligent_set_name" jdbcType="VARCHAR" property="intelligentSetName" />
    <result column="record_location" jdbcType="VARCHAR" property="recordLocation" />
    <result column="record_location_name" jdbcType="VARCHAR" property="recordLocationName" />
    <result column="ptz_controller" jdbcType="INTEGER" property="ptzController" />
    <result column="ptz_controller_name" jdbcType="VARCHAR" property="ptzControllerName" />
    <result column="device_resource_type" jdbcType="VARCHAR" property="deviceResourceType" />
    <result column="device_resource_type_name" jdbcType="VARCHAR" property="deviceResourceTypeName" />
    <result column="channel_type" jdbcType="VARCHAR" property="channelType" />
    <result column="channel_type_name" jdbcType="VARCHAR" property="channelTypeName" />
    <result column="trans_type" jdbcType="INTEGER" property="transType" />
    <result column="trans_type_name" jdbcType="VARCHAR" property="transTypeName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="unit_index_code" jdbcType="VARCHAR" property="unitIndexCode" />
    <result column="treaty_type" jdbcType="VARCHAR" property="treatyType" />
    <result column="treaty_type_name" jdbcType="VARCHAR" property="treatyTypeName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="statusName" jdbcType="VARCHAR" property="statusname" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, camera_index_code, gb_index_code, `name`, device_index_code, longitude, latitude,
    altitude, pixel, camera_type, camera_type_name, install_place, matrix_code, chan_num,
    viewshed, capability_set, capability_set_name, intelligent_set, intelligent_set_name,
    record_location, record_location_name, ptz_controller, ptz_controller_name, device_resource_type,
    device_resource_type_name, channel_type, channel_type_name, trans_type, trans_type_name,
    update_time, unit_index_code, treaty_type, treaty_type_name, create_time, `status`,
    statusName
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_device_info_hikvision_video
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_device_info_hikvision_video
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoHikvisionVideo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_hikvision_video (camera_index_code, gb_index_code, `name`,
      device_index_code, longitude, latitude,
      altitude, pixel, camera_type,
      camera_type_name, install_place, matrix_code,
      chan_num, viewshed, capability_set,
      capability_set_name, intelligent_set, intelligent_set_name,
      record_location, record_location_name, ptz_controller,
      ptz_controller_name, device_resource_type,
      device_resource_type_name, channel_type, channel_type_name,
      trans_type, trans_type_name, update_time,
      unit_index_code, treaty_type, treaty_type_name,
      create_time, `status`, statusName
      )
    values (#{cameraIndexCode,jdbcType=VARCHAR}, #{gbIndexCode,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{deviceIndexCode,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR},
      #{altitude,jdbcType=VARCHAR}, #{pixel,jdbcType=INTEGER}, #{cameraType,jdbcType=INTEGER},
      #{cameraTypeName,jdbcType=VARCHAR}, #{installPlace,jdbcType=VARCHAR}, #{matrixCode,jdbcType=VARCHAR},
      #{chanNum,jdbcType=VARCHAR}, #{viewshed,jdbcType=LONGVARCHAR}, #{capabilitySet,jdbcType=VARCHAR},
      #{capabilitySetName,jdbcType=VARCHAR}, #{intelligentSet,jdbcType=VARCHAR}, #{intelligentSetName,jdbcType=VARCHAR},
      #{recordLocation,jdbcType=VARCHAR}, #{recordLocationName,jdbcType=VARCHAR}, #{ptzController,jdbcType=INTEGER},
      #{ptzControllerName,jdbcType=VARCHAR}, #{deviceResourceType,jdbcType=VARCHAR},
      #{deviceResourceTypeName,jdbcType=VARCHAR}, #{channelType,jdbcType=VARCHAR}, #{channelTypeName,jdbcType=VARCHAR},
      #{transType,jdbcType=INTEGER}, #{transTypeName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
      #{unitIndexCode,jdbcType=VARCHAR}, #{treatyType,jdbcType=VARCHAR}, #{treatyTypeName,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{statusname,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoHikvisionVideo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_hikvision_video
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cameraIndexCode != null">
        camera_index_code,
      </if>
      <if test="gbIndexCode != null">
        gb_index_code,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="deviceIndexCode != null">
        device_index_code,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="altitude != null">
        altitude,
      </if>
      <if test="pixel != null">
        pixel,
      </if>
      <if test="cameraType != null">
        camera_type,
      </if>
      <if test="cameraTypeName != null">
        camera_type_name,
      </if>
      <if test="installPlace != null">
        install_place,
      </if>
      <if test="matrixCode != null">
        matrix_code,
      </if>
      <if test="chanNum != null">
        chan_num,
      </if>
      <if test="viewshed != null">
        viewshed,
      </if>
      <if test="capabilitySet != null">
        capability_set,
      </if>
      <if test="capabilitySetName != null">
        capability_set_name,
      </if>
      <if test="intelligentSet != null">
        intelligent_set,
      </if>
      <if test="intelligentSetName != null">
        intelligent_set_name,
      </if>
      <if test="recordLocation != null">
        record_location,
      </if>
      <if test="recordLocationName != null">
        record_location_name,
      </if>
      <if test="ptzController != null">
        ptz_controller,
      </if>
      <if test="ptzControllerName != null">
        ptz_controller_name,
      </if>
      <if test="deviceResourceType != null">
        device_resource_type,
      </if>
      <if test="deviceResourceTypeName != null">
        device_resource_type_name,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="channelTypeName != null">
        channel_type_name,
      </if>
      <if test="transType != null">
        trans_type,
      </if>
      <if test="transTypeName != null">
        trans_type_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="unitIndexCode != null">
        unit_index_code,
      </if>
      <if test="treatyType != null">
        treaty_type,
      </if>
      <if test="treatyTypeName != null">
        treaty_type_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="statusname != null">
        statusName,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cameraIndexCode != null">
        #{cameraIndexCode,jdbcType=VARCHAR},
      </if>
      <if test="gbIndexCode != null">
        #{gbIndexCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="deviceIndexCode != null">
        #{deviceIndexCode,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="altitude != null">
        #{altitude,jdbcType=VARCHAR},
      </if>
      <if test="pixel != null">
        #{pixel,jdbcType=INTEGER},
      </if>
      <if test="cameraType != null">
        #{cameraType,jdbcType=INTEGER},
      </if>
      <if test="cameraTypeName != null">
        #{cameraTypeName,jdbcType=VARCHAR},
      </if>
      <if test="installPlace != null">
        #{installPlace,jdbcType=VARCHAR},
      </if>
      <if test="matrixCode != null">
        #{matrixCode,jdbcType=VARCHAR},
      </if>
      <if test="chanNum != null">
        #{chanNum,jdbcType=VARCHAR},
      </if>
      <if test="viewshed != null">
        #{viewshed,jdbcType=LONGVARCHAR},
      </if>
      <if test="capabilitySet != null">
        #{capabilitySet,jdbcType=VARCHAR},
      </if>
      <if test="capabilitySetName != null">
        #{capabilitySetName,jdbcType=VARCHAR},
      </if>
      <if test="intelligentSet != null">
        #{intelligentSet,jdbcType=VARCHAR},
      </if>
      <if test="intelligentSetName != null">
        #{intelligentSetName,jdbcType=VARCHAR},
      </if>
      <if test="recordLocation != null">
        #{recordLocation,jdbcType=VARCHAR},
      </if>
      <if test="recordLocationName != null">
        #{recordLocationName,jdbcType=VARCHAR},
      </if>
      <if test="ptzController != null">
        #{ptzController,jdbcType=INTEGER},
      </if>
      <if test="ptzControllerName != null">
        #{ptzControllerName,jdbcType=VARCHAR},
      </if>
      <if test="deviceResourceType != null">
        #{deviceResourceType,jdbcType=VARCHAR},
      </if>
      <if test="deviceResourceTypeName != null">
        #{deviceResourceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="channelTypeName != null">
        #{channelTypeName,jdbcType=VARCHAR},
      </if>
      <if test="transType != null">
        #{transType,jdbcType=INTEGER},
      </if>
      <if test="transTypeName != null">
        #{transTypeName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unitIndexCode != null">
        #{unitIndexCode,jdbcType=VARCHAR},
      </if>
      <if test="treatyType != null">
        #{treatyType,jdbcType=VARCHAR},
      </if>
      <if test="treatyTypeName != null">
        #{treatyTypeName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="statusname != null">
        #{statusname,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoHikvisionVideo">
    <!--@mbg.generated-->
    update t_device_info_hikvision_video
    <set>
      <if test="cameraIndexCode != null">
        camera_index_code = #{cameraIndexCode,jdbcType=VARCHAR},
      </if>
      <if test="gbIndexCode != null">
        gb_index_code = #{gbIndexCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="deviceIndexCode != null">
        device_index_code = #{deviceIndexCode,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="altitude != null">
        altitude = #{altitude,jdbcType=VARCHAR},
      </if>
      <if test="pixel != null">
        pixel = #{pixel,jdbcType=INTEGER},
      </if>
      <if test="cameraType != null">
        camera_type = #{cameraType,jdbcType=INTEGER},
      </if>
      <if test="cameraTypeName != null">
        camera_type_name = #{cameraTypeName,jdbcType=VARCHAR},
      </if>
      <if test="installPlace != null">
        install_place = #{installPlace,jdbcType=VARCHAR},
      </if>
      <if test="matrixCode != null">
        matrix_code = #{matrixCode,jdbcType=VARCHAR},
      </if>
      <if test="chanNum != null">
        chan_num = #{chanNum,jdbcType=VARCHAR},
      </if>
      <if test="viewshed != null">
        viewshed = #{viewshed,jdbcType=LONGVARCHAR},
      </if>
      <if test="capabilitySet != null">
        capability_set = #{capabilitySet,jdbcType=VARCHAR},
      </if>
      <if test="capabilitySetName != null">
        capability_set_name = #{capabilitySetName,jdbcType=VARCHAR},
      </if>
      <if test="intelligentSet != null">
        intelligent_set = #{intelligentSet,jdbcType=VARCHAR},
      </if>
      <if test="intelligentSetName != null">
        intelligent_set_name = #{intelligentSetName,jdbcType=VARCHAR},
      </if>
      <if test="recordLocation != null">
        record_location = #{recordLocation,jdbcType=VARCHAR},
      </if>
      <if test="recordLocationName != null">
        record_location_name = #{recordLocationName,jdbcType=VARCHAR},
      </if>
      <if test="ptzController != null">
        ptz_controller = #{ptzController,jdbcType=INTEGER},
      </if>
      <if test="ptzControllerName != null">
        ptz_controller_name = #{ptzControllerName,jdbcType=VARCHAR},
      </if>
      <if test="deviceResourceType != null">
        device_resource_type = #{deviceResourceType,jdbcType=VARCHAR},
      </if>
      <if test="deviceResourceTypeName != null">
        device_resource_type_name = #{deviceResourceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="channelTypeName != null">
        channel_type_name = #{channelTypeName,jdbcType=VARCHAR},
      </if>
      <if test="transType != null">
        trans_type = #{transType,jdbcType=INTEGER},
      </if>
      <if test="transTypeName != null">
        trans_type_name = #{transTypeName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unitIndexCode != null">
        unit_index_code = #{unitIndexCode,jdbcType=VARCHAR},
      </if>
      <if test="treatyType != null">
        treaty_type = #{treatyType,jdbcType=VARCHAR},
      </if>
      <if test="treatyTypeName != null">
        treaty_type_name = #{treatyTypeName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="statusname != null">
        statusName = #{statusname,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoHikvisionVideo">
    <!--@mbg.generated-->
    update t_device_info_hikvision_video
    set camera_index_code = #{cameraIndexCode,jdbcType=VARCHAR},
      gb_index_code = #{gbIndexCode,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      device_index_code = #{deviceIndexCode,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      altitude = #{altitude,jdbcType=VARCHAR},
      pixel = #{pixel,jdbcType=INTEGER},
      camera_type = #{cameraType,jdbcType=INTEGER},
      camera_type_name = #{cameraTypeName,jdbcType=VARCHAR},
      install_place = #{installPlace,jdbcType=VARCHAR},
      matrix_code = #{matrixCode,jdbcType=VARCHAR},
      chan_num = #{chanNum,jdbcType=VARCHAR},
      viewshed = #{viewshed,jdbcType=LONGVARCHAR},
      capability_set = #{capabilitySet,jdbcType=VARCHAR},
      capability_set_name = #{capabilitySetName,jdbcType=VARCHAR},
      intelligent_set = #{intelligentSet,jdbcType=VARCHAR},
      intelligent_set_name = #{intelligentSetName,jdbcType=VARCHAR},
      record_location = #{recordLocation,jdbcType=VARCHAR},
      record_location_name = #{recordLocationName,jdbcType=VARCHAR},
      ptz_controller = #{ptzController,jdbcType=INTEGER},
      ptz_controller_name = #{ptzControllerName,jdbcType=VARCHAR},
      device_resource_type = #{deviceResourceType,jdbcType=VARCHAR},
      device_resource_type_name = #{deviceResourceTypeName,jdbcType=VARCHAR},
      channel_type = #{channelType,jdbcType=VARCHAR},
      channel_type_name = #{channelTypeName,jdbcType=VARCHAR},
      trans_type = #{transType,jdbcType=INTEGER},
      trans_type_name = #{transTypeName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      unit_index_code = #{unitIndexCode,jdbcType=VARCHAR},
      treaty_type = #{treatyType,jdbcType=VARCHAR},
      treaty_type_name = #{treatyTypeName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      statusName = #{statusname,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_device_info_hikvision_video
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="camera_index_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.cameraIndexCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="gb_index_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.gbIndexCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="device_index_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceIndexCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="longitude = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.longitude,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="latitude = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.latitude,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="altitude = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.altitude,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="pixel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.pixel,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="camera_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.cameraType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="camera_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.cameraTypeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="install_place = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.installPlace,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="matrix_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.matrixCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="chan_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.chanNum,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="viewshed = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.viewshed,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="capability_set = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.capabilitySet,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="capability_set_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.capabilitySetName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="intelligent_set = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.intelligentSet,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="intelligent_set_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.intelligentSetName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="record_location = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.recordLocation,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="record_location_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.recordLocationName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ptz_controller = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ptzController,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ptz_controller_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ptzControllerName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="device_resource_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceResourceType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="device_resource_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceResourceTypeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelTypeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="trans_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.transType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="trans_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.transTypeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="unit_index_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.unitIndexCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="treaty_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.treatyType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="treaty_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.treatyTypeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="statusName = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.statusname,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_hikvision_video
    (camera_index_code, gb_index_code, `name`, device_index_code, longitude, latitude,
      altitude, pixel, camera_type, camera_type_name, install_place, matrix_code, chan_num,
      viewshed, capability_set, capability_set_name, intelligent_set, intelligent_set_name,
      record_location, record_location_name, ptz_controller, ptz_controller_name, device_resource_type,
      device_resource_type_name, channel_type, channel_type_name, trans_type, trans_type_name,
      update_time, unit_index_code, treaty_type, treaty_type_name, create_time, `status`,
      statusName)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.cameraIndexCode,jdbcType=VARCHAR}, #{item.gbIndexCode,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR}, #{item.deviceIndexCode,jdbcType=VARCHAR}, #{item.longitude,jdbcType=VARCHAR},
        #{item.latitude,jdbcType=VARCHAR}, #{item.altitude,jdbcType=VARCHAR}, #{item.pixel,jdbcType=INTEGER},
        #{item.cameraType,jdbcType=INTEGER}, #{item.cameraTypeName,jdbcType=VARCHAR}, #{item.installPlace,jdbcType=VARCHAR},
        #{item.matrixCode,jdbcType=VARCHAR}, #{item.chanNum,jdbcType=VARCHAR}, #{item.viewshed,jdbcType=LONGVARCHAR},
        #{item.capabilitySet,jdbcType=VARCHAR}, #{item.capabilitySetName,jdbcType=VARCHAR},
        #{item.intelligentSet,jdbcType=VARCHAR}, #{item.intelligentSetName,jdbcType=VARCHAR},
        #{item.recordLocation,jdbcType=VARCHAR}, #{item.recordLocationName,jdbcType=VARCHAR},
        #{item.ptzController,jdbcType=INTEGER}, #{item.ptzControllerName,jdbcType=VARCHAR},
        #{item.deviceResourceType,jdbcType=VARCHAR}, #{item.deviceResourceTypeName,jdbcType=VARCHAR},
        #{item.channelType,jdbcType=VARCHAR}, #{item.channelTypeName,jdbcType=VARCHAR},
        #{item.transType,jdbcType=INTEGER}, #{item.transTypeName,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
        #{item.unitIndexCode,jdbcType=VARCHAR}, #{item.treatyType,jdbcType=VARCHAR}, #{item.treatyTypeName,jdbcType=VARCHAR},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER}, #{item.statusname,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

  <select id="listAll" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_device_info_hikvision_video
  </select>
</mapper>
