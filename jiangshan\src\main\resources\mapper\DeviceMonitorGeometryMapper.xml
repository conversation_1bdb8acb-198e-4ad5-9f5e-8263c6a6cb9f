<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceMonitorGeometryMapper">

    <resultMap type="DeviceMonitorGeometry" id="DeviceMonitorGeometryResult">
        <result property="id"    column="id"    />
        <result property="monitorName"    column="monitor_name"    />
        <result property="geometry"    column="geometry"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectDeviceMonitorGeometryVo">
        select id, monitor_name, `geometry`, type from t_device_monitor_geometry
    </sql>

    <select id="selectDeviceMonitorGeometryList" parameterType="DeviceMonitorGeometry" resultMap="DeviceMonitorGeometryResult">
        <include refid="selectDeviceMonitorGeometryVo"/>
        <where>
            <if test="monitorName != null  and monitorName != ''"> and monitor_name like concat('%', #{monitorName}, '%')</if>
            <if test="geometry != null  and geometry != ''"> and `geometry` = #{geometry}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
        </where>
    </select>

    <select id="selectDeviceMonitorGeometryById" parameterType="Long" resultMap="DeviceMonitorGeometryResult">
        <include refid="selectDeviceMonitorGeometryVo"/>
        where id = #{id}
    </select>

    <insert id="insertDeviceMonitorGeometry" parameterType="DeviceMonitorGeometry" useGeneratedKeys="true" keyProperty="id">
        insert into t_device_monitor_geometry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monitorName != null">monitor_name,</if>
            <if test="geometry != null">geometry,</if>
            <if test="type != null">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monitorName != null">#{monitorName},</if>
            <if test="geometry != null">#{geometry},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <update id="updateDeviceMonitorGeometry" parameterType="DeviceMonitorGeometry">
        update t_device_monitor_geometry
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitorName != null">monitor_name = #{monitorName},</if>
            <if test="geometry != null">`geometry` = #{geometry},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceMonitorGeometryById" parameterType="Long">
        delete from t_device_monitor_geometry where id = #{id}
    </delete>

    <delete id="deleteDeviceMonitorGeometryByIds" parameterType="String">
        delete from t_device_monitor_geometry where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into t_device_monitor_geometry
        (monitor_name, `geometry`, type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.monitorName,jdbcType=BIGINT}, #{item.geometry,jdbcType=TINYINT}, #{item.type,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="getByMonitorName" resultMap="DeviceMonitorGeometryResult">
        <include refid="selectDeviceMonitorGeometryVo">
        </include>
        where monitor_name = #{monitorName}
    </select>
</mapper>
