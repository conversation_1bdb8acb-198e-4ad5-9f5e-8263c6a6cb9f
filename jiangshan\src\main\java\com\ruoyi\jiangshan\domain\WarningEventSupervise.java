package com.ruoyi.jiangshan.domain;

import lombok.Data;

import java.util.Date;

/**
 * 设备预警事件-督办
 */
@Data
public class WarningEventSupervise {
    /**
     * 主键
     */
    private Long id;

    /**
     * 事件id
     */
    private Long eventId;

    /**
     * 督办类型 抄送、超时、重点关注
     */
    private String superviseType;

    private Long superviseDepartmentId;

    private String superviseDepartmentName;

    /**
     * 督办用户id
     */
    private Long superviseUserId;

    /**
     * 督办用户accountId
     */
    private String superviseUserAccountId;

    /**
     * 督办用户employeeCode
     */
    private String superviseUserEmployeeCode;

    /**
     * 督办推送用户名称
     */
    private String superviseUserName;

    /**
     * 督办意见
     */
    private String superviseContent;

    /**
     * 督办推送用户id
     */
    private Long supervisePushUserId;

    /**
     * 推送用户姓名
     */
    private String supervisePushUserName;

    /**
     * 督办浙政钉accountid
     */
    private String superviseAccountId;

    /**
     * 督办浙政钉employeeCode
     */
    private String superviceEmployeeCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;
}
