package com.ruoyi.jiangshan.dingding.client;

import com.alibaba.fastjson2.JSON;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.aliyun.dingtalktodo_1_0.models.CreateTodoTaskHeaders;
import com.aliyun.dingtalktodo_1_0.models.CreateTodoTaskRequest;
import com.aliyun.dingtalktodo_1_0.models.CreateTodoTaskResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserGetbymobileRequest;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserGetbymobileResponse;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.jiangshan.util.HttpUtil;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class DingDingClient {

    @Value("${dingding.appKey}")
    private String appkey;
    @Value("${dingding.appSecret}")
    private String appSecret;
    @Autowired
    private RedisCache redisCache;

    /**
     * 使用 Token 初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.dingtalkoauth2_1_0.Client createClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkoauth2_1_0.Client(config);
    }

    /**
     * 使用 Token 初始化账号Client
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.dingtalktodo_1_0.Client createClientV2() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalktodo_1_0.Client(config);
    }

    public String getAccessToken() {
        try {
            Object dingdingAccessToken = redisCache.getCacheObject("dingding_access_token");
            if (Objects.isNull(dingdingAccessToken)) {
                com.aliyun.dingtalkoauth2_1_0.Client client = DingDingClient.createClient();
                com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest getAccessTokenRequest = new com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest()
                        .setAppKey(appkey)
                        .setAppSecret(appSecret);

                GetAccessTokenResponse response = client.getAccessToken(getAccessTokenRequest);
                String accessToken = response.getBody().getAccessToken();
                //缓存
                redisCache.setCacheObject("dingding_access_token", accessToken, 1, TimeUnit.HOURS);

                return accessToken;
            } else {
                return (String) dingdingAccessToken;
            }
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.info("accessToken, err:{}", JSON.toJSONString(err));
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.info("accessToken, err:{}", JSON.toJSONString(err));
            }

        }

        return "";
    }

    /**
     * 根据手机号查询用户
     *
     * @return
     */
    public String getUserByMobile(String mobile) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getbymobile");
            OapiV2UserGetbymobileRequest req = new OapiV2UserGetbymobileRequest();
            req.setMobile(mobile);
            OapiV2UserGetbymobileResponse rsp = client.execute(req, getAccessToken());

            return rsp.getResult().getUserid();
        } catch (ApiException e) {
            e.printStackTrace();
        }

        return "";
    }

    public String getUnionIdByUserId(String userId) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
            OapiV2UserGetRequest req = new OapiV2UserGetRequest();
            req.setUserid(userId);
            req.setLanguage("zh_CN");
            OapiV2UserGetResponse rsp = client.execute(req, getAccessToken());

            return rsp.getResult().getUnionid();
        } catch (ApiException e) {
            e.printStackTrace();
        }

        return "";
    }

    /**
     * 发送工作通知
     *
     * @return
     */
    public String sendWordRecord(String mobile, String title, String content, Map<String, String> dingdingContentMap, String singleUrl, String singlePcUrl) {
        try {
            String userId = getUserByMobile(mobile);

            String unionId = getUnionIdByUserId(userId);

            com.aliyun.dingtalktodo_1_0.Client client = DingDingClient.createClientV2();
            CreateTodoTaskHeaders createTodoTaskHeaders = new CreateTodoTaskHeaders();
            createTodoTaskHeaders.xAcsDingtalkAccessToken = getAccessToken();

            CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs notifyConfigs = new CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs()
                    .setDingNotify("1");

            List<CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList> fieldList = Lists.newArrayList();
            for (Map.Entry<String, String> entry : dingdingContentMap.entrySet()) {
                CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList contentFieldList = new CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList();
                contentFieldList.setFieldKey(entry.getKey());
                contentFieldList.setFieldValue(entry.getValue());

                fieldList.add(contentFieldList);
            }
//            CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList contentFieldList0 = new CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList();
//            contentFieldList0.setFieldKey("事件描述");
//            contentFieldList0.setFieldValue(content);

            CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl detailUrl = new CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl()
                    .setAppUrl(singleUrl)
                    .setPcUrl(singlePcUrl);
            CreateTodoTaskRequest createTodoTaskRequest = new CreateTodoTaskRequest()
                    .setOperatorId(unionId)
                    .setSourceId(IdUtils.fastUUID())
                    .setSubject(title)
                    .setCreatorId("qiiDqCcCeHUiik9HYOP5WyeQiEiE")
                    .setDescription(content)
//                    .setDueTime(1617675000000L)
                    .setExecutorIds(java.util.Arrays.asList(
                            unionId
                    ))
                    .setParticipantIds(java.util.Arrays.asList(
                            unionId
                    ))
                    .setDetailUrl(detailUrl)
                    .setContentFieldList(fieldList)
//                    .setIsOnlyShowExecutor(true)
                    .setPriority(30)
                    .setNotifyConfigs(notifyConfigs);

            CreateTodoTaskResponse response = client.createTodoTaskWithOptions(unionId, createTodoTaskRequest, createTodoTaskHeaders, new RuntimeOptions());

            log.info("CreateTodoTaskResponse, response:{}", JSON.toJSONString(response));
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题

                log.info("CreateTodoTaskResponse, err:{}", err.getMessage());
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题

                log.info("CreateTodoTaskResponse, err:{}", err.getMessage());
            }

        }

        return "";
    }

    /**
     * 发送工作通知
     *
     * @return
     */
    public void sendBatchWordRecord(String allMobile, String title, String content, Map<String, String> dingdingContent,
                                    String singleUrl, String singlePcUrl) {
        if (StringUtils.isBlank(allMobile)) {
            return;
        }

        List<String> mobileList = Arrays.asList(StringUtils.split(allMobile, ","));

        CompletableFuture.runAsync(() -> {
            try {
                for (String mobile : mobileList) {
                    sendWordRecordTransfer(mobile, title, content, dingdingContent, singleUrl, singlePcUrl);
                }
            } catch (Exception e) {
                log.info("sendWordRecord, exception:{}", e.getMessage());
            }
        });
    }

    private void sendWordRecordTransfer(String mobile, String title, String content, Map<String, String> dingdingContent,
                                        String singleUrl, String singlePcUrl) {
        HttpUtil httpUtil = HttpUtil.getInstance();

        Map<String, Object> params = new HashMap<>();
        params.put("mobile", mobile);
        params.put("title", title);
        params.put("content", content);
        params.put("dingdingContentMap", dingdingContent);
        params.put("singleUrl", singleUrl);
        params.put("singlePcUrl", singlePcUrl);

        log.info("sendWordRecordTransfer, start, params:{}", JSON.toJSONString(params));

        String response = httpUtil.sendHttpPostJSON("https://csaqyj.jiangshan.gov.cn:8501/local-api/dingding/sendMsg", params);

        Map map = com.alibaba.fastjson.JSON.parseObject(response, Map.class);

        log.info("sendWordRecordTransfer, success, map:{}", JSON.toJSONString(map));
    }

}
