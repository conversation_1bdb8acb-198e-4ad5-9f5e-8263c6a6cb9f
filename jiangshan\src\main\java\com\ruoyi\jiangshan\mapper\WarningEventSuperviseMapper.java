package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningEventSupervise;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface WarningEventSuperviseMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningEventSupervise record);

    int insertSelective(WarningEventSupervise record);

    WarningEventSupervise selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningEventSupervise record);

    int updateByPrimaryKey(WarningEventSupervise record);

    int updateBatch(List<WarningEventSupervise> list);

    int batchInsert(@Param("list") List<WarningEventSupervise> list);

    WarningEventSupervise getByEventId(Long eventId);

    void updateBatchByEventId(List<WarningEventSupervise> superviseList);
}
