package com.ruoyi.jiangshan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 巡查处置信息对象 t_patrol_disposal_info
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class PatrolDisposalInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 处置id */
    @Excel(name = "处置id")
    private String disposalId;

    /** 监测区编码 */
    @Excel(name = "监测区编码")
    private String riskZoneNum;

    /** 防范区名称 */
    @Excel(name = "防范区名称")
    private String riskZoneName;

    /** 地区编码 */
    @Excel(name = "地区编码")
    private String areaCode;

    /** 预警等级 */
    @Excel(name = "预警等级")
    private String warningLevel;

    /** 预警类型(0:实时预警,1:监测预警) */
    @Excel(name = "预警类型", readConverterExp = "0=实时预警,1=监测预警")
    private String warningType;

    /** 已读状态(2:已完成,1:已读,0:未读) */
    @Excel(name = "已读状态", readConverterExp = "2=已完成,1=已读,0=未读")
    private String readStatus;

    /** 当前任务是否失效(1:失效,0:正常) */
    @Excel(name = "任务状态", readConverterExp = "1=失效,0=正常")
    private String deadStatus;

    /** 巡查创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "巡查创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeXc;

    /** 处置用户id */
    @Excel(name = "处置用户id")
    private String userId;

    /** 是否误报(1是2否) */
    @Excel(name = "是否误报", readConverterExp = "1=是,2=否")
    private String isPositive;

    /** 是否处于临滑阶段 */
    @Excel(name = "是否处于临滑阶段")
    private String isSlid;

    /** 分类 */
    @Excel(name = "分类")
    private String type;

    /** 综合研判 */
    @Excel(name = "综合研判")
    private String content;

    /** 处置用户账号 */
    @Excel(name = "处置用户账号")
    private String dealPeople;

    /** 处置时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处置时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dealTime;

    /** 监测点编号 */
    @Excel(name = "监测点编号")
    private String monitorPointCode;

    /** 预警消息状态(0未处置,1待处置,2已处置) */
    @Excel(name = "预警消息状态", readConverterExp = "0=未处置,1=待处置,2=已处置")
    private String status;

    /** 处置任务创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处置任务创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeCz;

    /** 处置任务最后操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处置任务最后操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeCz;

    /** 创建用户ID */
    @Excel(name = "创建用户ID")
    private String createBy;

    /** 最后操作人 */
    @Excel(name = "最后操作人")
    private String updateBy;

    /** 是否已删除 */
    @Excel(name = "是否已删除")
    private String isDeleted;

    /** 是否电话告知 */
    @Excel(name = "是否电话告知")
    private String isCallNotify;

    /** 声光报警信息 */
    @Excel(name = "声光报警信息")
    private String isTriggerBroadcast;

    /** 监测点名称 */
    @Excel(name = "监测点名称")
    private String monitorPointName;

    /** 同步时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "同步时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date syncTime;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setDisposalId(String disposalId)
    {
        this.disposalId = disposalId;
    }

    public String getDisposalId()
    {
        return disposalId;
    }
    public void setRiskZoneNum(String riskZoneNum)
    {
        this.riskZoneNum = riskZoneNum;
    }

    public String getRiskZoneNum()
    {
        return riskZoneNum;
    }
    public void setRiskZoneName(String riskZoneName)
    {
        this.riskZoneName = riskZoneName;
    }

    public String getRiskZoneName()
    {
        return riskZoneName;
    }
    public void setAreaCode(String areaCode)
    {
        this.areaCode = areaCode;
    }

    public String getAreaCode()
    {
        return areaCode;
    }
    public void setWarningLevel(String warningLevel)
    {
        this.warningLevel = warningLevel;
    }

    public String getWarningLevel()
    {
        return warningLevel;
    }
    public void setWarningType(String warningType)
    {
        this.warningType = warningType;
    }

    public String getWarningType()
    {
        return warningType;
    }
    public void setReadStatus(String readStatus)
    {
        this.readStatus = readStatus;
    }

    public String getReadStatus()
    {
        return readStatus;
    }
    public void setDeadStatus(String deadStatus)
    {
        this.deadStatus = deadStatus;
    }

    public String getDeadStatus()
    {
        return deadStatus;
    }
    public void setCreateTimeXc(Date createTimeXc)
    {
        this.createTimeXc = createTimeXc;
    }

    public Date getCreateTimeXc()
    {
        return createTimeXc;
    }
    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public String getUserId()
    {
        return userId;
    }
    public void setIsPositive(String isPositive)
    {
        this.isPositive = isPositive;
    }

    public String getIsPositive()
    {
        return isPositive;
    }
    public void setIsSlid(String isSlid)
    {
        this.isSlid = isSlid;
    }

    public String getIsSlid()
    {
        return isSlid;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setDealPeople(String dealPeople)
    {
        this.dealPeople = dealPeople;
    }

    public String getDealPeople()
    {
        return dealPeople;
    }
    public void setDealTime(Date dealTime)
    {
        this.dealTime = dealTime;
    }

    public Date getDealTime()
    {
        return dealTime;
    }
    public void setMonitorPointCode(String monitorPointCode)
    {
        this.monitorPointCode = monitorPointCode;
    }

    public String getMonitorPointCode()
    {
        return monitorPointCode;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setCreateTimeCz(Date createTimeCz)
    {
        this.createTimeCz = createTimeCz;
    }

    public Date getCreateTimeCz()
    {
        return createTimeCz;
    }
    public void setUpdateTimeCz(Date updateTimeCz)
    {
        this.updateTimeCz = updateTimeCz;
    }

    public Date getUpdateTimeCz()
    {
        return updateTimeCz;
    }
    public void setCreateBy(String createBy)
    {
        this.createBy = createBy;
    }

    public String getCreateBy()
    {
        return createBy;
    }
    public void setUpdateBy(String updateBy)
    {
        this.updateBy = updateBy;
    }

    public String getUpdateBy()
    {
        return updateBy;
    }
    public void setIsDeleted(String isDeleted)
    {
        this.isDeleted = isDeleted;
    }

    public String getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsCallNotify(String isCallNotify)
    {
        this.isCallNotify = isCallNotify;
    }

    public String getIsCallNotify()
    {
        return isCallNotify;
    }
    public void setIsTriggerBroadcast(String isTriggerBroadcast)
    {
        this.isTriggerBroadcast = isTriggerBroadcast;
    }

    public String getIsTriggerBroadcast()
    {
        return isTriggerBroadcast;
    }
    public void setMonitorPointName(String monitorPointName)
    {
        this.monitorPointName = monitorPointName;
    }

    public String getMonitorPointName()
    {
        return monitorPointName;
    }
    public void setSyncTime(Date syncTime)
    {
        this.syncTime = syncTime;
    }

    public Date getSyncTime()
    {
        return syncTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("disposalId", getDisposalId())
                .append("riskZoneNum", getRiskZoneNum())
                .append("riskZoneName", getRiskZoneName())
                .append("areaCode", getAreaCode())
                .append("warningLevel", getWarningLevel())
                .append("warningType", getWarningType())
                .append("readStatus", getReadStatus())
                .append("deadStatus", getDeadStatus())
                .append("createTimeXc", getCreateTimeXc())
                .append("userId", getUserId())
                .append("isPositive", getIsPositive())
                .append("isSlid", getIsSlid())
                .append("type", getType())
                .append("content", getContent())
                .append("dealPeople", getDealPeople())
                .append("dealTime", getDealTime())
                .append("monitorPointCode", getMonitorPointCode())
                .append("status", getStatus())
                .append("createTimeCz", getCreateTimeCz())
                .append("updateTimeCz", getUpdateTimeCz())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .append("isDeleted", getIsDeleted())
                .append("isCallNotify", getIsCallNotify())
                .append("isTriggerBroadcast", getIsTriggerBroadcast())
                .append("monitorPointName", getMonitorPointName())
                .append("syncTime", getSyncTime())
                .toString();
    }
}
