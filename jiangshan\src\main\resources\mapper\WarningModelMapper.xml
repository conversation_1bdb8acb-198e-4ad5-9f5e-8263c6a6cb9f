<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningModelMapper">

    <resultMap type="WarningModel" id="WarningModelResult">
        <result property="id"    column="id"    />
        <result property="modelName"    column="model_name"    />
        <result property="modelScene"    column="model_scene"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectWarningModelVo">
        select id, model_name, model_scene, status, create_time, create_by, update_time, update_by from t_warning_model
    </sql>

    <select id="selectWarningModelList" parameterType="WarningModel" resultMap="WarningModelResult">
        <include refid="selectWarningModelVo"/>
        <where>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="modelScene != null  and modelScene != ''"> and model_scene = #{modelScene}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectWarningModelById" parameterType="Long" resultMap="WarningModelResult">
        <include refid="selectWarningModelVo"/>
        where id = #{id}
    </select>

    <insert id="insertWarningModel" parameterType="WarningModel" useGeneratedKeys="true" keyProperty="id">
        insert into t_warning_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modelName != null">model_name,</if>
            <if test="modelScene != null">model_scene,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modelName != null">#{modelName},</if>
            <if test="modelScene != null">#{modelScene},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateWarningModel" parameterType="WarningModel">
        update t_warning_model
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelName != null">model_name = #{modelName},</if>
            <if test="modelScene != null">model_scene = #{modelScene},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWarningModelById" parameterType="Long">
        delete from t_warning_model where id = #{id}
    </delete>

    <delete id="deleteWarningModelByIds" parameterType="String">
        delete from t_warning_model where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="listByIdList" resultMap="WarningModelResult">
        select * from t_warning_model where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="listAll" resultMap="WarningModelResult">
        <include refid="selectWarningModelVo">
        </include>
    </select>
</mapper>
