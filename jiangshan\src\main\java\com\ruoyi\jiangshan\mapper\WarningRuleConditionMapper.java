package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.domain.WarningRuleCondition;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WarningRuleConditionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningRuleCondition record);

    int insertSelective(WarningRuleCondition record);

    WarningRuleCondition selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningRuleCondition record);

    int updateByPrimaryKey(WarningRuleCondition record);

    int updateBatch(List<WarningRuleCondition> list);

    int batchInsert(@Param("list") List<WarningRuleCondition> list);

    List<WarningRuleCondition> listByMonitorItemAndDevice(@Param("deviceThirdId") String deviceThirdId, @Param("monitorItemList") List<String> monitorItemList);

    void deleteByRuleId(@Param("id") Long id);

    List<WarningRuleCondition> listByRuleIdList(@Param("list") List<Long> ruleIdList);

    WarningRuleCondition getMinConditionByMonitorItem(@Param("deviceThirdId") String deviceThirdId,
                                                      @Param("monitorItem") String monitorItem);
}
