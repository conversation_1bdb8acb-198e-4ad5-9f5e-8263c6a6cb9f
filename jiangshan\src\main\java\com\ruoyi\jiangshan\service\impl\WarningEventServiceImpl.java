package com.ruoyi.jiangshan.service.impl;

import java.awt.image.BufferedImage;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.Security;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.dto.DateDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.framework.web.service.dtalk.UserDtalkService;
import com.ruoyi.jiangshan.client.VoiceClient;
import com.ruoyi.jiangshan.constant.CommonConstant;
import com.ruoyi.jiangshan.convert.WarningConvert;
import com.ruoyi.jiangshan.dingding.client.DingDingClient;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.enums.*;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.openapi.config.WarningSceneDepartmentConfig;
import com.ruoyi.jiangshan.openapi.vo.WarningEventOpenApiVO;
import com.ruoyi.jiangshan.openapi.vo.WarningEventQueryVO;
import com.ruoyi.jiangshan.openapi.vo.WarningEventThresholdQueryVO;
import com.ruoyi.jiangshan.service.BusinessFileService;
import com.ruoyi.jiangshan.service.IDeviceValueService;
import com.ruoyi.jiangshan.util.HttpUtil;
import com.ruoyi.jiangshan.vo.*;
import com.ruoyi.jiangshan.vo.hk.WarningEventPictureVO;
import com.ruoyi.jiangshan.vo.jczz.WarningEventAttachmentVO;
import com.ruoyi.jiangshan.vo.jczz.WarningEventJczzFlowVO;
import com.ruoyi.jiangshan.vo.jczz.WarningEventJczzLogVO;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import freemarker.template.Configuration;
import freemarker.template.Template;
import io.netty.util.concurrent.CompleteFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import com.ruoyi.jiangshan.service.IWarningEventService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 设备预警事件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
@Slf4j
public class WarningEventServiceImpl implements IWarningEventService
{
    @Autowired
    private WarningEventMapper warningEventMapper;
    @Autowired
    private WarningInfoMapper warningInfoMapper;
    @Autowired
    private BusinessFileMapper businessFileMapper;
    @Autowired
    private WarningEventProcessMapper warningEventProcessMapper;
    @Autowired
    private WarningEventDepartmentMapper warningEventDepartmentMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private WarningEventSuperviseMapper warningEventSuperviseMapper;
    @Autowired
    private UserDtalkService userDtalkService;
    @Autowired
    private WarningDepartmentInfoMapper warningDepartmentInfoMapper;
    @Autowired
    private IDeviceValueService deviceValueService;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private WarningRuleConditionShuxinMapper warningRuleConditionShuxinMapper;
    @Autowired
    private WarningRuleShuxinMapper warningRuleShuxinMapper;
    @Autowired
    private Configuration configuration;
    @Autowired
    private DingDingClient dingDingClient;
    @Autowired
    private WarningEventJczzLogMapper warningEventJczzLogMapper;
    @Autowired
    private WarningEventHikvisionMapper warningEventHikvisionMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private BusinessFileService businessFileService;
    @Autowired
    private DeviceStreetMapper deviceStreetMapper;
    @Autowired
    private VoiceClient voiceClient;

    @Autowired
    private IWarningEventService warningEventService;

    @Value("${single.url}")
    private String singleUrl;
    @Value("${single.pcUrl}")
    private String singlePcUrl;
    @Value("${file.tmpUrl}")
    private String fileTmpUrl;

    @Value("${file.uploadUrl}")
    private String fileUploadUrl;
    @Value("${file.openUploadUrl}")
    private String fileOpenUploadUrl;

    private final String APPCODE = "QEWFAS4124SDVWT5";

    /**
     * 查询设备预警事件
     *
     * @param id 设备预警事件主键
     * @return 设备预警事件
     */
    @Override
    public WarningEvent selectWarningEventById(Long id, String operateUserId)
    {
        WarningEvent warningEvent = warningEventMapper.selectWarningEventById(id);

        List<Long> eventIdList = Lists.newArrayList(warningEvent.getId());

        fillFileList(Lists.newArrayList(warningEvent), eventIdList);
        fillProcessList(Lists.newArrayList(warningEvent), eventIdList);
        convertProcessList(warningEvent);
        fillSupervise(Lists.newArrayList(warningEvent), eventIdList);
        fillProcessLog(warningEvent);
        fillOperatorFlag(Lists.newArrayList(warningEvent), operateUserId);

        return warningEvent;
    }

    private void convertProcessList(WarningEvent warningEvent) {
        List<WarningEventProcess> processList = warningEvent.getProcessList();
        if (CollectionUtils.isEmpty(processList)) {
            return;
        }

        List<WarningEventProcess> newProcessList = Lists.newArrayList();

        //处理之前的退回流程
        List<WarningEventProcess> oneLayerList = Lists.newArrayList();
        for (WarningEventProcess warningEventProcess : processList) {
            oneLayerList.add(warningEventProcess);
            if (warningEventProcess.getProcessStep().equals(WarningEventStepEnum.STATUS_10.getCode())) {
                newProcessList.addAll(convertOneLayerProcessList(oneLayerList));
                oneLayerList.clear();
            }
        }

        //处理后续的流程
        if (CollectionUtils.isNotEmpty(oneLayerList)) {
            newProcessList.addAll(convertOneLayerProcessList(oneLayerList));
        }

        if (CollectionUtils.isNotEmpty(newProcessList)) {
            for (WarningEventProcess warningEventProcess : newProcessList) {
                if (warningEventProcess.getProcessStep().equals(WarningEventStepEnum.STATUS_06.getCode())
                    || warningEventProcess.getProcessStep().equals(WarningEventStepEnum.STATUS_10.getCode())
                    || warningEventProcess.getStatus().equals(WarningEventHandleStatus.STATUS_01.getCode())) {
                    warningEventProcess.setSignFlag(1);
                }
            }
        }

        warningEvent.setProcessList(newProcessList);
    }

    private List<WarningEventProcess> convertOneLayerProcessList(List<WarningEventProcess> processList) {
        List<WarningEventProcess> newProcessList = Lists.newArrayList();

        Integer maxStep = processList.stream()
                .map(WarningEventProcess::getProcessStep)
                .filter(item -> !Lists.newArrayList(WarningEventStepEnum.STATUS_06.getCode(), WarningEventStepEnum.STATUS_10.getCode()).contains(item))
                .max(Integer::compareTo)
                .get();

        List<WarningEventProcess> stepOneProcessList = processList.stream()
                .filter(tmp -> tmp.getProcessStep().equals(WarningEventStepEnum.STATUS_01.getCode()))
                .collect(Collectors.toList());

        List<WarningEventProcess> stepTwoProcessList = processList.stream()
                .filter(tmp -> tmp.getProcessStep().equals(WarningEventStepEnum.STATUS_02.getCode()))
                .collect(Collectors.toList());

        List<WarningEventProcess> stepThreeProcessList = processList.stream()
                .filter(tmp -> tmp.getProcessStep().equals(WarningEventStepEnum.STATUS_03.getCode()))
                .collect(Collectors.toList());

        List<WarningEventProcess> stepFourProcessList = processList.stream()
                .filter(tmp -> tmp.getProcessStep().equals(WarningEventStepEnum.STATUS_04.getCode()))
                .collect(Collectors.toList());

        List<WarningEventProcess> rollBackProcessList = processList.stream()
                .filter(tmp -> tmp.getProcessStep().equals(WarningEventStepEnum.STATUS_06.getCode()))
                .collect(Collectors.toList());

        List<WarningEventProcess> restartProcessList = processList.stream()
                .filter(tmp -> tmp.getProcessStep().equals(WarningEventStepEnum.STATUS_10.getCode()))
                .collect(Collectors.toList());

        if (Objects.equals(maxStep, WarningEventStepEnum.STATUS_01.getCode())) {
            WarningEventProcess stepTwoNewProcess = createNewProcess(stepOneProcessList, 2);

            newProcessList.add(stepTwoNewProcess);
        } else if (Objects.equals(maxStep, WarningEventStepEnum.STATUS_02.getCode())) {
            fillProcessTwoContent(stepOneProcessList, stepTwoProcessList, newProcessList, 2);

        } else if (Objects.equals(maxStep, WarningEventStepEnum.STATUS_03.getCode())) {
            fillProcessTwoContent(stepOneProcessList, stepTwoProcessList, newProcessList, 2);

            WarningEventProcess stepTwoNewProcess = createNewProcess(stepOneProcessList, 4);

            newProcessList.add(stepTwoNewProcess);
        } else if (Objects.equals(maxStep, WarningEventStepEnum.STATUS_04.getCode())) {
            fillProcessTwoContent(stepOneProcessList, stepTwoProcessList, newProcessList, 2);

            fillProcessTwoContent(stepThreeProcessList, stepFourProcessList, newProcessList, 4);
        }

        newProcessList.addAll(rollBackProcessList);
        newProcessList.addAll(restartProcessList);

        return newProcessList;
    }

    private void fillProcessTwoContent(List<WarningEventProcess> stepOneProcessList,
                                       List<WarningEventProcess> stepTwoProcessList,
                                       List<WarningEventProcess> newProcessList, int step) {
        WarningEventProcess transferProcess = stepOneProcessList.get(stepOneProcessList.size() - 1);
        WarningEventProcess warningEventProcess = stepTwoProcessList.get(0);

        warningEventProcess.setSignFlag(1);

        List<WarningEventProcessContentVO> contentList = Lists.newArrayList();

        fillSignContentList(transferProcess, warningEventProcess, stepOneProcessList, contentList, step);

        if (warningEventProcess.getStatus() == 3) {
            if (step == 2) {
                contentList.add(fillOneContent(warningEventProcess, "核实"));
            } else {
                contentList.add(fillOneContent(warningEventProcess, "提交处置结果"));
            }
        }

        warningEventProcess.setContentList(contentList);

        newProcessList.add(warningEventProcess);
    }

    private WarningEventProcess createNewProcess(List<WarningEventProcess> stepOneProcessList, Integer step) {
        WarningEventProcess defaultProcess = stepOneProcessList.get(stepOneProcessList.size() - 1);

        WarningEventProcess warningEventProcess = new WarningEventProcess();
        BeanUtils.copyProperties(defaultProcess, warningEventProcess);

        warningEventProcess.setProcessStep(step);
        warningEventProcess.setStatus(defaultProcess.getStatus());

        List<WarningEventProcessContentVO> contentList = Lists.newArrayList();

        fillSignContentList(warningEventProcess, warningEventProcess, stepOneProcessList, contentList, step);

        warningEventProcess.setContentList(contentList);

        return warningEventProcess;
    }

    private void fillSignContentList(WarningEventProcess transferProcess,
                                     WarningEventProcess warningEventProcess,
                                     List<WarningEventProcess> stepOneProcessList,
                                     List<WarningEventProcessContentVO> contentList, int step) {
        String content = "签收";
        if (step == 4) {
            content = "开始处置";
        }

        for (int i = 0; i < stepOneProcessList.size(); i++) {
            WarningEventProcess processOne = stepOneProcessList.get(i);
//            if (i == 0 && processOne.getStatus() != 3 && processOne.getStatus() != 4) {
//                break;
//            }
//
//            if (i == stepOneProcessList.size() - 1) {
//                //最后一个节点
//                if (processOne.getStatus() == 3) {
//                    //签收
//                    contentList.add(fillOneContent(warningEventProcess, content));
//                    continue;
//                }
//            }
//
//            contentList.add(fillOneContent(warningEventProcess, "转派"));

            if (processOne.getStatus() == 4) {
                //转派
                contentList.add(fillOneContent(transferProcess, "转派"));
            }

            if (processOne.getStatus() == 3) {
                //签收
                contentList.add(fillOneContent(warningEventProcess, content));
            }
        }

    }

    private WarningEventProcessContentVO fillOneContent(WarningEventProcess warningEventProcess, String content) {
        WarningEventProcessContentVO contentVO = new WarningEventProcessContentVO();

        String processTime = "";
        if (Objects.isNull(warningEventProcess.getProcessTime())) {
            processTime = DateUtils.formatDateSecond(warningEventProcess.getCreateTime());
        } else {
            processTime = DateUtils.formatDateSecond(warningEventProcess.getProcessTime());
        }

        contentVO.setOperateTime(processTime);
        contentVO.setOperateUser(warningEventProcess.getUserName());
        contentVO.setOperateType(content);

        return contentVO;
    }

    @Override
    public void fillProcessLog(WarningEvent warningEvent) {
        List<WarningEventProcess> processList = warningEvent.getProcessList();
        if (CollectionUtils.isEmpty(processList)) {
            return;
        }

        List<WarningEventProcessLogVO> logList = Lists.newArrayList();
        WarningEventProcessLogVO startLog = new WarningEventProcessLogVO();
        startLog.setTitle(WarningEventStepEnum.STATUS_00.getProcessName());
        startLog.setContent(warningEvent.getWarningDetail());
        startLog.setProcessTime(warningEvent.getWarningTime());

        logList.add(startLog);

        for (WarningEventProcess warningEventProcess : processList) {
            WarningEventProcessLogVO logVO = WarningEventStepEnum.getLog(warningEventProcess);

            if (Objects.isNull(logVO)) {
                continue;
            }

            logList.add(logVO);

            if (Objects.equals(warningEventProcess.getProcessStep(), WarningEventStepEnum.STATUS_03.getCode())
                    && Objects.equals(warningEventProcess.getStatus(), WarningEventHandleStatus.STATUS_03.getCode())) {
                WarningEventProcessLogVO completeLog = new WarningEventProcessLogVO();
                completeLog.setTitle("完成");
                completeLog.setContent("完成事件");
                completeLog.setProcessTime(warningEventProcess.getProcessTime());

                logList.add(completeLog);
            }
        }

        warningEvent.setLogList(logList);
    }

    /**
     * 查询设备预警事件列表
     *
     * @param pageVO 设备预警事件
     * @return 设备预警事件
     */
    @Override
    public List<WarningEvent> selectWarningEventList(WarningEventPageVO pageVO)
    {
        List<WarningEvent> warningEventList = warningEventMapper.selectWarningEventList(pageVO);

        if (CollectionUtils.isEmpty(warningEventList)) {
            return Lists.newArrayList();
        }

        List<Long> warningEventIdList = warningEventList.stream()
                .map(WarningEvent::getId)
                .collect(Collectors.toList());

        fillFileList(warningEventList, warningEventIdList);
        fillProcessList(warningEventList, warningEventIdList);
//        fillEventDepartment(warningEventList, warningEventIdList);
//        fillOverTime(warningEventList);
        fillSupervise(warningEventList, warningEventIdList);
        fillOperatorFlag(warningEventList, pageVO.getOperateUserId());

        return warningEventList;
    }

    private void fillOperatorFlag(List<WarningEvent> warningEventList, String accountId) {
        if (StringUtils.isBlank(accountId)) {
            return;
        }

        if (CollectionUtils.isEmpty(warningEventList)) {
            return;
        }

        for (WarningEvent warningEvent : warningEventList) {
            List<WarningEventProcess> processList = warningEvent.getProcessList();
            if (CollectionUtils.isNotEmpty(processList)) {
                WarningEventProcess warningEventProcess = processList.get(processList.size() - 1);

                WarningEventDepartment department = warningEventDepartmentMapper
                        .getByEventAndProcessId(warningEvent.getId(), warningEventProcess.getId());

                if (Objects.nonNull(department)) {
                    if (Objects.equals(department.getAccountId(), accountId)) {
                        warningEvent.setOperatorFlag(1);
                    }
                }
            }
        }
    }

    private Boolean checkCurrentOperation(List<WarningEventProcess> processList,
                                          WarningEventStepEnum status, String employeeCode) {
        for (WarningEventProcess warningEventProcess : processList) {
            if (status.getCode().equals(warningEventProcess.getProcessStep()) &&
                    !WarningEventHandleStatus.STATUS_01.getCode().equals(warningEventProcess.getStatus())) {
                List<WarningEventDepartment> departmentList = warningEventProcess.getDepartmentList();
                if (CollectionUtils.isNotEmpty(departmentList)) {
                    Optional<WarningEventDepartment> departmentOptional = departmentList.stream()
                            .filter(department -> department.getMainFlag() == 1)
                            .findFirst();

                    if (departmentOptional.isPresent()) {
                        WarningEventDepartment warningEventDepartment = departmentOptional.get();

                        if (Objects.equals(warningEventDepartment.getEmployeeCode(), employeeCode)) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }


    private void fillSupervise(List<WarningEvent> warningEventList, List<Long> warningEventIdList) {


    }

    private void fillOverTime(List<WarningEvent> warningEventList) {


    }

//    private void fillEventDepartment(List<WarningEvent> warningEventList, List<Long> warningEventIdList) {
//        List<WarningEventDepartment> departmentList = warningEventDepartmentMapper.listByEventIdList(warningEventIdList);
//        if (CollectionUtils.isNotEmpty(departmentList)) {
//            Map<Long, List<WarningEventDepartment>> departmentMap = departmentList.stream()
//                    .collect(Collectors.groupingBy(WarningEventDepartment::getEventId));
//            for (WarningEvent warningEvent : warningEventList) {
//                List<WarningEventDepartment> departmentListByEventId = departmentMap.get(warningEvent.getId());
//                if (CollectionUtils.isNotEmpty(departmentListByEventId)) {
//                    warningEvent.setDepartmentList(departmentListByEventId);
//                }
//            }
//        }
//    }

    @Override
    public void fillProcessList(List<WarningEvent> warningEventList, List<Long> warningEventIdList) {
        //添加流程
        List<WarningEventProcess> processList = warningEventProcessMapper.listByEventIdList(warningEventIdList);
        if (CollectionUtils.isNotEmpty(processList)) {
            Map<Long, List<WarningEventProcess>> processMap = processList.stream()
                    .collect(Collectors.groupingBy(WarningEventProcess::getEventId));

            for (WarningEvent warningEvent : warningEventList) {
                List<WarningEventProcess> processOneList = processMap.get(warningEvent.getId());

                if (CollectionUtils.isNotEmpty(processOneList)) {
                    warningEvent.setProcessList(processOneList);

                    if (warningEvent.getStatus().equals(WarningEventStepEnum.STATUS_00.getCode())) {
                        warningEvent.setDeliveryStatus(WarningEventDeliveryEnum.STATUS_02.getCode());
                    } else {
                        warningEvent.setDeliveryStatus(WarningEventDeliveryEnum.STATUS_03.getCode());
                    }
                } else {
                    warningEvent.setDeliveryStatus(WarningEventDeliveryEnum.STATUS_01.getCode());
                }
            }
        }

        //添加流程部门
        List<WarningEventDepartment> departmentList = warningEventDepartmentMapper.listByEventIdList(warningEventIdList);
        if (CollectionUtils.isNotEmpty(departmentList)) {
            Map<Long, List<WarningEventDepartment>> departmentMap = departmentList.stream()
                    .collect(Collectors.groupingBy(WarningEventDepartment::getEventId));
            for (WarningEvent warningEvent : warningEventList) {
                List<WarningEventDepartment> departmentListByEventId = departmentMap.get(warningEvent.getId());
                if (CollectionUtils.isNotEmpty(departmentListByEventId)) {
                    List<WarningEventDepartment> eventDepartmentList = departmentListByEventId.stream()
                            .filter(department -> Objects.equals(department.getEventStatus(), WarningEventStepEnum.STATUS_02.getCode()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(eventDepartmentList)) {
                        Optional<WarningEventDepartment> max = eventDepartmentList.stream()
//                               .map(WarningEventDepartment::getProcessId)
                                .max(Comparator.comparing(WarningEventDepartment::getProcessId));

                        if (max.isPresent()) {
                            WarningEventDepartment warningEventDepartment = max.get();

                            eventDepartmentList = eventDepartmentList.stream()
                                    .filter(department -> Objects.equals(department.getProcessId(), warningEventDepartment.getProcessId()))
                                    .collect(Collectors.toList());
                        }
                    }

                    if (CollectionUtils.isNotEmpty(eventDepartmentList)) {
                        warningEvent.setDepartmentList(eventDepartmentList);

                        Optional<WarningEventDepartment> optionalDepartment = eventDepartmentList.stream()
                                .filter(department -> department.getMainFlag() == 1)
                                .findFirst();

                        if (optionalDepartment.isPresent()) {
                            WarningEventDepartment mainDepartment = optionalDepartment.get();

                            warningEvent.setDepartmentOrganizationCode(mainDepartment.getOrganizationCode());
                        }
                    } else {
                        warningEvent.setDepartmentList(departmentListByEventId);

                        Optional<WarningEventDepartment> optionalDepartment = departmentListByEventId.stream()
                                .filter(department -> department.getMainFlag() == 1)
                                .findFirst();

                        if (optionalDepartment.isPresent()) {
                            WarningEventDepartment mainDepartment = optionalDepartment.get();

                            warningEvent.setDepartmentOrganizationCode(mainDepartment.getOrganizationCode());
                        }
                    }

                    Map<Long, List<WarningEventDepartment>> departmentOneMap = departmentListByEventId.stream()
                            .collect(Collectors.groupingBy(WarningEventDepartment::getProcessId));

                    for (WarningEventProcess warningEventProcess : processList) {
                        List<WarningEventDepartment> departmentOneList = departmentOneMap.get(warningEventProcess.getId());

                        warningEventProcess.setDepartmentList(departmentOneList);

                        if (CollectionUtils.isNotEmpty(departmentOneList)) {
                            List<WarningEventDepartment> mainDepartmentList = departmentOneList.stream()
                                    .filter(department -> department.getMainFlag() == 1)
                                    .collect(Collectors.toList());

                            if (CollectionUtils.isNotEmpty(mainDepartmentList)) {
                                WarningEventDepartment mainDepartment = mainDepartmentList.get(0);
                                warningEventProcess.setUserName(mainDepartment.getEmployeeName());
                            }
                        }
                    }
                }
            }
        }

        //添加文件
        List<WarningEventProcess> processVerifyList = processList.stream()
                .filter(warningEventProcess ->
                        WarningEventStepEnum.STATUS_02.getCode().equals(warningEventProcess.getProcessStep())
                                || WarningEventStepEnum.STATUS_04.getCode().equals(warningEventProcess.getProcessStep()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(processVerifyList)) {
            List<Long> processIdList = processVerifyList.stream()
                    .map(WarningEventProcess::getId)
                    .collect(Collectors.toList());

            List<BusinessFile> fileList = businessFileMapper
                    .listByBusinessIdListAndTypeList(processIdList,
                            Lists.newArrayList(FileTypeEnum.TYPE_03.getCode(), FileTypeEnum.TYPE_04.getCode()));

            Map<Long, List<BusinessFile>> fileMap = fileList.stream()
                    .collect(Collectors.groupingBy(BusinessFile::getBusinessId));

            for (WarningEventProcess warningEventProcess : processVerifyList) {
                List<BusinessFile> businessFileList = fileMap.get(warningEventProcess.getId());

                warningEventProcess.setFileList(businessFileList);
            }
        }
    }

    @Override
    @Transactional
    public String saveOpenApiWarningEvent(WarningEventOpenApiVO apiVO) {
        log.info("saveOpenApiWarningEvent, apiVO:{}", JSON.toJSONString(apiVO));

        if (!MonitorSceneType.checkExist(apiVO.getEventType())) {
            throw new RuntimeException("场景填写错误");
        }

        if (apiVO.getWarningLevel() < WarningLevelType.WARNING_LEVEL_1.getCode()
                || apiVO.getWarningLevel() > WarningLevelType.WARNING_LEVEL_4.getCode()) {
            throw new RuntimeException("告警等级填写错误");
        }

        WarningInfo existWarning = warningInfoMapper.getByEventThirdId(apiVO.getEventThirdId());
        if (Objects.nonNull(existWarning)) {
            throw new RuntimeException("预警事件已存在");
        }
//        WarningEvent existEvent = warningEventMapper.checkExist(apiVO.getDeviceThirdId());
//        if (Objects.nonNull(existEvent) && existEvent.getStatus() < WarningEventStepEnum.STATUS_05.getCode()) {
//            throw new RuntimeException("预警事件已在处置中");
//        }

        String deviceName = null;
        DeviceInfo deviceInfo = deviceInfoMapper.getByThirdId(apiVO.getDeviceThirdId());
        if (Objects.nonNull(deviceInfo)) {
            deviceName = deviceInfo.getDeviceName();
        }

        //生成预警
        WarningInfo warningInfo = new WarningInfo();
        warningInfo.setFxqbh(apiVO.getFxqbh());
        warningInfo.setEventThirdId(apiVO.getEventThirdId());
        warningInfo.setModelName(apiVO.getModelName());
        warningInfo.setDeviceThirdId(apiVO.getDeviceThirdId());
        warningInfo.setLenTime(apiVO.getLenTime());
        warningInfo.setWarningData(apiVO.getWarningValue());
        warningInfo.setWarningLevel(apiVO.getWarningLevel());
        warningInfo.setMonitorValue(apiVO.getWaringThreshold());
        warningInfo.setWarningTime(apiVO.getWarningTime());
        warningInfo.setStartTime(apiVO.getStartTime());
        warningInfo.setEndTime(apiVO.getEndTime());
        warningInfo.setLon(apiVO.getLon());
        warningInfo.setLat(apiVO.getLat());
        warningInfo.setWarningName(apiVO.getModelName() + apiVO.getEventThirdId() + "预警事件");
        warningInfo.setWarningScene(apiVO.getEventType());
        warningInfo.setWarningAddress(apiVO.getEventAddress());
        warningInfo.setWarningReason(getWarningReason(apiVO, deviceInfo));
        warningInfo.setCreateBy("SYSTEM");

        DeviceStreet deviceStreet = deviceStreetMapper.getStreet(apiVO.getLon(), apiVO.getLat());
        if (Objects.nonNull(deviceStreet)) {
            warningInfo.setEventStreet(deviceStreet.getLabel());
            warningInfo.setEventStreetAreaCode(deviceStreet.getBsm());
        } else {
            warningInfo.setEventStreet(apiVO.getEventStreet());
            warningInfo.setEventStreetAreaCode(apiVO.getEventStreetAreaCode());
        }

        warningInfoMapper.insertSelective(warningInfo);

        WarningEvent warningEvent = WarningConvert.INSTANCE.openApi2WarningEvent(apiVO);

        WarningRuleShuxin warningRuleShuxin = warningRuleShuxinMapper.getAccountByScene(apiVO.getEventType());

        SysUser sysUser = sysUserMapper.selectUserById(warningRuleShuxin.getWarningUserId());

        if (StringUtils.isNotBlank(warningRuleShuxin.getAllAccountId())) {
            SysDept dept = sysUser.getDept();
            if (Objects.nonNull(dept)) {
                warningEvent.setDepartmentId(dept.getDeptId());
                warningEvent.setDepartmentName(dept.getDeptName());
                warningEvent.setCreateBy(sysUser.getNickName());
            }
        } else {
            WarningEventSceneDepartment warningEventSceneDepartment = WarningSceneDepartmentConfig.SCENE_DEPARTMENT_MAP
                    .get(warningEvent.getEventType());

            warningEvent.setDepartmentId(warningEventSceneDepartment.getDepartmentId());
            warningEvent.setDepartmentName(warningEventSceneDepartment.getDepartmentName());
            warningEvent.setCreateBy(warningEventSceneDepartment.getEmployeeCode());
        }
        warningEvent.setStatus(0);
        warningEvent.setStopFlag(0);
        warningEvent.setOverFlag(0);
        warningEvent.setSuperviseFlag(1);
        warningEvent.setCreateTime(new Date());

        warningEvent.setWarningId(warningInfo.getId());
        warningEvent.setEventName(warningInfo.getWarningName());
        warningEvent.setWarningDetail(warningInfo.getWarningReason());
        warningEvent.setDeviceName(deviceName);
        warningEvent.setBizCode(warningEvent.getEventThirdId());
        warningEvent.setEventStreet(warningInfo.getEventStreet());
        warningEvent.setEventStreetAreaCode(warningInfo.getEventStreetAreaCode());
        warningEvent.setFirstWarningTime(warningInfo.getWarningTime());

        warningEventMapper.insertWarningEvent(warningEvent);

        String accountIdAll = warningRuleShuxin.getAllAccountId();
        if (StringUtils.isNotBlank(warningRuleShuxin.getAllAccountId())) {
            accountIdAll = accountIdAll + ",********";
        } else {
            accountIdAll = "********";
        }
        //异步
        deviceValueService.createNewWarningEventDispatch(warningEvent, sysUser, accountIdAll);

        return warningInfo.getEventThirdId();
    }

    private String getWarningReason(WarningEventOpenApiVO apiVO, DeviceInfo deviceInfo) {
        StringBuilder sb = new StringBuilder();
//        sb.append(apiVO.getWarningTime());
//        if (Objects.nonNull(deviceInfo)) {
//            sb.append(deviceInfo.getDeviceStreet())
//                    .append(deviceInfo.getDeviceName())
//                    .append("设备");
//        } else {
//            sb.append(apiVO.getEventAddress())
//                    .append("设备编号为")
//                    .append(apiVO.getDeviceThirdId())
//                    .append("的设备");
//        }
        sb.append("监测到")
                .append("数据值异常");

        sb.append("(监测值")
                .append(apiVO.getWarningValue())
                .append(",阈值")
                .append(apiVO.getWaringThreshold())
                .append("), ");

        sb.append("请及时处置。");

        return sb.toString();
    }

    @Override
    public WarningRuleConditionShuxin getThreshold(WarningEventThresholdQueryVO apiVO) {
        return warningRuleConditionShuxinMapper.getByFxqbh(apiVO.getFxqbh());
    }

    @Override
    public void receiptV2(Long eventId) {
        WarningEvent warningEvent = checkEvent(eventId);

        Integer status = warningEvent.getStatus();

        Date currentDate = new Date();
//        Date nextDay = DateUtils.getNextDay(currentDate);

        if (!WarningEventStepEnum.STATUS_01.getCode().equals(status)
                && !WarningEventStepEnum.STATUS_03.getCode().equals(status)) {
            throw new RuntimeException("预警事件已被签收");
        }

        //处理老的process
        WarningEventProcess warningEventProcess = warningEventProcessMapper
                .getLastByEventIdAndStep(eventId, status);

        WarningEventProcess updateProcess = new WarningEventProcess();
        updateProcess.setId(warningEventProcess.getId());
        updateProcess.setStatus(WarningEventHandleStatus.STATUS_03.getCode());
        updateProcess.setProcessTime(currentDate);

        warningEventProcessMapper.updateByPrimaryKeySelective(updateProcess);
        //生成新的process
        WarningEventProcess process = new WarningEventProcess();
        process.setEventId(warningEvent.getId());

        Long userId = SecurityUtils.getUserId();
        SysUser sysUser = sysUserMapper.selectUserById(userId);
        if (Objects.nonNull(sysUser)) {
            process.setUserId(sysUser.getUserId() + "");
            process.setUserName(sysUser.getNickName());
            process.setUserMobile(sysUser.getPhonenumber());

            Long deptId = SecurityUtils.getDeptId();

            SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
            if (Objects.nonNull(sysDept)) {
                process.setDepartmentId(sysDept.getDeptId());
                process.setDepartmentName(sysDept.getDeptName());
            } else {
                process.setUserId(warningEventProcess.getUserId());
                process.setUserName(warningEventProcess.getUserName());
                process.setDepartmentId(warningEventProcess.getDepartmentId());
                process.setDepartmentName(warningEventProcess.getDepartmentName());
                process.setUserMobile(warningEventProcess.getUserMobile());
            }
        } else {
            process.setUserId(warningEventProcess.getUserId());
            process.setUserName(warningEventProcess.getUserName());
            process.setDepartmentId(warningEventProcess.getDepartmentId());
            process.setDepartmentName(warningEventProcess.getDepartmentName());
            process.setUserMobile(warningEventProcess.getUserMobile());
        }


        if (WarningEventStepEnum.STATUS_01.getCode().equals(status)) {
            process.setProcessStep(WarningEventStepEnum.STATUS_02.getCode());
        } else {
            process.setProcessStep(WarningEventStepEnum.STATUS_04.getCode());
        }
        process.setStatus(WarningEventHandleStatus.STATUS_00.getCode());
        process.setExpectedCompleteTime(warningEvent.getExpectedCompleteTime());
        process.setCreateTime(currentDate);
        process.setCreateBy(warningEventProcess.getCreateBy());

        warningEventProcessMapper.insertSelective(process);

        //更新event状态
        WarningEvent updateEvent = new WarningEvent();
        updateEvent.setId(warningEvent.getId());
        updateEvent.setStatus(warningEvent.getStatus() + 1);
        if (currentDate.after(warningEventProcess.getExpectedCompleteTime())) {
            updateEvent.setOverFlag(1);
        }
        updateEvent.setUpdateTime(currentDate);
        updateEvent.setUpdateBy(warningEventProcess.getCreateBy());

        warningEventMapper.updateWarningEvent(updateEvent);

//        //生成department
//        WarningEventDepartment oldDepartment = warningEventDepartmentMapper.getByEventAndProcessId(warningEventProcess.getId(), warningEvent.getId());
//        if (Objects.isNull(oldDepartment)) {
//            //生成新department
//            WarningEventDepartment warningEventDepartment = new WarningEventDepartment();
//            warningEventDepartment.setEventId(warningEvent.getId());
//            warningEventDepartment.setProcessId(process.getId());
//            warningEventDepartment.setEventStatus(status);
//            warningEventDepartment.setDepartmentId(dispatchVO.getDepartmentId());
//            warningEventDepartment.setDepartmentName(dispatchVO.getDepartmentName());
//            warningEventDepartment.setOrganizationCode(dispatchVO.getOrganizationCode());
//            warningEventDepartment.setAccountId(dispatchVO.getAccountId());
//            warningEventDepartment.setEmployeeCode(dispatchVO.getEmployeeCode());
//            warningEventDepartment.setEmployeeName(dispatchVO.getEmployeeName());
//            warningEventDepartment.setMainFlag(1);
//            warningEventDepartment.setCreateBy(warningEventProcess.getCreateBy());
//            warningEventDepartment.setCreateTime(currentDate);
//
//            warningEventDepartmentMapper.insertSelective(warningEventDepartment);
//        }
    }

    @Override
    public void transfer(WarningEventDispatchVO dispatchVO) {
        WarningEvent warningEvent = checkEvent(dispatchVO.getId());

        Integer status = warningEvent.getStatus();

        Date currentDate = new Date();
        Date nextHour = DateUtils.getNextHour(currentDate);

        //更新老process
        WarningEventProcess warningEventProcess = warningEventProcessMapper
                .getLastByEventIdAndStep(dispatchVO.getId(), warningEvent.getStatus());

        if (currentDate.after(warningEventProcess.getExpectedCompleteTime())) {
            WarningEvent updateEvent = new WarningEvent();
            updateEvent.setId(warningEvent.getId());
            updateEvent.setOverFlag(1);
            updateEvent.setUpdateBy(dispatchVO.getAccountId());
            updateEvent.setUpdateTime(currentDate);

            warningEventMapper.updateWarningEvent(updateEvent);
        }

        WarningEventProcess updateProcess = new WarningEventProcess();
        updateProcess.setId(warningEventProcess.getId());
        updateProcess.setStatus(WarningEventHandleStatus.STATUS_04.getCode());
        updateProcess.setProcessTime(currentDate);

        warningEventProcessMapper.updateByPrimaryKeySelective(updateProcess);

        //生成新process
        WarningEventProcess process = new WarningEventProcess();
        process.setEventId(warningEvent.getId());
        process.setUserId(dispatchVO.getAccountId());
        process.setUserName(dispatchVO.getEmployeeName());
        process.setDepartmentId(dispatchVO.getDepartmentId());
        process.setDepartmentName(dispatchVO.getDepartmentName());
        process.setUserMobile(dispatchVO.getEmployeeMobile());
        process.setProcessStep(status);
        process.setStatus(WarningEventHandleStatus.STATUS_00.getCode());
        process.setExpectedCompleteTime(nextHour);
        process.setCreateTime(currentDate);
        process.setCreateBy(warningEventProcess.getCreateBy());

        warningEventProcessMapper.insertSelective(process);

        //生成新department
        WarningEventDepartment warningEventDepartment = new WarningEventDepartment();
        warningEventDepartment.setEventId(dispatchVO.getId());
        warningEventDepartment.setProcessId(process.getId());
        warningEventDepartment.setEventStatus(status);
        warningEventDepartment.setDepartmentId(dispatchVO.getDepartmentId());
        warningEventDepartment.setDepartmentName(dispatchVO.getDepartmentName());
        warningEventDepartment.setOrganizationCode(dispatchVO.getOrganizationCode());
        warningEventDepartment.setAccountId(dispatchVO.getAccountId());
        warningEventDepartment.setEmployeeCode(dispatchVO.getEmployeeCode());
        warningEventDepartment.setEmployeeName(dispatchVO.getEmployeeName());
        warningEventDepartment.setEmployeeMobile(dispatchVO.getEmployeeMobile());
        warningEventDepartment.setMainFlag(1);
        warningEventDepartment.setCreateBy(warningEventProcess.getCreateBy());
        warningEventDepartment.setCreateTime(currentDate);

        warningEventDepartmentMapper.insertSelective(warningEventDepartment);

        String userRelation = getDingDingUserRelation(dispatchVO.getAccountId(), dispatchVO.getEmployeeMobile());

        String mobileUrl = getMobileUrl(warningEvent.getId(), userRelation);

        String content = "收到一条转派工作通知，请及时查看通知内容并处置";

        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("转派通知", content);

        sendDingTalkAndDingDingMsg(dispatchVO.getAccountId(), dispatchVO.getEmployeeMobile(),
                CommonConstant.TRANSFER_ID, "转派工作通知", content, contentMap,
                mobileUrl, singlePcUrl);
    }

    private String getDingDingUserRelation(String accountId, String employeeMobile) {
        if (StringUtils.isBlank(accountId)) {
            return employeeMobile;
        }

        return accountId;
    }

    @Override
    public String sendNotify(Long id) {
        WarningEvent warningEvent = warningEventMapper.selectWarningEventById(id);

        String mobileUrl = getMobileUrl(warningEvent.getId(), "********");

        String warningContent = getWarningContent(warningEvent, null);
        Map<String, String> dingdingMap = getWarningDingDingContent(warningEvent);

        sendDingTalkAndDingDingMsg(StringUtils.join("********", ","), "***********",
                CommonConstant.WARNING_INFO_DTALK_DISPATCH_MSG,
                "您有一条新的预警事件(" + warningEvent.getBizCode() + ")需要核实，请及时签收！",
                warningContent, dingdingMap,
                mobileUrl, singlePcUrl);

        return "success";
    }

    private Map<String, String> getWarningDingDingContent(WarningEvent warningEvent) {
        Map<String, String> dingdingMap = new HashMap<>();
        dingdingMap.put("时间", DateUtils.formatDateSecond(warningEvent.getWarningTime()));
        dingdingMap.put("地点",
                warningEvent.getEventAddress() + "(" + warningEvent.getLon() + "," + warningEvent.getLat() + ")");
        dingdingMap.put("预警设备", warningEvent.getDeviceName());
        dingdingMap.put("告警信息描述", warningEvent.getWarningDetail());

        return dingdingMap;
    }

    @Override
    public void createWord(Long id, HttpServletResponse response) {
        BufferedWriter bufferedWriter = null;
//        File file = null;
        String currentFilePath = fileTmpUrl + "/" + IdUtils.fastSimpleUUID() + ".doc";
        log.info("FileUtils.getTempDirectoryPath():" + FileUtils.getTempDirectoryPath());
        try {
            // 获取模板文件
            Template template = configuration.getTemplate("event.ftl", "UTF-8");
            //指定输出流到的位置
//            bufferedWriter = new BufferedWriter(
//                    new OutputStreamWriter(new FileOutputStream(currentFilePath), StandardCharsets.UTF_8));

            Map<String, Object> dataMap = getDataMap(id);

            response.setCharacterEncoding("UTF-8");
            //请求头定义为下载
            response.setHeader("Content-Disposition","attachment;filename=" +
                    URLEncoder.encode(dataMap.get("warningEvent") + ".doc", "UTF-8"));
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
//            response.setContentType("application/pdf");

            bufferedWriter = new BufferedWriter(
                    new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8));

            // 将数据输出到html中
            template.process(dataMap, bufferedWriter);

//            file = new File(currentFilePath);

//            if (FileExtendUtil.getLicense()) {
//                FontSettings.setFontsFolder(fontUrl, true);
//                Document doc = new Document(currentFilePath);
//                doc.save(response.getOutputStream(), SaveFormat.PDF);
//                log.info("转换成功");
//            }
        } catch (Exception e) {
            log.info("pdf生成失败, e:" + e.getMessage());
//            throw new RuntimeException("生成pdf失败:" + e.getMessage());
        } finally {
            if (bufferedWriter != null) {
                try {
                    bufferedWriter.close();
                } catch (IOException e) {
                    log.info("流关闭异常");
                }
            }

//            if (file != null && file.exists()) {
//                file.delete();
//            }
        }
    }

    @Override
    public List<WarningEvent> listNonCompleteEvent() {
        List<WarningEvent> eventList = warningEventMapper.listNonCompleteEvent();

        return eventList;
    }

    @Override
    public void updateStatusByJczz() {
        List<WarningEvent> eventList = listNonCompleteEvent();
        if (org.springframework.util.CollectionUtils.isEmpty(eventList)) {
            log.info("JczzEventTask, updateWarningEventStatus, nonList");

            return;
        }

        for (WarningEvent warningEvent : eventList) {
            log.info("JczzEventTask, updateWarningEventStatus, warningEvent:{}", warningEvent.getEventThirdId());

            createJczzWarningEvent(warningEvent, null);
        }
    }

    @Override
    public void createJczzWarningEvent(WarningEvent warningEvent, WarningEventJczzLogVO jczzLogVO) {
        if (Objects.nonNull(jczzLogVO)) {
            warningEvent = warningEventMapper.getByEventThirdId(jczzLogVO.getEventNumber());
        } else {
            jczzLogVO = getJczzLogVO(warningEvent.getBizCode());
        }

        WarningEventJczzLog warningEventJczzLog = WarningConvert.INSTANCE.jczzVO2DO(jczzLogVO);

        //存储日志
        warningEventJczzLogMapper.insertSelective(warningEventJczzLog);

        if ("10".equals(jczzLogVO.getState())) {
            //未开始处置，直接跳过
            return;
        } else if ("32".equals(jczzLogVO.getState())) {
            //设置为处置中，更新事件流程状态
            insertJczzHandling(warningEvent, jczzLogVO);
        } else if ("38".equals(jczzLogVO.getState())) {
            //设置为处置完成，更新事件流程状态
            insertJczzHandleComplete(warningEvent, jczzLogVO);
        }
    }

    private WarningEventJczzLogVO getJczzLogVO(String bizCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("appCode", APPCODE);

        Map<String, String> bizContent = new HashMap<>();
        bizContent.put("eventNumber", bizCode);

        params.put("bizContent", bizContent);

        HttpUtil httpUtil = HttpUtil.getInstance();
        String response = httpUtil.sendHttpPostJSON("http://10.27.171.78:8080/API_16462819566279713/sjzx/event/synch/detail", params);

        log.info("JczzEventTask, createJczzWarningEvent, response:{}", response);

        Map map = com.alibaba.fastjson.JSON.parseObject(response, Map.class);

        log.info("JczzEventTask, createJczzWarningEvent, map:{}", JSON.toJSONString(map));

        Integer code = (Integer) map.get("code");
        if (code != 200) {
            return null;
        }

        WarningEventJczzLogVO jczzLogVO = JSON.parseObject(JSON.toJSONString(map.get("data")), WarningEventJczzLogVO.class);

        log.info("JczzEventTask, createJczzWarningEvent, jczzLogVO:{}", JSON.toJSONString(jczzLogVO));

        return jczzLogVO;
    }

    @Override
    public void handleHkWarningEvent(String fireEscapeDetection,
                                     HttpServletRequest request, HttpServletResponse response,
                                     MultipartFile file1, MultipartFile file2) {
        JSONObject jsonObject = JSON.parseObject(fireEscapeDetection);
        WarningEventHikvision warningEventHikvision = JSON.parseObject(fireEscapeDetection, WarningEventHikvision.class);

        String redisKey = CommonConstant.REDIS_CHANNEL_HK + warningEventHikvision.getDeviceID();
        Object cacheLock = redisCache.getCacheObject(redisKey);
        if (Objects.isNull(cacheLock)) {
            redisCache.setCacheObject(CommonConstant.REDIS_CHANNEL_HK + warningEventHikvision.getDeviceID(),
                    1, 5, TimeUnit.MINUTES);
        } else {
            log.info("HkWarningController, 重复事件");
            return;
        }

        String dateStr = warningEventHikvision.getDateTime();
        LocalDateTime dateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        String formattedDate = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        warningEventHikvisionMapper.insertSelective(warningEventHikvision);

        JSONObject detectionJsonObject = jsonObject.getJSONObject("FireEscapeDetection");

        JSONArray targetJsonArray = detectionJsonObject.getJSONArray("Target");

        JSONObject carObject = targetJsonArray.getJSONObject(0);

        String carLicense = (String) carObject.get("licensePlate");

        if (StringUtils.isBlank(carLicense)) {
            log.info("HkWarningController, 车牌为空");
            return;
        }

        DeviceInfo deviceInfo = deviceInfoMapper.getByField05(warningEventHikvision.getDeviceID());
        if (Objects.isNull(deviceInfo)) {
            log.info("HkWarningController, 设备不存在");
            return;
        }
        //获取第一次占用的时长
        Object firstOccupyTimeStr = redisCache.getCacheObject(CommonConstant.REDIS_CAR_OCCUPY + carLicense);
        if (Objects.isNull(firstOccupyTimeStr)) {
            //刚监测到
            redisCache.setCacheObject(CommonConstant.REDIS_CAR_OCCUPY + carLicense, formattedDate, 24, TimeUnit.HOURS);

            return;
        } else {
            Date firstOccupyDate = DateUtils.parseDate((String) firstOccupyTimeStr);

            long minutesRange = DateUtils.getMinutesBetweenDates(firstOccupyDate, new Date());

            if (minutesRange >= 60) {
                //已经生成过事件，走更新逻辑
                //        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), "日");
                WarningEvent existEvent = warningEventMapper.checkFireExist(carLicense, null, null);
                if (Objects.nonNull(existEvent)) {
                    //更新风险点具体描述、预警时间和占用时长
                    WarningEvent warningEvent = new WarningEvent();
                    warningEvent.setId(existEvent.getId());
                    warningEvent.setWarningDetail(getHkWarningReason(warningEventHikvision, carLicense, null, null));
                    warningEvent.setWarningTime(DateUtils.getNowDate());
                    warningEvent.setOccupyRange(minutesRange);

                    WarningInfo warningInfo = new WarningInfo();
                    warningInfo.setId(existEvent.getWarningId());
                    warningInfo.setWarningTime(warningEvent.getWarningTime());
                    warningInfo.setWarningReason(warningEvent.getWarningDetail());
                    //更新
                    warningEventMapper.updateWarningEvent(warningEvent);

                    warningInfoMapper.updateByPrimaryKeySelective(warningInfo);
                    //新增预警附件
                    saveFireFile(file1, file2, warningEvent.getId(), FileTypeEnum.TYPE_02.getCode());

                    return;
                }

                WarningEventPictureVO backGroudImage = JSON.parseObject(JSON.toJSONString(detectionJsonObject.get("BackGroudImage")),
                        WarningEventPictureVO.class);

                if (Objects.nonNull(backGroudImage)) {
                    log.info("HkWarningController, backGroudImage:{}", backGroudImage);

                    byte[] imageData = Base64.getDecoder().decode(backGroudImage.getResourcesContent());

                    log.info("HkWarningController, imageData:{}", Arrays.toString(imageData));
                }

                //创建warningInfo
                WarningInfo newWarningInfo = createNewWarningInfo(warningEventHikvision, carLicense, formattedDate, deviceInfo);

                warningInfoMapper.insertSelective(newWarningInfo);

                WarningEvent warningEvent = createNewWarningEvent(warningEventHikvision, carLicense, formattedDate, deviceInfo, newWarningInfo, minutesRange);

                fillEventBizCode(Lists.newArrayList(warningEvent), warningEvent.getEventType());

                warningEventMapper.insertWarningEvent(warningEvent);

                WarningEventDispatchVO dispatchVO = new WarningEventDispatchVO();
                dispatchVO.setId(warningEvent.getId());

                WarningRuleShuxin warningRuleShuxin = warningRuleShuxinMapper.selectByPrimaryKey(10L);
                String allAccountId = warningRuleShuxin.getAllAccountId();
                String allMobile = warningRuleShuxin.getAllMobile();

                SysUser sysUser = sysUserMapper.selectUserById(warningRuleShuxin.getWarningUserId());
                if (Objects.nonNull(sysUser)) {
                    dispatchVO.setDepartmentId(sysUser.getDeptId());
                    SysDept dept = sysUser.getDept();
                    if (Objects.nonNull(dept)) {
                        dispatchVO.setDepartmentName(dept.getDeptName());
                    }
                    dispatchVO.setAccountId(sysUser.getAccountId());
                    dispatchVO.setEmployeeCode(sysUser.getEmployeeCode());
                    dispatchVO.setEmployeeName(sysUser.getNickName());
                    dispatchVO.setEmployeeMobile(sysUser.getPhonenumber());

                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    dispatchVO.setExpectedCompleteTime(calendar.getTime());
                }

                dispatch(dispatchVO, allAccountId, allMobile);

                saveFireFile(file1, file2, warningEvent.getId(), FileTypeEnum.TYPE_02.getCode());

                warningEventService.push141PlatForm(warningEvent.getId());
            }
        }
    }

    private void saveFireFile(MultipartFile file1, MultipartFile file2, Long id, Integer code) {
        try {
            businessFileService.uploadFile(file1, id, FileTypeEnum.TYPE_02.getCode());
            businessFileService.uploadFile(file2, id, FileTypeEnum.TYPE_02.getCode());
        } catch (Exception e) {
            log.info("HkWarningController, file, error");
        }
    }

    private WarningEvent createNewWarningEvent(WarningEventHikvision warningEventHikvision, String carLicense,
                                               String dateStr, DeviceInfo deviceInfo, WarningInfo warningInfo, long minutesRange) {
        WarningEventSceneDepartment warningEventSceneDepartment = WarningSceneDepartmentConfig.SCENE_DEPARTMENT_MAP
                .get(deviceInfo.getDeviceScene());

        WarningEvent warningEvent = new WarningEvent();
        warningEvent.setWarningId(warningInfo.getId());
        warningEvent.setEventName(warningInfo.getWarningName());
        warningEvent.setEventType(warningInfo.getWarningScene());
        warningEvent.setDepartmentId(warningEventSceneDepartment.getDepartmentId());
        warningEvent.setDepartmentName(warningEventSceneDepartment.getDepartmentName());
        warningEvent.setEventAddress(warningInfo.getWarningAddress());
        warningEvent.setWarningDetail(warningInfo.getWarningReason());
        warningEvent.setWarningLevel(warningInfo.getWarningLevel());
        warningEvent.setWarningTime(warningInfo.getWarningTime());
        warningEvent.setStatus(0);
        warningEvent.setStopFlag(0);
        warningEvent.setOverFlag(0);
        warningEvent.setSuperviseFlag(1);
        warningEvent.setCreateTime(new Date());
        warningEvent.setCreateBy(warningEventSceneDepartment.getEmployeeCode());
        warningEvent.setLon(deviceInfo.getLon());
        warningEvent.setLat(deviceInfo.getLat());
        warningEvent.setEventStreet(deviceInfo.getDeviceStreet());
        warningEvent.setEventStreetAreaCode(deviceInfo.getDeviceStreetAreaCode());
        warningEvent.setDeviceThirdId(deviceInfo.getDeviceThirdId());
        warningEvent.setDeviceName(deviceInfo.getDeviceName());
        warningEvent.setConditionId(warningInfo.getConditionId());
        warningEvent.setOccupyRange(minutesRange);
        warningEvent.setFirstWarningTime(warningInfo.getWarningTime());

        return warningEvent;
    }

    private WarningInfo createNewWarningInfo(WarningEventHikvision warningEventHikvision,
                                      String carLicense, String dateStr, DeviceInfo deviceInfo) {
        WarningInfo warningInfo = new WarningInfo();
        warningInfo.setDeviceThirdId(deviceInfo.getDeviceThirdId());
        warningInfo.setDeviceId(deviceInfo.getId());
        warningInfo.setMonitorId(deviceInfo.getMonitorId());
        warningInfo.setMonitorValue(carLicense);
        warningInfo.setWarningData(deviceInfo.getMonitorName());
        warningInfo.setWarningReason(getHkWarningReason(warningEventHikvision, carLicense, dateStr, deviceInfo));
        warningInfo.setWarningLevel(WarningLevelType.WARNING_LEVEL_5.getCode());
//        warningInfo.setWarningTime(DateUtils.parseDate(dateStr));
        warningInfo.setWarningTime(DateUtils.getNowDate());
        warningInfo.setWarningAddress(deviceInfo.getDeviceAddress());
        warningInfo.setWarningName(deviceInfo.getDeviceScene() + deviceInfo.getDeviceName() + "登高面占用事件");
        warningInfo.setWarningScene(deviceInfo.getDeviceScene());
        warningInfo.setCreateTime(new Date());
        warningInfo.setCreateBy("SYSTEM");
        warningInfo.setModelName("消防安全子场景模型");
        warningInfo.setLon(deviceInfo.getLon());
        warningInfo.setLat(deviceInfo.getLat());
        warningInfo.setEventStreetAreaCode(deviceInfo.getDeviceStreetAreaCode());
        warningInfo.setEventStreet(deviceInfo.getDeviceStreet());

        return warningInfo;
    }

    private String getHkWarningReason(WarningEventHikvision warningEventHikvision, String carLicense, String dateStr, DeviceInfo deviceInfo) {
        StringBuilder sb = new StringBuilder();

//        sb.append(dateStr);
//        sb.append(deviceInfo.getDeviceAddress());
//        sb.append(deviceInfo.getDeviceName());
        sb.append("监测到登高面占用, ");
        sb.append("占用车辆车牌号为");
        sb.append(carLicense);

        return sb.toString();
    }

    private void insertJczzHandling(WarningEvent warningEvent, WarningEventJczzLogVO jczzLogVO) {
        if (warningEvent.getStatus() >= 4) {
            return;
        }

        warningEvent.setStatus(4);

        WarningEvent exist = new WarningEvent();
        exist.setId(warningEvent.getId());
        exist.setStatus(WarningEventStepEnum.STATUS_04.getCode());
        exist.setOverFlag(0);

        warningEventMapper.updateWarningEvent(exist);

        //直接设置处置完成
        warningEvent.setStatus(4);
        //更新流程
        WarningEventProcess warningEventProcess = new WarningEventProcess();
        warningEventProcess.setEventId(warningEvent.getId());
        warningEventProcess.setDepartmentId(warningEvent.getDepartmentId());
        warningEventProcess.setDepartmentName(warningEvent.getDepartmentName());

        List<WarningEventJczzFlowVO> eventFlowList = jczzLogVO.getEventFlowList();

        if (CollectionUtils.isNotEmpty(eventFlowList)) {
            for (WarningEventJczzFlowVO jczzEventVO : eventFlowList) {
                if ("32".equals(jczzEventVO.getState())) {
                    //更新其他所有状态为已完成
                    warningEventProcessMapper.updateEventStatus(warningEvent.getId());

                    warningEventProcess.setUserId(jczzEventVO.getUserId());
                    warningEventProcess.setUserName(jczzEventVO.getUserName());
                    warningEventProcess.setStatus(0);
                    warningEventProcess.setOverFlag(0);
                    warningEventProcess.setCreateTime(new Date());
                    warningEventProcess.setCreateBy(jczzEventVO.getUserId());
                    warningEventProcess.setUserMobile(jczzEventVO.getMobilePhone());
//                        warningEventProcess.setExpectedCompleteTime(0);

                    warningEventProcess.setProcessStep(WarningEventStepEnum.STATUS_04.getCode());

                    warningEventProcessMapper.insertSelective(warningEventProcess);

                    break;
                }
            }
        }
    }

    private void insertJczzHandleComplete(WarningEvent warningEvent, WarningEventJczzLogVO jczzLogVO) {
        if (warningEvent.getStatus() == 5 || warningEvent.getStatus() == 6) {
            return;
        }

        List<WarningEventJczzFlowVO> eventFlowList = jczzLogVO.getEventFlowList();
        if (CollectionUtils.isEmpty(eventFlowList)) {
            return;
        }

        WarningEventJczzFlowVO completeFlowVO = null;
        for (WarningEventJczzFlowVO jczzEventVO : eventFlowList) {
            if ("38".equals(jczzEventVO.getState())) {
                completeFlowVO = jczzEventVO;
            }
        }

        if (Objects.isNull(completeFlowVO)) {
            return;
        }

        WarningEvent exist = new WarningEvent();
        exist.setId(warningEvent.getId());
        exist.setStatus(WarningEventStepEnum.STATUS_05.getCode());
        exist.setOverFlag(0);

        warningEventMapper.updateWarningEvent(exist);

        WarningEventProcess warningEventProcess = new WarningEventProcess();

        if (warningEvent.getStatus() != 4) {
            //直接设置处置完成
            warningEvent.setStatus(4);
            //更新流程
            warningEventProcess.setEventId(warningEvent.getId());
            warningEventProcess.setDepartmentId(warningEvent.getDepartmentId());
            warningEventProcess.setDepartmentName(warningEvent.getDepartmentName());

            //更新其他所有状态为已完成
            warningEventProcessMapper.updateEventStatus(warningEvent.getId());

            warningEventProcess.setUserId(completeFlowVO.getUserId());
            warningEventProcess.setUserName(completeFlowVO.getUserName());
            warningEventProcess.setProcessContent(completeFlowVO.getSuggestion());

            Date processDate = DateUtils.parseDate(completeFlowVO.getProcessDate());
            warningEventProcess.setProcessTime(processDate);
            warningEventProcess.setExpectedCompleteTime(processDate);
            warningEventProcess.setStatus(3);
            warningEventProcess.setOverFlag(0);
            warningEventProcess.setCreateTime(new Date());
            warningEventProcess.setCreateBy(completeFlowVO.getUserId());
            warningEventProcess.setUserMobile(completeFlowVO.getMobilePhone());
//                        warningEventProcess.setExpectedCompleteTime(0);

            warningEventProcess.setProcessStep(WarningEventStepEnum.STATUS_04.getCode());

            warningEventProcessMapper.insertSelective(warningEventProcess);
        } else {
            warningEventProcess = warningEventProcessMapper.getLastByEventIdAndStep(warningEvent.getId(),
                    WarningEventStepEnum.STATUS_04.getCode());

            warningEventProcessMapper.updateHandleStatus(warningEventProcess.getId(), completeFlowVO);
        }

        WarningEventDepartment warningEventDepartment = new WarningEventDepartment();
        warningEventDepartment.setEventId(warningEvent.getId());
        warningEventDepartment.setProcessId(warningEventProcess.getId());
        warningEventDepartment.setEventStatus(4);
        warningEventDepartment.setDepartmentId(warningEvent.getDepartmentId());
        warningEventDepartment.setDepartmentName(warningEvent.getDepartmentName());
        warningEventDepartment.setAccountId(completeFlowVO.getUserId());
        warningEventDepartment.setEmployeeName(completeFlowVO.getUserId());
        warningEventDepartment.setEmployeeMobile(completeFlowVO.getMobilePhone());

        warningEventDepartmentMapper.insertSelective(warningEventDepartment);

        if (CollectionUtils.isNotEmpty(jczzLogVO.getAttachments())) {
            List<BusinessFile> businessFileList = Lists.newArrayList();

            for (WarningEventAttachmentVO attachment : jczzLogVO.getAttachments()) {
                BusinessFile businessFile = new BusinessFile();
                businessFile.setFileName(attachment.getFileName());
                businessFile.setFileUrl(attachment.getFilePath());
                businessFile.setBusinessId(warningEventProcess.getId());
                businessFile.setBusinessType(FileTypeEnum.TYPE_04.getCode());
                businessFile.setCreateTime(new Date());

                businessFileList.add(businessFile);
            }

            businessFileMapper.batchInsert(businessFileList);
        }
    }

    private Map<String, Object> getDataMap(Long id) {
        Map<String, Object> resultMap = new HashMap<>();

        WarningEvent warningEvent = selectWarningEventById(id, null);

        resultMap.put("warningEvent", warningEvent.getEventName());
        resultMap.put("warningTime", DateUtils.formatDateSecond(warningEvent.getWarningTime()));
        if (StringUtils.isNotBlank(warningEvent.getEventAddress())) {
            resultMap.put("warningAddress", warningEvent.getEventAddress());
        } else {
            resultMap.put("warningAddress", "");
        }
        resultMap.put("warningScene", warningEvent.getEventType());
        resultMap.put("warningLevel", WarningLevelType.getByCode(warningEvent.getWarningLevel()));
        if (StringUtils.isNotBlank(warningEvent.getDepartmentName())) {
            resultMap.put("warningMainDeaprtment", warningEvent.getDepartmentName());
        } else {
            resultMap.put("warningMainDeaprtment", "");
        }
        resultMap.put("warningDetail", warningEvent.getWarningDetail());

        List<BusinessFile> fileList = warningEvent.getFileList();
        if (CollectionUtils.isNotEmpty(fileList)) {
            List<BusinessFile> eventFileList = fileList.stream()
                    .filter(businessFile -> Objects.equals(businessFile.getBusinessType(), FileTypeEnum.TYPE_02.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(eventFileList)) {
                List<String> eventFileNameList = eventFileList.stream()
                        .map(BusinessFile::getFileName)
                        .collect(Collectors.toList());

                resultMap.put("fileName01", StringUtils.join(eventFileNameList, ","));
            } else {
                resultMap.put("fileName01", "无");
            }
        } else {
            resultMap.put("fileName01", "无");
        }

        List<WarningEventDepartment> departmentList = warningEvent.getDepartmentList();

        List<WarningEventProcess> processList = warningEvent.getProcessList();
        Optional<WarningEventProcess> verifyProcess = processList.stream()
                .filter(warningEventProcess -> Objects.equals(warningEventProcess.getProcessStep(), WarningEventStepEnum.STATUS_02.getCode()))
                .findFirst();
        if (verifyProcess.isPresent()) {
            WarningEventProcess warningEventProcess = verifyProcess.get();

            List<WarningEventDepartment> verifyDepartmentEventList =
                    warningEventDepartmentMapper.listByEventIdAndStatus(warningEventProcess.getEventId(), 2);

            List<WarningEventDepartment> mainVerifyList = verifyDepartmentEventList.stream()
                    .filter(warningEventDepartment -> Objects.equals(warningEventDepartment.getMainFlag(), 1))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mainVerifyList)) {
                WarningEventDepartment verifyMainDepartment = mainVerifyList.get(0);
                if (StringUtils.isNotBlank(verifyMainDepartment.getEmployeeName())) {
                    resultMap.put("verifyPerson", verifyMainDepartment.getEmployeeName());
                } else {
                    resultMap.put("verifyPerson", "");
                }
                if (StringUtils.isNotBlank(verifyMainDepartment.getEmployeeMobile())) {
                    resultMap.put("verifyMobile", verifyMainDepartment.getEmployeeMobile());
                } else {
                    resultMap.put("verifyMobile", "");
                }
            }

            List<WarningEventDepartment> cooperateVerifyList = verifyDepartmentEventList.stream()
                    .filter(warningEventDepartment -> Objects.equals(warningEventDepartment.getMainFlag(), 0))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cooperateVerifyList)) {
                List<String> cooperateEmployeeList = cooperateVerifyList.stream()
                        .map(WarningEventDepartment::getEmployeeName)
                        .collect(Collectors.toList());
                List<String> cooperateEmployeeMobileList = cooperateVerifyList.stream()
                        .map(WarningEventDepartment::getEmployeeMobile)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(cooperateEmployeeList)) {
                    resultMap.put("coperatePerson", StringUtils.join(cooperateEmployeeList, ","));
                } else {
                    resultMap.put("coperatePerson", "");
                }
                if (CollectionUtils.isNotEmpty(cooperateEmployeeMobileList)) {
                    resultMap.put("coperateMobile", StringUtils.join(cooperateEmployeeMobileList, ","));
                } else {
                    resultMap.put("coperateMobile", "");
                }
            }

            if (StringUtils.isNotBlank(warningEventProcess.getProcessContent())) {
                resultMap.put("verifyDetail", warningEventProcess.getProcessContent());
            } else {
                resultMap.put("verifyDetail", "");
            }

            if (Objects.nonNull(warningEventProcess.getProcessTime())) {
                resultMap.put("verifyTime", DateUtils.formatDateSecond(warningEventProcess.getProcessTime()));
            } else {
                resultMap.put("verifyTime", "");
            }

            if (CollectionUtils.isNotEmpty(fileList)) {
                List<BusinessFile> verifyFileList = fileList.stream()
                        .filter(businessFile -> Objects.equals(businessFile.getBusinessType(), FileTypeEnum.TYPE_03.getCode()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(verifyFileList)) {
                    resultMap.put("fileName02", StringUtils.join(verifyFileList, ","));
                } else {
                    resultMap.put("fileName02", "无");
                }
            } else {
                resultMap.put("fileName02", "无");
            }
        }

        Optional<WarningEventProcess> disposeProcess = processList.stream()
                .filter(warningEventProcess -> Objects.equals(warningEventProcess.getProcessStep(), WarningEventStepEnum.STATUS_04.getCode()))
                .findFirst();
        if (disposeProcess.isPresent()) {
            WarningEventProcess warningEventProcess = disposeProcess.get();

            List<WarningEventDepartment> disposeDepartmentEventList =
                    warningEventDepartmentMapper.listByEventIdAndStatus(warningEventProcess.getEventId(), 3);

            List<WarningEventDepartment> mainVerifyList = disposeDepartmentEventList.stream()
                    .filter(warningEventDepartment -> Objects.equals(warningEventDepartment.getMainFlag(), 1))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mainVerifyList)) {
                WarningEventDepartment disposeMainDepartment = mainVerifyList.get(0);
                if (StringUtils.isNotBlank(disposeMainDepartment.getDepartmentName())) {
                    resultMap.put("disposePerson", disposeMainDepartment.getEmployeeName());
                } else {
                    resultMap.put("disposePerson", "");
                }

                if (StringUtils.isNotBlank(disposeMainDepartment.getEmployeeMobile())) {
                    resultMap.put("disposeMobile", disposeMainDepartment.getEmployeeMobile());
                } else {
                    resultMap.put("disposeMobile", "");
                }
            }

            List<WarningEventDepartment> cooperateDisposeList = disposeDepartmentEventList.stream()
                    .filter(warningEventDepartment -> Objects.equals(warningEventDepartment.getMainFlag(), 0))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cooperateDisposeList)) {
                List<String> cooperateEmployeeList = cooperateDisposeList.stream()
                        .map(WarningEventDepartment::getEmployeeName)
                        .collect(Collectors.toList());
                List<String> cooperateEmployeeMobileList = cooperateDisposeList.stream()
                        .map(WarningEventDepartment::getEmployeeMobile)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(cooperateEmployeeList)) {
                    resultMap.put("disposeCoperatePerson", StringUtils.join(cooperateEmployeeList, ","));
                } else {
                    resultMap.put("disposeCoperatePerson", "");
                }

                if (CollectionUtils.isNotEmpty(cooperateEmployeeMobileList)) {
                    resultMap.put("disposeCoperateMobile", StringUtils.join(cooperateEmployeeMobileList, ","));
                } else {
                    resultMap.put("disposeCoperateMobile", "");
                }
            } else {
                resultMap.put("disposeCoperatePerson", "");
                resultMap.put("disposeCoperateMobile", "");
            }

            if (StringUtils.isNotBlank(warningEventProcess.getProcessContent())) {
                resultMap.put("disposeDetail", warningEventProcess.getProcessContent());
            } else {
                resultMap.put("disposeDetail", "");
            }
            if (Objects.nonNull(warningEventProcess.getProcessTime())) {
                resultMap.put("disposeTime", DateUtils.formatDateSecond(warningEventProcess.getProcessTime()));
            } else {
                resultMap.put("disposeTime", "");
            }

            if (CollectionUtils.isNotEmpty(fileList)) {
                List<BusinessFile> disposeFileList = fileList.stream()
                        .filter(businessFile -> Objects.equals(businessFile.getBusinessType(), FileTypeEnum.TYPE_04.getCode()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(disposeFileList)) {
                    resultMap.put("fileName03", StringUtils.join(disposeFileList, ","));
                } else {
                    resultMap.put("fileName03", "无");
                }
            } else {
                resultMap.put("fileName03", "无");
            }
        }
//        resultMap.put("warningDetail", warningEvent.getWarningDetail());

        return resultMap;
    }

    @Override
    public void fillFileList(List<WarningEvent> warningEventList, List<Long> warningEventIdList) {
        //添加文件
        List<BusinessFile> fileList = businessFileMapper.listByBusinessIdListAndType(warningEventIdList, FileTypeEnum.TYPE_02.getCode());
        if (CollectionUtils.isNotEmpty(fileList)) {
            Map<Long, List<BusinessFile>> fileMap = fileList.stream()
                    .collect(Collectors.groupingBy(BusinessFile::getBusinessId));

            for (WarningEvent event : warningEventList) {
                List<BusinessFile> needFileList = Lists.newArrayList();
                List<BusinessFile> fileListByEventId = fileMap.get(event.getId());
                if (Objects.nonNull(fileListByEventId)) {
                    if (fileListByEventId.size() > 4) {
                        List<BusinessFile> startFileList = fileListByEventId.subList(0, 2);
                        List<BusinessFile> endFileList = fileListByEventId.subList(fileListByEventId.size() - 2, fileListByEventId.size());
                        needFileList.addAll(startFileList);
                        needFileList.addAll(endFileList);
                    } else {
                        if (CollectionUtils.isNotEmpty(fileListByEventId)) {
                            needFileList.addAll(fileListByEventId);
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(needFileList)) {
                    event.setFileList(needFileList);
                }
            }
        }
    }

    @Override
    public void fillEventBizCode(List<WarningEvent> warningEventList, String scene) {
        int bizCodeCount = 1;
        Long warningCount = warningEventMapper.getCountByMonthAndScene(scene);
        if (Objects.isNull(warningCount)) {
            warningCount = 0L;
        }
        String abbr = MonitorSceneType.getAbbrByScene(scene);
        String monthStr = DateUtils.formatYearMonthV3(new Date());
        for (WarningEvent warningEvent : warningEventList) {
            long currentCount = warningCount + bizCodeCount;

            String currentCountStr = "";
            if (currentCount < 100) {
                currentCountStr = "0" + currentCount;
            } else {
                currentCountStr = String.valueOf(currentCount);
            }

            String bizCode = abbr + "-" + monthStr + "-" + currentCountStr;

            warningEvent.setBizCode(bizCode);

            bizCodeCount++;
        }
    }

    @Override
    public List<WarningEvent> listNonCompleteOrangeEvent(WarningEventQueryVO queryVO) {
        List<WarningEvent> warningEventList = warningEventMapper.listNonCompleteOrangeEvent(queryVO);

        fillAdviceDepartment(warningEventList);
        fillQrCode(warningEventList);

        return warningEventList;
    }

    @Override
    public WarningEventJczzLogVO get141Detail(String bizCode) {
        WarningEventJczzLogVO jczzLogVO = getJczzLogVO(bizCode);

        return jczzLogVO;
    }

    @Override
    public void flushEventStatus() {
        List<WarningEvent> list = warningEventMapper.listNonCompleteOrangeEvent(null);

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (WarningEvent warningEvent : list) {
            WarningEventJczzLogVO jczzLogVO = get141Detail(warningEvent.getBizCode());
            if (Objects.nonNull(jczzLogVO) && "38".equals(jczzLogVO.getState())) {
                //设置为处置完成，更新事件流程状态
//                insertJczzHandleComplete(warningEvent, jczzLogVO);
            }
        }
    }

    @Override
    public WarningEvent getByEventThirdId(String eventThirdId) {
        if (StringUtils.isBlank(eventThirdId)) {
            return null;
        }

        WarningEvent warningEvent = warningEventMapper.getByEventThirdId(eventThirdId);

        if (Objects.isNull(warningEvent)) {
            return null;
        }

        List<Long> eventIdList = Lists.newArrayList(warningEvent.getId());

        fillFileList(Lists.newArrayList(warningEvent), eventIdList);
        fillProcessList(Lists.newArrayList(warningEvent), eventIdList);
        convertProcessList(warningEvent);
        fillSupervise(Lists.newArrayList(warningEvent), eventIdList);
        fillProcessLog(warningEvent);

        return warningEvent;
    }

    @Override
    public void sendMobileMsg(String mobile) {
        voiceClient.sendMobileMsg(mobile);
    }

    @Override
    public int return2Dispatch(WarningEventDispatchVO dispatchVO) {
        WarningEvent warningEvent = checkEvent(dispatchVO.getId());

        warningEvent.setId(dispatchVO.getId());
        warningEvent.setStatus(WarningEventStepEnum.STATUS_00.getCode());
        warningEvent.setRejectReason(dispatchVO.getReason());
        warningEvent.setUpdateTime(new Date());
        warningEvent.setUpdateBy(dispatchVO.getOperateUserId());

        warningEventMapper.updateWarningEvent(warningEvent);

        List<WarningEventProcess> nonCompleteProcessList = warningEventProcessMapper.listNonCompleteProcess(dispatchVO.getId());
        for (WarningEventProcess warningEventProcess : nonCompleteProcessList) {
            WarningEventProcess updateProcess = new WarningEventProcess();

            updateProcess.setId(warningEventProcess.getId());
            updateProcess.setStatus(WarningEventHandleStatus.STATUS_01.getCode());
            updateProcess.setRejectContent(dispatchVO.getReason());
            warningEventProcessMapper.updateByPrimaryKeySelective(updateProcess);
        }

        WarningEventProcess rollBackProcess = new WarningEventProcess();
        rollBackProcess.setEventId(dispatchVO.getId());

        SysUser sysUser = sysUserMapper.selectUserById(SecurityUtils.getUserId());
        SysDept dept = sysUser.getDept();
        rollBackProcess.setDepartmentId(dept.getDeptId());
        rollBackProcess.setDepartmentName(dept.getDeptName());
        rollBackProcess.setUserId(sysUser.getUserId() + "");
        rollBackProcess.setUserName(sysUser.getUserName());
        rollBackProcess.setUserMobile(sysUser.getPhonenumber());
        rollBackProcess.setProcessStep(WarningEventStepEnum.STATUS_10.getCode());
        rollBackProcess.setProcessContent(dispatchVO.getReason());
        rollBackProcess.setProcessTime(new Date());
        rollBackProcess.setStatus(WarningEventHandleStatus.STATUS_03.getCode());

        warningEventProcessMapper.insertSelective(rollBackProcess);

        return 1;


    }

    @Override
    public Map push141PlatForm(Long id) {
        WarningEvent warningEvent1 = warningEventService.selectWarningEventById(id, null);
        return eventReport(warningEvent1, "40");
    }

    @Override
    public WarningEventDepartment getLastUserMobile(Long id) {
        return warningEventDepartmentMapper.getLastUserMobile(id);
    }

    /**
     * 事件上报
     * @param warningEvent
     */
    @Override
    public Map eventReport(WarningEvent warningEvent, String state) {
        Map<String, Object> params = new HashMap<>();
        params.put("appCode", APPCODE);

        Map<String, Object> bizContent = new HashMap<>();
        bizContent.put("eventNumber", warningEvent.getBizCode());
        bizContent.put("sourceWayCode", "1");
        bizContent.put("sourceWayName", "巡检录入");
        bizContent.put("eventTypeOne", "03");//事件类型一级代码
        bizContent.put("eventTypeTwo", "99");//事件类型一级代码
        bizContent.put("eventTypeThree", "99999");//事件类型一级代码
        bizContent.put("title", warningEvent.getEventName());
        bizContent.put("content", warningEvent.getWarningDetail());
        bizContent.put("address", warningEvent.getEventAddress());
        bizContent.put("longitude", warningEvent.getLon());
        bizContent.put("latitude", warningEvent.getLat());
        bizContent.put("eventTime", warningEvent.getWarningTime());
        bizContent.put("state", state);
        bizContent.put("cityCode", "330800");
        bizContent.put("cityName", "衢州市");
        bizContent.put("countyCode", "330881000");
        bizContent.put("countyName", "江山市");
        // 附件列表 attachments

        List<Map<String, String>> attachments = new ArrayList<>();
        if (warningEvent.getFileList() != null && !warningEvent.getFileList().isEmpty()) {
            for (BusinessFile file : warningEvent.getFileList()) {
                Map<String, String> attachment = new HashMap<>();
                attachment.put("fileName", file.getFileName());
                attachment.put("filePath", fileOpenUploadUrl + file.getFilePath());
                attachments.add(attachment);
            }
        }
        bizContent.put("attachments", attachments);

        // 流程信息 eventFlowList
        List<Map<String, Object>> eventFlowList = new ArrayList<>();
        if (warningEvent.getProcessList() != null && !warningEvent.getProcessList().isEmpty()) {
            for (WarningEventProcess flow : warningEvent.getProcessList()) {
                Map<String, Object> flowItem = new HashMap<>();
                flowItem.put("state", state);
                flowItem.put("userId", flow.getUserId());
                flowItem.put("userName", flow.getUserName());
                flowItem.put("mobilePhone", flow.getUserMobile());
                flowItem.put("organizationCode", flow.getDepartmentId());
                flowItem.put("organizationName", flow.getDepartmentName());
                flowItem.put("suggestion", flow.getProcessContent());
                flowItem.put("processDate", flow.getProcessTime());
                eventFlowList.add(flowItem);
            }
        }
        bizContent.put("eventFlowList", eventFlowList);
        params.put("bizContent", bizContent);
        //将params 转换成json字符串
        String json = JSON.toJSONString(params);
        log.info("EventReport, eventNumber:{}, json:{}", warningEvent.getBizCode(), json);
        HttpUtil httpUtil = HttpUtil.getInstance();
        String response = httpUtil.sendHttpPostJSON("http://10.27.171.78:8080/API_16462814415314148/sjzx/event/synch/report", params);
        log.info("EventReport, eventNumber:{}, response:{}", warningEvent.getBizCode(), response);

        Map map = com.alibaba.fastjson.JSON.parseObject(response, Map.class);

        log.info("EventReport, eventNumber:{}, map:{}", warningEvent.getBizCode(), JSON.toJSONString(map));

        eventDispose(warningEvent);

        return map;
    }

    private void fillQrCode(List<WarningEvent> warningEventList) {
        if (CollectionUtils.isEmpty(warningEventList)) {
            return;
        }

        for (WarningEvent warningEvent : warningEventList) {
            if (StringUtils.isBlank(warningEvent.getQrCode())) {
                String qrCodeUrl = generateQrCode(warningEvent);

                warningEvent.setQrCode(qrCodeUrl);
            }
        }
    }

    private String generateQrCode(WarningEvent warningEvent) {
        try {
            // 1. 构建二维码内容（根据需求调整URL格式）
            String qrContent = getMobileUrl(warningEvent.getId(), null);

            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");

            BitMatrix bitMatrix = new QRCodeWriter().encode(
                    qrContent,
                    BarcodeFormat.QR_CODE,
                    300, 300, hints
            );

            // 转换为MultipartFile
            BufferedImage bufferedImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "png", baos);

            MultipartFile multipartFile = new MockMultipartFile(
                    "qrcode_" + warningEvent.getBizCode() + ".png",
                    "qrcode.png",
                    "image/png",
                    baos.toByteArray()
            );

            // 使用现有文件服务保存
            BusinessFile businessFile = businessFileService.uploadFile(
                    multipartFile,
                    warningEvent.getId(),  // 关联业务ID
                    FileTypeEnum.TYPE_09.getCode() // 根据实际情况指定文件类型
            );

            WarningEvent updateEvent = new WarningEvent();
            updateEvent.setId(warningEvent.getId());
            updateEvent.setQrCode(businessFile.getFileUrl());
            warningEventMapper.updateWarningEvent(updateEvent);

            return businessFile.getFileUrl();
        } catch (Exception e) {
            log.error("二维码生成失败", e);
            return null;
        }
    }

    private void fillAdviceDepartment(List<WarningEvent> warningEventList) {
        if (CollectionUtils.isEmpty(warningEventList)) {
            return;
        }

        for (WarningEvent warningEvent : warningEventList) {
            String adviceDepartment = MonitorSceneType.getAdviceDepartmentByScene(warningEvent.getEventType());
            warningEvent.setAdviceDepartment(adviceDepartment);
        }
    }

    /**
     * 新增设备预警事件
     *
     * @param warningEvent 设备预警事件
     * @return 结果
     */
    @Override
    public int insertWarningEvent(WarningEvent warningEvent)
    {
        warningEvent.setCreateTime(DateUtils.getNowDate());
        return warningEventMapper.insertWarningEvent(warningEvent);
    }

    /**
     * 修改设备预警事件
     *
     * @param warningEvent 设备预警事件
     * @return 结果
     */
    @Override
    public int updateWarningEvent(WarningEvent warningEvent)
    {
        warningEvent.setUpdateTime(DateUtils.getNowDate());
        return warningEventMapper.updateWarningEvent(warningEvent);
    }

    /**
     * 批量删除设备预警事件
     *
     * @param ids 需要删除的设备预警事件主键
     * @return 结果
     */
    @Override
    public int deleteWarningEventByIds(Long[] ids)
    {
        return warningEventMapper.deleteWarningEventByIds(ids);
    }

    /**
     * 删除设备预警事件信息
     *
     * @param id 设备预警事件主键
     * @return 结果
     */
    @Override
    public int deleteWarningEventById(Long id)
    {
        return warningEventMapper.deleteWarningEventById(id);
    }

    @Override
    public Map<String, Object> getWarningDispose(String scene, Date startTime, Date endTime) {
        Map<String, Object> resultMap = new HashMap<>();

        //统计
        WarningCountVO warningCountVO = new WarningCountVO();

        List<BusinessCountVO> countList = warningEventMapper.countLevelBySceneAndTime(scene, startTime, endTime);
        if (CollectionUtils.isNotEmpty(countList)) {
            Map<String, Long> map = countList.stream()
                    .collect(Collectors.toMap(BusinessCountVO::getKey, BusinessCountVO::getValue));

            warningCountVO.setWarningLevel1(Objects.isNull(map.get("1")) ? 0L : map.get("1"));
            warningCountVO.setWarningLevel2(Objects.isNull(map.get("2")) ? 0L : map.get("2"));
            warningCountVO.setWarningLevel3(Objects.isNull(map.get("3")) ? 0L : map.get("3"));
            warningCountVO.setWarningLevel4(Objects.isNull(map.get("4")) ? 0L : map.get("4"));
            warningCountVO.setWarningLevel5(Objects.isNull(map.get("5")) ? 0L : map.get("5"));
        }

        resultMap.put("resultCount", warningCountVO);

        //最新5条
        List<WarningEvent> resultList = warningEventMapper.listByConditionLimit(scene, null, null, null, 5);

        resultMap.put("resultList", resultList);

        return resultMap;
    }

    @Override
    public WarningCountVO getSafetyCount(String dateStr, String scene) {
        WarningCountVO warningCountVO = new WarningCountVO();

        DateDTO dateDTO = DateUtils.getTimeRange(new Date(), dateStr);

        List<BusinessCountVO> countVOList = warningInfoMapper.countByMonitorScene(scene, dateDTO.getStartTime(), dateDTO.getEndTime());

        Map<String, Long> map = countVOList.stream()
                .collect(Collectors.toMap(BusinessCountVO::getKey, BusinessCountVO::getValue));

        warningCountVO.setWarningLevel1(Objects.isNull(map.get("1")) ? 0L : map.get("1"));
        warningCountVO.setWarningLevel2(Objects.isNull(map.get("2")) ? 0L : map.get("2"));
        warningCountVO.setWarningLevel3(Objects.isNull(map.get("3")) ? 0L : map.get("3"));
        warningCountVO.setWarningLevel4(Objects.isNull(map.get("4")) ? 0L : map.get("4"));
        warningCountVO.setWarningLevel5(Objects.isNull(map.get("5")) ? 0L : map.get("5"));

        return warningCountVO;
    }

//    @Override
//    @Transactional
//    @Deprecated
//    public int dispatchOld(WarningEventDispatchVO dispatchVO, Long userId) {
//        WarningEvent warningEvent = checkEvent(dispatchVO.getId());
//        WarningEventProcess existProcess = checkProcess(dispatchVO.getId(), WarningEventStepEnum.STATUS_01.getCode());
//
//        Date currentDate = new Date();
//
//        warningEvent.setDeliveryTime(currentDate);
//        warningEvent.setStatus(WarningEventStepEnum.STATUS_01.getCode());
//
//        warningEventMapper.updateWarningEvent(warningEvent);
//
//        //生成流程
//        WarningEventProcess process = new WarningEventProcess();
//        process.setEventId(dispatchVO.getId());
//        process.setDepartmentId(dispatchVO.getDepartmentId());
//        process.setDepartmentName(dispatchVO.getDepartmentName());
//        process.setProcessStep(1);
//        process.setStatus(0);
//        process.setExpectedCompleteTime(dispatchVO.getExpectedCompleteTime());
//        process.setCreateTime(currentDate);
//        process.setCreateBy(userId + "");
//
//        warningEventProcessMapper.insertSelective(process);
//
//        //生成责任部门
//        WarningEventDepartment warningEventDepartment = new WarningEventDepartment();
//        warningEventDepartment.setEventId(dispatchVO.getId());
//        warningEventDepartment.setProcessId(process.getId());
//        warningEventDepartment.setEventStatus(1);
//        warningEventDepartment.setDepartmentId(dispatchVO.getDepartmentId());
//        warningEventDepartment.setDepartmentName(dispatchVO.getDepartmentName());
//        warningEventDepartment.setOrganizationCode(dispatchVO.getOrganizationCode());
//        warningEventDepartment.setAccountId(dispatchVO.getAccountId());
//        warningEventDepartment.setEmployeeCode(dispatchVO.getEmployeeCode());
//        warningEventDepartment.setEmployeeName(dispatchVO.getEmployeeName());
//        warningEventDepartment.setMainFlag(1);
//        warningEventDepartment.setCreateBy(userId + "");
//        warningEventDepartment.setCreateTime(currentDate);
//
//        warningEventDepartmentMapper.insertSelective(warningEventDepartment);
//
//        String mobileUrl = getMobileUrl(warningEvent.getId(), dispatchVO.getOperateUserId());
//
//        userDtalkService.workNotification(StringUtils.join(dispatchVO.getAccountId(), ","),
//                CommonConstant.WARNING_INFO_DTALK_DISPATCH_MSG + warningEvent.getId(),
//                "您有一条新的预警事件需要核实，请及时处理！",
//                getWarningContent(warningEvent, null),
//                mobileUrl, singlePcUrl);
//
//        return 1;
//    }

    @Override
    @Transactional
    public int dispatch(WarningEventDispatchVO dispatchVO, String accountId, String employeeMobile) {
        WarningEvent warningEvent = checkEvent(dispatchVO.getId());
//        WarningEventProcess existProcess = checkProcess(dispatchVO.getId(), WarningEventStepEnum.STATUS_01.getCode());

        Date currentDate = new Date();

        warningEvent.setDeliveryTime(currentDate);
        warningEvent.setStatus(WarningEventStepEnum.STATUS_01.getCode());
        warningEvent.setExpectedCompleteTime(dispatchVO.getExpectedCompleteTime());

        warningEventMapper.updateWarningEvent(warningEvent);

        //生成流程
        WarningEventProcess process = new WarningEventProcess();
        process.setEventId(dispatchVO.getId());
        process.setUserId(dispatchVO.getAccountId());
        process.setUserName(dispatchVO.getEmployeeName());
        process.setDepartmentId(dispatchVO.getDepartmentId());
        process.setDepartmentName(dispatchVO.getDepartmentName());
        process.setUserMobile(dispatchVO.getEmployeeMobile());
        process.setProcessStep(WarningEventStepEnum.STATUS_01.getCode());
        process.setStatus(WarningEventHandleStatus.STATUS_00.getCode());

        Date nextHour = DateUtils.getNextHour(currentDate);
        process.setExpectedCompleteTime(nextHour);
        process.setCreateTime(currentDate);
        process.setCreateBy(dispatchVO.getAccountId());

        warningEventProcessMapper.insertSelective(process);

        //生成责任部门
        WarningEventDepartment warningEventDepartment = new WarningEventDepartment();
        warningEventDepartment.setEventId(dispatchVO.getId());
        warningEventDepartment.setProcessId(process.getId());
        warningEventDepartment.setEventStatus(WarningEventStepEnum.STATUS_01.getCode());
        warningEventDepartment.setDepartmentId(dispatchVO.getDepartmentId());
        warningEventDepartment.setDepartmentName(dispatchVO.getDepartmentName());
        warningEventDepartment.setOrganizationCode(dispatchVO.getOrganizationCode());
        warningEventDepartment.setAccountId(dispatchVO.getAccountId());
        warningEventDepartment.setEmployeeCode(dispatchVO.getEmployeeCode());
        warningEventDepartment.setEmployeeName(dispatchVO.getEmployeeName());
        warningEventDepartment.setEmployeeMobile(dispatchVO.getEmployeeMobile());
        warningEventDepartment.setMainFlag(1);
        warningEventDepartment.setCreateBy(dispatchVO.getAccountId());
        warningEventDepartment.setCreateTime(currentDate);

        warningEventDepartmentMapper.insertSelective(warningEventDepartment);

        String userRelation = getDingDingUserRelation(dispatchVO.getAccountId(), dispatchVO.getEmployeeMobile());

        String mobileUrl = getMobileUrl(warningEvent.getId(), userRelation);

        //浙政钉和钉钉通知
        sendDingTalkAndDingDingMsg(accountId,
                employeeMobile,
                CommonConstant.WARNING_INFO_DTALK_DISPATCH_MSG,
                "您有一条新的预警事件(" + warningEvent.getBizCode() + ")需要核实，请及时签收！",
                getWarningContent(warningEvent, null),
                getWarningDingDingContent(warningEvent),
                mobileUrl, singlePcUrl);

        return 1;
    }

    private void sendDingTalkAndDingDingMsg(String accountId, String employeeMobile, String bizMsgId,
                                            String title, String content, Map<String, String> dingdingContent,
                                            String singleUrl, String singlePcUrl) {
        CompletableFuture.runAsync(() -> {
            String uniqueId = bizMsgId + IdUtils.fastUUID();

            userDtalkService.workNotification(accountId, uniqueId, title, content, singleUrl, singlePcUrl);

            dingDingClient.sendBatchWordRecord(employeeMobile, title, content, dingdingContent, singleUrl, singlePcUrl);
        });
    }

    @Override
    public int rollbackV2(WarningEventDispatchVO dispatchVO) {
        WarningEvent warningEvent = checkEvent(dispatchVO.getId());

        warningEvent.setId(dispatchVO.getId());
        warningEvent.setStatus(WarningEventStepEnum.STATUS_06.getCode());
        warningEvent.setRejectReason(dispatchVO.getReason());
        warningEvent.setUpdateTime(new Date());
        warningEvent.setUpdateBy(dispatchVO.getOperateUserId());

        warningEventMapper.updateWarningEvent(warningEvent);

//        WarningEventProcess warningEventProcess = warningEventProcessMapper.getLastByEventId(dispatchVO.getId());
//
//        WarningEventProcess updateProcess = new WarningEventProcess();
//        updateProcess.setId(warningEventProcess.getId());
//        updateProcess.setStatus(WarningEventHandleStatus.STATUS_01.getCode());
//        updateProcess.setRejectContent(dispatchVO.getReason());
//
//        warningEventProcessMapper.updateByPrimaryKeySelective(updateProcess);
        List<WarningEventProcess> nonCompleteProcessList = warningEventProcessMapper.listNonCompleteProcess(dispatchVO.getId());
        for (WarningEventProcess warningEventProcess : nonCompleteProcessList) {
            WarningEventProcess updateProcess = new WarningEventProcess();

            updateProcess.setId(warningEventProcess.getId());
            updateProcess.setStatus(WarningEventHandleStatus.STATUS_01.getCode());
            updateProcess.setRejectContent(dispatchVO.getReason());
            warningEventProcessMapper.updateByPrimaryKeySelective(updateProcess);
        }

        WarningEventProcess rollBackProcess = new WarningEventProcess();
        rollBackProcess.setEventId(dispatchVO.getId());

        SysUser sysUser = sysUserMapper.selectUserById(SecurityUtils.getUserId());
        SysDept dept = sysUser.getDept();
        rollBackProcess.setDepartmentId(dept.getDeptId());
        rollBackProcess.setDepartmentName(dept.getDeptName());
        rollBackProcess.setUserId(sysUser.getUserId() + "");
        rollBackProcess.setUserName(sysUser.getUserName());
        rollBackProcess.setUserMobile(sysUser.getPhonenumber());
        rollBackProcess.setProcessStep(WarningEventStepEnum.STATUS_06.getCode());
        rollBackProcess.setProcessContent(dispatchVO.getReason());
        rollBackProcess.setProcessTime(new Date());
        rollBackProcess.setStatus(WarningEventHandleStatus.STATUS_03.getCode());

        warningEventProcessMapper.insertSelective(rollBackProcess);

        return 1;
    }

    private SysUser getUserByAccountId(String accountId) {
        SysUser sysUser = sysUserMapper.getByAccountId(accountId);

        return sysUser;
    }

    private String getMobileUrl(Long id, String operateUserId) {
        String url = singleUrl + "?id=" + id;

        if (StringUtils.isBlank(operateUserId)) {
            operateUserId = "********";
        }

        Map map = new HashMap();

        map.put("userId", operateUserId);

        String encodeStr = Base64Encoder.encode(JSON.toJSONString(map));

        url = singleUrl + "?id=" + id + "&params=" + encodeStr;

        return url;
    }

    private String getWarningContent(WarningEvent warningEvent, String verifyReason) {
        StringBuilder sb = new StringBuilder();
//        sb.append("时间:");
        sb.append(DateUtils.formatDateSecond(warningEvent.getWarningTime()));
//        sb.append("<br>");

//        sb.append("地点:");
        sb.append(warningEvent.getEventAddress());
        sb.append("(");
        sb.append(warningEvent.getLon());
        sb.append(",");
        sb.append(warningEvent.getLat());
        sb.append(")");
//        sb.append("<br>");

//        sb.append("预警设备:");
        sb.append(warningEvent.getDeviceName());
//        sb.append("<br>");

//        sb.append("告警信息描述:");
        sb.append(warningEvent.getWarningDetail());

        return sb.toString();
    }

//    @Override
    @Transactional
    public int rollback(WarningEventDispatchVO dispatchVO) {
        WarningEvent warningEvent = checkEvent(dispatchVO.getId());
        Integer status = warningEvent.getStatus();

        WarningEventProcess currentProcess = checkProcessNotExist(dispatchVO.getId(), status);
        WarningEventProcess lastProcess = checkProcessNotExist(dispatchVO.getId(), status-1);

        warningEvent.setStatus(warningEvent.getStatus() - 1);
        warningEvent.setRejectReason(dispatchVO.getReason());
        warningEvent.setUpdateTime(new Date());
        warningEvent.setUpdateBy(dispatchVO.getOperateUserId());

        warningEventMapper.updateWarningEvent(warningEvent);

        if (Objects.nonNull(lastProcess)) {
            WarningEventProcess newProcess = new WarningEventProcess();
            newProcess.setEventId(lastProcess.getEventId());
            newProcess.setDepartmentId(lastProcess.getDepartmentId());
            newProcess.setDepartmentName(lastProcess.getDepartmentName());
            newProcess.setProcessStep(lastProcess.getProcessStep());
            newProcess.setStatus(WarningEventHandleStatus.STATUS_00.getCode());
            newProcess.setOverFlag(0);
            newProcess.setExpectedCompleteTime(lastProcess.getExpectedCompleteTime());
            newProcess.setCreateTime(new Date());
            newProcess.setCreateBy(dispatchVO.getOperateUserId());
            warningEventProcessMapper.insertSelective(newProcess);

            WarningEventProcess exist = new WarningEventProcess();
            exist.setId(lastProcess.getId());
            exist.setStatus(WarningEventHandleStatus.STATUS_01.getCode());
            exist.setRejectContent(dispatchVO.getReason());

            warningEventProcessMapper.updateByPrimaryKeySelective(exist);
        }

        warningEventProcessMapper.deleteByPrimaryKey(currentProcess.getId());

        //删除主责部门
//        if (Objects.equals(WarningEventStepEnum.STATUS_02.getCode(), warningEvent.getStatus())) {
//            //待签收退回到待核实,删除主责和协同部门
//            warningEventDepartmentMapper.deleteByEventIdAndStatus(warningEvent.getId(), warningEvent.getStatus());
//            //删除附件
//            businessFileMapper.deleteByBusinessIdAndTypeList(warningEvent.getId(), Lists.newArrayList(FileTypeEnum.TYPE_03.getCode()));
//        } else if (Objects.equals(WarningEventStepEnum.STATUS_01.getCode(), warningEvent.getStatus())) {
//            warningEventDepartmentMapper.deleteByEventIdAndStatus(warningEvent.getId(), warningEvent.getStatus());
//        }

        return 1;
    }

    private WarningEventProcess checkProcessNotExist(Long id, Integer status) {
        WarningEventProcess process = warningEventProcessMapper.getByEventId(id, status);
        if (Objects.isNull(process)) {
            throw new RuntimeException("流程不存在或已退回");
        }

        return process;
    }

    @Override
    public int verify(WarningEventVerifyVO verifyVO) {
        WarningEvent warningEvent = checkEvent(verifyVO.getId());
        WarningEventProcess currentProcess = checkProcessNotExist(verifyVO.getId(), WarningEventStepEnum.STATUS_02.getCode());
        WarningEventProcess lastProcess = checkProcessNotExist(verifyVO.getId(), WarningEventStepEnum.STATUS_01.getCode());

        List<WarningEventDepartment> departmentList = verifyVO.getDepartmentList();

        Optional<WarningEventDepartment> departmentOptional = departmentList.stream()
                .filter(department -> department.getMainFlag() == 1)
                .findFirst();

        if (!departmentOptional.isPresent()) {
            throw new RuntimeException("请选择主责部门");
        }

        Date currentDate = new Date();

        WarningEventDepartment mainDepartment = departmentOptional.get();

        WarningEventDepartment lastDepartment = warningEventDepartmentMapper.getByEventAndProcessId(lastProcess.getId(), warningEvent.getId());

        WarningEvent updateEvent = new WarningEvent();
        updateEvent.setId(warningEvent.getId());
        updateEvent.setStatus(WarningEventStepEnum.STATUS_03.getCode());
        updateEvent.setDepartmentId(mainDepartment.getDepartmentId());
        updateEvent.setDepartmentName(mainDepartment.getDepartmentName());

        if (currentProcess.getExpectedCompleteTime().before(currentDate)) {
            updateEvent.setOverFlag(1);
        }
        updateEvent.setUpdateBy(verifyVO.getOperateUserId());

        warningEventMapper.updateWarningEvent(updateEvent);

        //生成流程
        WarningEventProcess process = createProcess(verifyVO, mainDepartment,
                WarningEventStepEnum.STATUS_03.getCode(), WarningEventHandleStatus.STATUS_00.getCode());

        //更新流程
        updateProcess(currentProcess, verifyVO, currentDate);

        //生成责任部门
        createDepartment(verifyVO, process,
                WarningEventStepEnum.STATUS_02.getCode());

        //生成附件
        createFileList(verifyVO, currentProcess);

        if (CollectionUtils.isNotEmpty(departmentList)) {
            List<String> accountIdList = departmentList.stream()
                    .map(WarningEventDepartment::getAccountId)
                    .collect(Collectors.toList());

            List<String> employeeMobileList = departmentList.stream()
                    .map(WarningEventDepartment::getEmployeeMobile)
                    .collect(Collectors.toList());

            String mobileUrl = getMobileUrl(warningEvent.getId(), verifyVO.getOperateUserId());

            sendDingTalkAndDingDingMsg(StringUtils.join(accountIdList, ","),
                    StringUtils.join(employeeMobileList, ","),
                    CommonConstant.WARNING_INFO_DTALK_VERFIVY_MSG,
                    "您有一条新的预警事件(" + warningEvent.getBizCode() + ")需要处置，请及时签收！",
                    getWarningContent(warningEvent, verifyVO.getReason()),
                    getWarningDingDingContent(warningEvent),
                    mobileUrl, singlePcUrl);

        }

//        if (Objects.equals(lastDepartment.getAccountId(), mainDepartment.getAccountId())) {
            //自动签收
        receiptV2(warningEvent.getId());
//        }

        return 1;
    }

    private void updateProcess(WarningEventProcess process, WarningEventVerifyVO verifyVO, Date currentDate) {
        WarningEventProcess updateProcess = new WarningEventProcess();
        updateProcess.setId(process.getId());
        updateProcess.setProcessContent(verifyVO.getReason());
        updateProcess.setProcessTime(new Date());
        updateProcess.setStatus(WarningEventHandleStatus.STATUS_03.getCode());
        if (process.getExpectedCompleteTime().before(currentDate)) {
            updateProcess.setOverFlag(1);
        }
        updateProcess.setUpdateBy(verifyVO.getOperateUserId());

        warningEventProcessMapper.updateByPrimaryKeySelective(updateProcess);
    }

    private WarningEventProcess checkProcess(Long id, Integer code) {
        WarningEventProcess existProcess = warningEventProcessMapper.getLastByEventId(id);
        if (Objects.nonNull(existProcess)
                && Objects.equals(code, existProcess.getProcessStep())) {
            throw new RuntimeException("流程已存在");
        }

        return existProcess;
    }

    @Override
    @Transactional
    public int hangUp(WarningEventVerifyVO verifyVO) {
        WarningEvent warningEvent = checkEvent(verifyVO.getId());
        WarningEventProcess warningEventProcess = warningEventProcessMapper.getLastByEventId(warningEvent.getId());

        WarningEvent exist = new WarningEvent();
        exist.setId(warningEvent.getId());
        exist.setStopFlag(1);
        exist.setUpdateBy(verifyVO.getOperateUserId());

        warningEventMapper.updateWarningEvent(exist);

        WarningEventProcess existProcess = new WarningEventProcess();
        existProcess.setId(warningEventProcess.getId());
        existProcess.setStopContent(verifyVO.getReason());
        existProcess.setStopStartTime(new Date());
        existProcess.setStatus(WarningEventHandleStatus.STATUS_02.getCode());
        existProcess.setUpdateBy(verifyVO.getOperateUserId());

        warningEventProcessMapper.updateByPrimaryKeySelective(existProcess);

        return 1;
    }

    @Override
    public int restart(WarningEventVerifyVO verifyVO) {
        WarningEvent warningEvent = checkEvent(verifyVO.getId());
        WarningEventProcess warningEventProcess = warningEventProcessMapper.getLastByEventId(warningEvent.getId());

        WarningEvent exist = new WarningEvent();
        exist.setId(warningEvent.getId());
        exist.setStopFlag(0);
        exist.setUpdateBy(verifyVO.getOperateUserId());

        warningEventMapper.updateWarningEvent(exist);

        WarningEventProcess existProcess = new WarningEventProcess();
        existProcess.setId(warningEventProcess.getId());
        existProcess.setStopEndTime(new Date());
        existProcess.setStatus(WarningEventHandleStatus.STATUS_00.getCode());
        existProcess.setExpectedCompleteTime(verifyVO.getExpectedCompleteTime());
        existProcess.setUpdateBy(verifyVO.getOperateUserId());

        warningEventProcessMapper.updateByPrimaryKeySelective(existProcess);

        return 1;
    }

    @Override
    public int handle(WarningEventVerifyVO verifyVO) {
        WarningEvent warningEvent = checkEvent(verifyVO.getId());
        WarningEventProcess warningEventProcess = checkProcessNotExist(warningEvent.getId(), WarningEventStepEnum.STATUS_04.getCode());

        Date currentDate = new Date();

        WarningEvent exist = new WarningEvent();
        exist.setId(warningEvent.getId());
        exist.setStatus(WarningEventStepEnum.STATUS_05.getCode());
        exist.setUpdateBy(verifyVO.getOperateUserId());
        exist.setOverFlag(0);
        if (warningEventProcess.getExpectedCompleteTime().before(currentDate)) {
            exist.setOverFlag(1);
        }

        warningEventMapper.updateWarningEvent(exist);

        //更新流程
        updateProcess(warningEventProcess, verifyVO, currentDate);

        //生成附件
        createFileList(verifyVO, warningEventProcess);

        //给部门加分
        exist.setDepartmentId(warningEvent.getDepartmentId());
        exist.setDepartmentName(warningEvent.getDepartmentName());
        addScore(exist);

        //141事件处置2.6  获取详情 取流程最后一条记录，其他信息取WarningEventDepartment 第一条数据
        CompletableFuture.runAsync(() -> {
            eventDisposeFeedback(warningEvent);
        });

        return 1;
    }

    /**
     * 事件处置
     * @param event
     */
    private void eventDispose(final WarningEvent event) {
        //发送事件协同数据
        Map<String, Object> params = new HashMap<>();
        params.put("appCode", APPCODE);
        Map<String, Object> bizContent = new HashMap<>();
        bizContent.put("eventNumber", event.getBizCode());
        bizContent.put("appCode", "CW81YLHNHOPHJDWDN");
        bizContent.put("appName", "三民工程");

        //获取详情 取流程最后一条记录，其他信息取WarningEventDepartment 第一条数据
        if (event.getProcessList() != null && !event.getProcessList().isEmpty()) {
            //取出最后一条记录
            WarningEventProcess process = event.getProcessList().get(event.getProcessList().size() - 1);
            if (process.getDepartmentList()!=null&&!process.getDepartmentList().isEmpty()){
                //取出第一条记录
                WarningEventDepartment department = process.getDepartmentList().get(0);

                bizContent.put("disposeName", department.getEmployeeName());
                bizContent.put("disposePhone", department.getEmployeeMobile());
                bizContent.put("disposeDepartCode", process.getDepartmentId());
                bizContent.put("disposeDepartName", process.getDepartmentName());
                bizContent.put("opinion", process.getProcessContent());
            }
        } else {
            bizContent.put("disposeName", "推送周报");
            bizContent.put("disposePhone", "13283826185");
            bizContent.put("disposeDepartCode", "1");
            bizContent.put("disposeDepartName", "推送周报");
            bizContent.put("opinion", "推送周报");
        }

        params.put("bizContent", bizContent);
        //将params 转换成json字符串
        String json = JSON.toJSONString(params);
        log.info("EventReport, eventDispose, eventNumber:{}, json:{}", event.getBizCode(), json);
        HttpUtil httpUtil = HttpUtil.getInstance();
        String response = httpUtil.sendHttpPostJSON("http://10.27.171.78:8080/API_16462822068718763/sjzx/event/synch/handle", params);
        log.info("EventReport, eventDispose, eventNumber:{}, response:{}", event.getBizCode(), response);

        Map map = com.alibaba.fastjson.JSON.parseObject(response, Map.class);

        log.info("EventReport, eventDispose, eventNumber:{}, map:{}", event.getBizCode(), JSON.toJSONString(map));

    }

    /**
     * 事件处置反馈
     * @param warningEvent
     */
    private void eventDisposeFeedback(final WarningEvent warningEvent) {
        WarningEvent event = warningEventService.selectWarningEventById(warningEvent.getId(), null);
        //获取详情 取流程最后一条记录，其他信息取WarningEventDepartment 第一条数据
        if (event.getProcessList() != null && !event.getProcessList().isEmpty()) {
            //取出最后一条记录
            WarningEventProcess process = event.getProcessList().get(event.getProcessList().size() - 1);
            if (process.getDepartmentList()!=null&&!process.getDepartmentList().isEmpty()){
                //取出第一条记录
                WarningEventDepartment department = process.getDepartmentList().get(0);
                //发送事件协同数据
                Map<String, Object> params = new HashMap<>();
                params.put("appCode", APPCODE);

                Map<String, Object> bizContent = new HashMap<>();
                bizContent.put("eventNumber", event.getBizCode());
                bizContent.put("state", 50);
                bizContent.put("userId",  StringUtils.isNotBlank(department.getAccountId())
                        ? department.getAccountId() : department.getEmployeeMobile());
                bizContent.put("userName", department.getEmployeeName());
                bizContent.put("mobilePhone", department.getEmployeeMobile());
                bizContent.put("organizationCode", department.getOrganizationCode());
                bizContent.put("organizationName", department.getDepartmentName());
                bizContent.put("suggestion", process.getProcessContent());
                bizContent.put("processDate", DateUtils.formatDateSecond(process.getProcessTime()));

                params.put("bizContent", bizContent);
                //将params 转换成json字符串
                String json = JSON.toJSONString(params);
                log.info("json:{}", json);
                HttpUtil httpUtil = HttpUtil.getInstance();
                String response = httpUtil.sendHttpPostJSON("http://10.27.171.78:8080/API_16462823166699470/sjzx/event/synch/feedback", params);
                log.info("EventReport, eventDisposeFeedback, eventNumber:{}, response:{}", event.getBizCode(), response);

                Map map = com.alibaba.fastjson.JSON.parseObject(response, Map.class);

                log.info("EventReport, eventDisposeFeedback, eventNumber:{}, map:{}", event.getBizCode(), JSON.toJSONString(map));
            }
        }

    }

    private void addScore(WarningEvent exist) {
        int score = 10;

        if (exist.getOverFlag() == 1) {
            score = 6;
        }

        warningEventDepartmentMapper.addScore(exist.getDepartmentId(), score);
    }

//    @Override
//    @Transactional
//    public int receipt(WarningEventVerifyVO verifyVO) {
//        WarningEvent warningEvent = checkEvent(verifyVO.getId());
//        WarningEventProcess warningEventProcess = checkProcess(verifyVO.getId(), WarningEventStepEnum.STATUS_03.getCode());
//
//        Date currentDate = new Date();
//
//        WarningEvent updateEvent = new WarningEvent();
//        updateEvent.setId(warningEvent.getId());
//        updateEvent.setStatus(WarningEventStepEnum.STATUS_03.getCode());
//        if (warningEventProcess.getExpectedCompleteTime().before(currentDate)) {
//            updateEvent.setOverFlag(1);
//        }
//        updateEvent.setUpdateBy(verifyVO.getOperateUserId());
//
//        warningEventMapper.updateWarningEvent(updateEvent);
//
//        WarningEventProcess existProcess = createProcess(verifyVO,
//                warningEvent.getDepartmentId(), warningEvent.getDepartmentName(),
//                WarningEventStepEnum.STATUS_03.getCode(), WarningEventHandleStatus.STATUS_00.getCode());
//
//        updateProcess(warningEventProcess, verifyVO, currentDate);
//
//        return 1;
//    }

    @Override
    public int copy(WarningCopyVO copyVO) {
        WarningEvent warningEvent = checkEvent(copyVO.getId());
        WarningEventProcess warningEventProcess = checkProcessNotExist(copyVO.getId(), warningEvent.getStatus());
        WarningEventSupervise existSupervise = checkSupervise(copyVO.getId());

        WarningEvent updateEvent = new WarningEvent();
        updateEvent.setId(warningEvent.getId());
//        updateEvent.setLeaderContent(copyVO.getContent());
        updateEvent.setSuperviseFlag(1);
        updateEvent.setSuperviseType(WarningSuperviseEnum.SUPERVISE_1.getCode() + "");
        updateEvent.setUpdateBy(copyVO.getOperateUserId());

        warningEventMapper.updateWarningEvent(updateEvent);

        WarningEventSupervise warningEventSupervise = new WarningEventSupervise();
        warningEventSupervise.setEventId(copyVO.getId());
        warningEventSupervise.setSuperviseType(WarningSuperviseEnum.SUPERVISE_1.getCode() + "");
        warningEventSupervise.setSuperviseContent(copyVO.getContent());
        warningEventSupervise.setSuperviseDepartmentId(copyVO.getDepartmentId());
        warningEventSupervise.setSuperviseDepartmentName(copyVO.getDepartmentName());
        warningEventSupervise.setSuperviseUserId(copyVO.getUserId());
        warningEventSupervise.setSuperviseUserName(copyVO.getEmployeeName());
        warningEventSupervise.setSuperviseUserAccountId(copyVO.getAccountId());
        warningEventSupervise.setSuperviceEmployeeCode(copyVO.getEmployeeCode());
//        warningEventSupervise.setSupervisePushUserName(copyVO.getEmployeeName());
//        warningEventSupervise.setSuperviseAccountId(copyVO.getAccountId());
//        warningEventSupervise.setSuperviceEmployeeCode(copyVO.getEmployeeCode());

        warningEventSuperviseMapper.insertSelective(warningEventSupervise);

        String mobileUrl = getMobileUrl(warningEvent.getId(), copyVO.getOperateUserId());

        String copyContent = getCopyContent(WarningSuperviseEnum.SUPERVISE_1, warningEvent, copyVO.getContent());
        Map<String, String> dingdingContent = getCopyDingDingContent(WarningSuperviseEnum.SUPERVISE_1, warningEvent, copyVO.getContent());

        //发送浙政钉通知
        sendDingTalkAndDingDingMsg(copyVO.getAccountId(), copyVO.getEmployeeMobile(),
                CommonConstant.SUPERVISE_DTALK_NOTIFY_ID,
                "您有一条新的事件抄送(" + warningEvent.getBizCode() + ")",
                copyContent,
                dingdingContent,
                mobileUrl, singlePcUrl);
//        userDtalkService.workNotification(copyVO.getAccountId(),
//                CommonConstant.SUPERVISE_DTALK_NOTIFY_ID + warningEventSupervise.getId(),
//                "您有一条新的事件抄送",
//                getCopyContent(WarningSuperviseEnum.SUPERVISE_1, warningEvent, copyVO.getContent()),
//                mobileUrl, singlePcUrl);

        return 1;
    }

    private Map<String, String> getCopyDingDingContent(WarningSuperviseEnum supervise, WarningEvent warningEvent,
                                                       String content) {
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("事件名称", warningEvent.getEventName());
        resultMap.put("督办类型", supervise.getDesc());
        resultMap.put("情况说明", content);

        return resultMap;
    }

    private WarningEventSupervise checkSupervise(Long id) {
        WarningEventSupervise existWarningEvent = warningEventSuperviseMapper.getByEventId(id);

        if (Objects.nonNull(existWarningEvent)) {
            throw new RuntimeException("请勿重复抄送");
        }

        return existWarningEvent;
    }

    @Override
    public List<WarningSuperviseVO> listSupervise(WarningEventPageVO pageVO) {
        List<WarningSuperviseVO> superviseVOList = warningEventMapper.listSupervise(pageVO);

        if (CollectionUtils.isNotEmpty(superviseVOList)) {
            List<Long> eventIdList = superviseVOList.stream()
                    .map(WarningSuperviseVO::getId)
                    .collect(Collectors.toList());

            //添加流程部门
            List<WarningEventDepartment> departmentList = warningEventDepartmentMapper.listByEventIdList(eventIdList);
            if (CollectionUtils.isNotEmpty(departmentList)) {
                Map<Long, List<WarningEventDepartment>> departmentMap = departmentList.stream()
                        .collect(Collectors.groupingBy(WarningEventDepartment::getEventId));
                for (WarningSuperviseVO warningSuperviseVO : superviseVOList) {
                    List<WarningEventDepartment> departmentListByEventId = departmentMap.get(warningSuperviseVO.getId());
                    if (CollectionUtils.isNotEmpty(departmentListByEventId)) {
                        List<WarningEventDepartment> eventDepartmentList = departmentListByEventId.stream()
                                .filter(department -> Objects.equals(department.getEventStatus(), WarningEventStepEnum.STATUS_02.getCode()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(eventDepartmentList)) {
                            Optional<WarningEventDepartment> optionalDepartment = eventDepartmentList.stream()
                                    .filter(department -> department.getMainFlag() == 1)
                                    .findFirst();

                            if (optionalDepartment.isPresent()) {
                                WarningEventDepartment mainDepartment = optionalDepartment.get();

                                warningSuperviseVO.setDepartmentOrganizationCode(mainDepartment.getOrganizationCode());
                            }
                        } else {
                            Optional<WarningEventDepartment> optionalDepartment = departmentListByEventId.stream()
                                    .filter(department -> department.getMainFlag() == 1)
                                    .findFirst();

                            if (optionalDepartment.isPresent()) {
                                WarningEventDepartment mainDepartment = optionalDepartment.get();

                                warningSuperviseVO.setDepartmentOrganizationCode(mainDepartment.getOrganizationCode());
                            }
                        }
                    }
                }
            }
        }

        return superviseVOList;
    }

    @Override
    public void superviseWorkNotify(List<Long> updateEventIdList, WarningSuperviseEnum warningSuperviseEnum) {
        List<WarningEventDepartment> departmentList = warningEventDepartmentMapper.listByEventIdList(updateEventIdList);

        Map<Long, List<WarningEventDepartment>> departmentMap = departmentList.stream()
                .collect(Collectors.groupingBy(WarningEventDepartment::getEventId));

        List<WarningEventSupervise> superviseList = Lists.newArrayList();
        List<Long> eventIdUpdateList = Lists.newArrayList();

        for (Long eventId : updateEventIdList) {
            List<WarningEventDepartment> departmentOneList = departmentMap.get(eventId);

            if (CollectionUtils.isNotEmpty(departmentOneList)) {
                WarningEventDepartment existDepartment = departmentOneList.get(0);

                List<String> needNodifyList = Lists.newArrayList(existDepartment.getAccountId());
                List<String> needNodifyMobileList = Lists.newArrayList(existDepartment.getEmployeeMobile());

                //获取分管领导
                WarningDepartmentInfo departmentInfo = warningDepartmentInfoMapper.selectByPrimaryKey(existDepartment.getDepartmentId());

                String pushAccountId = existDepartment.getAccountId();
                String pushEmployeeCode = existDepartment.getEmployeeCode();
                String pushUserName = existDepartment.getEmployeeName();

                if (Objects.nonNull(departmentInfo)) {
                    if (warningSuperviseEnum == WarningSuperviseEnum.SUPERVISE_2) {
                        pushAccountId = departmentInfo.getLeaderAccountId();
                        pushEmployeeCode = departmentInfo.getLeaderEmployeeCode();
                        pushUserName = departmentInfo.getLeaderName();
                    } else if (warningSuperviseEnum == WarningSuperviseEnum.SUPERVISE_3) {
                        pushAccountId = departmentInfo.getMessengerAccountId();
                        pushEmployeeCode = departmentInfo.getMessengerEmployeeCode();
                        pushUserName = departmentInfo.getMessengerName();
                    }
                }

                if (StringUtils.isNotBlank(pushAccountId)) {
                    needNodifyList.add(pushAccountId);
                }

                WarningEventSupervise warningEventSupervise = warningEventSuperviseMapper.getByEventId(eventId);
                if (Objects.isNull(warningEventSupervise)) {
                    //生成督办
                    WarningEventSupervise existSupervise = new WarningEventSupervise();
                    existSupervise.setEventId(eventId);
                    existSupervise.setSuperviseType(warningSuperviseEnum.getCode() + "");
                    existSupervise.setSuperviseUserEmployeeCode(pushEmployeeCode);
                    existSupervise.setSuperviseUserAccountId(pushAccountId);
                    existSupervise.setSuperviseUserName(pushUserName);
                    existSupervise.setSuperviseContent(warningSuperviseEnum.getDesc());
                    existSupervise.setSuperviseAccountId(existDepartment.getAccountId());
                    existSupervise.setSuperviceEmployeeCode(existDepartment.getEmployeeCode());
                    existSupervise.setSupervisePushUserName(existDepartment.getEmployeeName());
                    existSupervise.setCreateBy("SYSTEM");

                    superviseList.add(existSupervise);

                    eventIdUpdateList.add(eventId);
                } else {
                    if (warningSuperviseEnum == WarningSuperviseEnum.SUPERVISE_3) {
                        throw new RuntimeException("事件已被督办");
                    }
                }

//                } else {
//                    if (warningSuperviseEnum == WarningSuperviseEnum.SUPERVISE_3) {
//                        throw new RuntimeException("未找到对应人员，请到事件派单中进行手动处理");
//                    }
//                }

                WarningEvent warningEvent = warningEventMapper.selectWarningEventById(eventId);

                String mobileUrl = getMobileUrl(warningEvent.getId(), null);

                String content = getSuperviseContent(warningSuperviseEnum, warningEvent.getEventName(), null);
                Map<String, String> superviseDingDingMap = getSuperviseDingDingContent(warningSuperviseEnum, warningEvent.getEventName(), null);

                //发送消息
//                sendDingTalkAndDingDingMsg(StringUtils.join(needNodifyList, ","),
//                        StringUtils.join(needNodifyMobileList, ","),
//                        CommonConstant.SUPERVISE_DTALK_NOTIFY_ID,
//                        "您有一条新的督办意见",
//                        content,
//                        superviseDingDingMap,
//                        mobileUrl, singlePcUrl);
            }
        }

        if (CollectionUtils.isNotEmpty(superviseList)) {
            warningEventSuperviseMapper.batchInsert(superviseList);
        }
        if (CollectionUtils.isNotEmpty(eventIdUpdateList)) {
            warningEventMapper.batchUpdateSuperviseFlag(eventIdUpdateList, warningSuperviseEnum.getCode());
        }
    }

    private Map<String, String> getSuperviseDingDingContent(WarningSuperviseEnum warningSuperviseEnum,
                                                            String eventName, String situationContent) {
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("事件名称", eventName);
        resultMap.put("督办类型", warningSuperviseEnum.getDesc());
        if (warningSuperviseEnum == WarningSuperviseEnum.SUPERVISE_1) {
            resultMap.put("督办意见", situationContent);
        }

        return resultMap;
    }

    @Override
    @Transactional
    public int supervise(WarningBatchSuperviseVO superviseVO) {
        List<WarningCopyVO> employeeList = superviseVO.getEmployeeList();
        if (CollectionUtils.isEmpty(employeeList)) {
            return 1;
        }

        List<Long> employeeIdList = employeeList.stream()
                .map(WarningCopyVO::getId)
                .collect(Collectors.toList());

        warningEventMapper.batchUpdateLeaderContent(employeeIdList, superviseVO.getContent());

        //更新提醒人员
        List<WarningEventSupervise> superviseList = Lists.newArrayList();

        for (WarningCopyVO warningCopyVO : employeeList) {
            WarningEventSupervise warningEventSupervise = new WarningEventSupervise();
            warningEventSupervise.setEventId(warningCopyVO.getId());
            warningEventSupervise.setSuperviseDepartmentId(warningCopyVO.getDepartmentId());
            warningEventSupervise.setSuperviseDepartmentName(warningCopyVO.getDepartmentName());
            warningEventSupervise.setSuperviseUserId(warningCopyVO.getUserId());
            warningEventSupervise.setSuperviseUserAccountId(warningCopyVO.getAccountId());
            warningEventSupervise.setSuperviseUserEmployeeCode(warningCopyVO.getEmployeeCode());
            warningEventSupervise.setSuperviseUserName(warningCopyVO.getEmployeeName());
            superviseList.add(warningEventSupervise);
        }

        warningEventSuperviseMapper.updateBatchByEventId(superviseList);

        for (WarningCopyVO warningCopyVO : employeeList) {
            String mobileUrl = getMobileUrl(warningCopyVO.getId(), warningCopyVO.getOperateUserId());

            //发送消息
            sendDingTalkAndDingDingMsg(warningCopyVO.getAccountId(),
                    warningCopyVO.getEmployeeMobile(),
                    CommonConstant.SUPERVISE_DTALK_NOTIFY_ID,
                    "您有一条新的督办意见",
                    getSuperviseContent(WarningSuperviseEnum.SUPERVISE_1, warningCopyVO.getEmployeeName(), null),
                    getSuperviseDingDingContent(WarningSuperviseEnum.SUPERVISE_1, warningCopyVO.getEmployeeName(), null),
                    mobileUrl, singlePcUrl);
        }

        return 1;
    }

    private String getSuperviseContent(WarningSuperviseEnum warningSuperviseEnum, String eventName,
                                       String situationContent) {
        StringBuilder content = new StringBuilder();
        content.append("事件名称：" + eventName + " <br> ");
        content.append("督办类型：" + warningSuperviseEnum.getDesc());
        if (warningSuperviseEnum == WarningSuperviseEnum.SUPERVISE_1) {
            content.append("督办意见：" + situationContent);
        }

        return content.toString();
    }

    private String getCopyContent(WarningSuperviseEnum warningSuperviseEnum, WarningEvent warningEvent, String situationContent) {
        StringBuilder content = new StringBuilder();
        content.append("事件名称：" + warningEvent.getEventName() + "<br>");
        content.append("督办类型：" + warningSuperviseEnum.getDesc());
        content.append("情况说明：" + situationContent);

        return content.toString();
    }

    private WarningEvent checkEvent(Long id) {
        WarningEvent warningEvent = warningEventMapper.selectWarningEventById(id);
        if (Objects.isNull(warningEvent)) {
            throw new RuntimeException("事件不存在");
        }

        return warningEvent;
    }

    private void createFileList(WarningEventVerifyVO verifyVO, WarningEventProcess process) {
        List<BusinessFile> fileList = verifyVO.getFileList();
        if (CollectionUtils.isEmpty(fileList)) {
            return;
        }

        for (BusinessFile businessFile : fileList) {
            businessFile.setBusinessId(process.getId());
            businessFile.setBusinessType(FileTypeEnum.TYPE_03.getCode());
            businessFile.setCreateBy(verifyVO.getOperateUserId());
            businessFile.setCreateTime(new Date());
        }

        businessFileMapper.batchInsert(fileList);
    }

    private void createDepartment(WarningEventVerifyVO verifyVO, WarningEventProcess process,
                                  Integer step) {
        List<WarningEventDepartment> departmentList = verifyVO.getDepartmentList();

        if (CollectionUtils.isNotEmpty(departmentList)) {
            for (WarningEventDepartment warningEventDepartment : departmentList) {
                warningEventDepartment.setEventId(verifyVO.getId());
                warningEventDepartment.setProcessId(process.getId());
                warningEventDepartment.setEventStatus(step);
                warningEventDepartment.setCreateBy(verifyVO.getOperateUserId());
                warningEventDepartment.setCreateTime(new Date());
            }

            WarningEventDepartment mainDepartment = departmentList.stream()
                    .filter(warningEventDepartment -> warningEventDepartment.getMainFlag() == 1)
                    .findFirst()
                    .get();

            WarningEventDepartment nextDepartment = new WarningEventDepartment();

            BeanUtils.copyProperties(mainDepartment, nextDepartment);

            nextDepartment.setEventStatus(step + 1);

            departmentList.add(nextDepartment);
        }

        warningEventDepartmentMapper.batchInsert(departmentList);
    }

    private WarningEventProcess createProcess(WarningEventVerifyVO verifyVO, WarningEventDepartment mainDepartment,
                                              Integer step, Integer status) {
        Date currentDate = new Date();

        WarningEventProcess process = new WarningEventProcess();
        process.setEventId(verifyVO.getId());
        process.setUserId(mainDepartment.getAccountId());
        process.setUserName(mainDepartment.getEmployeeName());
        process.setDepartmentId(mainDepartment.getDepartmentId());
        process.setDepartmentName(mainDepartment.getDepartmentName());
        process.setProcessStep(step);
        process.setStatus(status);

        Date nextHour = DateUtils.getNextHour(currentDate);
        process.setExpectedCompleteTime(nextHour);
        process.setCreateTime(currentDate);
        process.setCreateBy(verifyVO.getOperateUserId());
//        process.setProcessContent(verifyVO.getReason());

        warningEventProcessMapper.insertSelective(process);

        return process;
    }
}
