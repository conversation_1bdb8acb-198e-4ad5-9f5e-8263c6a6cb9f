package com.ruoyi.jiangshan.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.jiangshan.domain.BusinessFile;
import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.enums.FileTypeEnum;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.BusinessFileMapper;
import com.ruoyi.jiangshan.vo.DeviceMonitorVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.jiangshan.mapper.DeviceMonitorMapper;
import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.service.IDeviceMonitorService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 设备监测对象Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class DeviceMonitorServiceImpl implements IDeviceMonitorService
{
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Autowired
    private BusinessFileMapper businessFileMapper;

    /**
     * 查询设备监测对象
     *
     * @param id 设备监测对象主键
     * @return 设备监测对象
     */
    @Override
    public DeviceMonitor selectDeviceMonitorById(Long id)
    {
        DeviceMonitor deviceMonitor = deviceMonitorMapper.selectDeviceMonitorById(id);

        fillPictureAndAttachment(Lists.newArrayList(deviceMonitor));

        return deviceMonitor;
    }

    /**
     * 查询设备监测对象列表
     *
     * @param deviceMonitor 设备监测对象
     * @return 设备监测对象
     */
    @Override
    public List<DeviceMonitor> selectDeviceMonitorList(DeviceMonitor deviceMonitor)
    {
        return deviceMonitorMapper.selectDeviceMonitorList(deviceMonitor);
    }

    /**
     * 新增设备监测对象
     *
     * @param deviceMonitor 设备监测对象
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDeviceMonitor(DeviceMonitor deviceMonitor)
    {
        Date createTime = DateUtils.getNowDate();
        String createBy = SecurityUtils.getUserId() + "";

        deviceMonitor.setCreateTime(createTime);
        deviceMonitor.setCreateBy(createBy);

        deviceMonitorMapper.insertDeviceMonitor(deviceMonitor);

        savePictureAndAttachment(deviceMonitor, createTime, createBy);

        return 1;
    }

    private void savePictureAndAttachment(DeviceMonitor deviceMonitor, Date createTime, String createBy) {
        List<BusinessFile> pictureList = deviceMonitor.getPictureList();
        if (CollectionUtils.isNotEmpty(pictureList)) {
            for (BusinessFile businessFile : pictureList) {
                businessFile.setBusinessId(deviceMonitor.getId());
                businessFile.setBusinessType(FileTypeEnum.TYPE_07.getCode());
                businessFile.setCreateTime(createTime);
                businessFile.setCreateBy(createBy);
            }

            businessFileMapper.updateBatch(pictureList);
        }

        List<BusinessFile> attachmentList = deviceMonitor.getAttachmentList();
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            for (BusinessFile businessFile : attachmentList) {
                businessFile.setBusinessId(deviceMonitor.getId());
                businessFile.setBusinessType(FileTypeEnum.TYPE_08.getCode());
                businessFile.setCreateTime(createTime);
                businessFile.setCreateBy(createBy);
            }

            businessFileMapper.updateBatch(attachmentList);
        }
    }

    /**
     * 修改设备监测对象
     *
     * @param deviceMonitor 设备监测对象
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDeviceMonitor(DeviceMonitor deviceMonitor)
    {
        Date updateTime = DateUtils.getNowDate();
        String updateBy = SecurityUtils.getUserId() + "";

        deviceMonitor.setUpdateTime(updateTime);
        deviceMonitor.setUpdateBy(updateBy);

        deviceMonitorMapper.updateDeviceMonitor(deviceMonitor);

        //删除
        deletePictureAndAttachment(deviceMonitor.getId());

        savePictureAndAttachment(deviceMonitor, updateTime, updateBy);

        return 1;
    }

    private void deletePictureAndAttachment(Long id) {
        businessFileMapper.deleteByBusinessIdAndTypeList(id, Lists.newArrayList(FileTypeEnum.TYPE_07.getCode(), FileTypeEnum.TYPE_08.getCode()));
    }

    /**
     * 批量删除设备监测对象
     *
     * @param ids 需要删除的设备监测对象主键
     * @return 结果
     */
    @Override
    public int deleteDeviceMonitorByIds(Long[] ids)
    {
        return deviceMonitorMapper.deleteDeviceMonitorByIds(ids);
    }

    /**
     * 删除设备监测对象信息
     *
     * @param id 设备监测对象主键
     * @return 结果
     */
    @Override
    public int deleteDeviceMonitorById(Long id)
    {
        return deviceMonitorMapper.deleteDeviceMonitorById(id);
    }

    @Override
    public List<DeviceMonitor> getMonitor(String type) {
        List<DeviceMonitor> monitorList = deviceMonitorMapper.listByType(Lists.newArrayList(type));

        if (CollectionUtils.isEmpty(monitorList)) {
            return Lists.newArrayList();
        }

        Map<String, List<DeviceMonitor>> existMap = monitorList.stream()
                .collect(Collectors.groupingBy(DeviceMonitor::getMonitorType));

        List<DeviceMonitor> resultList = existMap.get(type);

        List<Long> valueIdList = resultList.stream()
                .map(DeviceMonitor::getId)
                .collect(Collectors.toList());

        List<BusinessFile> fileList =
                businessFileMapper.listByBusinessIdListAndType(valueIdList, FileTypeEnum.TYPE_07.getCode());

        Map<Long, List<BusinessFile>> fileMap = fileList.stream()
                .collect(Collectors.groupingBy(BusinessFile::getBusinessId));

        for (DeviceMonitor value : resultList) {
            List<BusinessFile> fileOneList = fileMap.get(value.getId());

            if (CollectionUtils.isNotEmpty(fileOneList)) {
                value.setFileUrl(fileOneList.get(0).getFileUrl());
            }
        }

        return resultList;
    }

    @Override
    public List<DeviceMonitorVO> listByScene(String keyword, String monitorScene) {
        List<DeviceMonitorVO> monitorVOList = Lists.newArrayList();

        List<DeviceMonitor> monitorList = deviceMonitorMapper.listAll(keyword, monitorScene, null);

        Map<String, List<DeviceMonitor>> monitorMap = monitorList.stream()
                .collect(Collectors.groupingBy(DeviceMonitor::getMonitorType));

        for (Map.Entry<String, List<DeviceMonitor>> entry : monitorMap.entrySet()) {
            DeviceMonitorVO deviceMonitorVO = new DeviceMonitorVO();
            deviceMonitorVO.setMonitorType(entry.getKey());
            deviceMonitorVO.setDeviceMonitorList(entry.getValue());

            monitorVOList.add(deviceMonitorVO);
        }

        fillPictureAndAttachment(monitorList);

        return monitorVOList;
    }

    @Override
    public List<DeviceMonitor> listAll() {
        return deviceMonitorMapper.listAll(null, null, null);
    }

    @Override
    public void excelImport(List<DeviceMonitor> list) {
        Date createTime = new Date();
        String createBy = SecurityUtils.getUserId() + "";

        for (DeviceMonitor deviceMonitor : list) {
            String monitorScene = deviceMonitor.getMonitorScene();

            MonitorSceneType sceneType = MonitorSceneType.getByDesc(monitorScene);
            if (Objects.isNull(sceneType)) {
                throw new RuntimeException("监测场景填写错误");
            }

            deviceMonitor.setCreateTime(createTime);
            deviceMonitor.setCreateBy(createBy);
        }

        deviceMonitorMapper.batchInsert(list);
    }

    private void fillPictureAndAttachment(List<DeviceMonitor> monitorList) {
        //添加图片和附件
        if (CollectionUtils.isNotEmpty(monitorList)) {
            List<Long> monitorIdList = monitorList.stream()
                    .map(DeviceMonitor::getId)
                    .collect(Collectors.toList());

            List<BusinessFile> businessFileList = businessFileMapper.listByBusinessIdListAndTypeList(monitorIdList,
                    Lists.newArrayList(FileTypeEnum.TYPE_07.getCode(), FileTypeEnum.TYPE_08.getCode()));
            if (CollectionUtils.isNotEmpty(businessFileList)) {
                Map<Long, List<BusinessFile>> businessFileMap = businessFileList.stream()
                        .collect(Collectors.groupingBy(BusinessFile::getBusinessId));

                for (DeviceMonitor deviceMonitor : monitorList) {
                    List<BusinessFile> businessFileOneList = businessFileMap.get(deviceMonitor.getId());
                    if (CollectionUtils.isEmpty(businessFileOneList)) {
                        continue;
                    }

                    Map<Integer, List<BusinessFile>> businessFileOneMap= businessFileOneList.stream()
                            .collect(Collectors.groupingBy(BusinessFile::getBusinessType));

                    List<BusinessFile> businessFile07List = businessFileOneMap.get(FileTypeEnum.TYPE_07.getCode());
                    List<BusinessFile> businessFile08List = businessFileOneMap.get(FileTypeEnum.TYPE_08.getCode());

                    if (CollectionUtils.isNotEmpty(businessFile07List)) {
                        deviceMonitor.setPictureList(businessFile07List);
                        deviceMonitor.setFileUrl(businessFile07List.get(0).getFileUrl());
                    }
                    if (CollectionUtils.isNotEmpty(businessFile08List)) {
                        deviceMonitor.setAttachmentList(businessFile08List);
                    }
                }
            }
        }
    }
}
