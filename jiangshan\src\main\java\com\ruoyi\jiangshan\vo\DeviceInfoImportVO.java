package com.ruoyi.jiangshan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.jiangshan.domain.DeviceValue;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DeviceInfoImportVO {

    /** 设备第三方id */
    @Excel(name = "设备编号")
    private String deviceThirdId;

//    /** 设备编号 */
//    @Excel(name = "设备编号")
//    private String deviceNum;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 设备类型 */
    @Excel(name = "设备类型")
    private String deviceType;

    /** 设备场景 */
    @Excel(name = "设备场景")
    private String deviceScene;

    /** 设备地址 */
    @Excel(name = "设备地址")
    private String deviceAddress;

//    /** 设备部门id */
//    @Excel(name = "设备部门id")
//    private Long deviceDepartmentId;

    /** 设备部门name */
    @Excel(name = "设备所属部门")
    private String deviceDepartmentName;

    /** 设备状态  0下线 1上线 2维护 6拆除 */
    @Excel(name = "设备状态", readConverterExp = "0=下线,1=上线,2=维护,6=拆除")
    private Integer deviceStatus;

//    /** 监测对象id */
//    @Excel(name = "监测对象Id")
//    private Long monitorId;

    /** 监测对象name */
    @Excel(name = "监测对象")
    private String monitorName;

    /** 设备图片url */
    @Excel(name = "设备图片URL")
    private String fileUrl;

    /** 经度 */
    @Excel(name = "经度")
    private String lon;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    @Excel(name = "设备所属行政区划")
    private String deviceStreet;

    @Excel(name = "设备所属行政区划编码")
    private String deviceStreetAreaCode;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "拓展字段1")
    private String field01;

    @Excel(name = "拓展字段2")
    private String field02;

    @Excel(name = "拓展字段3")
    private String field03;

    @Excel(name = "拓展字段4")
    private String field04;

    @Excel(name = "拓展字段5")
    private String field05;
}
