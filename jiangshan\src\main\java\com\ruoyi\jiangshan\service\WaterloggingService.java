package com.ruoyi.jiangshan.service;

import com.ruoyi.jiangshan.vo.*;

import java.util.List;
import java.util.Map;

public interface WaterloggingService {

    WarningCountVO getSafetyCount(String dateStr);

    List<FloorCountVO> getFloodCount();

    List<WarningInfoVO> getWarning(Integer warningLevel);

    Map<String, List<BusinessCountVO>> getRainfallCount(Long monitorId);

    DeviceOnlineStatusVO getOnlineStatus();

}
