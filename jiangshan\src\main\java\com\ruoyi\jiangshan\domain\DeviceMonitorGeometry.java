package com.ruoyi.jiangshan.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 空间范围坐标对象 t_device_monitor_geometry
 * 
 * <AUTHOR>
 * @date 2025-03-27
 */
public class DeviceMonitorGeometry extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String monitorName;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String geometry;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String type;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setMonitorName(String monitorName) 
    {
        this.monitorName = monitorName;
    }

    public String getMonitorName() 
    {
        return monitorName;
    }
    public void setGeometry(String geometry) 
    {
        this.geometry = geometry;
    }

    public String getGeometry() 
    {
        return geometry;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("monitorName", getMonitorName())
            .append("geometry", getGeometry())
            .append("type", getType())
            .toString();
    }
}
