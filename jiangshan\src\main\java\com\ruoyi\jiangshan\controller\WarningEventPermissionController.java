package com.ruoyi.jiangshan.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.enums.MonitorSceneType;
import com.ruoyi.jiangshan.mapper.WarningEventSceneDepartmentMapper;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.vo.WarningEventPageVO;
import com.ruoyi.system.mapper.SysMenuMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 设备预警事件Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/warning-event")
public class WarningEventPermissionController extends BaseController {
    @Autowired
    private IWarningEventService warningEventService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysMenuMapper sysMenuMapper;
    @Autowired
    private WarningEventSceneDepartmentMapper warningEventSceneDepartmentMapper;

    /**
     * 查询设备预警事件列表
     */
//    @PreAuthorize("@ss.hasPermi('warningEvent:warningEvent:list')")
    @GetMapping("/list")
    public TableDataInfo list(WarningEventPageVO pageVO)
    {
        fillEmployeeCode(pageVO);

        startPage();
        List<WarningEvent> list = warningEventService.selectWarningEventList(pageVO);
        return getDataTable(list);
    }

    private void fillEmployeeCode(WarningEventPageVO pageVO) {
        Long userId = SecurityUtils.getUserId();

        SysUser sysUser = sysUserMapper.selectUserById(userId);

        //根据用户获得拥有的场景权限
        if (Objects.nonNull(sysUser)) {
            List<String> sceneList = MonitorSceneType.getScenePermissionByUser(sysUser, warningEventSceneDepartmentMapper);

            pageVO.setSceneList(sceneList);

            pageVO.setEmployeeCode(sysUser.getEmployeeCode());
            pageVO.setUserId(sysUser.getUserId());
        }
    }

}
