<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningRuleConditionMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningRuleCondition">
    <!--@mbg.generated-->
    <!--@Table t_warning_rule_condition-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="device_id" jdbcType="BIGINT" property="deviceId" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="device_third_id" jdbcType="VARCHAR" property="deviceThirdId" />
    <result column="rule_condition" jdbcType="VARCHAR" property="ruleCondition" />
    <result column="monitor_item" jdbcType="VARCHAR" property="monitorItem" />
    <result column="monitor_item_english" jdbcType="VARCHAR" property="monitorItemEnglish" />
    <result column="monitor_value" jdbcType="VARCHAR" property="monitorValue" />
    <result column="monitor_unit" jdbcType="VARCHAR" property="monitorUnit" />
    <result column="warning_level" jdbcType="BOOLEAN" property="warningLevel" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="warning_user_id" jdbcType="VARCHAR" property="warningUserId" />
    <result column="warning_user_name" jdbcType="VARCHAR" property="warningUserName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, rule_id, device_id, device_name, device_third_id, rule_condition, monitor_item, monitor_item_english,
    monitor_value, monitor_unit, warning_level, create_time, create_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_rule_condition
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_rule_condition
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningRuleCondition" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_rule_condition (rule_id, device_id, device_name,
      device_third_id, rule_condition, monitor_item, monitor_item_english,
      monitor_value, monitor_unit, warning_level, create_time,
      create_by)
    values (#{ruleId,jdbcType=BIGINT}, #{deviceId,jdbcType=VARCHAR}, #{deviceName,jdbcType=VARCHAR},
      #{deviceThirdId,jdbcType=VARCHAR}, #{ruleCondition,jdbcType=VARCHAR}, #{monitorItem,jdbcType=VARCHAR},
      #{monitorItemEnglish,jdbcType=VARCHAR}, #{monitorValue,jdbcType=VARCHAR}, #{monitorUnit,jdbcType=VARCHAR}
      #{warningLevel,jdbcType=BOOLEAN}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningRuleCondition" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_rule_condition
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="deviceName != null">
        device_name,
      </if>
      <if test="deviceThirdId != null">
        device_third_id,
      </if>
      <if test="ruleCondition != null">
        rule_condition,
      </if>
      <if test="monitorItem != null">
        monitor_item,
      </if>
      <if test="monitorItemEnglish != null">
        monitor_item_english,
      </if>
      <if test="monitorValue != null">
        monitor_value,
      </if>
      <if test="monitorUnit != null">
        monitor_unit,
      </if>
      <if test="warningLevel != null">
        warning_level,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="deviceName != null">
        #{deviceName,jdbcType=VARCHAR},
      </if>
      <if test="deviceThirdId != null">
        #{deviceThirdId,jdbcType=VARCHAR},
      </if>
      <if test="ruleCondition != null">
        #{ruleCondition,jdbcType=VARCHAR},
      </if>
      <if test="monitorItem != null">
        #{monitorItem,jdbcType=VARCHAR},
      </if>
      <if test="monitorItemEnglish != null">
        #{monitorItemEnglish,jdbcType=VARCHAR},
      </if>
      <if test="monitorValue != null">
        #{monitorValue,jdbcType=VARCHAR},
      </if>
      <if test="monitorUnit != null">
        #{monitorUnit,jdbcType=VARCHAR},
      </if>
      <if test="warningLevel != null">
        #{warningLevel,jdbcType=BOOLEAN},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningRuleCondition">
    <!--@mbg.generated-->
    update t_warning_rule_condition
    <set>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="deviceName != null">
        device_name = #{deviceName,jdbcType=VARCHAR},
      </if>
      <if test="deviceThirdId != null">
        device_third_id = #{deviceThirdId,jdbcType=VARCHAR},
      </if>
      <if test="ruleCondition != null">
        rule_condition = #{ruleCondition,jdbcType=VARCHAR},
      </if>
      <if test="monitorItem != null">
        monitor_item = #{monitorItem,jdbcType=VARCHAR},
      </if>
      <if test="monitorItemEnglish != null">
        monitor_item_english = #{monitorItemEnglish,jdbcType=VARCHAR},
      </if>
      <if test="monitorValue != null">
        monitor_value = #{monitorValue,jdbcType=VARCHAR},
      </if>
      <if test="monitorUnit != null">
        monitor_unit = #{monitorUnit,jdbcType=VARCHAR},
      </if>
      <if test="warningLevel != null">
        warning_level = #{warningLevel,jdbcType=BOOLEAN},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningRuleCondition">
    <!--@mbg.generated-->
    update t_warning_rule_condition
    set rule_id = #{ruleId,jdbcType=BIGINT},
      device_id = #{deviceId,jdbcType=VARCHAR},
      device_name = #{deviceName,jdbcType=VARCHAR},
      device_third_id = #{deviceThirdId,jdbcType=VARCHAR},
      rule_condition = #{ruleCondition,jdbcType=VARCHAR},
      monitor_item = #{monitorItem,jdbcType=VARCHAR},
      monitor_item_english = #{monitorItemEnglish,jdbcType=VARCHAR},
      monitor_value = #{monitorValue,jdbcType=VARCHAR},
      monitor_unit = #{monitorUnit,jdbcType=VARCHAR},
      warning_level = #{warningLevel,jdbcType=BOOLEAN},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_rule_condition
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="rule_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ruleId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="device_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="device_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="device_third_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceThirdId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="rule_condition = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ruleCondition,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="monitor_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorItem,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="monitor_item_english = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorItemEnglish,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="monitor_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorValue,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="monitor_unit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorUnit,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="warning_level = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.warningLevel,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_rule_condition
    (rule_id, device_id, device_name, device_third_id, rule_condition, monitor_item,
      monitor_item_english, monitor_value, monitor_unit, warning_level, create_time, create_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.ruleId,jdbcType=BIGINT}, #{item.deviceId,jdbcType=VARCHAR}, #{item.deviceName,jdbcType=VARCHAR},
        #{item.deviceThirdId,jdbcType=VARCHAR}, #{item.ruleCondition,jdbcType=VARCHAR},
        #{item.monitorItem,jdbcType=VARCHAR}, #{item.monitorItemEnglish,jdbcType=VARCHAR},
        #{item.monitorValue,jdbcType=VARCHAR}, #{item.monitorUnit,jdbcType=VARCHAR},
        #{item.warningLevel,jdbcType=BOOLEAN}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.createBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="listByMonitorItemAndDevice" resultMap="BaseResultMap">
    select t1.id, rule_id, device_id, device_name, device_third_id, rule_condition, monitor_item, monitor_item_english,
    monitor_value, monitor_unit, warning_level, t1.create_time, t1.create_by, t2.warning_user_id, t2.warning_user_name
    from t_warning_rule_condition t1
    left join t_warning_rule t2 on t1.rule_id = t2.id
    where monitor_item_english in
    <foreach collection="monitorItemList" item="deviceValue" open="(" separator="," close=")">
      #{deviceValue}
    </foreach>
    and find_in_set(#{deviceThirdId}, device_third_id)
    order by warning_level
  </select>

  <delete id="deleteByRuleId">
    delete from t_warning_rule_condition where rule_id = #{id,jdbcType=BIGINT}
  </delete>

  <select id="listByRuleIdList" resultMap="BaseResultMap">
    select  id, rule_id, device_id, rule_condition, monitor_item, monitor_item_english,
    monitor_value, monitor_unit, warning_level, create_time, create_by
    from t_warning_rule_condition
    where rule_id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="getMinConditionByMonitorItem" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_rule_condition
    where find_in_set(#{deviceThirdId}, device_third_id)
    and monitor_item = #{monitorItem}
    order by warning_level desc, monitor_value limit 1
  </select>
</mapper>
