package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.DeviceMonitorRanqijing;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeviceMonitorRanqijingMapper {
    int deleteByPrimaryKey(String id);

    int insert(DeviceMonitorRanqijing record);

    int insertSelective(DeviceMonitorRanqijing record);

    DeviceMonitorRanqijing selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(DeviceMonitorRanqijing record);

    int updateByPrimaryKey(DeviceMonitorRanqijing record);

    int updateBatch(List<DeviceMonitorRanqijing> list);

    int batchInsert(@Param("list") List<DeviceMonitorRanqijing> list);

    List<DeviceMonitorRanqijing> listGasWell();

}
