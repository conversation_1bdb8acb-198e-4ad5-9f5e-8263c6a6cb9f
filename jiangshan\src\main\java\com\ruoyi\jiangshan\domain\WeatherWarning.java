package com.ruoyi.jiangshan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 气象预警+专业监测预警对象 t_weather_warning
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class WeatherWarning extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 预警id */
    @Excel(name = "预警id")
    private String warningId;

    /** 风险区编号 */
    @Excel(name = "风险区编号")
    private String riskZoneNum;

    /** 风险区id */
    @Excel(name = "风险区id")
    private String riskZoneId;

    /** 行政区编号 */
    @Excel(name = "行政区编号")
    private String areaCode;

    /** 预警等级(红橙黄 123) */
    @Excel(name = "预警等级", readConverterExp = "1=红色,2=橙色,3=黄色")
    private Integer warningLevel;

    /** 监测站编号 */
    @Excel(name = "监测站编号")
    private String stationCode;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 预警类型(0:实时预警,1:监测预警) */
    @Excel(name = "预警类型", readConverterExp = "0=实时预警,1=监测预警")
    private Integer warningType;

    /** 预警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date warningTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 同步时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "同步时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date syncTime;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setWarningId(String warningId)
    {
        this.warningId = warningId;
    }

    public String getWarningId()
    {
        return warningId;
    }
    public void setRiskZoneNum(String riskZoneNum)
    {
        this.riskZoneNum = riskZoneNum;
    }

    public String getRiskZoneNum()
    {
        return riskZoneNum;
    }
    public void setRiskZoneId(String riskZoneId)
    {
        this.riskZoneId = riskZoneId;
    }

    public String getRiskZoneId()
    {
        return riskZoneId;
    }
    public void setAreaCode(String areaCode)
    {
        this.areaCode = areaCode;
    }

    public String getAreaCode()
    {
        return areaCode;
    }
    public void setWarningLevel(Integer warningLevel)
    {
        this.warningLevel = warningLevel;
    }

    public Integer getWarningLevel()
    {
        return warningLevel;
    }
    public void setStationCode(String stationCode)
    {
        this.stationCode = stationCode;
    }

    public String getStationCode()
    {
        return stationCode;
    }
    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public String getRemark()
    {
        return remark;
    }
    public void setWarningType(Integer warningType)
    {
        this.warningType = warningType;
    }

    public Integer getWarningType()
    {
        return warningType;
    }
    public void setWarningTime(Date warningTime)
    {
        this.warningTime = warningTime;
    }

    public Date getWarningTime()
    {
        return warningTime;
    }
    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }

    public Date getCreateTime()
    {
        return createTime;
    }
    public void setUpdateTime(Date updateTime)
    {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime()
    {
        return updateTime;
    }
    public void setSyncTime(Date syncTime)
    {
        this.syncTime = syncTime;
    }

    public Date getSyncTime()
    {
        return syncTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("warningId", getWarningId())
                .append("riskZoneNum", getRiskZoneNum())
                .append("riskZoneId", getRiskZoneId())
                .append("areaCode", getAreaCode())
                .append("warningLevel", getWarningLevel())
                .append("stationCode", getStationCode())
                .append("remark", getRemark())
                .append("warningType", getWarningType())
                .append("warningTime", getWarningTime())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("syncTime", getSyncTime())
                .toString();
    }
}
