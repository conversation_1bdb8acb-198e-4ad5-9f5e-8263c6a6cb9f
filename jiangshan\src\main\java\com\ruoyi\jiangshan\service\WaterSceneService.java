package com.ruoyi.jiangshan.service;

import com.ruoyi.jiangshan.domain.DeviceMonitor;
import com.ruoyi.jiangshan.domain.WarningInfo;
import com.ruoyi.jiangshan.vo.CastVO;
import com.ruoyi.jiangshan.vo.WarningInfoVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface WaterSceneService {

    List<CastVO> getWeather();

    List<DeviceMonitor> getMonitor(String type);

    Map<String, Object> getEquipment(String type);

    List<WarningInfoVO> getWarning(Integer warningLevel);

    Map<String, Object> getWarningDispose(Date startTime, Date endTime);

}
