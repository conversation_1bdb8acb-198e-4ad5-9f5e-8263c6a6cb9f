package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.EmergencyMaterial;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmergencyMaterialMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(EmergencyMaterial record);

    int insertSelective(EmergencyMaterial record);

    EmergencyMaterial selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(EmergencyMaterial record);

    int updateByPrimaryKey(EmergencyMaterial record);

    int updateBatch(List<EmergencyMaterial> list);

    int batchInsert(@Param("list") List<EmergencyMaterial> list);

    List<EmergencyMaterial> listByStreetAndVillage(@Param("street") String street, @Param("village") String village);
}
