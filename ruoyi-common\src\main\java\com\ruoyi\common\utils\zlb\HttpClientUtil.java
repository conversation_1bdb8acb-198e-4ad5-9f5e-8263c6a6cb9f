package com.ruoyi.common.utils.zlb;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: http工具类
 */

@Slf4j
public class HttpClientUtil {

    /**
     * 发送get无参请求
     * @param url 请求url
     * @return
     */
    public static JSONObject doGet(String url) {
        return doGet(url, null);
    }

    /**
     *  发送get有参请求
     * @param url 请求url
     * @param param 请求参数
     * @return
     */
    public static JSONObject doGet(String url, Map<String, String> param) {

        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        JSONObject result = null;
        try {
            // 创建uri
            URIBuilder builder = new URIBuilder(url);
            if (param != null) {
                for (String key : param.keySet()) {
                    builder.addParameter(key, param.get(key));
                }
            }
            URI uri = builder.build();
            // 创建http GET请求
            HttpGet httpGet = new HttpGet(uri);
            // 执行请求
            response = httpclient.execute(httpGet);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                //响应实体JSON处理
                HttpEntity entity = response.getEntity();
                String resultString = EntityUtils.toString(entity, "UTF-8");
                result = JSONObject.parseObject(resultString);
            }else {
                log.info("http get请求失败->code：{}", response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 发送post无参请求
     * @param url
     * @return
     */
    public static JSONObject doPost(String url) {
        return doPost(url, null,null);
    }

    /**
     * 发送post有参请求
     * @param url
     * @param param
     * @return
     */
    public static JSONObject doPost(String url, Map<String, String> param,Map<String, String> stringStringMap) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        JSONObject result = null;
        try {
            // 创建Http Post请求
            HttpPost httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded");
            httpPost.addHeader("X-BG-HMAC-SIGNATURE", stringStringMap.get("X-BG-HMAC-SIGNATURE"));
            httpPost.addHeader("X-BG-HMAC-ALGORITHM", stringStringMap.get("X-BG-HMAC-ALGORITHM"));
            httpPost.addHeader("X-BG-HMAC-ACCESS-KEY", stringStringMap.get("X-BG-HMAC-ACCESS-KEY"));
            httpPost.addHeader("X-BG-DATE-TIME", stringStringMap.get("X-BG-DATE-TIME"));
            // 创建参数列表
            if (param != null) {
                List<NameValuePair> paramList = new ArrayList<>();
                for (String key : param.keySet()) {
                    paramList.add(new BasicNameValuePair(key, param.get(key)));
                }
                // 模拟表单
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList,"UTF-8");
                httpPost.setEntity(entity);
            }
            // 执行http请求
            response = httpClient.execute(httpPost);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                //响应实体JSON处理
                HttpEntity entity = response.getEntity();
                String resultString = EntityUtils.toString(entity, "UTF-8");
                result = JSONObject.parseObject(resultString);
            }else {
                log.info("http post请求失败->code：{}", response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if(response != null){
                    response.close();
                }
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 发送post有参请求
     * @param url
     * @param param
     * @return
     */
    public static String doHttpPost(String url, Map<String, String> param,Map<String, String> stringStringMap) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String result = null;
        try {
            // 创建Http Post请求
            HttpPost httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type", "application/json");
            httpPost.addHeader("X-BG-HMAC-SIGNATURE", stringStringMap.get("X-BG-HMAC-SIGNATURE"));
            httpPost.addHeader("X-BG-HMAC-ALGORITHM", stringStringMap.get("X-BG-HMAC-ALGORITHM"));
            httpPost.addHeader("X-BG-HMAC-ACCESS-KEY", stringStringMap.get("X-BG-HMAC-ACCESS-KEY"));
            httpPost.addHeader("X-BG-DATE-TIME", stringStringMap.get("X-BG-DATE-TIME"));
            // 创建参数列表
            if (param != null) {
                List<NameValuePair> paramList = new ArrayList<>();
                for (String key : param.keySet()) {
                    paramList.add(new BasicNameValuePair(key, param.get(key)));
                }
                // 模拟表单
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList,"UTF-8");
                httpPost.setEntity(entity);
            }
            // 执行http请求
            response = httpClient.execute(httpPost);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                //响应实体JSON处理
                HttpEntity entity = response.getEntity();
                result = EntityUtils.toString(entity, "UTF-8");
            }else {
                log.info("http post请求失败->code：{}", response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if(response != null){
                    response.close();
                }
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }
    /**
     * 发送post请求
     * @param url
     * @param json
     * @return
     */
    public static JSONObject HttpPostWithJson(String url, String json,Map<String, String> stringStringMap) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        JSONObject result = null;
        try {
            //第一步：创建HttpClient对象
            httpClient = HttpClients.createDefault();
            //第二步：创建httpPost对象
            HttpPost httpPost = new HttpPost(url);
            httpPost.addHeader("X-BG-HMAC-SIGNATURE", stringStringMap.get("X-BG-HMAC-SIGNATURE"));
            httpPost.addHeader("X-BG-HMAC-ALGORITHM", stringStringMap.get("X-BG-HMAC-ALGORITHM"));
            httpPost.addHeader("X-BG-HMAC-ACCESS-KEY", stringStringMap.get("X-BG-HMAC-ACCESS-KEY"));
            httpPost.addHeader("X-BG-DATE-TIME", stringStringMap.get("X-BG-DATE-TIME"));
            //第三步：给httpPost设置JSON格式的参数
            StringEntity requestEntity = new StringEntity(json, "utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setEntity(requestEntity);
            //第四步：发送HttpPost请求，获取返回值
            response = httpClient.execute(httpPost);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                //响应实体JSON处理
                HttpEntity entity = response.getEntity();
                String resultString = EntityUtils.toString(entity, "UTF-8");
                result = JSONObject.parseObject(resultString);
            } else {
                log.info("http post请求失败->code：{}", response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        //第五步：处理返回值
        return result;
    }


    /**
     * 发送post请求
     *
     * @param url        路径
     * @param jsonObject 参数(json类型)
     * @param encoding   编码格式
     * @return
     * @throws ParseException
     * @throws IOException
     */
    public static String send(String url, JSONObject jsonObject, String encoding,Map<String, String> stringStringMap) throws ParseException, IOException {
        String body = "";

        //创建httpclient对象
        CloseableHttpClient client = HttpClients.createDefault();
        //创建post方式请求对象
        HttpPost httpPost = new HttpPost(url);

        //装填参数
        StringEntity s = new StringEntity(jsonObject.toString(), "utf-8");
        s.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE,
                "application/json"));
        //设置参数到请求对象中
        httpPost.setEntity(s);
        System.out.println("请求地址：" + url);
//        System.out.println("请求参数："+nvps.toString());

        //设置header信息
        //指定报文头【Content-type】、【User-Agent】
        httpPost.setHeader("Content-type", "application/json");
        httpPost.addHeader("X-BG-HMAC-SIGNATURE", stringStringMap.get("X-BG-HMAC-SIGNATURE"));
        httpPost.addHeader("X-BG-HMAC-ALGORITHM", stringStringMap.get("X-BG-HMAC-ALGORITHM"));
        httpPost.addHeader("X-BG-HMAC-ACCESS-KEY", stringStringMap.get("X-BG-HMAC-ACCESS-KEY"));
        httpPost.addHeader("X-BG-DATE-TIME", stringStringMap.get("X-BG-DATE-TIME"));
        //执行请求操作，并拿到结果（同步阻塞）
        CloseableHttpResponse response = client.execute(httpPost);
        //获取结果实体
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            //按指定编码转换结果实体为String类型
            body = EntityUtils.toString(entity, encoding);
        }
        EntityUtils.consume(entity);
        //释放链接
        response.close();
        return body;

    }


    /**
     * 发送get请求
     * @param url 请求URL
     * @param param 请求参数 key:value url携带参数 或者无参可不填
     * @return
     */
    public static String sendGet(String url,Map<String, String> stringStringMap) throws ParseException, IOException {

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);
        httpGet.addHeader("X-BG-HMAC-SIGNATURE", stringStringMap.get("X-BG-HMAC-SIGNATURE"));
        httpGet.addHeader("X-BG-HMAC-ALGORITHM", stringStringMap.get("X-BG-HMAC-ALGORITHM"));
        httpGet.addHeader("X-BG-HMAC-ACCESS-KEY", stringStringMap.get("X-BG-HMAC-ACCESS-KEY"));
        httpGet.addHeader("X-BG-DATE-TIME", stringStringMap.get("X-BG-DATE-TIME"));
        CloseableHttpResponse response = httpClient.execute(httpGet);
        String resp;
        try {
            HttpEntity entity = response.getEntity();
            resp = EntityUtils.toString(entity, "utf-8");
            EntityUtils.consume(entity);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        response.close();
        return resp;
    }

    public static void main(String[] args) {
        String url = "http://************";
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("transId","444444444444444");
        jsonParam.put("origin", "KMS");
        jsonParam.put("appId", "APP_B6DC6909C07342D08BBDEA86865A2880");
        jsonParam.put("keySpec", "SM4_128");
        jsonParam.put("keyUsage", "ENCRYPT/DECRYPT");
        try {
            String data = send(url,jsonParam,"UTF-8",null);
            JSONObject jsonObject = JSONObject.parseObject(data);
            System.out.println("result="+data);
            System.out.println("result="+jsonObject);
            System.out.println("keyId="+jsonObject.getJSONObject("data").getJSONObject("cmkMetadata").get("keyId"));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

