package com.ruoyi.jiangshan.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.jiangshan.mapper.CitysafetyRuleMapper;
import com.ruoyi.jiangshan.domain.CitysafetyRule;
import com.ruoyi.jiangshan.service.ICitysafetyRuleService;

/**
 * 报告生成管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Service
public class CitysafetyRuleServiceImpl implements ICitysafetyRuleService
{
    @Autowired
    private CitysafetyRuleMapper citysafetyRuleMapper;

    /**
     * 查询报告生成管理
     *
     * @param id 报告生成管理主键
     * @return 报告生成管理
     */
    @Override
    public CitysafetyRule selectCitysafetyRuleById(Long id)
    {
        return citysafetyRuleMapper.selectCitysafetyRuleById(id);
    }

    /**
     * 查询报告生成管理列表
     *
     * @param citysafetyRule 报告生成管理
     * @return 报告生成管理
     */
    @Override
    public List<CitysafetyRule> selectCitysafetyRuleList(CitysafetyRule citysafetyRule)
    {
        return citysafetyRuleMapper.selectCitysafetyRuleList(citysafetyRule);
    }

    /**
     * 新增报告生成管理
     *
     * @param citysafetyRule 报告生成管理
     * @return 结果
     */
    @Override
    public int insertCitysafetyRule(CitysafetyRule citysafetyRule)
    {
        checkSameRule(citysafetyRule);

        citysafetyRule.setCreateTime(DateUtils.getNowDate());
        citysafetyRule.setCreateBy(SecurityUtils.getUserId() + "");
        citysafetyRule.setBizCode(IdUtils.simpleUUID());
        return citysafetyRuleMapper.insertCitysafetyRule(citysafetyRule);
    }

    private void checkSameRule(CitysafetyRule citysafetyRule) {
        List<CitysafetyRule> citysafetyRuleList = citysafetyRuleMapper.listByFrequnceWithOutStatus(citysafetyRule.getGenerateFrequency());
        if (CollectionUtils.isNotEmpty(citysafetyRuleList)) {
            String[] split = citysafetyRule.getScene().split(",");

            for (CitysafetyRule rule : citysafetyRuleList) {
                String[] split2 = rule.getScene().split(",");

                if (!Objects.equals(citysafetyRule.getId(), rule.getId()) && Arrays.equals(split, split2)) {
                    throw new RuntimeException("相同生成频率和场景的规则已存在");
                }
            }

//            throw new RuntimeException("同一生成频率规则已存在");
        }
    }

    /**
     * 修改报告生成管理
     *
     * @param citysafetyRule 报告生成管理
     * @return 结果
     */
    @Override
    public int updateCitysafetyRule(CitysafetyRule citysafetyRule)
    {
        checkSameRule(citysafetyRule);

        citysafetyRule.setUpdateTime(new Date());
        citysafetyRule.setUpdateBy(SecurityUtils.getUserId() + "");
        return citysafetyRuleMapper.updateCitysafetyRule(citysafetyRule);
    }

    /**
     * 批量删除报告生成管理
     *
     * @param ids 需要删除的报告生成管理主键
     * @return 结果
     */
    @Override
    public int deleteCitysafetyRuleByIds(Long[] ids)
    {
        return citysafetyRuleMapper.deleteCitysafetyRuleByIds(ids);
    }

    /**
     * 删除报告生成管理信息
     *
     * @param id 报告生成管理主键
     * @return 结果
     */
    @Override
    public int deleteCitysafetyRuleById(Long id)
    {
        return citysafetyRuleMapper.deleteCitysafetyRuleById(id);
    }
}
