<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningRuleShuxinMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningRuleShuxin">
    <!--@mbg.generated-->
    <!--@Table t_warning_rule_shuxin-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="publish_way" jdbcType="INTEGER" property="publishWay" />
    <result column="rule_detail" jdbcType="VARCHAR" property="ruleDetail" />
    <result column="warning_user_id" jdbcType="BIGINT" property="warningUserId" />
    <result column="warning_user_name" jdbcType="VARCHAR" property="warningUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="other_user_id" jdbcType="VARCHAR" property="otherUserId" />
    <result column="all_account_id" jdbcType="VARCHAR" property="allAccountId" />
    <result column="all_mobile" jdbcType="VARCHAR" property="allMobile" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, rule_name, model_id, publish_way, rule_detail, warning_user_id, warning_user_name,
    create_time, create_by, update_time, update_by, remark, other_user_id, all_account_id, all_mobile
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_rule_shuxin
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_rule_shuxin
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningRuleShuxin" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_rule_shuxin (rule_name, model_id, publish_way,
      rule_detail, warning_user_id, warning_user_name,
      create_time, create_by, update_time,
      update_by, remark)
    values (#{ruleName,jdbcType=VARCHAR}, #{modelId,jdbcType=VARCHAR}, #{publishWay,jdbcType=INTEGER},
      #{ruleDetail,jdbcType=VARCHAR}, #{warningUserId,jdbcType=BIGINT}, #{warningUserName,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningRuleShuxin" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_rule_shuxin
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="modelId != null">
        model_id,
      </if>
      <if test="publishWay != null">
        publish_way,
      </if>
      <if test="ruleDetail != null">
        rule_detail,
      </if>
      <if test="warningUserId != null">
        warning_user_id,
      </if>
      <if test="warningUserName != null">
        warning_user_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="publishWay != null">
        #{publishWay,jdbcType=INTEGER},
      </if>
      <if test="ruleDetail != null">
        #{ruleDetail,jdbcType=VARCHAR},
      </if>
      <if test="warningUserId != null">
        #{warningUserId,jdbcType=BIGINT},
      </if>
      <if test="warningUserName != null">
        #{warningUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelectiveByModelId" parameterType="com.ruoyi.jiangshan.domain.WarningRuleShuxin">
    <!--@mbg.generated-->
    update t_warning_rule_shuxin
    <set>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="publishWay != null">
        publish_way = #{publishWay,jdbcType=INTEGER},
      </if>
      <if test="ruleDetail != null">
        rule_detail = #{ruleDetail,jdbcType=VARCHAR},
      </if>
      <if test="warningUserId != null">
        warning_user_id = #{warningUserId,jdbcType=BIGINT},
      </if>
      <if test="warningUserName != null">
        warning_user_name = #{warningUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      other_user_id = #{otherUserId,jdbcType=VARCHAR},
      all_account_id = #{allAccountId,jdbcType=VARCHAR},
      all_mobile = #{allMobile,jdbcType=VARCHAR}
    </set>
    where model_id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningRuleShuxin">
    <!--@mbg.generated-->
    update t_warning_rule_shuxin
    set rule_name = #{ruleName,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=VARCHAR},
      publish_way = #{publishWay,jdbcType=INTEGER},
      rule_detail = #{ruleDetail,jdbcType=VARCHAR},
      warning_user_id = #{warningUserId,jdbcType=BIGINT},
      warning_user_name = #{warningUserName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_rule_shuxin
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="rule_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ruleName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="model_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modelId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="publish_way = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.publishWay,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="rule_detail = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ruleDetail,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="warning_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.warningUserId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="warning_user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.warningUserName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_rule_shuxin
    (rule_name, model_id, publish_way, rule_detail, warning_user_id, warning_user_name,
      create_time, create_by, update_time, update_by, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.ruleName,jdbcType=VARCHAR}, #{item.modelId,jdbcType=VARCHAR}, #{item.publishWay,jdbcType=INTEGER},
        #{item.ruleDetail,jdbcType=VARCHAR}, #{item.warningUserId,jdbcType=BIGINT}, #{item.warningUserName,jdbcType=VARCHAR},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
        #{item.updateBy,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="getByModelId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from t_warning_rule_shuxin
    where model_id = #{id}
  </select>

  <select id="getAccountByScene" resultMap="BaseResultMap">
    select t1.* from t_warning_rule_shuxin t1
    left join t_warning_model t2
    on t1.model_id = t2.id
    where t2.model_scene = #{eventType}
    limit 1
  </select>
</mapper>
