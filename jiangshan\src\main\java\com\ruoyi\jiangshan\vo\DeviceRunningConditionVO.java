package com.ruoyi.jiangshan.vo;

import lombok.Data;

@Data
public class DeviceRunningConditionVO {
    //设备类型
    private String deviceType;
    //设备名称
    private String deviceName;
    //设备编号
    private String deviceNum;
    //安装位置
    private String deviceAddress;
    //检测数据（数值）
    private double monitorValue;
    //检测数据（单位）
    private String monitorUnit;
    //状态
    private String state;
    //预警级别
    private Integer warningLevel;
    //形变位移情况
    private String displacementCondition;
    //异常流量
    private String warningFlow;
    //隐患点核实情况
    private String hazardVerifyCondition ;
}
