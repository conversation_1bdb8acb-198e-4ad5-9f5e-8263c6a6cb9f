package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.DeviceInfoItem;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface DeviceInfoItemMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeviceInfoItem record);

    int insertSelective(DeviceInfoItem record);

    DeviceInfoItem selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeviceInfoItem record);

    int updateByPrimaryKey(DeviceInfoItem record);

    int updateBatch(List<DeviceInfoItem> list);

    int batchInsert(@Param("list") List<DeviceInfoItem> list);

    List<DeviceInfoItem> listMonitorItemByType(String type);
}
