<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningEventTimeoutRuleMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningEventTimeoutRule">
    <!--@mbg.generated-->
    <!--@Table t_warning_event_timeout_rule-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="timeout_day" jdbcType="VARCHAR" property="timeoutDay" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `status`, timeout_day, create_time, create_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_event_timeout_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_event_timeout_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventTimeoutRule" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_timeout_rule (`status`, timeout_day, create_time,
      create_by)
    values (#{status,jdbcType=BOOLEAN}, #{timeoutDay,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningEventTimeoutRule" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_timeout_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        `status`,
      </if>
      <if test="timeoutDay != null">
        timeout_day,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="timeoutDay != null">
        #{timeoutDay,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningEventTimeoutRule">
    <!--@mbg.generated-->
    update t_warning_event_timeout_rule
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="timeoutDay != null">
        timeout_day = #{timeoutDay,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningEventTimeoutRule">
    <!--@mbg.generated-->
    update t_warning_event_timeout_rule
    set `status` = #{status,jdbcType=BOOLEAN},
      timeout_day = #{timeoutDay,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_event_timeout_rule
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="timeout_day = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.timeoutDay,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_event_timeout_rule
    (`status`, timeout_day, create_time, create_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.status,jdbcType=BOOLEAN}, #{item.timeoutDay,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.createBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="listAll" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_event_timeout_rule
  </select>
</mapper>
