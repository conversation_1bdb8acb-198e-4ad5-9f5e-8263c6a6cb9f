package com.ruoyi.jiangshan.dingding.controller;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.jiangshan.dingding.client.DingDingClient;
import com.ruoyi.jiangshan.dingding.dto.DingDingTransferVO;
import com.ruoyi.jiangshan.domain.CitysafetyReport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 钉钉待办
 */
@Slf4j
@RestController
@RequestMapping("/dingding")
public class DingDingController {
    @Autowired
    private DingDingClient dingDingClient;

    /**
     * 发送钉钉待办
     */
    @PostMapping("/sendMsg")
    public AjaxResult sendMsg(@RequestBody DingDingTransferVO dingDingTransferVO)
    {
        log.info("DingDingController, dingDingTransferVO:{}", JSON.toJSONString(dingDingTransferVO));

        dingDingClient.sendWordRecord(dingDingTransferVO.getMobile(), dingDingTransferVO.getTitle(),
                dingDingTransferVO.getContent(), dingDingTransferVO.getDingdingContentMap(),
                dingDingTransferVO.getSingleUrl(), dingDingTransferVO.getSinglePcUrl());

        log.info("DingDingController, sendMsg, end");
        return AjaxResult.success();
    }
}
