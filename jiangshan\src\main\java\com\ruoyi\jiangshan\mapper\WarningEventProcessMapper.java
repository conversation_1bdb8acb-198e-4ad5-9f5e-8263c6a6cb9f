package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningEventProcess;

import java.time.LocalDateTime;
import java.util.List;

import com.ruoyi.jiangshan.vo.jczz.WarningEventJczzFlowVO;
import org.apache.ibatis.annotations.Param;

public interface WarningEventProcessMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningEventProcess record);

    int insertSelective(WarningEventProcess record);

    WarningEventProcess selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningEventProcess record);

    int updateByPrimaryKey(WarningEventProcess record);

    int updateBatch(List<WarningEventProcess> list);

    int batchInsert(@Param("list") List<WarningEventProcess> list);

    List<WarningEventProcess> listByEventIdList(@Param("list") List<Long> warningEventIdList);

    List<WarningEventProcess> listNonOvertimeByLastProcess();

    WarningEventProcess getByEventId(@Param("id") Long id, @Param("status") Integer status);

    WarningEventProcess getLastByEventId(Long id);

    WarningEventProcess getLastByEventIdAndStep(@Param("eventId") Long eventId, @Param("status") Integer status);

    void updateEventStatus(Long id);

    void updateHandleStatus(@Param("id") Long id, @Param("completeFlowVO") WarningEventJczzFlowVO completeFlowVO);

    List<WarningEventProcess> listNonCompleteProcess(Long id);

    List<WarningEventProcess> listPushValue(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
