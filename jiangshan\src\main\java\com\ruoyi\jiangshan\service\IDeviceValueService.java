package com.ruoyi.jiangshan.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.openapi.vo.DeviceValueOpenApiVO;
import com.ruoyi.jiangshan.vo.BusinessCountVO;
import com.ruoyi.jiangshan.vo.BusinessDeviceValueLineVO;

/**
 * 设备实时数据Service接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface IDeviceValueService
{
    /**
     * 查询设备实时数据
     *
     * @param id 设备实时数据主键
     * @return 设备实时数据
     */
    public DeviceValue selectDeviceValueById(Long id);

    /**
     * 查询设备实时数据列表
     *
     * @param deviceValue 设备实时数据
     * @return 设备实时数据集合
     */
    public List<DeviceValue> selectDeviceValueList(DeviceValue deviceValue);

    /**
     * 新增设备实时数据
     *
     * @param deviceValue 设备实时数据
     * @return 结果
     */
    public int insertDeviceValue(DeviceValue deviceValue);

    /**
     * 修改设备实时数据
     *
     * @param deviceValue 设备实时数据
     * @return 结果
     */
    public int updateDeviceValue(DeviceValue deviceValue);

    /**
     * 批量删除设备实时数据
     *
     * @param ids 需要删除的设备实时数据主键集合
     * @return 结果
     */
    public int deleteDeviceValueByIds(Long[] ids);

    /**
     * 删除设备实时数据信息
     *
     * @param id 设备实时数据主键
     * @return 结果
     */
    public int deleteDeviceValueById(Long id);

    void saveDeviceValue(DeviceValueOpenApiVO apiVO);

    Map<String, Object> getMonitor(String dateStr, Long deviceId, String monitorItem);

    List<BusinessCountVO> getOneLineByMonitorIdAndItem(Long monitorId, String monitorItem);

//    Map<String, Object> getMonitorV2(String dateStr, String deviceThirdId);

    void createNewWarningEventDispatch(WarningEvent warningEvent, SysUser sysUser, String accountId);

    List<BusinessDeviceValueLineVO> getLineData(DeviceValue deviceValue);
}
