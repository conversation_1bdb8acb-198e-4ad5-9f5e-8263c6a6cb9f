package com.ruoyi.jiangshan.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.jiangshan.client.GeologicClient;
import com.ruoyi.jiangshan.domain.WeatherWarning;
import com.ruoyi.jiangshan.mapper.WeatherWarningMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 气象预警+专业监测预警Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/jiangshan/weatherWarning")
public class WeatherWarningController extends BaseController
{
    @Autowired
    private WeatherWarningMapper weatherWarningMapper;

    @Autowired
    private GeologicClient geologicClient;

    /**
     * 查询气象预警+专业监测预警列表
     */
    @GetMapping("/list")
    public TableDataInfo list(WeatherWarning weatherWarning)
    {
        startPage();
        List<WeatherWarning> list = weatherWarningMapper.selectWeatherWarningList(weatherWarning);
        return getDataTable(list);
    }

    /**
     * 导出气象预警+专业监测预警列表
     */
    @Log(title = "气象预警+专业监测预警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WeatherWarning weatherWarning)
    {
        List<WeatherWarning> list = weatherWarningMapper.selectWeatherWarningList(weatherWarning);
        ExcelUtil<WeatherWarning> util = new ExcelUtil<WeatherWarning>(WeatherWarning.class);
        util.exportExcel(response, list, "气象预警+专业监测预警数据");
    }

    /**
     * 获取气象预警+专业监测预警详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(weatherWarningMapper.selectWeatherWarningById(id));
    }

    /**
     * 新增气象预警+专业监测预警
     */
    @Log(title = "气象预警+专业监测预警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WeatherWarning weatherWarning)
    {
        return toAjax(weatherWarningMapper.insertWeatherWarning(weatherWarning));
    }

    /**
     * 修改气象预警+专业监测预警
     */
    @Log(title = "气象预警+专业监测预警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WeatherWarning weatherWarning)
    {
        return toAjax(weatherWarningMapper.updateWeatherWarning(weatherWarning));
    }

    /**
     * 删除气象预警+专业监测预警
     */
    @Log(title = "气象预警+专业监测预警", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(weatherWarningMapper.deleteWeatherWarningByIds(ids));
    }

    /**
     * 同步第三方气象预警数据
     */
    @Log(title = "同步气象预警数据", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    public AjaxResult syncWeatherWarningData(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "3000") Integer size)
    {
        Map<String, Object> result = geologicClient.syncWeatherWarningData(startTime, endTime, page, size);
        return AjaxResult.success(result);
    }

    /**
     * 同步第三方地址灾害数据
     */
    @Log(title = "同步第三方地址灾害数据", businessType = BusinessType.OTHER)
    @PostMapping("/syncGeo")
    public AjaxResult syncGeoData(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "3000") Integer size)
    {
        Map<String, Object> result = geologicClient.syncRiskPreventionZoneData(startTime, endTime, page, size);
        return AjaxResult.success(result);
    }

    /**
     * 同步第三方巡查处置信息数据
     */
    @Log(title = "同步第三方巡查处置信息数据", businessType = BusinessType.OTHER)
    @PostMapping("/syncDisposal")
    public AjaxResult syncDisposal(
            @RequestParam(value = "apiPageNum", defaultValue = "1") Integer apiPageNum,
            @RequestParam(value = "apiPageSize", defaultValue = "3000") Integer apiPageSize)
    {
        Map<String, Object> result = geologicClient.syncPatrolDisposalInfoData(apiPageNum, apiPageSize);
        return AjaxResult.success(result);
    }

    /**
     * 分页同步所有第三方气象预警数据
     */
    @Log(title = "分页同步所有气象预警数据", businessType = BusinessType.OTHER)
    @PostMapping("/syncAll")
    public AjaxResult syncAllWeatherWarningData(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "pageSize", defaultValue = "3000") Integer pageSize)
    {
        Map<String, Object> result = geologicClient.syncAllWeatherWarningData(startTime, endTime, pageSize);
        return AjaxResult.success(result);
    }
}
