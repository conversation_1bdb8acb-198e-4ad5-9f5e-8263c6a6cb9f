package com.ruoyi.jiangshan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.jiangshan.vo.cityrisk.WarningEventReportVO;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class WarningReportVO {

    private String scene;

    private Long warningLevel01 = 0L;
    private Long warningLevel02 = 0L;
    private Long warningLevel03 = 0L;
    private Long warningLevel04 = 0L;
    private Long warningLevel05 = 0L;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private List<WarningReportDeviceVO> deviceList = Lists.newArrayList();

    private List<CitySafetyLineVO> lineList;

    private List<WarningEventReportVO> warningList = Lists.newArrayList();

    private List<WarningEventReportVO> modelList = Lists.newArrayList();

    private String imageUrl;

    private String fileName;
}
