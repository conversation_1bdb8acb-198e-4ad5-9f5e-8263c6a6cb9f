<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceMonitorMapper">

    <resultMap type="DeviceMonitor" id="DeviceMonitorResult">
        <result property="id"    column="id"    />
        <result property="monitorName"    column="monitor_name"    />
        <result property="monitorType"    column="monitor_type"    />
        <result property="monitorScene"    column="monitor_scene"    />
        <result property="serviceUrl"    column="service_url"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="lon"    column="lon"    />
        <result property="lat"    column="lat"    />
        <result property="monitorDepartmentId"    column="monitor_department_id"    />
        <result property="monitorDepartmentName"    column="monitor_department_name"    />
        <result property="scale"    column="scale"    />
        <result property="scaleUnit"    column="scale_unit"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="monitorThirdId"    column="monitor_third_id"    />
        <result property="newType"    column="new_type"    />
    </resultMap>

    <sql id="selectDeviceMonitorVo">
        select id, monitor_name, monitor_type, monitor_scene, service_url, file_url, lon, lat, monitor_department_id, monitor_department_name, scale, scale_unit,
               create_time, create_by, update_time, update_by, remark, monitor_third_id,new_type from t_device_monitor
    </sql>

    <select id="selectDeviceMonitorList" parameterType="DeviceMonitor" resultMap="DeviceMonitorResult">
        <include refid="selectDeviceMonitorVo"/>
        <where>
            <if test="monitorName != null  and monitorName != ''"> and monitor_name like concat('%', #{monitorName}, '%')</if>
            <if test="monitorType != null  and monitorType != ''"> and monitor_type = #{monitorType}</if>
            <if test="monitorScene != null  and monitorScene != ''"> and monitor_scene = #{monitorScene}</if>
            <if test="serviceUrl != null  and serviceUrl != ''"> and service_url = #{serviceUrl}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="lon != null  and lon != ''"> and lon = #{lon}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="monitorDepartmentId != null "> and monitor_department_id = #{monitorDepartmentId}</if>
            <if test="monitorDepartmentName != null  and monitorDepartmentName != ''"> and monitor_department_name like concat('%', #{monitorDepartmentName}, '%')</if>
            <if test="monitorThirdId != ''"> and monitor_third_id like concat('%', #{monitorThirdId}, '%')</if>
        </where>
    </select>

    <select id="selectDeviceMonitorById" parameterType="Long" resultMap="DeviceMonitorResult">
        <include refid="selectDeviceMonitorVo"/>
        where id = #{id}
    </select>

    <insert id="insertDeviceMonitor" parameterType="DeviceMonitor" useGeneratedKeys="true" keyProperty="id">
        insert into t_device_monitor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monitorName != null">monitor_name,</if>
            <if test="monitorType != null">monitor_type,</if>
            <if test="monitorScene != null">monitor_scene,</if>
            <if test="serviceUrl != null">service_url,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="lon != null">lon,</if>
            <if test="lat != null">lat,</if>
            <if test="monitorDepartmentId != null">monitor_department_id,</if>
            <if test="monitorDepartmentName != null">monitor_department_name,</if>
            <if test="scale != null">scale,</if>
            <if test="scaleUnit != null">scale_unit,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="monitorThirdId != null">monitor_third_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monitorName != null">#{monitorName},</if>
            <if test="monitorType != null">#{monitorType},</if>
            <if test="monitorScene != null">#{monitorScene},</if>
            <if test="serviceUrl != null">#{serviceUrl},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="lon != null">#{lon},</if>
            <if test="lat != null">#{lat},</if>
            <if test="monitorDepartmentId != null">#{monitorDepartmentId},</if>
            <if test="monitorDepartmentName != null">#{monitorDepartmentName},</if>
            <if test="scale != null">#{scale},</if>
            <if test="scaleUnit != null">#{scaleUnit},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="monitorThirdId != null">#{monitorThirdId},</if>
         </trim>
    </insert>

    <update id="updateDeviceMonitor" parameterType="DeviceMonitor">
        update t_device_monitor
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitorName != null">monitor_name = #{monitorName},</if>
            <if test="monitorType != null">monitor_type = #{monitorType},</if>
            <if test="monitorScene != null">monitor_scene = #{monitorScene},</if>
            <if test="serviceUrl != null">service_url = #{serviceUrl},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="monitorDepartmentId != null">monitor_department_id = #{monitorDepartmentId},</if>
            <if test="monitorDepartmentName != null">monitor_department_name = #{monitorDepartmentName},</if>
            <if test="scale != null">scale = #{scale},</if>
            <if test="scaleUnit != null">scale_unit = #{scaleUnit},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="monitorThirdId != null">monitor_third_id = #{monitorThirdId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceMonitorById" parameterType="Long">
        delete from t_device_monitor where id = #{id}
    </delete>

    <delete id="deleteDeviceMonitorByIds" parameterType="String">
        delete from t_device_monitor where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByType" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
        select monitor_type as `key`, count(1) as `value`
        from t_device_monitor
        where 1=1
        <if test="list != null and list.size() != 0">
            and monitor_type in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by monitor_type
    </select>

    <select id="countScaleByType" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
        select monitor_type as `key`, sum(scale) as `value`
        from t_device_monitor
        where 1=1
        <if test="list != null and list.size() != 0">
            and monitor_type in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by monitor_type
    </select>

    <select id="listByType" resultMap="DeviceMonitorResult">
        <include refid="selectDeviceMonitorVo">
        </include>
        where 1=1
        <if test="list != null and list.size() != 0">
            and monitor_type in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by create_time desc
    </select>

    <select id="getRiskPrevention" resultType="com.ruoyi.jiangshan.vo.RiskPreventVO">
        select t1.monitor_name as monitorName, t2.warning_address as monitorArea
        from t_device_monitor t1
        left join(
            select max(warning_time) as warning_time, monitor_id, warning_level, warning_address
            from (select distinct * from t_warning_info order by warning_time desc) t3
            group by monitor_id
        ) t2 on t1.id = t2.monitor_id
        where 1=1
        and monitor_type = #{type,jdbcType=VARCHAR}
        <if test="riskPreventVO.monitorName != null and riskPreventVO.monitorName != ''">
            and t1.monitor_name like concat('%', #{riskPreventVO.monitorName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="riskPreventVO.monitorArea != null and riskPreventVO.monitorArea != ''">
            and t2.warning_address like concat('%', #{riskPreventVO.monitorArea}, '%')
        </if>
        <if test="riskPreventVO.warningLevel != null">
            and t2.warning_level = #{riskPreventVO.warningLevel,jdbcType=INTEGER}
        </if>
        order by t2.warning_level desc
        limit 3
    </select>

    <select id="listAll" resultMap="DeviceMonitorResult">
        <include refid="selectDeviceMonitorVo">
        </include>
        where 1=1
        <if test="keyword != null and keyword != ''">
            and monitor_name like concat('%', #{keyword}, '%')
        </if>
        <if test="monitorScene != null and monitorScene != ''">
            and monitor_scene = #{monitorScene,jdbcType=VARCHAR}
        </if>
        <if test="excludeList != null and excludeList.size() != 0">
            and monitor_scene not in
            <foreach close=")" collection="excludeList" item="item" open="(" separator=", ">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by create_by desc
    </select>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into t_device_monitor
        (monitor_name, monitor_type, monitor_scene, service_url, file_url, lon, lat, monitor_department_id,
        monitor_department_name, create_time, create_by, update_time, update_by, `scale`,
        scale_unit, remark, monitor_third_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.monitorName,jdbcType=VARCHAR}, #{item.monitorType,jdbcType=VARCHAR}, #{item.monitorScene,jdbcType=VARCHAR},
            #{item.serviceUrl,jdbcType=VARCHAR}, #{item.fileUrl,jdbcType=VARCHAR}, #{item.lon,jdbcType=VARCHAR},
            #{item.lat,jdbcType=VARCHAR}, #{item.monitorDepartmentId,jdbcType=BIGINT}, #{item.monitorDepartmentName,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.scale,jdbcType=VARCHAR}, #{item.scaleUnit,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.monitorThirdId,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="listByIdList" resultMap="DeviceMonitorResult">
        <include refid="selectDeviceMonitorVo">
        </include>
        where 1=1
        <if test="modelIdList != null and modelIdList.size() != 0">
            and id in
            <foreach collection="modelIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by create_by desc
    </select>

    <select id="getByName" resultMap="DeviceMonitorResult">
        <include refid="selectDeviceMonitorVo"></include>
        where monitor_name = #{monitorName,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="listByTypeAndNewType" resultMap="DeviceMonitorResult">
        <include refid="selectDeviceMonitorVo">
        </include>
        where 1=1
        and lon is not null and lat is not null
        <if test="list != null and list.size() != 0">
            and monitor_type in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="newType != null and newType != ''">
            and new_type = #{newType}
        </if>
        order by create_time desc
    </select>

    <select id="listByTypeAndKeywords" resultMap="DeviceMonitorResult">
        <include refid="selectDeviceMonitorVo">
        </include>
        where 1=1
        <if test="list != null and list.size() != 0">
            and monitor_type in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="keywords != null and keywords != ''">
            and monitor_name like concat('%', #{keywords}, '%')
        </if>
        order by create_time desc
    </select>

    <select id="listPushValue" resultType="com.ruoyi.jiangshan.domain.DeviceMonitor">
        <include refid="selectDeviceMonitorVo">
        </include>
        where 1=1
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time <![CDATA[ <= ]]> #{endTime}
        </if>
    </select>

</mapper>
