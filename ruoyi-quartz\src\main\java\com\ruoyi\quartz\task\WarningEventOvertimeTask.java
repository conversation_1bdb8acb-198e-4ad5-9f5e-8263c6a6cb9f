package com.ruoyi.quartz.task;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.domain.WarningEventProcess;
import com.ruoyi.jiangshan.domain.WarningEventTimeoutRule;
import com.ruoyi.jiangshan.enums.WarningSuperviseEnum;
import com.ruoyi.jiangshan.mapper.WarningEventMapper;
import com.ruoyi.jiangshan.mapper.WarningEventProcessMapper;
import com.ruoyi.jiangshan.mapper.WarningEventTimeoutRuleMapper;
import com.ruoyi.jiangshan.mapper.WarningInfoMapper;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.service.impl.WarningEventServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component("warningEventOvertimeTask")
public class WarningEventOvertimeTask {

//    public void warningEventOvertime() {
//        WarningEventMapper warningEventMapper = SpringUtils.getBean(WarningEventMapper.class);
//        WarningEventProcessMapper warningEventProcessMapper = SpringUtils.getBean(WarningEventProcessMapper.class);
//        WarningEventTimeoutRuleMapper warningEventTimeoutRuleMapper = SpringUtils.getBean(WarningEventTimeoutRuleMapper.class);
//
//        List<WarningEventTimeoutRule> timeoutRuleList = warningEventTimeoutRuleMapper.listAll();
//        if (CollectionUtils.isEmpty(timeoutRuleList)) {
//            return;
//        }
//
//        Date currentDate = new Date();
//
//        Map<Integer, String> timeoutMap = timeoutRuleList.stream()
//                .collect(Collectors.toMap(WarningEventTimeoutRule::getStatus, WarningEventTimeoutRule::getTimeoutDay));
//
//        List<WarningEventProcess> processList = warningEventProcessMapper.listNonOvertimeByLastProcess();
//
//        List<WarningEventProcess> updateProcessList = Lists.newArrayList();
//        List<WarningEvent> updateEventList = Lists.newArrayList();
//        if (CollectionUtils.isNotEmpty(processList)) {
//            for (WarningEventProcess warningEventProcess : processList) {
//                String timeoutDayStr = timeoutMap.get(warningEventProcess.getStatus());
//
//                Calendar calendar = Calendar.getInstance();
//                calendar.setTime(warningEventProcess.getCreateTime());
//                calendar.set(Calendar.DAY_OF_MONTH, Integer.parseInt(timeoutDayStr));
//                Date overTime = calendar.getTime();
//
//                if (currentDate.after(overTime)) {
//                    //已超期
//                    WarningEventProcess updateProcess = new WarningEventProcess();
//                    updateProcess.setOverFlag(1);
//                    updateProcess.setId(warningEventProcess.getId());
//
//                    updateProcessList.add(updateProcess);
//
//                    WarningEvent warningEvent = new WarningEvent();
//                    warningEvent.setId(warningEventProcess.getEventId());
//                    warningEvent.setOverFlag(1);
//
//                    updateEventList.add(warningEvent);
//                }
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(updateProcessList)) {
//            warningEventProcessMapper.batchInsert(updateProcessList);
//        }
//        if (CollectionUtils.isNotEmpty(updateEventList)) {
//            warningEventMapper.batchInsert(updateEventList);
//        }
//
//    }

    public void warningEventOvertimeV2() {
        WarningEventMapper warningEventMapper = SpringUtils.getBean(WarningEventMapper.class);
        WarningEventServiceImpl warningEventService = SpringUtils.getBean(WarningEventServiceImpl.class);
        WarningEventProcessMapper warningEventProcessMapper = SpringUtils.getBean(WarningEventProcessMapper.class);
        WarningEventTimeoutRuleMapper warningEventTimeoutRuleMapper = SpringUtils.getBean(WarningEventTimeoutRuleMapper.class);

        List<WarningEventProcess> processList = warningEventProcessMapper.listNonOvertimeByLastProcess();
        if (CollectionUtils.isEmpty(processList)) {
            return;
        }

        Date currentDate = new Date();

        List<WarningEventProcess> updateProcessList = Lists.newArrayList();
        List<Long> updateEventIdList = Lists.newArrayList();
        for (WarningEventProcess warningEventProcess : processList) {
            if (warningEventProcess.getExpectedCompleteTime().before(currentDate)) {
                //已超期
                WarningEventProcess updateProcess = new WarningEventProcess();
                updateProcess.setId(warningEventProcess.getId());
                updateProcess.setOverFlag(1);

                updateProcessList.add(updateProcess);


                updateEventIdList.add(warningEventProcess.getEventId());
            }
        }

        if (CollectionUtils.isNotEmpty(updateProcessList)) {
            warningEventProcessMapper.updateBatch(updateProcessList);
        }
        if (CollectionUtils.isNotEmpty(updateEventIdList)) {
            warningEventMapper.batchUpdateOverFlag(updateEventIdList);
        }

        //超期提醒
        warningEventService.superviseWorkNotify(updateEventIdList, WarningSuperviseEnum.SUPERVISE_2);

    }

    public void warningEventThreeDayTask() {
        WarningEventMapper warningEventMapper = SpringUtils.getBean(WarningEventMapper.class);
        WarningEventServiceImpl warningEventService = SpringUtils.getBean(WarningEventServiceImpl.class);

        List<WarningEvent> needWarningList = Lists.newArrayList();
        List<WarningEvent> eventList = warningEventMapper.listWarningEventThreeDayTask();
        List<WarningEvent> occupyEventList = warningEventMapper.listWarningEventOccupyOverTime();
        needWarningList.addAll(eventList);
        needWarningList.addAll(occupyEventList);

        if (CollectionUtils.isEmpty(needWarningList)) {
            return;
        }

        List<Long> eventIdList = needWarningList.stream()
                .map(WarningEvent::getId)
                .collect(Collectors.toList());

        //超期提醒
        warningEventService.superviseWorkNotify(eventIdList, WarningSuperviseEnum.SUPERVISE_2);
    }

    public void deleteVideoWarningEvent() {
        WarningEventMapper warningEventMapper = SpringUtils.getBean(WarningEventMapper.class);
        WarningInfoMapper warningInfoMapper = SpringUtils.getBean(WarningInfoMapper.class);

        List<WarningEvent> eventList = warningEventMapper.listVideoEvent();
        if (CollectionUtils.isEmpty(eventList)) {
            return;
        }

        Long[] eventIdArray = eventList.stream()
                .map(WarningEvent::getId)
                .toArray(Long[]::new);

        Long[] warningIdArray = eventList.stream()
                .map(WarningEvent::getWarningId)
                .toArray(Long[]::new);

        warningEventMapper.deleteWarningEventByIds(eventIdArray);

        warningInfoMapper.deleteByIds(warningIdArray);
    }

}
