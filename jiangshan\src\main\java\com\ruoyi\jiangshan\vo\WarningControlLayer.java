package com.ruoyi.jiangshan.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.jiangshan.domain.DeviceValue;
import lombok.Data;

import java.util.List;

@Data
public class WarningControlLayer {

    private Long id;

    private String name;

    private String scene;

    private Integer warningLevel;

    private Boolean historyFlag;

    private String monitorValue;

    private String monitorUnit;

    private List<DeviceValue> valueList;

    private String lon;

    private String lat;

    /** 服务地址 */
//    @Excel(name = "服务地址")
    private String serviceUrl;

    /** 对象图片url */
//    @Excel(name = "对象图片url")
    private String fileUrl;

    /** 对象责任部门id */
//    @Excel(name = "对象责任部门id")
    private Long monitorDepartmentId;

    /** 对象责任部门名称 */
//    @Excel(name = "对象责任部门名称")
    private String monitorDepartmentName;

    private String scale;

    private String scaleUnit;

    /** 备注 */
    private String remark;
}
