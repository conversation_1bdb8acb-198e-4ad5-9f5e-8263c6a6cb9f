package com.ruoyi.jiangshan.util;

import java.util.regex.Pattern;

public class PasswordValidator {
    // 特殊字符集（可根据需求扩展）
    private static final String SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;:'\",.<>/?`~";

    /**
     * 密码强度验证
     * @param password 待验证的密码
     * @return 符合规则返回true，否则返回false
     */
    public static boolean validatePassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }

        // 定义验证规则：必须包含数字、大写字母、小写字母、特殊字符
        String pattern = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[" + Pattern.quote(SPECIAL_CHARS) + "]).{8,}$";

        // 验证字符组合复杂度
        boolean hasDigit = false;
        boolean hasUpper = false;
        boolean hasLower = false;
        boolean hasSpecial = false;

        for (char c : password.toCharArray()) {
            if (Character.isDigit(c)) hasDigit = true;
            else if (Character.isUpperCase(c)) hasUpper = true;
            else if (Character.isLowerCase(c)) hasLower = true;
            else if (SPECIAL_CHARS.indexOf(c) != -1) hasSpecial = true;
        }

        // 同时满足正则表达式和复杂度要求
        return Pattern.matches(pattern, password)
                && hasDigit
                && hasUpper
                && hasLower
                && hasSpecial;
    }

    // 测试用例
    public static void main(String[] args) {
        System.out.println(validatePassword("Aa123456!"));  // true
        System.out.println(validatePassword("abc123ABC"));  // false（缺少特殊字符）
        System.out.println(validatePassword("Aa!123"));     // false（长度不足）
        System.out.println(validatePassword("AAAAaaaa!!")); // false（缺少数字）
    }
}
