package com.ruoyi.jiangshan.domain;

import java.util.Date;

/**
 * 设备预警事件-流程场景部门关系
 */
public class WarningEventSceneDepartment {
    /**
     * 主键
     */
    private Long id;

    /**
     * 监测场景
     */
    private String monitorScene;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 部门name
     */
    private String departmentName;

    /**
     * 组织code
     */
    private String organizationCode;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户name
     */
    private String userName;

    /**
     * 浙政钉accountid
     */
    private String accountId;

    /**
     * 浙政钉employeeCode
     */
    private String employeeCode;

    /**
     * 预计完成时间，天
     */
    private String expectedCompleteDay;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMonitorScene() {
        return monitorScene;
    }

    public void setMonitorScene(String monitorScene) {
        this.monitorScene = monitorScene;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getExpectedCompleteDay() {
        return expectedCompleteDay;
    }

    public void setExpectedCompleteDay(String expectedCompleteDay) {
        this.expectedCompleteDay = expectedCompleteDay;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}