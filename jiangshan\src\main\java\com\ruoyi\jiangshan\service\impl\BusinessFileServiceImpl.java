package com.ruoyi.jiangshan.service.impl;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.jiangshan.domain.BusinessFile;
import com.ruoyi.jiangshan.mapper.BusinessFileMapper;
import com.ruoyi.jiangshan.service.BusinessFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

@Service
public class BusinessFileServiceImpl implements BusinessFileService {
    @Autowired
    private BusinessFileMapper businessFileMapper;
    @Value("${file.uploadUrl}")
    private String fileUploadUrl;

    @Override
    public BusinessFile uploadFile(MultipartFile file) {
        try {
            String filePath = RuoYiConfig.getUploadPath();
            String fileName = FileUploadUtils.upload(filePath, file);

            fileName = fileName.replaceAll("\\[", "%5B");

            String url = fileUploadUrl + fileName;
            BusinessFile businessFile = new BusinessFile();
//            businessFile.setBusinessType(type);
            businessFile.setFileName(FileUtils.getName(fileName));
            businessFile.setFilePath(fileName);
            businessFile.setFileUrl(url);
            businessFile.setCreateBy(SecurityUtils.getUserId() + "");
            businessFile.setCreateTime(new Date());

            businessFileMapper.insertSelective(businessFile);

            return businessFile;
        } catch (Exception e) {
            throw new RuntimeException("上传文件失败, msg:" + e.getMessage());
        }
    }

    @Override
    public BusinessFile uploadFile(MultipartFile file, Long id, Integer type) {
        try {
            String filePath = RuoYiConfig.getUploadPath();
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = fileUploadUrl + fileName;
            BusinessFile businessFile = new BusinessFile();
//            businessFile.setBusinessType(type);
            businessFile.setFileName(FileUtils.getName(fileName));
            businessFile.setFilePath(fileName);
            businessFile.setFileUrl(url);
            businessFile.setCreateBy(SecurityUtils.getUserId() + "");
            businessFile.setCreateTime(new Date());
            businessFile.setBusinessId(id);
            businessFile.setBusinessType(type);

            businessFileMapper.insertSelective(businessFile);

            return businessFile;
        } catch (Exception e) {
            throw new RuntimeException("上传文件失败, msg:" + e.getMessage());
        }
    }
}
