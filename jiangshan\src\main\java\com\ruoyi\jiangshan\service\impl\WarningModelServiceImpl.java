package com.ruoyi.jiangshan.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jiangshan.convert.WarningConvert;
import com.ruoyi.jiangshan.domain.WarningRule;
import com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin;
import com.ruoyi.jiangshan.domain.WarningRuleShuxin;
import com.ruoyi.jiangshan.mapper.WarningRuleConditionShuxinMapper;
import com.ruoyi.jiangshan.mapper.WarningRuleShuxinMapper;
import com.ruoyi.jiangshan.openapi.vo.WarningEventThresholdSaveVO;
import com.ruoyi.system.mapper.SysUserMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.jiangshan.mapper.WarningModelMapper;
import com.ruoyi.jiangshan.domain.WarningModel;
import com.ruoyi.jiangshan.service.IWarningModelService;

/**
 * 设备模型Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class WarningModelServiceImpl implements IWarningModelService
{
    @Autowired
    private WarningModelMapper warningModelMapper;
    @Autowired
    private WarningRuleShuxinMapper warningRuleShuxinMapper;
    @Autowired
    private WarningRuleConditionShuxinMapper warningRuleConditionShuxinMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询设备模型
     *
     * @param id 设备模型主键
     * @return 设备模型
     */
    @Override
    public WarningModel selectWarningModelById(Long id)
    {
        return warningModelMapper.selectWarningModelById(id);
    }

    /**
     * 查询设备模型列表
     *
     * @param warningModel 设备模型
     * @return 设备模型
     */
    @Override
    public List<WarningModel> selectWarningModelList(WarningModel warningModel)
    {
        return warningModelMapper.selectWarningModelList(warningModel);
    }

    /**
     * 新增设备模型
     *
     * @param warningModel 设备模型
     * @return 结果
     */
    @Override
    public int insertWarningModel(WarningModel warningModel)
    {
        warningModel.setCreateTime(DateUtils.getNowDate());
        return warningModelMapper.insertWarningModel(warningModel);
    }

    /**
     * 修改设备模型
     *
     * @param warningModel 设备模型
     * @return 结果
     */
    @Override
    public int updateWarningModel(WarningModel warningModel)
    {
        warningModel.setUpdateTime(DateUtils.getNowDate());
        return warningModelMapper.updateWarningModel(warningModel);
    }

    /**
     * 批量删除设备模型
     *
     * @param ids 需要删除的设备模型主键
     * @return 结果
     */
    @Override
    public int deleteWarningModelByIds(Long[] ids)
    {
        return warningModelMapper.deleteWarningModelByIds(ids);
    }

    /**
     * 删除设备模型信息
     *
     * @param id 设备模型主键
     * @return 结果
     */
    @Override
    public int deleteWarningModelById(Long id)
    {
        return warningModelMapper.deleteWarningModelById(id);
    }

    @Override
    public int saveSxModel(WarningRuleShuxin warningRuleShuxin) {
        if (Objects.isNull(warningRuleShuxin.getId())) {
            //新增
//            warningRuleShuxin.setCreateTime(new Date());
//            warningRuleShuxin.setCreateBy(SecurityUtils.getUsername());
//            warningRuleShuxinMapper.insertSelective(warningRuleShuxin);
//
//            if (CollectionUtils.isNotEmpty(warningRuleShuxin.getConditionList())) {
//                warningRuleConditionShuxinMapper.batchInsert(warningRuleShuxin.getConditionList());
//            }
        } else {
            //更新
            WarningModel warningModel = new WarningModel();
            warningModel.setId(warningRuleShuxin.getId());
            warningModel.setModelName(warningRuleShuxin.getModelName());
            warningModel.setStatus(warningRuleShuxin.getStatus());
            warningModel.setUpdateTime(new Date());
            warningModel.setUpdateBy(SecurityUtils.getUsername());

            warningModelMapper.updateWarningModel(warningModel);

            warningRuleShuxin.setUpdateTime(new Date());
            warningRuleShuxin.setUpdateBy(SecurityUtils.getUsername());

            fillOtherUserIdList(warningRuleShuxin);

            warningRuleShuxinMapper.updateByPrimaryKeySelectiveByModelId(warningRuleShuxin);

            if (CollectionUtils.isNotEmpty(warningRuleShuxin.getConditionList())) {
                warningRuleConditionShuxinMapper.updateBatch(warningRuleShuxin.getConditionList());
            }
        }

        return 1;
    }

    private void fillOtherUserId(List<WarningRuleShuxin> warningRuleList) {
        if (CollectionUtils.isEmpty(warningRuleList)) {
            return;
        }
        for (WarningRuleShuxin warningRule : warningRuleList) {
            if (StringUtils.isNotBlank(warningRule.getOtherUserId())) {
                List<String> otherUserIdList = Arrays.asList(StringUtils.split(warningRule.getOtherUserId(), ","));

                warningRule.setOtherUserIdList(otherUserIdList);
            }
        }
    }

    private void fillOtherUserIdList(WarningRuleShuxin warningRule) {
        List<String> userIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(warningRule.getOtherUserIdList())) {
            userIdList = warningRule.getOtherUserIdList();

            String otherUserIdStr = StringUtils.join(userIdList, ",");
            warningRule.setOtherUserId(otherUserIdStr);

            userIdList.add(warningRule.getWarningUserId() + "");
        } else {
            userIdList = Lists.newArrayList(warningRule.getWarningUserId() + "");
        }

        List<String> accountList = sysUserMapper.listAccountByIds(userIdList);

        List<String> accountIdList = accountList.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        warningRule.setAllAccountId(StringUtils.join(accountIdList, ","));

        List<String> mobileList = sysUserMapper.listMobileByIds(userIdList);

        warningRule.setAllMobile(StringUtils.join(mobileList, ","));

        if (StringUtils.isBlank(warningRule.getAllAccountId())) {
            warningRule.setAllAccountId(null);
        }
        if (StringUtils.isBlank(warningRule.getAllMobile())) {
            warningRule.setAllMobile(null);
        }
    }

    @Override
    public WarningRuleShuxin getSxModel(Long id) {
        WarningModel warningModel = warningModelMapper.selectWarningModelById(id);
        if (Objects.isNull(warningModel)) {
            throw new RuntimeException("模型不存在");
        }
        WarningRuleShuxin warningRuleShuxin = warningRuleShuxinMapper.getByModelId(id);
        if (Objects.isNull(warningRuleShuxin)) {
            throw new RuntimeException("模型阈值不存在");
        }

        warningRuleShuxin.setId(warningModel.getId());
        warningRuleShuxin.setModelName(warningModel.getModelName());
        warningRuleShuxin.setStatus(warningModel.getStatus());

        fillOtherUserId(Lists.newArrayList(warningRuleShuxin));

        List<WarningRuleConditionShuxin> conditionShuxinList = warningRuleConditionShuxinMapper.listByModelId(id);

        warningRuleShuxin.setConditionList(conditionShuxinList);

        return warningRuleShuxin;
    }

    @Override
    public List<String> getArea() {
        return warningRuleConditionShuxinMapper.getArea();
    }

    @Override
    public String saveThreshold(WarningEventThresholdSaveVO saveVO) {
        WarningRuleConditionShuxin exist = warningRuleConditionShuxinMapper.getByFxqbh(saveVO.getFxqbh());

        WarningRuleConditionShuxin warningRuleConditionShuxin = WarningConvert.INSTANCE.openApi2ConditionShuxin(saveVO);

        if (Objects.isNull(exist)) {
            warningRuleConditionShuxin.setRuleId(1L);
            warningRuleConditionShuxin.setModelId(1L);
            warningRuleConditionShuxinMapper.insertSelective(warningRuleConditionShuxin);
        } else {
            warningRuleConditionShuxinMapper.updateByFxqbh(warningRuleConditionShuxin);
        }

        return saveVO.getFxqbh();
    }
}
