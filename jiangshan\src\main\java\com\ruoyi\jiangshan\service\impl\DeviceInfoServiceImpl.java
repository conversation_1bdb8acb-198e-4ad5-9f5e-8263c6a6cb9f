package com.ruoyi.jiangshan.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.jiangshan.convert.DeviceConvert;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.enums.FileTypeEnum;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.openapi.vo.DeviceInfoCountOpenApiVO;
import com.ruoyi.jiangshan.service.BusinessFileService;
import com.ruoyi.jiangshan.vo.BusinessSaveFileVO;
import com.ruoyi.jiangshan.vo.DeviceInfoImportVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.jiangshan.service.IDeviceInfoService;

/**
 * 设备信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class DeviceInfoServiceImpl implements IDeviceInfoService
{
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private DeviceValueMapper deviceValueMapper;
    @Autowired
    private DeviceInfoFavoriteMapper deviceInfoFavoriteMapper;
    @Autowired
    private DeviceInfoTypeMapper deviceInfoTypeMapper;
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Autowired
    private BusinessFileMapper businessFileMapper;

    /**
     * 查询设备信息
     *
     * @param id 设备信息主键
     * @return 设备信息
     */
    @Override
    public DeviceInfo selectDeviceInfoById(Long id)
    {
        DeviceInfo deviceInfo = deviceInfoMapper.selectDeviceInfoById(id, SecurityUtils.getUserId());

        List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceThirdId(deviceInfo.getDeviceThirdId());

        deviceInfo.setValueList(deviceValueList);

        fillFileList(Lists.newArrayList(deviceInfo));

        return deviceInfo;
    }

    /**
     * 查询设备信息列表
     *
     * @param deviceInfo 设备信息
     * @return 设备信息
     */
    @Override
    public List<DeviceInfo> selectDeviceInfoList(DeviceInfo deviceInfo)
    {
        deviceInfo.setCreateBy(SecurityUtils.getUserId() + "");

        List<DeviceInfo> deviceInfoList = deviceInfoMapper.selectDeviceInfoList(deviceInfo);

        if (CollectionUtils.isEmpty(deviceInfoList)) {
            return Lists.newArrayList();
        }

        List<String> deviceInfoThirdIdList = deviceInfoList.stream()
                .map(DeviceInfo::getDeviceThirdId)
                .collect(Collectors.toList());

        List<DeviceValue> deviceValueList = deviceValueMapper.listByDeviceThirdIdList(deviceInfoThirdIdList, 7);

        Map<String, List<DeviceValue>> deviceValueMap = deviceValueList.stream()
                .collect(Collectors.groupingBy(DeviceValue::getDeviceThirdId));

        for (DeviceInfo info : deviceInfoList) {
            List<DeviceValue> deviceValueOneList = deviceValueMap.get(info.getDeviceThirdId());

            info.setValueList(deviceValueOneList);
        }

        fillFileList(deviceInfoList);

        return deviceInfoList;
    }

    private void fillFileList(List<DeviceInfo> deviceInfoList) {
        if (CollectionUtils.isEmpty(deviceInfoList)) {
            return;
        }

        List<Long> deviceIdList = deviceInfoList.stream()
                .map(DeviceInfo::getId)
                .collect(Collectors.toList());

        List<BusinessFile> fileList = businessFileMapper.listByBusinessIdListAndType(deviceIdList, FileTypeEnum.TYPE_06.getCode());

        if (CollectionUtils.isEmpty(fileList)) {
            return;
        }

        Map<Long, List<BusinessFile>> fileMap = fileList.stream()
                .collect(Collectors.groupingBy(BusinessFile::getBusinessId));

        for (DeviceInfo info : deviceInfoList) {
            List<BusinessFile> businessFileList = fileMap.get(info.getId());
            info.setFileList(businessFileList);
        }
    }

    /**
     * 新增设备信息
     *
     * @param deviceInfo 设备信息
     * @return 结果
     */
    @Override
    public int insertDeviceInfo(DeviceInfo deviceInfo)
    {
        deviceInfo.setCreateTime(DateUtils.getNowDate());
        return deviceInfoMapper.insertDeviceInfo(deviceInfo);
    }

    /**
     * 修改设备信息
     *
     * @param deviceInfo 设备信息
     * @return 结果
     */
    @Override
    public int updateDeviceInfo(DeviceInfo deviceInfo)
    {
        deviceInfo.setUpdateTime(DateUtils.getNowDate());
        return deviceInfoMapper.updateDeviceInfo(deviceInfo);
    }

    /**
     * 批量删除设备信息
     *
     * @param ids 需要删除的设备信息主键
     * @return 结果
     */
    @Override
    public int deleteDeviceInfoByIds(Long[] ids)
    {
        return deviceInfoMapper.deleteDeviceInfoByIds(ids);
    }

    /**
     * 删除设备信息信息
     *
     * @param id 设备信息主键
     * @return 结果
     */
    @Override
    public int deleteDeviceInfoById(Long id)
    {
        return deviceInfoMapper.deleteDeviceInfoById(id);
    }

    @Override
    public int updateFavorite(Long id) {
        Long userId = SecurityUtils.getUserId();

        DeviceInfoFavorite exist = deviceInfoFavoriteMapper.getByUserId(id, userId);
        if (Objects.nonNull(exist)) {
            throw new RuntimeException("请勿重复收藏");
        }

        DeviceInfoFavorite deviceInfoFavorite = new DeviceInfoFavorite();
        deviceInfoFavorite.setDeviceId(id);
        deviceInfoFavorite.setCreateBy(SecurityUtils.getUserId() + "");
        deviceInfoFavorite.setCreateTime(new Date());
        deviceInfoFavorite.setUserId(SecurityUtils.getUserId());

        deviceInfoFavoriteMapper.insertSelective(deviceInfoFavorite);

        return 1;
    }

    @Override
    public int updateCancelFavorite(Long id) {
        Long userId = SecurityUtils.getUserId();

        DeviceInfoFavorite exist = deviceInfoFavoriteMapper.getByUserId(id, userId);
        if (Objects.isNull(exist)) {
            throw new RuntimeException("未收藏");
        }

        deviceInfoFavoriteMapper.deleteByUserId(id, userId);

        return 1;
    }

    @Override
    public List<DeviceInfo> getByScene(String scene) {
        return deviceInfoMapper.listByScene(scene);
    }

    @Override
    public List<String> getDeviceType(String scene) {
        return deviceInfoTypeMapper.listByScene(scene);
    }

    @Override
    public void excelImport(List<DeviceInfoImportVO> list) {
        List<DeviceInfo> deviceInfoList = Lists.newArrayList();
        for (DeviceInfoImportVO deviceInfoImportVO : list) {
            DeviceInfo existDevice = deviceInfoMapper.getByThirdId(deviceInfoImportVO.getDeviceThirdId());
            if (Objects.nonNull(existDevice)) {
                continue;
            }

            DeviceInfo deviceInfo = DeviceConvert.INSTANCE.excelVo2Do(deviceInfoImportVO);

            deviceInfo.setDeviceNum(deviceInfoImportVO.getDeviceThirdId());
            deviceInfo.setOpenFlag(0);

            DeviceMonitor deviceMonitor = deviceMonitorMapper.getByName(deviceInfoImportVO.getMonitorName());
            if (Objects.nonNull(deviceMonitor)) {
                deviceInfo.setMonitorId(deviceMonitor.getId());
            }

            deviceInfo.setCreateBy(SecurityUtils.getUsername());
            deviceInfo.setCreateTime(new Date());

            deviceInfoList.add(deviceInfo);
        }

        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
            deviceInfoMapper.batchInsert(deviceInfoList);
        }
    }

    @Override
    public List<String> getDeviceName(String name) {
        return deviceInfoMapper.getDeviceName(name);
    }

    @Override
    public void uploadPicture(BusinessSaveFileVO saveFileVO) {
        if (CollectionUtils.isEmpty(saveFileVO.getFileList())) {
            return;
        }

        businessFileMapper.deleteByBusinessIdAndTypeList(saveFileVO.getId(),
                Lists.newArrayList(FileTypeEnum.TYPE_06.getCode()));

        for (BusinessFile businessFile : saveFileVO.getFileList()) {
            businessFile.setBusinessId(saveFileVO.getId());
            businessFile.setBusinessType(FileTypeEnum.TYPE_06.getCode());
        }

        businessFileMapper.updateBatch(saveFileVO.getFileList());
    }

    @Override
    public List<DeviceInfo> selectDeviceInfoListV2(DeviceInfo deviceInfo) {
        List<DeviceInfo> deviceInfoList = deviceInfoMapper.selectDeviceInfoList(deviceInfo);

        return deviceInfoList;
    }

    @Override
    public DeviceInfoCountOpenApiVO countNum() {
        DeviceInfoCountOpenApiVO result = new DeviceInfoCountOpenApiVO();

        result.setTotalCount(deviceInfoMapper.countByStatusAndSource(null, null));
        result.setTotalOnlineCount(deviceInfoMapper.countByStatusAndSource(null, 1));
        result.setTotalInlineCount(deviceInfoMapper.countByStatusAndSource(null, 0));
        result.setSelfCount(deviceInfoMapper.countByStatusAndSource("自建", null));
        result.setSelfOnlineCount(deviceInfoMapper.countByStatusAndSource("自建", 1));
        result.setSelfInlineCount(deviceInfoMapper.countByStatusAndSource("自建", 0));
        result.setStockCount(deviceInfoMapper.countByStatusAndSource("存量", null));
        result.setStockOnlineCount(deviceInfoMapper.countByStatusAndSource("存量", 1));
        result.setStockInlineCount(deviceInfoMapper.countByStatusAndSource("存量", 0));

        return result;
    }

}
