package com.ruoyi.jiangshan.vo.cityrisk;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.jiangshan.vo.CityRiskLineVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CityRiskScreenVO {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date warningTime;

    private Integer warningCount;

    private Integer warningLevel;

    private List<CityRiskWarningCountVO> countList;

    private Long warningIndicator;

    private List<CityRiskLineVO> lineList;
}
