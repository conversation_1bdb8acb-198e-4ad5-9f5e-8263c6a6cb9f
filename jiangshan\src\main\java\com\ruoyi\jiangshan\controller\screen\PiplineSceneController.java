package com.ruoyi.jiangshan.controller.screen;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.service.PiplineSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 污水管线驾驶舱
 */
@RestController
@RequestMapping("/pipline")
public class PiplineSceneController {

    @Autowired
    private PiplineSceneService piplineSceneService;

    /**
     * 污水管线详情
     * @return
     */
    @GetMapping("/detail")
    public AjaxResult getPiplineDetail() {
        return AjaxResult.success(piplineSceneService.getPiplineDetail());
    }

    /**
     * 根据设备类型获取所有设备
     * @return
     */
    @GetMapping("/getDevice")
    public AjaxResult getDevice(String deviceType) {
        return AjaxResult.success(piplineSceneService.getDevice(deviceType));
    }

    /**
     * 根据场景获取所有设备
     * @return
     */
    @GetMapping("/getDeviceByScene")
    public AjaxResult getDeviceByScene(String scene) {
        return AjaxResult.success(piplineSceneService.getDeviceByScene(scene));
    }

    /**
     * 安全风险统计
     * @return
     */
    @GetMapping("/safetyCount")
    public AjaxResult getSafetyCount(String dateStr) {
        return AjaxResult.success(piplineSceneService.getSafetyCount(dateStr));
    }

    /**
     * 安全风险事件排序
     * @return
     */
    @GetMapping("/event")
    public AjaxResult getEvent(String eventName, Integer status, Integer warningLevel) {
        return AjaxResult.success(piplineSceneService.getEvent(eventName, status, warningLevel));
    }

    /**
     * 实时预警
     * @return
     */
    @GetMapping("/warning")
    public AjaxResult getWarning(Integer warningLevel) {
        return AjaxResult.success(piplineSceneService.getWarning(warningLevel));
    }

    /**
     * 流量监控
     * @return
     */
    @GetMapping("/flowMonitor")
    public AjaxResult getFlowMonitor(String dateStr, Long deviceId) {
        return AjaxResult.success(piplineSceneService.getFlowMonitor(dateStr, deviceId));
    }

    /**
     * 液位监控
     * @return
     */
    @GetMapping("/levelMonitor")
    public AjaxResult getLevelMonitor(String dateStr, Long deviceId) {
        return AjaxResult.success(piplineSceneService.getLevelMonitor(dateStr, deviceId));
    }

}
