<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningEventMapper">

    <resultMap type="WarningEvent" id="WarningEventResult">
        <result property="id"    column="id"    />
        <result property="warningId"    column="warning_id"    />
        <result property="eventThirdId"    column="event_third_id"    />
        <result property="eventName"    column="event_name"    />
        <result property="eventType"    column="event_type"    />
        <result property="departmentId"    column="department_id"    />
        <result property="departmentName"    column="department_name"    />
        <result property="eventAddress"    column="event_address"    />
        <result property="warningDetail"    column="warning_detail"    />
        <result property="warningLevel"    column="warning_level"    />
        <result property="warningTime"    column="warning_time"    />
        <result property="deliveryTime"    column="delivery_time"    />
        <result property="rejectReason"    column="reject_reason"    />
        <result property="status"    column="status"    />
        <result property="stopFlag"    column="stop_flag"    />
        <result property="overFlag"    column="over_flag"    />
        <result property="superviseFlag"    column="supervise_flag"    />
        <result property="superviseType"    column="supervise_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="lon"    column="lon"    />
        <result property="lat"    column="lat"    />
        <result property="leaderContent"    column="leader_content"    />
        <result property="eventStreet"    column="event_street"    />
        <result property="eventStreetAreaCode"    column="event_street_area_code"    />
        <result property="deviceThirdId"    column="device_third_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="expectedCompleteTime"    column="expected_complete_time"    />
        <result property="bizCode"    column="biz_code"    />
        <result property="qrCode"    column="qr_code"    />
        <result property="occupyRange"    column="occupy_range"    />
        <result property="firstWarningTime"    column="first_warning_time"    />
    </resultMap>

    <sql id="selectWarningEventVo">
        select distinct (t1.id), warning_id, event_third_id, event_name, event_type, t1.department_id, t1.department_name,
               event_address, warning_detail, warning_level, warning_time, t1.delivery_time, reject_reason,
               status, stop_flag, over_flag, supervise_flag, supervise_type, t1.create_time, t1.create_by,
               update_time, update_by, lon, lat, leader_content, event_street, event_street_area_code,
               device_third_id, device_name, t1.expected_complete_time, biz_code, qr_code, occupy_range, t1.first_warning_time
               from t_warning_event t1
               left join t_warning_event_department t2
               on t1.id = t2.event_id
    </sql>

    <select id="selectWarningEventList" parameterType="WarningEvent" resultMap="WarningEventResult">
        <include refid="selectWarningEventVo"/>
        <where>
            and
            (device_third_id not in ('f483fb9636da4aed8a45a25299e33e61',
            '3607848c77b74fecbd847eec72cfa722',
            '7215c6061f9845609329419fe7b6f0d7',
            'f86958db70df4ed6803fd78043cf8128',
            '18a4596643714933a311732cd2113948',
            '1712dd80b1cf4a0d83ecf24444a6cfc1',
            'f8f314f54e004b8b96fa64b63ad5b03b',
            'cfd568df256c4cc0a91d3a8e27e77ad1',
            '2790920b776140faad4ff6e71ad24665')
            or
            (device_third_id in ('f483fb9636da4aed8a45a25299e33e61',
            '3607848c77b74fecbd847eec72cfa722',
            '7215c6061f9845609329419fe7b6f0d7',
            'f86958db70df4ed6803fd78043cf8128',
            '18a4596643714933a311732cd2113948',
            '1712dd80b1cf4a0d83ecf24444a6cfc1',
            'f8f314f54e004b8b96fa64b63ad5b03b',
            'cfd568df256c4cc0a91d3a8e27e77ad1',
            '2790920b776140faad4ff6e71ad24665') and warning_time <![CDATA[ >= ]]> NOW() - INTERVAL 24 HOUR)
            )
            <if test="eventName != null  and eventName != ''"> and event_name like concat('%', #{eventName}, '%')</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
            <if test="departmentId != null "> and t1.department_id = #{departmentId}</if>
            <if test="warningLevel != null "> and warning_level = #{warningLevel}</if>
            <if test="warningStartTime != null">
                and warning_time <![CDATA[ >= ]]> #{warningStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="warningEndTime != null">
                and warning_time <![CDATA[ <= ]]> #{warningEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=INTEGER}
            </if>
            <if test="statusList != null and statusList.size() != 0">
                and status in
                <foreach item="item" collection="statusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="eventStreetAreaCode != null and eventStreetAreaCode != ''">
                and event_street_area_code = #{eventStreetAreaCode,jdbcType=VARCHAR}
            </if>
            <if test="eventStreet != null and eventStreet != ''">
                and event_street like concat('%', #{eventStreet,jdbcType=VARCHAR}, '%')
            </if>
            <if test="permissionFlag == 1">
                and t2.account_id = #{operateUserId,jdbcType=VARCHAR}
            </if>
            <if test="sceneList != null and sceneList.size() != 0">
                and event_type in
                <foreach item="item" collection="sceneList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by warning_time desc, status
    </select>

    <select id="selectWarningEventById" parameterType="Long" resultMap="WarningEventResult">
        <include refid="selectWarningEventVo"/>
        where t1.id = #{id}
    </select>

    <insert id="insertWarningEvent" parameterType="WarningEvent" useGeneratedKeys="true" keyProperty="id">
        insert into t_warning_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warningId != null">warning_id,</if>
            <if test="eventThirdId != null">event_third_id,</if>
            <if test="eventName != null">event_name,</if>
            <if test="eventType != null">event_type,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="departmentName != null">department_name,</if>
            <if test="eventAddress != null">event_address,</if>
            <if test="warningDetail != null">warning_detail,</if>
            <if test="warningLevel != null">warning_level,</if>
            <if test="warningTime != null">warning_time,</if>
            <if test="deliveryTime != null">delivery_time,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="status != null">status,</if>
            <if test="stopFlag != null">stop_flag,</if>
            <if test="overFlag != null">over_flag,</if>
            <if test="superviseFlag != null">supervise_flag,</if>
            <if test="superviseType != null">supervise_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="lon != null">lon,</if>
            <if test="lat != null">lat,</if>
            <if test="leaderContent != null">leader_content,</if>
            <if test="eventStreet != null">event_street,</if>
            <if test="eventStreetAreaCode != null">event_street_area_code,</if>
            <if test="deviceThirdId != null">device_third_id,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="expectedCompleteTime != null">expected_complete_time,</if>
            <if test="bizCode != null">biz_code,</if>
            <if test="occupyRange != null">occupy_range,</if>
            <if test="firstWarningTime != null">first_warning_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warningId != null">#{warningId},</if>
            <if test="eventThirdId != null">#{eventThirdId},</if>
            <if test="eventName != null">#{eventName},</if>
            <if test="eventType != null">#{eventType},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="departmentName != null">#{departmentName},</if>
            <if test="eventAddress != null">#{eventAddress},</if>
            <if test="warningDetail != null">#{warningDetail},</if>
            <if test="warningLevel != null">#{warningLevel},</if>
            <if test="warningTime != null">#{warningTime},</if>
            <if test="deliveryTime != null">#{deliveryTime},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="status != null">#{status},</if>
            <if test="stopFlag != null">#{stopFlag},</if>
            <if test="overFlag != null">#{overFlag},</if>
            <if test="superviseFlag != null">#{superviseFlag},</if>
            <if test="superviseType != null">#{superviseType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="lon != null">#{lon},</if>
            <if test="lat != null">#{lat},</if>
            <if test="leaderContent != null">#{leaderContent},</if>
            <if test="eventStreet != null">#{eventStreet},</if>
            <if test="eventStreetAreaCode != null">#{eventStreetAreaCode},</if>
            <if test="deviceThirdId != null">#{deviceThirdId},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="expectedCompleteTime != null">#{expectedCompleteTime},</if>
            <if test="bizCode != null">#{bizCode},</if>
            <if test="occupyRange != null">#{occupyRange},</if>
            <if test="firstWarningTime != null">#{firstWarningTime},</if>
         </trim>
    </insert>

    <update id="updateWarningEvent" parameterType="WarningEvent">
        update t_warning_event
        <trim prefix="SET" suffixOverrides=",">
            <if test="warningId != null">warning_id = #{warningId},</if>
            <if test="eventThirdId != null">event_third_id = #{eventThirdId},</if>
            <if test="eventName != null">event_name = #{eventName},</if>
            <if test="eventType != null">event_type = #{eventType},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="departmentName != null">department_name = #{departmentName},</if>
            <if test="eventAddress != null">event_address = #{eventAddress},</if>
            <if test="warningDetail != null">warning_detail = #{warningDetail},</if>
            <if test="warningLevel != null">warning_level = #{warningLevel},</if>
            <if test="warningTime != null">warning_time = #{warningTime},</if>
            <if test="deliveryTime != null">delivery_time = #{deliveryTime},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="status != null">status = #{status},</if>
            <if test="stopFlag != null">stop_flag = #{stopFlag},</if>
            <if test="overFlag != null">over_flag = #{overFlag},</if>
            <if test="superviseFlag != null">supervise_flag = #{superviseFlag},</if>
            <if test="superviseType != null">supervise_type = #{superviseType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="leaderContent != null">leader_content = #{leaderContent,jdbcType=VARCHAR},</if>
            <if test="eventStreet != null">event_street = #{eventStreet,jdbcType=VARCHAR},</if>
            <if test="eventStreetAreaCode != null">event_street_area_code = #{eventStreetAreaCode,jdbcType=VARCHAR},</if>
            <if test="deviceThirdId != null">device_third_id = #{deviceThirdId,jdbcType=VARCHAR},</if>
            <if test="deviceName != null">device_name = #{deviceName,jdbcType=VARCHAR},</if>
            <if test="expectedCompleteTime != null">expected_complete_time = #{expectedCompleteTime,jdbcType=TIMESTAMP},</if>
            <if test="bizCode != null">biz_code = #{bizCode,jdbcType=VARCHAR},</if>
            <if test="qrCode != null">qr_code = #{qrCode,jdbcType=VARCHAR},</if>
            <if test="occupyRange != null">occupy_range = #{occupyRange,jdbcType=VARCHAR},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWarningEventById" parameterType="Long">
        delete from t_warning_event where id = #{id}
    </delete>

    <delete id="deleteWarningEventByIds" parameterType="String">
        delete from t_warning_event where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByStatus" resultType="long">
        select count(distinct t1.id)
        from t_warning_event t1
        left join t_warning_event_supervise t2
        on t1.id = t2.event_id
        where 1=1
        <if test="statusList != null and statusList.size() != 0">
            and status in
            <foreach item="status" collection="statusList" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="overFlag != null">
            and over_flag = #{overFlag,jdbcType=BOOLEAN}
        </if>
        <if test="superviseFlag != null">
            and t2.id is not null
        </if>
    </select>

    <select id="listLastFourWarning" resultMap="WarningEventResult">
        <include refid="selectWarningEventVo">
        </include>
        where status not in (5, 6)
        and stop_flag = 0
        order by warning_time desc
        limit 4
    </select>

    <select id="countAll" resultType="long">
        select count(1)
        from t_warning_event
    </select>

    <select id="countByDepartment" resultType="com.ruoyi.jiangshan.vo.BusinessPercentCountVO">
        select department_name as `key`, count(1) as value
        from t_warning_event
        where 1=1
        <if test="overFlag != null">
            and over_flag = #{overFlag,jdbcType=BOOLEAN}
        </if>
        group by department_name
    </select>

    <select id="countLevelBySceneAndTime" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
        select warning_level as `key`, count(1) as value
        from t_warning_event
        where 1=1
        <if test="scene != null">
            and event_type = #{scene}
        </if>
        <if test="startTime != null">
            and warning_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and warning_time <![CDATA[ <= ]]> #{endTime}
        </if>
        group by warning_level
    </select>

    <select id="listByConditionLimit" resultMap="WarningEventResult">
        <include refid="selectWarningEventVo">
        </include>
        where 1=1
        <if test="scene != null and scene != ''">
            and event_type = #{scene}
        </if>
        <if test="eventName != null and eventName != ''">
            and event_name like concat('%', #{eventName}, '%')
        </if>
        <if test="status != null">
            and status = #{status,jdbcType=INTEGER}
        </if>
        <if test="warningLevel != null">
            and warning_level = #{warningLevel,jdbcType=INTEGER}
        </if>
        order by warning_time desc
        limit ${limitNum}
    </select>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into t_warning_event
        (warning_id, event_third_id, event_name, event_type, department_id, department_name,
        event_address, warning_detail, warning_level, warning_time, delivery_time, reject_reason,
        `status`, stop_flag, over_flag, supervise_flag, supervise_type, create_time, create_by,
        update_time, update_by, lon, lat, leader_content, event_street, event_street_area_code,
        device_third_id, device_name, expected_complete_time, biz_code, first_warning_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.warningId,jdbcType=BIGINT}, #{item.eventThirdId,jdbcType=VARCHAR}, #{item.eventName,jdbcType=VARCHAR},
            #{item.eventType,jdbcType=VARCHAR}, #{item.departmentId,jdbcType=BIGINT}, #{item.departmentName,jdbcType=VARCHAR},
            #{item.eventAddress,jdbcType=VARCHAR}, #{item.warningDetail,jdbcType=VARCHAR},
            #{item.warningLevel,jdbcType=BOOLEAN}, #{item.warningTime,jdbcType=TIMESTAMP},
            #{item.deliveryTime,jdbcType=TIMESTAMP}, #{item.rejectReason,jdbcType=VARCHAR},
            #{item.status,jdbcType=BOOLEAN}, #{item.stopFlag,jdbcType=BOOLEAN}, #{item.overFlag,jdbcType=BOOLEAN},
            #{item.superviseFlag,jdbcType=BOOLEAN}, #{item.superviseType,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.lon,jdbcType=VARCHAR}, #{item.lat,jdbcType=VARCHAR},
            #{item.leaderContent,jdbcType=VARCHAR}, #{item.eventStreet,jdbcType=VARCHAR},
            #{item.eventStreetAreaCode,jdbcType=VARCHAR}, #{item.deviceThirdId}, #{item.deviceName},
            #{item.expectedCompleteTime,jdbcType=TIMESTAMP}, #{item.bizCode,jdbcType=VARCHAR},
            #{item.firstWarningTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="listAllNonOvertime" resultMap="WarningEventResult">
        <include refid="selectWarningEventVo">
        </include>
        where over_flag = false
    </select>

    <select id="listAllWarningOneDayInner" resultMap="WarningEventResult">
        WITH RankedDeviceValues AS (
        SELECT
        distinct id, warning_id, event_third_id, event_name, event_type, department_id, department_name,
        event_address, warning_detail, warning_level, warning_time, delivery_time, reject_reason,
        status, stop_flag, over_flag, supervise_flag, supervise_type, create_time, create_by,
        update_time, update_by, lon, lat, leader_content, event_street, event_street_area_code,
        device_third_id, device_name, expected_complete_time, biz_code,
        ROW_NUMBER() OVER (PARTITION BY lat, lon ORDER BY warning_time DESC) AS rn
        FROM
        t_warning_event
        where 1=1
        and status not in (5, 6)
        and warning_time <![CDATA[ >= ]]> NOW() - INTERVAL 3 DAY
        <if test="scene != null and scene != ''">
            and event_type = #{scene,jdbcType=VARCHAR}
        </if>
        <if test="warningLevelList != null and warningLevelList.size() > 0">
            and warning_level in
            <foreach collection="warningLevelList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        )
        SELECT
        *
        FROM
        RankedDeviceValues t1
        left join t_warning_event_department t2
        on t1.id = t2.event_id
        WHERE
        rn = 1
        order by warning_time desc
        ;

<!--        select distinct (t1.id), warning_id, event_third_id, event_name, event_type, t1.department_id, t1.department_name,-->
<!--        event_address, warning_detail, warning_level, max(warning_time) as warning_time, t1.delivery_time, reject_reason,-->
<!--        status, stop_flag, over_flag, supervise_flag, supervise_type, t1.create_time, t1.create_by,-->
<!--        update_time, update_by, lon, lat, leader_content, event_street, event_street_area_code,-->
<!--        device_third_id, device_name, t1.expected_complete_time from-->
<!--        (select * from t_warning_event order by warning_time desc) t1-->
<!--        left join t_warning_event_department t2-->
<!--        on t1.id = t2.event_id-->
<!--        where status != 4-->
<!--        <if test="scene != null and scene != ''">-->
<!--            and event_type = #{scene,jdbcType=VARCHAR}-->
<!--        </if>-->
<!--        group by lon, lat-->
    </select>

    <select id="listByEventType" resultType="com.ruoyi.jiangshan.domain.WarningEvent">
        select * from t_warning_event
        where event_type = #{eventType}
        limit 2
    </select>

    <select id="countByScene" resultType="com.ruoyi.jiangshan.vo.BusinessPercentCountVO">
        select event_type as `key`, count(1) as value
        from t_warning_event
        where 1=1
        <if test="overFlag != null">
            and over_flag = #{overFlag,jdbcType=BOOLEAN}
        </if>
        group by event_type
    </select>

    <select id="listSupervise" resultType="com.ruoyi.jiangshan.vo.WarningSuperviseVO">
        select distinct(t2.id) as id, t2.event_type as eventType, t1.supervise_type as superviseType, t2.warning_level as warningLevel,
            t2.department_id as departmentId, t2.department_name as departmentName,
            t2.event_address as eventAddress, t2.status as status, t2.warning_time as warningTime, t2.event_name as eventName,
            t2.biz_code as bizCode
        from t_warning_event_supervise t1
                 left join t_warning_event t2 on t1.event_id = t2.id
        <where>
            and t2.id is not null
            <if test="eventName != null  and eventName != ''"> and t2.event_name like concat('%', #{eventName}, '%')</if>
            <if test="eventType != null  and eventType != ''"> and t2.event_type = #{eventType}</if>
            <if test="departmentId != null "> and t2.department_id = #{departmentId}</if>
            <if test="warningLevel != null "> and t2.warning_level = #{warningLevel}</if>
            <if test="warningStartTime != null">
                and t2.warning_time <![CDATA[ >= ]]> #{warningStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="warningEndTime != null">
                and t2.warning_time <![CDATA[ <= ]]> #{warningEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="sceneList != null and sceneList.size() != 0">
                and t2.event_type in
                <foreach collection="sceneList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by t2.warning_time desc
    </select>

    <update id="batchUpdateOverFlag">
        update t_warning_event
        set over_flag = 1
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdateSuperviseFlag">
        update t_warning_event
        set supervise_flag = 1,
        supervise_type = #{superviseType}
        where id in
        <foreach collection="eventIdUpdateList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdateLeaderContent">
        update t_warning_event
        set leader_content = #{content,jdbcType=VARCHAR}
        where id in
        <foreach collection="employeeIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="countByAddress" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
        select event_street as `key`, count(1) as value
        from t_warning_event
        where event_street is not null
        and event_street != ''
        group by event_street
        order by value desc
        limit 5
    </select>

    <select id="countByDate" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
        select warning_level as `key`, count(1) as value
        from t_warning_event
        where 1 = 1
        <if test="startTime != null">
            and warning_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and warning_time <![CDATA[ <= ]]> #{endTime}
        </if>
        group by warning_level
    </select>

    <select id="listByAreaCode" resultType="com.ruoyi.jiangshan.vo.CityRiskEventVO">
        select t2.id as id, t2.warning_time as warningTime,
        t2.warning_level as warningLevel, t1.monitor_id as monitorId,
        t1.warning_address as monitorName, t2.status as status
        from t_warning_info t1
        left join t_warning_event t2 on t1.id = t2.warning_id
        left join t_device_info t3 on t1.device_third_id = t3.device_third_id
        where t3.device_street_area_code like concat(#{areaCode}, '%')
        and t2.status not in (5, 6)
        and t1.warning_time <![CDATA[ >= ]]> NOW() - INTERVAL 3 DAY
        order by t1.warning_time desc
    </select>

    <select id="listUnCompleteEvent" resultMap="WarningEventResult">
        select * from t_warning_event
        where status != 4
    </select>

    <select id="listLastFourWarningV2" resultMap="WarningEventResult">
        <include refid="selectWarningEventVo">
        </include>
        <where>
            and status not in (5, 6)
            <if test="eventName != null  and eventName != ''"> and event_name like concat('%', #{eventName}, '%')</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
            <if test="departmentId != null "> and t1.department_id = #{departmentId}</if>
            <if test="warningLevel != null "> and warning_level = #{warningLevel}</if>
            <if test="warningStartTime != null">
                and warning_time <![CDATA[ >= ]]> #{warningStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="warningEndTime != null">
                and warning_time <![CDATA[ <= ]]> #{warningEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="statusList != null and statusList.size() != 0">
                and status in
                <foreach item="item" collection="statusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="eventStreetAreaCode != null and eventStreetAreaCode != ''">
                and event_street_area_code = #{eventStreetAreaCode,jdbcType=VARCHAR}
            </if>
            <if test="eventStreet != null and eventStreet != ''">
                and event_street like concat('%', #{eventStreet,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        order by warning_time desc
        limit 4
    </select>

    <select id="listNonCompleteEvent" resultMap="WarningEventResult">
        select *
        from t_warning_event t1
        where event_third_id is not null
        and status != 5
        and create_time <![CDATA[ >= ]]> '2025-01-16 15:20:00'
    </select>

    <select id="getByEventThirdId" resultMap="WarningEventResult">
        select *
        from t_warning_event t1
        where (event_third_id = #{eventThirdId,jdbcType=VARCHAR}
            or biz_code = #{eventThirdId,jdbcType=VARCHAR}
            or id = #{eventThirdId,jdbcType=VARCHAR})
        limit 1
    </select>

    <select id="checkExist" resultMap="WarningEventResult">
        select *
        from t_warning_event
        where device_third_id = #{deviceThirdId}
        order by warning_time desc
        limit 1
    </select>

    <update id="updateByWarningId">
        update t_warning_event
            set warning_id = #{warningId},
                event_name = #{eventName},
                warning_detail = #{warningDetail},
                warning_level = #{warningLevel},
                warning_time = #{warningTime},
                update_time = #{updateTime}
            where warning_id = #{warningId}
    </update>

    <select id="listWarningEventThreeDayTask" resultMap="WarningEventResult">
        select
        *
        from t_warning_event
        where 1=1
        and ((warning_level = 1 and warning_time <![CDATA[ <= ]]> NOW() - INTERVAL 1 DAY)
            or (warning_level = 2 and warning_time <![CDATA[ <= ]]> NOW() - INTERVAL 3 DAY)
            or (warning_level in (3, 4, 5) and event_name not like '%占用事件%'
                and warning_time <![CDATA[ <= ]]> NOW() - INTERVAL 5 DAY))
        and status not in (5, 6)
        and supervise_flag != 1
    </select>

    <select id="listVideoEvent" resultMap="WarningEventResult">
        select * from t_warning_event
        where device_third_id in
              ('f483fb9636da4aed8a45a25299e33e61',
               '3607848c77b74fecbd847eec72cfa722',
               '7215c6061f9845609329419fe7b6f0d7',
               'f86958db70df4ed6803fd78043cf8128',
               '18a4596643714933a311732cd2113948',
               '1712dd80b1cf4a0d83ecf24444a6cfc1',
               'f8f314f54e004b8b96fa64b63ad5b03b',
               'cfd568df256c4cc0a91d3a8e27e77ad1',
               '2790920b776140faad4ff6e71ad24665')
        and warning_time <![CDATA[ <= ]]> NOW() - INTERVAL 7 DAY
    </select>

    <select id="countNumberByDate" resultType="java.lang.Long">
        select count(1) from t_warning_event
        where warning_time <![CDATA[ >= ]]> #{startTime}
        and warning_time <![CDATA[ <= ]]> #{endTime}
    </select>

    <select id="listByDate" resultMap="WarningEventResult">
        select * from t_warning_event
        where warning_time <![CDATA[ >= ]]> #{startTime}
        and warning_time <![CDATA[ <= ]]> #{endTime}
    </select>

    <select id="countStopCount" resultType="long">
        select count(1) from t_warning_event
        where stop_flag = 1
    </select>

    <select id="getCountByMonthAndScene" resultType="java.lang.Long">
        select count(1) from t_warning_event
        where event_type = #{scene,jdbcType=VARCHAR}
        and warning_time
        BETWEEN DATE_FORMAT(CURDATE(), '%Y-%m-01')  -- 当月第一天
        AND LAST_DAY(CURDATE())
    </select>

    <select id="listNonCompleteOrangeEvent" resultMap="WarningEventResult">
        <include refid="selectWarningEventVo">
        </include>
        where
<!--        warning_level in (1, 2, 3, 4)-->
<!--        and biz_code is not null-->
<!--        and `status` not in (5, 6)-->
        <if test="startTime != null">
            and warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="getUnCompleteEventByThirdId" resultMap="WarningEventResult">
        select * from t_warning_event
        where status in (1, 2, 3, 4)
        and device_third_id = #{deviceThirdId}
        limit 1
    </select>

    <select id="checkFireExist" resultMap="WarningEventResult">
        select t1.* from t_warning_event t1
        left join t_warning_info t2 on t1.warning_id = t2.id
        where t2.monitor_value = #{carLicense}
        <if test="startTime != null">
            and t1.warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and t1.warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by t1.warning_time desc
        limit 1
    </select>

    <select id="listNonSignEvent" resultMap="WarningEventResult">
        select * from t_warning_event
        where status = 1
        and event_name not like '%占用事件%'
<!--        and event_third_id is null-->
        and (
            (warning_level in (1, 2) and delivery_time <![CDATA[ <= ]]> NOW() - INTERVAL 5 MINUTE and delivery_time <![CDATA[ >= ]]> NOW() - INTERVAL 15 MINUTE)
            or (warning_level in (3, 4, 5) and delivery_time <![CDATA[ <= ]]> NOW() - INTERVAL 15 MINUTE and delivery_time <![CDATA[ >= ]]> NOW() - INTERVAL 30 MINUTE)
            )
    </select>

    <select id="listBaiyiWarning" resultType="com.ruoyi.jiangshan.domain.WarningEvent">
        select t1.*
        from t_warning_event t1
            left join t_device_info t2 on t1.device_third_id = t2.device_third_id
        where t1.status not in (5, 6)
        and t2.device_type != '白蚁安全观测装置'
    </select>

    <select id="listByDateWithOutModel" resultMap="WarningEventResult">
        select * from t_warning_event
        where warning_time <![CDATA[ >= ]]> #{startDate}
        and warning_time <![CDATA[ <= ]]> #{endDate}
        and event_third_id is null
    </select>

    <select id="listWarningEventOccupyOverTime" resultMap="WarningEventResult">
        select
            *
        from t_warning_event
        where 1=1
        and warning_level = 5
        and event_name like '%占用事件%'
        and (
            -- 1. 昨天22点到今天8点产生，且今天10点后status还不是5或6
            (
                warning_time >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d 22:00:00')
                and warning_time <![CDATA[ < ]]> DATE_FORMAT(NOW(), '%Y-%m-%d 08:00:00')
                and NOW() >= DATE_FORMAT(NOW(), '%Y-%m-%d 10:00:00')
                and status not in (5,6)
            )
            or
            -- 2. 今天8点到22点产生，且2小时后status还不是5或6
            (
                warning_time >= DATE_FORMAT(NOW(), '%Y-%m-%d 08:00:00')
                and warning_time <![CDATA[ < ]]> DATE_FORMAT(NOW(), '%Y-%m-%d 22:00:00')
                and TIMESTAMPDIFF(HOUR, warning_time, NOW()) >= 2
                and status not in (5,6)
            )
        )
    </select>

    <select id="listAllAlarmDevice" resultType="java.lang.String">
        select distinct device_third_id from t_warning_event
        where status not in (5, 6)
        and warning_level = 5
        and warning_time <![CDATA[ >= ]]> DATE_SUB(now(), interval 3 day)
        and device_third_id is not null
        and device_third_id != ''
    </select>

    <select id="listPushValue" resultMap="WarningEventResult">
        <include refid="selectWarningEventVo">
        </include>
        where 1=1
        <if test="startTime != null">
            and warning_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and warning_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>
</mapper>
