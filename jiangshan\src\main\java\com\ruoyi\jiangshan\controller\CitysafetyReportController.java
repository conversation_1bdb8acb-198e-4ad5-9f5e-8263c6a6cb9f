package com.ruoyi.jiangshan.controller;

import java.util.Date;
import java.util.List;

import com.ruoyi.common.utils.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jiangshan.domain.CitysafetyReport;
import com.ruoyi.jiangshan.service.ICitysafetyReportService;
import com.ruoyi.common.core.page.TableDataInfo;

import javax.servlet.http.HttpServletResponse;

/**
 * 城市安全体检报告管理Controller
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@RestController
@RequestMapping("/citysafety/report")
public class CitysafetyReportController extends BaseController
{
    @Autowired
    private ICitysafetyReportService citysafetyReportService;

    /**
     * 查询城市安全体检报告管理列表
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(CitysafetyReport citysafetyReport)
    {
        startPage();
        List<CitysafetyReport> list = citysafetyReportService.selectCitysafetyReportList(citysafetyReport);
        return getDataTable(list);
    }

//    /**
//     * 导出城市安全体检报告管理列表
//     */
////    @PreAuthorize("@ss.hasPermi('citysafetyReport:citysafetyReport:export')")
//    @Log(title = "城市安全体检报告管理", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, CitysafetyReport citysafetyReport)
//    {
//        List<CitysafetyReport> list = citysafetyReportService.selectCitysafetyReportList(citysafetyReport);
//        ExcelUtil<CitysafetyReport> util = new ExcelUtil<CitysafetyReport>(CitysafetyReport.class);
//        util.exportExcel(response, list, "城市安全体检报告管理数据");
//    }

    /**
     * 获取城市安全体检报告管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyReport:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(citysafetyReportService.getInfo(id, null));
    }

    /**
     * 测试
     */
//    @PreAuthorize("@ss.hasPermi('citysafetyReport:citysafetyReport:query')")
    @GetMapping(value = "/dateTest")
    public AjaxResult getDateTest(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime, Integer reportType)
    {
        return success(DateUtils.generateDateRanges(startTime, endTime, reportType));
    }

    /**
     * 生成瞬报
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyReport:add')")
    @Log(title = "城市安全体检报告管理", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public AjaxResult generate(@RequestBody CitysafetyReport citysafetyReport)
    {
        return toAjax(citysafetyReportService.generateReport(citysafetyReport));
    }

    /**
     * 预览瞬报
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyReport:query')")
    @PostMapping("/preview")
    public AjaxResult preview(@RequestBody CitysafetyReport citysafetyReport)
    {
        return AjaxResult.success(citysafetyReportService.previewReport(citysafetyReport));
    }

    /**
     * 导出
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyReport:export')")
    @PostMapping("/createPdf")
    public void createPdf(Long id, HttpServletResponse response)
    {
        citysafetyReportService.createPdf(id, response);
    }

    /**
     * 批量导出
     */
    @PreAuthorize("@ss.hasPermi('jiangshan:TCitysafetyReport:export')")
    @PostMapping("/createBatchPdf")
    public void createBatchPdf(Long[] idList, HttpServletResponse response)
    {
        citysafetyReportService.createBatchPdf(idList, response);
    }

    /**
     * 获取街道/社区
     */
//    @PreAuthorize("@ss.hasPermi('citysafetyReport:citysafetyReport:query')")
    @GetMapping(value = "/getStreetTree")
    public AjaxResult getStreetTree()
    {
        return success(citysafetyReportService.getStreet());
    }

//    /**
//     * 新增城市安全体检报告管理
//     */
////    @PreAuthorize("@ss.hasPermi('citysafetyReport:citysafetyReport:add')")
//    @Log(title = "城市安全体检报告管理", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody CitysafetyReport citysafetyReport)
//    {
//        return toAjax(citysafetyReportService.insertCitysafetyReport(citysafetyReport));
//    }
//
//    /**
//     * 修改城市安全体检报告管理
//     */
////    @PreAuthorize("@ss.hasPermi('citysafetyReport:citysafetyReport:edit')")
//    @Log(title = "城市安全体检报告管理", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody CitysafetyReport citysafetyReport)
//    {
//        return toAjax(citysafetyReportService.updateCitysafetyReport(citysafetyReport));
//    }

//    /**
//     * 删除城市安全体检报告管理
//     */
////    @PreAuthorize("@ss.hasPermi('citysafetyReport:citysafetyReport:remove')")
//    @Log(title = "城市安全体检报告管理", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(citysafetyReportService.deleteCitysafetyReportByIds(ids));
//    }
}
