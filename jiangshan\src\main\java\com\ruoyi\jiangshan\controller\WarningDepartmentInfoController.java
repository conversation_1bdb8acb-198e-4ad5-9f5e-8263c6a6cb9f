package com.ruoyi.jiangshan.controller;

import java.util.List;

import com.ruoyi.jiangshan.domain.SysArea;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jiangshan.domain.WarningDepartmentInfo;
import com.ruoyi.jiangshan.service.IWarningDepartmentInfoService;

/**
 * 联动处置中心部门Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/warningDepartmentInfo")
public class WarningDepartmentInfoController extends BaseController
{
    @Autowired
    private IWarningDepartmentInfoService warningDepartmentInfoService;

    /**
     * 查询联动处置中心部门列表
     */
//    @PreAuthorize("@ss.hasPermi('warningDepartmentInfo:warningDepartmentInfo:list')")
    @GetMapping("/list")
    public AjaxResult list(WarningDepartmentInfo warningDepartmentInfo)
    {
        List<WarningDepartmentInfo> list = warningDepartmentInfoService.selectWarningDepartmentInfoList(warningDepartmentInfo);
        return AjaxResult.success(list);
    }

    /**
     * 根据部门id获取下级部门
     */
//    @PreAuthorize("@ss.hasPermi('warningDepartmentInfo:warningDepartmentInfo:list')")
    @GetMapping("/tree")
    public AjaxResult getDeptTree(Long parentId)
    {
        List<WarningDepartmentInfo> list = warningDepartmentInfoService.getDeptTree(parentId);
        return AjaxResult.success(list);
    }

//    /**
//     * 导出联动处置中心部门列表
//     */
////    @PreAuthorize("@ss.hasPermi('warningDepartmentInfo:warningDepartmentInfo:export')")
//    @Log(title = "联动处置中心部门", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, WarningDepartmentInfo warningDepartmentInfo)
//    {
//        List<WarningDepartmentInfo> list = warningDepartmentInfoService.selectWarningDepartmentInfoList(warningDepartmentInfo);
//        ExcelUtil<WarningDepartmentInfo> util = new ExcelUtil<WarningDepartmentInfo>(WarningDepartmentInfo.class);
//        util.exportExcel(response, list, "联动处置中心部门数据");
//    }

    /**
     * 获取联动处置中心部门详细信息
     */
//    @PreAuthorize("@ss.hasPermi('warningDepartmentInfo:warningDepartmentInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(warningDepartmentInfoService.selectWarningDepartmentInfoById(id));
    }

//    /**
//     * 新增联动处置中心部门
//     */
////    @PreAuthorize("@ss.hasPermi('warningDepartmentInfo:warningDepartmentInfo:add')")
//    @Log(title = "联动处置中心部门", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody WarningDepartmentInfo warningDepartmentInfo)
//    {
//        return toAjax(warningDepartmentInfoService.insertWarningDepartmentInfo(warningDepartmentInfo));
//    }

    /**
     * 修改联动处置中心部门
     */
//    @PreAuthorize("@ss.hasPermi('warningDepartmentInfo:warningDepartmentInfo:edit')")
    @Log(title = "联动处置中心部门", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WarningDepartmentInfo warningDepartmentInfo)
    {
        return toAjax(warningDepartmentInfoService.updateWarningDepartmentInfo(warningDepartmentInfo));
    }

    /**
     * 删除联动处置中心部门
     */
//    @PreAuthorize("@ss.hasPermi('warningDepartmentInfo:warningDepartmentInfo:remove')")
    @Log(title = "联动处置中心部门", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(warningDepartmentInfoService.deleteWarningDepartmentInfoByIds(ids));
    }

    /**
     * 获取浙政钉顶层部门
     */
    @GetMapping("/dtalk/parentDept")
    public AjaxResult getDtalkParentDept()
    {
        return AjaxResult.success(warningDepartmentInfoService.getDtalkParentDept());
    }

    /**
     * 根据浙政钉部门code获取浙政钉子集部门
     */
    @GetMapping("/dtalk/subDept")
    public AjaxResult getDtalkSubDept(String organizationCode)
    {
        return AjaxResult.success(warningDepartmentInfoService.getDtalkSubDept(organizationCode));
    }

    /**
     * 根据浙政钉部门code和关键字获取浙政钉用户
     */
    @GetMapping("/dtalk/user")
    public AjaxResult getDtalkUser(String organizationCode, Integer pageNum, Integer pageSize, String keywords)
    {
        return AjaxResult.success(warningDepartmentInfoService.getDtalkUser(organizationCode, pageNum, pageSize, keywords));
    }

    /**
     * 根据浙政钉部门code获取浙政钉用户
     */
    @GetMapping("/dtalk/userV2")
    public AjaxResult getDtalkUserV2(String organizationCode, Integer pageNum, Integer pageSize)
    {
        return AjaxResult.success(warningDepartmentInfoService.getDtalkUserV2(organizationCode, pageNum, pageSize));
    }

    /**
     * 根据行政区划父id获取下级行政区划
     */
//    @PreAuthorize("@ss.hasPermi('warningDepartmentInfo:warningDepartmentInfo:list')")
    @GetMapping("/areaTree")
    public AjaxResult getAreaTree(Long parentId)
    {
        List<SysArea> list = warningDepartmentInfoService.getAreaTree(parentId);
        return AjaxResult.success(list);
    }

    /**
     * 获取所有用户
     */
    @GetMapping("/listAllUser")
    public AjaxResult listAllUser(String keywords, String deptId)
    {
        return AjaxResult.success(warningDepartmentInfoService.listAllUser(keywords, deptId));
    }

    /**
     * 获取抄送部门
     */
    @GetMapping("/listCopyDept")
    public AjaxResult listCopyDept(String keywords)
    {
        return AjaxResult.success(warningDepartmentInfoService.listCopyDept(keywords));
    }
}
