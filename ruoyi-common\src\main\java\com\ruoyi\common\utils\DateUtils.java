package com.ruoyi.common.utils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.dto.DateDTO;
import com.ruoyi.common.dto.TimeRange;
import com.ruoyi.common.dto.TimeRangeLocalDate;
import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate()
    {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算相差天数
     */
    public static int differentDaysByMillisecond(Date date1, Date date2)
    {
        return Math.abs((int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
    }

    /**
     * 计算时间差
     *
     * @param endDate 最后时间
     * @param startTime 开始时间
     * @return 时间差（天/小时/分钟）
     */
    public static String timeDistance(Date endDate, Date startTime)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startTime.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor)
    {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor)
    {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 获取指定日期的开始时间
     *
     * @param date 指定日期
     * @return 开始时间
     */
    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定日期的结束时间
     *
     * @param date 指定日期
     * @return 结束时间
     */
    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 根据时间范围获取开始和结束时间
     *
     * @param date      当前日期
     * @param timeRange 时间范围
     * @return 时间范围内的开始和结束时间
     */
    public static DateDTO getTimeRange(Date date, String timeRange) {
        DateDTO dateDTO = new DateDTO();

        Date startDate = null;
        Date endDate = null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        switch (timeRange) {
            case "日":
                startDate = getStartOfDay(date);
                endDate = getEndOfDay(date);
                break;
            case "周":
                calendar.setFirstDayOfWeek(Calendar.MONDAY); // 设置每周的第一天为周一
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY); // 设置为本周一
                startDate = getStartOfDay(calendar.getTime());
                calendar.add(Calendar.WEEK_OF_YEAR, 1); // 加一周
                endDate = getEndOfDay(calendar.getTime());
                break;
            case "月":
                calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为本月第一天
                startDate = getStartOfDay(calendar.getTime());
                calendar.add(Calendar.MONTH, 1); // 加一个月
                endDate = getEndOfDay(calendar.getTime());
                break;
            case "季度":
                int month = calendar.get(Calendar.MONTH);
                int quarterStartMonth = (month / 3) * 3; // 计算季度的第一个月
                calendar.set(Calendar.MONTH, quarterStartMonth);
                calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为季度第一天
                startDate = getStartOfDay(calendar.getTime());
                calendar.add(Calendar.MONTH, 3); // 加三个月
                endDate = getEndOfDay(calendar.getTime());
                break;
            case "半年":
                int halfYearStartMonth = calendar.get(Calendar.MONTH) < 6 ? 0 : 6; // 判断上半年还是下半年
                calendar.set(Calendar.MONTH, halfYearStartMonth);
                calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为半年第一天
                startDate = getStartOfDay(calendar.getTime());
                calendar.add(Calendar.MONTH, 6); // 加六个月
                endDate = getEndOfDay(calendar.getTime());
                break;
            case "年":
                calendar.set(Calendar.DAY_OF_YEAR, 1); // 设置为当年第一天
                startDate = getStartOfDay(calendar.getTime());
                calendar.add(Calendar.YEAR, 1); // 加一年
                endDate = getEndOfDay(calendar.getTime());
                break;
            case "近7天":
                endDate = getEndOfDay(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, -6); // 设置为本月第一天
                startDate = getStartOfDay(calendar.getTime());
                break;
            case "近3天":
                endDate = getEndOfDay(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, -2); // 设置为本月第一天
                startDate = getStartOfDay(calendar.getTime());
                break;
            default:
                throw new IllegalArgumentException("Invalid time range: " + timeRange);
        }

        dateDTO.setStartTime(startDate);
        dateDTO.setEndTime(endDate);

        return dateDTO;
    }

    /**
     * 获取指定日期的小时字符串
     *
     * @param date 指定日期
     * @return 日期字符串
     */
    public static String formatHour(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的日期字符串
     *
     * @param date 指定日期
     * @return 日期字符串
     */
    public static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的日期字符串
     *
     * @param date 指定日期
     * @return 日期字符串
     */
    public static String formatDateV2(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的日期字符串
     *
     * @param date 指定日期
     * @return 日期字符串
     */
    public static String formatDateSecond(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的年月字符串
     *
     * @param date 指定日期
     * @return 年月字符串
     */
    public static String formatYearMonth(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的年月字符串
     *
     * @param date 指定日期
     * @return 年月字符串
     */
    public static String formatYearMonthV3(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的年月字符串
     *
     * @param date 指定日期
     * @return 年月字符串
     */
    public static String formatYearMonthV2(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("M月");
        return sdf.format(date);
    }

    /**
     * 根据时间范围获取日期字符串列表
     *
     * @param timeRange 时间范围
     * @return 日期字符串列表
     */
    public static List<String> getDateRange(String timeRange) {
        Calendar calendar = Calendar.getInstance();
        Date today = calendar.getTime();

        List<String> dates = new ArrayList<>();

        switch (timeRange) {
            case "日":
                Date startOfDay = getStartOfDay(today);
                for (int hour = 0; hour <= 23; hour++) {
                    calendar.setTime(startOfDay);
                    calendar.set(Calendar.HOUR_OF_DAY, hour);
                    dates.add(formatHour(calendar.getTime()));
                }
                break;
            case "周":
                calendar.setFirstDayOfWeek(Calendar.MONDAY); // 设置每周的第一天为周一
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                int daysToMonday = dayOfWeek == Calendar.SUNDAY ? -6 : Calendar.MONDAY - dayOfWeek;
                calendar.add(Calendar.DAY_OF_MONTH, daysToMonday); // 回溯到本周一
                Date startOfWeek = getStartOfDay(calendar.getTime());
                Date endOfWeek = getEndOfDay(today);
                while (startOfWeek.before(endOfWeek)) {
                    dates.add(formatDate(startOfWeek));
                    calendar.setTime(startOfWeek);
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    startOfWeek = calendar.getTime();
                }
                break;
            case "月":
                calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为本月第一天
                Date startOfMonth = getStartOfDay(calendar.getTime());
                Date endOfMonth = getEndOfDay(today);
                while (startOfMonth.before(endOfMonth)) {
                    dates.add(formatDate(startOfMonth));
                    calendar.setTime(startOfMonth);
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    startOfMonth = calendar.getTime();
                }
                break;
            case "年":
                calendar.set(Calendar.DAY_OF_YEAR, 1); // 设置为当年第一天
                Date startOfYear = getStartOfDay(calendar.getTime());
                Date endOfYear = getEndOfDay(today);
                while (startOfYear.before(endOfYear)) {
                    dates.add(formatYearMonth(startOfYear));
                    calendar.setTime(startOfYear);
                    calendar.add(Calendar.MONTH, 1);
                    startOfYear = calendar.getTime();
                }
                break;
            case "近七天":
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                for (int i = 0; i < 7; i++) {
                    LocalDate date = LocalDate.now().minusDays(i);
                    String formattedDate = date.format(formatter);
                    dates.add(formattedDate);
                }

                break;
            default:
                throw new IllegalArgumentException("Invalid time range: " + timeRange);
        }
        return dates;
    }

    public static List<String> getLastSixDate() {
        List<String> dates = new ArrayList<>();
        LocalDate today = LocalDate.now();

        for (int i = 0; i <= 5; i++) {
            LocalDate date = today.minusDays(i);
            String dateString = date.format(DateTimeFormatter.ISO_LOCAL_DATE);
            dates.add(dateString);
        }

        return dates;
    }

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat HOUR_FORMAT = new SimpleDateFormat("HH时");

    /**
     * 根据reportType获取相应的时间范围对象列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param reportType 报告类型
     * @return 时间范围对象列表
     */
    public static List<TimeRange> generateDateRanges(Date startTime, Date endTime, int reportType) {
        List<TimeRange> timeRanges = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();

        switch (reportType) {
            case 0:
            case 2:
                addDatesBetween(timeRanges, calendar, startTime, endTime);
                break;
            case 1:
                addHoursOfDay(timeRanges, calendar, startTime);
                break;
            case 3:
                addWeeksBetween(timeRanges, calendar, startTime, endTime);
                break;
            case 4:
            case 5:
                addMonthsBetween(timeRanges, calendar, startTime, endTime);
                break;
            default:
                throw new IllegalArgumentException("Invalid report type: " + reportType);
        }

        return timeRanges;
    }

    private static void addDatesBetween(List<TimeRange> timeRanges, Calendar calendar, Date startTime, Date endTime) {
        calendar.setTime(startTime);
        while (!calendar.getTime().after(endTime)) {
            TimeRange timeRange = new TimeRange(DATE_FORMAT.format(calendar.getTime()), calendar.getTime());

            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);

            timeRange.setEndTime(calendar.getTime());

            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);

            timeRanges.add(timeRange);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
    }

    private static void addHoursOfDay(List<TimeRange> timeRanges, Calendar calendar, Date startTime) {
        calendar.setTime(startTime);
        for (int hour = 0; hour <= 23; hour++) {
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);

            calendar.set(Calendar.HOUR_OF_DAY, hour);

            TimeRange timeRange = new TimeRange(HOUR_FORMAT.format(calendar.getTime()), calendar.getTime());

            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);

            timeRange.setEndTime(calendar.getTime());

            timeRanges.add(timeRange);
        }
    }

    private static void addWeeksBetween(List<TimeRange> timeRanges, Calendar calendar, Date startTime, Date endTime) {
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(startTime);

        while (!calendar.getTime().after(endTime)) {
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date weekStart = calendar.getTime();

            calendar.add(Calendar.DAY_OF_MONTH, 6);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            Date weekEnd = calendar.getTime();
            timeRanges.add(new TimeRange("第" + calendar.get(Calendar.WEEK_OF_YEAR) + "周", weekStart, weekEnd));
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
    }

    private static void addMonthsBetween(List<TimeRange> timeRanges, Calendar calendar, Date startTime, Date endTime) {
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为每月第一天
        calendar.setTime(startTime);

        while (!calendar.getTime().after(endTime)) {
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date monthStart = calendar.getTime();

            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);

            Date monthEnd = calendar.getTime();
            timeRanges.add(new TimeRange((calendar.get(Calendar.MONTH) + 1) + "月", monthStart, monthEnd));

            calendar.add(Calendar.MONTH, 1);
        }
    }

    public static Date getNextDay(Date currentDate) {
        // 创建一个Calendar实例，并设置时间为currentDate
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);

        // 将日期往后推一天
        calendar.add(Calendar.DAY_OF_MONTH, 1);

        // 获取后一天的日期
        Date nextDay = calendar.getTime();

        return nextDay;
    }

    public static Date getNextHour(Date currentDate) {
        // 创建一个Calendar实例，并设置时间为currentDate
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);

        // 将日期往后推一天
        calendar.add(Calendar.HOUR_OF_DAY, 1);

        // 获取后一天的日期
        Date nextDay = calendar.getTime();

        return nextDay;
    }

    public static String formatDateIOS(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        return sdf.format(date);
    }

    public static long getMinutesBetweenDates(Date date1, Date date2) {
        // 获取两个时间的毫秒差值（取绝对值）
        long diffInMillis = Math.abs(date2.getTime() - date1.getTime());
        // 转换为分钟数（1分钟 = 60秒 * 1000毫秒）
        return diffInMillis / (60 * 1000);
    }

    public static TimeRangeLocalDate getBeforeOneHourAnd15Minutes() {
        // 1. 获取当前时间
        LocalDateTime end = LocalDateTime.now();
        // 2. 获取前1小时15分钟的开始时间
        LocalDateTime start = end.minusHours(1).minusMinutes(15);

        return new TimeRangeLocalDate(start, end);
    }

    public static TimeRangeLocalDate getBeforeOneHour() {
        // 1. 获取当前时间
        LocalDateTime end = LocalDateTime.now();
        // 2. 获取前1小时的开始时间
        LocalDateTime start = end.minusHours(1);

        return new TimeRangeLocalDate(start, end);
    }

    public static List<TimeRangeLocalDate> getSplitTimeRange(TimeRangeLocalDate beforeOneHour) {
        //按xx分钟切割
        List<TimeRangeLocalDate> ranges = splitByMinutes(beforeOneHour.getStartTime(), beforeOneHour.getEndTime(), 15);

        return ranges;
    }

    // 切割方法
    public static List<TimeRangeLocalDate> splitByMinutes(LocalDateTime start, LocalDateTime end, int minutes) {
        List<TimeRangeLocalDate> result = new ArrayList<>();
        LocalDateTime tempStart = start;
        while (tempStart.isBefore(end)) {
            LocalDateTime tempEnd = tempStart.plusMinutes(minutes);
            if (tempEnd.isAfter(end)) {
                tempEnd = end;
            }
            result.add(new TimeRangeLocalDate(tempStart, tempEnd));
            tempStart = tempEnd;
        }
        return result;
    }

    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        // 指定时区（一般用系统默认时区）
        ZoneId zone = ZoneId.systemDefault();
        // 转换为Instant，再转为Date
        Date date = Date.from(localDateTime.atZone(zone).toInstant());

        return date;
    }

    public static Date getLastHour() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, -1);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    public static Date getThisHour() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }
}
