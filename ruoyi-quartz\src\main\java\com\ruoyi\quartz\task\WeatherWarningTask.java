package com.ruoyi.quartz.task;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.jiangshan.client.GeologicClient;
import com.ruoyi.jiangshan.client.IrsWeatherClient;
import com.ruoyi.jiangshan.domain.JiangshanRainfall;
import com.ruoyi.jiangshan.mapper.JiangshanRainfallMapper;
import com.ruoyi.jiangshan.service.ComprehensiveSceneService;
import com.ruoyi.jiangshan.vo.compre.DeviceYuLiangExtendV2VO;
import com.ruoyi.jiangshan.vo.compre.DeviceYuLiangStreetAllV2VO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Slf4j
@Component("weatherWarningTask")
public class WeatherWarningTask {


    /**
     * 同步三方风险防范区接口
     */
    public void syncRiskPreventionZoneData() throws ExecutionException {
        GeologicClient geologicClient = SpringUtils.getBean(GeologicClient.class);

        log.info("syncRiskPreventionZoneData, start");

        String lastHour = DateUtils.formatDateSecond(DateUtils.getLastHour());
        String thisHour = DateUtils.formatDateSecond(DateUtils.getThisHour());

        Map<String, Object> map = geologicClient.syncRiskPreventionZoneData(lastHour, thisHour, 1, 1000);

        log.info("syncGeo, end, map:{}", JSON.toJSONString(map));
    }

    /**
     * 同步第三方巡查处置信息数据
     */
    public void syncDisposal() throws ExecutionException {
        GeologicClient geologicClient = SpringUtils.getBean(GeologicClient.class);

        log.info("syncDisposal, start");

        Map<String, Object> map = geologicClient.syncPatrolDisposalInfoData(1, 1000);

        log.info("syncDisposal, end, map:{}", JSON.toJSONString(map));
    }

    /**
     * 同步第三方气象预警数据
     */
    public void syncWarning() throws ExecutionException {
        GeologicClient geologicClient = SpringUtils.getBean(GeologicClient.class);

        log.info("syncWarning, start");

        String lastHour = DateUtils.formatDateSecond(DateUtils.getLastHour());
        String thisHour = DateUtils.formatDateSecond(DateUtils.getThisHour());

        Map<String, Object> map = geologicClient.syncWeatherWarningData(lastHour, thisHour, 1, 1000);

        log.info("syncWarning, end, map:{}", JSON.toJSONString(map));
    }

}
