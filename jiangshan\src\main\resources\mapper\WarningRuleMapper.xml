<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningRuleMapper">

    <resultMap type="WarningRule" id="WarningRuleResult">
        <result property="id"    column="id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="modelId"    column="model_id"    />
        <result property="publishWay"    column="publish_way"    />
        <result property="ruleDetail"    column="rule_detail"    />
        <result property="warningUserId"    column="warning_user_id"    />
        <result property="warningUserName"    column="warning_user_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="otherUserId"    column="other_user_id"    />
        <result property="allAccountId"    column="all_account_id"    />
        <result property="allMobile"    column="all_mobile"    />
    </resultMap>

    <sql id="selectWarningRuleVo">
        select id, rule_name, model_id, publish_way, rule_detail, warning_user_id, warning_user_name,
               create_time, create_by, update_time, update_by, remark, other_user_id, all_account_id, all_mobile
        from t_warning_rule
    </sql>

    <select id="selectWarningRuleList" parameterType="WarningRule" resultMap="WarningRuleResult">
        <include refid="selectWarningRuleVo"/>
        <where>
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="modelId != null  and modelId != ''"> and model_id = #{modelId}</if>
            <if test="publishWay != null "> and publish_way = #{publishWay}</if>
            <if test="ruleDetail != null  and ruleDetail != ''"> and rule_detail = #{ruleDetail}</if>
            <if test="warningUserId != null "> and warning_user_id = #{warningUserId}</if>
            <if test="warningUserName != null  and warningUserName != ''"> and warning_user_name like concat('%', #{warningUserName}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectWarningRuleById" parameterType="Long" resultMap="WarningRuleResult">
        <include refid="selectWarningRuleVo"/>
        where id = #{id}
    </select>

    <insert id="insertWarningRule" parameterType="WarningRule" useGeneratedKeys="true" keyProperty="id">
        insert into t_warning_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null">rule_name,</if>
            <if test="modelId != null">model_id,</if>
            <if test="publishWay != null">publish_way,</if>
            <if test="ruleDetail != null">rule_detail,</if>
            <if test="warningUserId != null">warning_user_id,</if>
            <if test="warningUserName != null">warning_user_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="otherUserId != null">other_user_id,</if>
            <if test="allAccountId != null">all_account_id,</if>
            <if test="allMobile != null">all_mobile,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null">#{ruleName},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="publishWay != null">#{publishWay},</if>
            <if test="ruleDetail != null">#{ruleDetail},</if>
            <if test="warningUserId != null">#{warningUserId},</if>
            <if test="warningUserName != null">#{warningUserName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="otherUserId != null">#{otherUserId},</if>
            <if test="allAccountId != null">#{allAccountId},</if>
            <if test="allMobile != null">#{allMobile},</if>
         </trim>
    </insert>

    <update id="updateWarningRule" parameterType="WarningRule">
        update t_warning_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="publishWay != null">publish_way = #{publishWay},</if>
            <if test="ruleDetail != null">rule_detail = #{ruleDetail},</if>
            <if test="warningUserId != null">warning_user_id = #{warningUserId},</if>
            <if test="warningUserName != null">warning_user_name = #{warningUserName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
            other_user_id = #{otherUserId,jdbcType=VARCHAR},
            all_account_id = #{allAccountId,jdbcType=VARCHAR},
            all_mobile = #{allMobile,jdbcType=VARCHAR}
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWarningRuleById" parameterType="Long">
        delete from t_warning_rule where id = #{id}
    </delete>

    <delete id="deleteWarningRuleByIds" parameterType="String">
        delete from t_warning_rule where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getByConditionId" resultMap="WarningRuleResult">
        select t1.* from t_warning_rule t1
        left join t_warning_rule_condition t2 on t1.id = t2.rule_id
        where t2.id = #{conditionId}
        limit 1;
    </select>
</mapper>
