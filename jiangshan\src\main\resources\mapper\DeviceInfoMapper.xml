<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceInfoMapper">

    <resultMap type="DeviceInfo" id="DeviceInfoResult">
        <result property="id"    column="id"    />
        <result property="deviceThirdId"    column="device_third_id"    />
        <result property="deviceNum"    column="device_num"    />
        <result property="deviceScene"    column="device_scene"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceAddress"    column="device_address"    />
        <result property="deviceDepartmentId"    column="device_department_id"    />
        <result property="deviceDepartmentName"    column="device_department_name"    />
        <result property="deviceStatus"    column="device_status"    />
        <result property="monitorId"    column="monitor_id"    />
        <result property="monitorName"    column="monitor_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="lon"    column="lon"    />
        <result property="lat"    column="lat"    />
        <result property="openFlag"    column="open_flag"    />
        <result property="lastConnectTime"    column="last_connect_time"    />
        <result property="lastDisConnectTime"    column="last_dis_connect_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="favoriteFlag"    column="favorite_flag"    />
        <result property="warningFlag"    column="warning_flag"    />
        <result property="deviceStreet"    column="device_street"    />
        <result property="deviceStreetAreaCode"    column="device_street_area_code"    />
        <result property="remark"    column="remark"    />
        <result property="field01"    column="field01"    />
        <result property="field02"    column="field02"    />
        <result property="field03"    column="field03"    />
        <result property="field04"    column="field04"    />
        <result property="field05"    column="field05"    />
        <result property="deviceSource"    column="device_source"    />
    </resultMap>

    <sql id="selectDeviceInfoVo">
        select id, device_third_id, device_num, device_scene, device_name, device_type, device_address,
               device_department_id, device_department_name, device_status, monitor_id, monitor_name,
               file_url, lon, lat, open_flag, last_connect_time, last_dis_connect_time, create_time,
               create_by, update_time, update_by, device_street, device_street_area_code, remark,
               field01, field02, field03, field04, field05, device_source from t_device_info
    </sql>

    <select id="selectDeviceInfoList" parameterType="DeviceInfo" resultMap="DeviceInfoResult">
        select distinct (t1.id), t1.device_third_id, t1.device_num, t1.device_scene, t1.device_name, t1.device_type, t1.device_address,
        t1.device_department_id, t1.device_department_name, t1.device_status, t1.monitor_id, t1.monitor_name,
        t1.file_url, t1.lon, t1.lat, t1.open_flag, t1.last_connect_time, t1.last_dis_connect_time, t1.create_time,
        t1.create_by, t1.update_time, t1.update_by, t1.device_street, t1.device_street_area_code,
        t1.remark, t1.field01, t1.field02, t1.field03, t1.field04, t1.field05, t1.device_source,
        if(t3.id is not null, true, false) as favorite_flag,
        if(t2.id is not null, true, false) as warning_flag from t_device_info t1
        left join (select * from t_warning_info where warning_time <![CDATA[ >= ]]> NOW() - INTERVAL 1 MINUTE) t2 on t1.device_third_id = t2.device_third_id
        left join (select * from t_device_info_favorite where user_id = #{createBy}) t3 on t1.id = t3.device_id
        <where>
            <if test="deviceThirdId != null and deviceThirdId != ''"> and t1.device_third_id = #{deviceThirdId}</if>
            <if test="deviceNum != null  and deviceNum != ''"> and device_num = #{deviceNum}</if>
            <if test="deviceScene != null  and deviceScene != ''"> and device_scene = #{deviceScene}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="deviceDepartmentId != null "> and device_department_id = #{deviceDepartmentId}</if>
            <if test="deviceDepartmentName != null  and deviceDepartmentName != ''"> and device_department_name like concat('%', #{deviceDepartmentName}, '%')</if>
            <if test="deviceStatus != null "> and device_status = #{deviceStatus}</if>
            <if test="monitorId != null "> and t1.monitor_id = #{monitorId}</if>
            <if test="monitorName != null  and monitorName != ''"> and monitor_name like concat('%', #{monitorName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="lon != null  and lon != ''"> and lon = #{lon}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="openFlag != null "> and open_flag = #{openFlag}</if>
            <if test="lastConnectTime != null "> and last_connect_time = #{lastConnectTime}</if>
            <if test="lastDisConnectTime != null "> and last_dis_connect_time = #{lastDisConnectTime}</if>
            <if test="favoriteFlag != null and favoriteFlag">
                and t3.id is not null
            </if>
            <if test="warningFlag != null and warningFlag">
                and t2.id is not null
            </if>
            <if test="warningFlag != null and warningFlag">
                and t2.id is not null
            </if>
            <if test="excludeDeviceInfo != null and excludeDeviceInfo != ''">
                and t1.device_type != #{excludeDeviceInfo}
            </if>
        </where>
        order by t1.create_time desc, t1.device_third_id
    </select>

    <select id="selectDeviceInfoById" parameterType="Long" resultMap="DeviceInfoResult">
        select distinct (t1.id), t1.device_third_id, t1.device_num, t1.device_scene, t1.device_name, t1.device_type, t1.device_address,
        t1.device_department_id, t1.device_department_name, t1.device_status, t1.monitor_id, t1.monitor_name,
        t1.file_url, t1.lon, t1.lat, t1.open_flag, t1.last_connect_time, t1.last_dis_connect_time, t1.create_time,
        t1.create_by, t1.update_time, t1.update_by, t1.device_street, t1.device_street_area_code,
        t1.remark, t1.field01, t1.field02, t1.field03, t1.field04, t1.field05, t1.device_source,
        if(t3.id is not null, true, false) as favorite_flag,
        if(t2.id is not null, true, false) as warning_flag from t_device_info t1
        left join (select * from t_warning_info where warning_time <![CDATA[ >= ]]> NOW() - INTERVAL 1 MINUTE) t2 on t1.device_third_id = t2.device_third_id
        left join (select * from t_device_info_favorite where user_id = #{userId}) t3 on t1.id = t3.device_id
        where t1.id = #{id}
    </select>

    <insert id="insertDeviceInfo" parameterType="DeviceInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_device_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceThirdId != null">device_third_id,</if>
            <if test="deviceNum != null">device_num,</if>
            <if test="deviceScene != null">device_scene,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="deviceAddress != null">device_address,</if>
            <if test="deviceDepartmentId != null">device_department_id,</if>
            <if test="deviceDepartmentName != null">device_department_name,</if>
            <if test="deviceStatus != null">device_status,</if>
            <if test="monitorId != null">monitor_id,</if>
            <if test="monitorName != null">monitor_name,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="lon != null">lon,</if>
            <if test="lat != null">lat,</if>
            <if test="openFlag != null">open_flag,</if>
            <if test="lastConnectTime != null">last_connect_time,</if>
            <if test="lastDisConnectTime != null">last_dis_connect_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="deviceStreet != null">device_street,</if>
            <if test="deviceStreetAreaCode != null">device_street_area_code,</if>
            <if test="remark != null">remark,</if>
            <if test="field01 != null">field01,</if>
            <if test="field02 != null">field02,</if>
            <if test="field03 != null">field03,</if>
            <if test="field04 != null">field04,</if>
            <if test="field05 != null">field05,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceThirdId != null">#{deviceThirdId},</if>
            <if test="deviceNum != null">#{deviceNum},</if>
            <if test="deviceScene != null">#{deviceScene},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="deviceAddress != null">#{deviceAddress},</if>
            <if test="deviceDepartmentId != null">#{deviceDepartmentId},</if>
            <if test="deviceDepartmentName != null">#{deviceDepartmentName},</if>
            <if test="deviceStatus != null">#{deviceStatus},</if>
            <if test="monitorId != null">#{monitorId},</if>
            <if test="monitorName != null">#{monitorName},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="lon != null">#{lon},</if>
            <if test="lat != null">#{lat},</if>
            <if test="openFlag != null">#{openFlag},</if>
            <if test="lastConnectTime != null">#{lastConnectTime},</if>
            <if test="lastDisConnectTime != null">#{lastDisConnectTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="deviceStreet != null">#{deviceStreet},</if>
            <if test="deviceStreetAreaCode != null">#{deviceStreetAreaCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="field01 != null">#{field01},</if>
            <if test="field02 != null">#{field02},</if>
            <if test="field03 != null">#{field03},</if>
            <if test="field04 != null">#{field04},</if>
            <if test="field05 != null">#{field05},</if>
         </trim>
    </insert>

    <update id="updateDeviceInfo" parameterType="DeviceInfo">
        update t_device_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceThirdId != null">device_third_id = #{deviceThirdId},</if>
            <if test="deviceNum != null">device_num = #{deviceNum},</if>
            <if test="deviceScene != null">device_scene = #{deviceScene},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="deviceAddress != null">device_address = #{deviceAddress},</if>
            <if test="deviceDepartmentId != null">device_department_id = #{deviceDepartmentId},</if>
            <if test="deviceDepartmentName != null">device_department_name = #{deviceDepartmentName},</if>
            <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
            <if test="monitorId != null">monitor_id = #{monitorId},</if>
            <if test="monitorName != null">monitor_name = #{monitorName},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="openFlag != null">open_flag = #{openFlag},</if>
            <if test="lastConnectTime != null">last_connect_time = #{lastConnectTime},</if>
            <if test="lastDisConnectTime != null">last_dis_connect_time = #{lastDisConnectTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deviceStreet != null">device_street = #{deviceStreet},</if>
            <if test="deviceStreetAreaCode != null">device_street_area_code = #{deviceStreetAreaCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="field01 != null">field01 = #{field01},</if>
            <if test="field02 != null">field02 = #{field02},</if>
            <if test="field03 != null">field03 = #{field03},</if>
            <if test="field04 != null">field04 = #{field04},</if>
            <if test="field05 != null">field05 = #{field05},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceInfoById" parameterType="Long">
        delete from t_device_info where id = #{id}
    </delete>

    <delete id="deleteDeviceInfoByIds" parameterType="String">
        delete from t_device_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByType" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
        select device_type as `key`, count(1) as value
        from t_device_info
        where 1=1
        <if test="list != null and list.size() != 0">
            and device_type in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by device_type
    </select>

    <select id="countByMonitorTypeAndStatus" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
        select t2.monitor_type as `key`, device_status as tmpKey, count(1) as value
        from t_device_info t1 left join t_device_monitor t2 on t1.monitor_id = t2.id
        where 1=1
        <if test="list != null and list.size() != 0">
            and t2.monitor_type in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by t2.monitor_type, device_status
    </select>

    <select id="countByMonitorSceneAndStatus" resultType="com.ruoyi.jiangshan.vo.BusinessCountVO">
        select device_status as `key`, count(1) as value
        from t_device_info t1 left join t_device_monitor t2 on t1.monitor_id = t2.id
        where 1=1
          and t2.monitor_scene = #{scene}
        group by device_status
    </select>

    <select id="getByThirdId" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where device_third_id = #{deviceThirdId}
        order by create_time desc
        limit 1
    </select>

    <select id="listByDeviceType" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where 1=1
        <if test="deviceType != null and deviceType != ''">
            and device_type = #{deviceType,jdbcType=VARCHAR}
        </if>
        order by create_time desc
    </select>

    <select id="listAll" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        order by create_time desc
    </select>

    <select id="listByScene" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where device_scene = #{scene,jdbcType=VARCHAR}
        order by create_time desc
    </select>

    <select id="listByDeviceTypeAndScene" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where 1=1
        <if test="scene != null and scene != ''">
            and device_scene = #{scene,jdbcType=VARCHAR}
        </if>
        <if test="deviceType != null and deviceType != ''">
            and device_type = #{deviceType,jdbcType=VARCHAR}
        </if>
        <if test="mapFlag != null and mapFlag">
            and lon is not null and lat is not null
        </if>
        order by create_time desc
    </select>

    <select id="listBySceneList"  resultMap="DeviceInfoResult">
        select * from t_device_info where device_scene in
        <foreach collection="sceneList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="sumByType" resultType="java.lang.Long">
        select count(1) as value
        from t_device_info
        where 1=1
        <if test="typeList != null and typeList.size() != 0">
            and device_type in
            <foreach collection="typeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listByDeviceThirdIdList" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo"></include>
        where device_third_id in
        <foreach collection="deviceThirdIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into t_device_info
        (device_third_id, device_num, device_scene, device_name, device_type, device_address,
        device_department_id, device_department_name, device_status, monitor_id, monitor_name,
        file_url, lon, lat, open_flag, last_connect_time, last_dis_connect_time, create_time,
        create_by, update_time, update_by, device_street, device_street_area_code, remark,
        field01, field02, field03, field04, field05)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deviceThirdId,jdbcType=VARCHAR}, #{item.deviceNum,jdbcType=VARCHAR}, #{item.deviceScene,jdbcType=VARCHAR},
            #{item.deviceName,jdbcType=VARCHAR}, #{item.deviceType,jdbcType=VARCHAR}, #{item.deviceAddress,jdbcType=VARCHAR},
            #{item.deviceDepartmentId,jdbcType=BIGINT}, #{item.deviceDepartmentName,jdbcType=VARCHAR},
            #{item.deviceStatus,jdbcType=TINYINT}, #{item.monitorId,jdbcType=BIGINT}, #{item.monitorName,jdbcType=VARCHAR},
            #{item.fileUrl,jdbcType=VARCHAR}, #{item.lon,jdbcType=VARCHAR}, #{item.lat,jdbcType=VARCHAR},
            #{item.openFlag,jdbcType=BOOLEAN}, #{item.lastConnectTime,jdbcType=TIMESTAMP},
            #{item.lastDisConnectTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
            #{item.deviceStreet,jdbcType=VARCHAR}, #{item.deviceStreetAreaCode,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.field01,jdbcType=VARCHAR}, #{item.field02,jdbcType=VARCHAR},
            #{item.field03,jdbcType=VARCHAR}, #{item.field04,jdbcType=VARCHAR}, #{item.field05,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="getDeviceName" resultType="java.lang.String">
        select device_name
        from t_device_info
        where 1=1
        <if test="name != null and name != ''">
            and device_name like concat('%', #{name,jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <select id="getStreetByDevice" resultType="com.ruoyi.jiangshan.vo.compre.VideoStreetVO">
        select device_street as deviceStreet, device_street_area_code as deviceStreetAreaCode
        from t_device_info
        where device_type = #{deviceType,jdbcType=VARCHAR}
        and device_street is not null
        and device_street != ''
        group by device_street, device_street_area_code
    </select>

    <select id="getDeviceByStreet" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo"></include>
        where device_type = #{deviceType,jdbcType=VARCHAR}
        <if test="deviceStreetAreaCode != null and deviceStreetAreaCode != ''">
            and device_street_area_code = #{deviceStreetAreaCode,jdbcType=VARCHAR}
        </if>
        <if test="scene != null and scene != ''">
            and device_scene = #{scene,jdbcType=VARCHAR}
        </if>
        order by monitor_id, create_time
    </select>

    <select id="listByDeviceTypeList" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where device_type in
        <foreach collection="deviceTypeList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getById" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where id = #{deviceId,jdbcType=BIGINT}
    </select>

    <select id="sumExcludeType" resultType="java.lang.Long">
        select count(1) as value
        from t_device_info
        where 1=1
        <if test="videoList != null and videoList.size() != 0">
            and device_type not in
            <foreach collection="videoList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listDeviceInfoByGem" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo" />
        where lon != '' and lat != ''
        and ST_Contains(ST_GeomFromText(#{geometry}), ST_PointFromText(CONCAT('POINT(', lon, ' ', lat, ')')))
        and (device_street_area_code is null or device_street_area_code = '')
    </select>

    <update id="updateAddressByIdList">
        update t_device_info
        set device_address = #{address},
        device_street = #{street,jdbcType=VARCHAR},
        device_street_area_code = #{streetCode,jdbcType=VARCHAR}
        where id in
        <foreach collection="deviceIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="listByIdList" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo"></include>
        where id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateDeviceConnectStatus">
        update t_device_info
        set device_status = #{status,jdbcType=INTEGER}
        <choose>
            <when test="status == 1">
                , last_connect_time = #{connectTime}
            </when>
            <otherwise>
                , last_dis_connect_time = #{connectTime}
            </otherwise>
        </choose>
        where device_third_id = #{deviceThirdId,jdbcType=VARCHAR}
    </update>

    <select id="listByMonitorItemAndDeviceType" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where monitor_id = #{monitorId,jdbcType=BIGINT}
        and device_type = #{deviceType,jdbcType=VARCHAR}
    </select>

    <select id="listAllDeviceType" resultType="java.lang.String">
        select distinct device_type
        from t_device_info
    </select>

    <select id="listByDeviceTypeAndStatus" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where device_type = #{deviceType,jdbcType=VARCHAR}
        and device_status = #{status,jdbcType=INTEGER}
        order by create_time desc
    </select>

    <select id="listByDeviceNum" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where device_num in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listInlineByDeviceType" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where device_status = 0
        <if test="deviceType != null and deviceType != ''">
            and device_type = #{deviceType,jdbcType=VARCHAR}
        </if>
        <if test="monitorId != null">
            and monitor_id = #{monitorId}
        </if>
        order by create_time desc
    </select>

    <select id="getByField05" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where field05 = #{field05}
    </select>

    <update id="updateStatusByDeviceThirdId">
        update t_device_info
            set device_status = #{deviceStatus}
        where device_third_id = #{deviceThirdId}
    </update>

    <select id="listAllSjxbjcy" resultType="java.lang.String">
        select device_third_id
        from t_device_info
        where device_scene = '桥梁场景'
        and device_type = '视觉形变监测仪'
    </select>

    <select id="listByMonitorIdsAndType" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where monitor_id in
        <foreach collection="monitorIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and device_type in
        <foreach collection="deviceTypeList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="countByStatusAndSource" resultType="java.lang.Long">
        select count(1) as value
        from t_device_info
        where 1=1
        <if test="deviceSource != null">
            and device_source = #{deviceSource}
        </if>
        <if test="status != null">
            and device_status = #{status}
        </if>
    </select>

    <select id="listByYuLiang" resultMap="DeviceInfoResult">
        select * from t_device_info
        where device_type like concat('%', #{typeLike}, '%')
    </select>

    <select id="listByDeviceTypeOrMonitorId" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where 1=1
        <if test="deviceType != null and deviceType != ''">
            and device_type = #{deviceType,jdbcType=VARCHAR}
        </if>
        <if test="monitorId != null">
            and monitor_id = #{monitorId}
        </if>
        <if test="excludeList != null and excludeList.size() > 0">
            and device_type not in
            <foreach collection="excludeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by create_time desc
    </select>

    <select id="listByDeviceTypeAndDeviceStreet" resultMap="DeviceInfoResult">
        select * from t_device_info
        where 1=1
        <if test="deviceStreet != null and deviceStreet != ''">
            and device_street = #{deviceStreet}
        </if>
        and device_type like concat('%', #{deviceType}, '%')
    </select>

    <select id="listByDeviceTypeAndSceneAndMonitorId" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo">
        </include>
        where 1=1
        <if test="scene != null and scene != ''">
            and device_scene = #{scene,jdbcType=VARCHAR}
        </if>
        <if test="deviceType != null and deviceType != ''">
            and device_type = #{deviceType,jdbcType=VARCHAR}
        </if>
        <if test="mapFlag != null and mapFlag">
            and lon is not null and lat is not null
        </if>
        <if test="monitorId != null">
            and monitor_id = #{monitorId}
        </if>
        <if test="includeList != null and includeList.size() > 0">
            and device_third_id in
            <foreach collection="includeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="excludeList != null and excludeList.size > 0">
            and device_third_id not in
            <foreach collection="excludeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by create_time desc
    </select>

    <select id="listPushValue" resultType="com.ruoyi.jiangshan.domain.DeviceInfo">
        <include refid="selectDeviceInfoVo">
        </include>
        where 1=1
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time <![CDATA[ <= ]]> #{endTime}
        </if>
    </select>
</mapper>
