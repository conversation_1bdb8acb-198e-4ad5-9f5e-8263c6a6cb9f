package com.ruoyi.jiangshan.mapper;

import java.util.List;
import com.ruoyi.jiangshan.domain.WarningRule;

/**
 * 设备规则Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface WarningRuleMapper
{
    /**
     * 查询设备规则
     *
     * @param id 设备规则主键
     * @return 设备规则
     */
    public WarningRule selectWarningRuleById(Long id);

    /**
     * 查询设备规则列表
     *
     * @param warningRule 设备规则
     * @return 设备规则集合
     */
    public List<WarningRule> selectWarningRuleList(WarningRule warningRule);

    /**
     * 新增设备规则
     *
     * @param warningRule 设备规则
     * @return 结果
     */
    public int insertWarningRule(WarningRule warningRule);

    /**
     * 修改设备规则
     *
     * @param warningRule 设备规则
     * @return 结果
     */
    public int updateWarningRule(WarningRule warningRule);

    /**
     * 删除设备规则
     *
     * @param id 设备规则主键
     * @return 结果
     */
    public int deleteWarningRuleById(Long id);

    /**
     * 批量删除设备规则
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWarningRuleByIds(Long[] ids);

    WarningRule getByConditionId(Long conditionId);
}
