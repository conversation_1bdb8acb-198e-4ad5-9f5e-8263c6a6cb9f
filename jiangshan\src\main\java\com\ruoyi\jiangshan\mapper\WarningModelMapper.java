package com.ruoyi.jiangshan.mapper;

import java.util.List;
import com.ruoyi.jiangshan.domain.WarningModel;

/**
 * 设备模型Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface WarningModelMapper
{
    /**
     * 查询设备模型
     *
     * @param id 设备模型主键
     * @return 设备模型
     */
    public WarningModel selectWarningModelById(Long id);

    /**
     * 查询设备模型列表
     *
     * @param warningModel 设备模型
     * @return 设备模型集合
     */
    public List<WarningModel> selectWarningModelList(WarningModel warningModel);

    /**
     * 新增设备模型
     *
     * @param warningModel 设备模型
     * @return 结果
     */
    public int insertWarningModel(WarningModel warningModel);

    /**
     * 修改设备模型
     *
     * @param warningModel 设备模型
     * @return 结果
     */
    public int updateWarningModel(WarningModel warningModel);

    /**
     * 删除设备模型
     *
     * @param id 设备模型主键
     * @return 结果
     */
    public int deleteWarningModelById(Long id);

    /**
     * 批量删除设备模型
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWarningModelByIds(Long[] ids);

    List<WarningModel> listByIdList(List<String> modelIdList);

    List<WarningModel> listAll();
}
