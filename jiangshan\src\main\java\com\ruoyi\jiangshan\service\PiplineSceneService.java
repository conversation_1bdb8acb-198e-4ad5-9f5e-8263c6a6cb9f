package com.ruoyi.jiangshan.service;

import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.vo.BusinessCountVO;
import com.ruoyi.jiangshan.vo.WarningCountVO;
import com.ruoyi.jiangshan.vo.WarningInfoVO;

import java.util.List;
import java.util.Map;

public interface PiplineSceneService {

    Map<String, Object> getPiplineDetail();

    WarningCountVO getSafetyCount(String dateStr);

    List<WarningEvent> getEvent(String eventName, Integer status, Integer warningLevel);

    List<WarningInfoVO> getWarning(Integer warningLevel);

    Map<String, Object> getFlowMonitor(String dateStr, Long deviceId);

    Map<String, Object> getLevelMonitor(String dateStr, Long deviceId);

    List<DeviceInfo> getDevice(String deviceType);

    List<DeviceInfo> getDeviceByScene(String scene);
}
