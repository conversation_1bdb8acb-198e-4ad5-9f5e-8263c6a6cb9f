# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.6
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8651
  servlet:
    # 应用的访问路径
    context-path: /
    multipart:
      max-file-size: 100MB
      max-request-size: 50MB
      enabled: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: ***************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: les4408!
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 240

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

file:
  uploadUrl: http://172.16.10.67:8651
  tmpUrl: D://ruoyi/uploadPath/tmp
  fontUrl: C:\Windows\Fonts
  openUploadUrl: https://csaqyj.jiangshan.gov.cn:8501/prod-api


# 专有钉钉登录配置
login:
  dtalk:
    appKey: jiangshan-saoma-test_din-y6rt55M9meUgAvubvVTgSeN4en0a8B161nI7Nza
    appSecret: lLo6J0zm69Dc0QZE45MT3D6vQAj17sf5r9vrzUxT

# 专有钉钉租户部门配置
dept:
  dtalk:
    appKey: jiangshan-app-test-Y8pyzjaA3uu
    appSecret: Tty1miMVj8s9bnWSh7qJE0p9FD2ojAoQX09PF6ur
    tenantId: 50415073

# 专有钉钉登录地址配置
dtalk:
  domain: open.on-premises.dingtalk.com
  login:
    url: https://login.dg-work.cn/oauth2/auth.htm?response_type=code&client_id=jiangshan-saoma-test_dingoa&scope=get_user_info&authType=QRCODE&redirect_uri=http://172.16.10.8:8081
  protocal: https

redis:
  message-channel: DEVICE_DATA_CHANNEL

single:
  #  url: http://***************:8501/h5/event/EventDetail
  url: https://csaqyj.jiangshan.gov.cn:8501/h5/event/EventDetail
  pcUrl: https://csaq.jiangshan.gov.cn:8501/system/user

#dingding:
#  appId: 1b7f30dc-5898-44b6-8af8-09181dcf8cf0
#  agentId: 3336163835
#  appKey: dingfpbrqk3ddjxlbjmv
#  appSecret: x-t_2-2YbS6P4G4KuqeTwFHWR5it062GOFJUao78wpkHc6A1pZA94yGDwAEm7lZO

dingding:
  appId: 749322c0-bc3f-44e3-a5ee-9f2cc37a8c84
  agentId: 3346556115
  appKey: ding4kimcmquc9wkdzp5
  appSecret: D0zKZuNkvhZhxJ9hbQ3CkD1JDK1qbtJWhIPywYYSydYmxagNrZI6_9mjwDfujujR


