package com.ruoyi.jiangshan.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.Data;
import java.util.Date;

/**
 * 降雨量对象 t_jiangshan_rainfall
 */
@Data
public class JiangshanRainfall {
    /** 主键 */
    private String id;

    /** p */
    private String p;

    /** 乡镇 */
    private String town;

    /** 村 */
    private String name;

    /** x */
    private String x;

    /** county */
    private String county;

    /** y */
    private String y;

    /** 降雨量 */
    private String v1;

    /** 监测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitorTime;
}
