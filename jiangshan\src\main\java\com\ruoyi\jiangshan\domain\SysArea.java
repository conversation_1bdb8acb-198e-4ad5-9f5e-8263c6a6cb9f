package com.ruoyi.jiangshan.domain;

import java.math.BigDecimal;

/**
    * 区域表
    */
public class SysArea {
    /**
    * 区域编码
    */
    private Long id;

    /**
    * 父区域编码
    */
    private Long parentId;

    /**
    * 地区名称
    */
    private String areaName;

    /**
    * 维度
    */
    private BigDecimal lat;

    /**
    * 经度
    */
    private BigDecimal lng;

    /**
    * 位置路径
    */
    private String locationPath;

    /**
    * 层级
    */
    private Integer layered;

    /**
    * 排序
    */
    private Integer sort;

    /**
    * 层级
    */
    private String levelCode;

    /**
    * 与父级拼接后的区域称
    */
    private String address;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public BigDecimal getLat() {
        return lat;
    }

    public void setLat(BigDecimal lat) {
        this.lat = lat;
    }

    public BigDecimal getLng() {
        return lng;
    }

    public void setLng(BigDecimal lng) {
        this.lng = lng;
    }

    public String getLocationPath() {
        return locationPath;
    }

    public void setLocationPath(String locationPath) {
        this.locationPath = locationPath;
    }

    public Integer getLayered() {
        return layered;
    }

    public void setLayered(Integer layered) {
        this.layered = layered;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}