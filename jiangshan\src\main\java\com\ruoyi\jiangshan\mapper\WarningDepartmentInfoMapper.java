package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningDepartmentInfo;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface WarningDepartmentInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningDepartmentInfo record);

    int insertSelective(WarningDepartmentInfo record);

    WarningDepartmentInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningDepartmentInfo record);

    int updateByPrimaryKey(WarningDepartmentInfo record);

    int updateBatch(List<WarningDepartmentInfo> list);

    int batchInsert(@Param("list") List<WarningDepartmentInfo> list);

    /**
     * 查询联动处置中心部门
     *
     * @param id 联动处置中心部门主键
     * @return 联动处置中心部门
     */
    WarningDepartmentInfo selectWarningDepartmentInfoById(Long id);

    /**
     * 查询联动处置中心部门列表
     *
     * @param warningDepartmentInfo 联动处置中心部门
     * @return 联动处置中心部门集合
     */
    List<WarningDepartmentInfo> selectWarningDepartmentInfoList(WarningDepartmentInfo warningDepartmentInfo);

    /**
     * 新增联动处置中心部门
     *
     * @param warningDepartmentInfo 联动处置中心部门
     * @return 结果
     */
    int insertWarningDepartmentInfo(WarningDepartmentInfo warningDepartmentInfo);

    /**
     * 修改联动处置中心部门
     *
     * @param warningDepartmentInfo 联动处置中心部门
     * @return 结果
     */
    int updateWarningDepartmentInfo(WarningDepartmentInfo warningDepartmentInfo);

    /**
     * 删除联动处置中心部门
     *
     * @param id 联动处置中心部门主键
     * @return 结果
     */
    int deleteWarningDepartmentInfoById(Long id);

    /**
     * 批量删除联动处置中心部门
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteWarningDepartmentInfoByIds(Long[] ids);

    List<WarningDepartmentInfo> listByParentId(Long parentId);

    WarningDepartmentInfo getByOrgCode(String organizationCode);

    List<WarningDepartmentInfo> listCopyDept(String keywords);
}
