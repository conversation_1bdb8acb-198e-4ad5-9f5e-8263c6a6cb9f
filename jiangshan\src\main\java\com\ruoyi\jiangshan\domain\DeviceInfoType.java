package com.ruoyi.jiangshan.domain;

import java.util.Date;

/**
    * 设备信息-设备类型
    */
public class DeviceInfoType {
    /**
    * 主键
    */
    private Long id;

    /**
    * 监测类型
    */
    private String monitorType;

    /**
    * 设备类型
    */
    private String deviceType;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 创建人
    */
    private String createBy;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMonitorType() {
        return monitorType;
    }

    public void setMonitorType(String monitorType) {
        this.monitorType = monitorType;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
}