package com.ruoyi.jiangshan.controller;

import java.io.IOException;
import java.util.List;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.jiangshan.vo.BusinessSaveFileVO;
import com.ruoyi.jiangshan.vo.DeviceInfoImportVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.service.IDeviceInfoService;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 设备信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/deviceInfo")
public class DeviceInfoController extends BaseController
{
    @Autowired
    private IDeviceInfoService deviceInfoService;

    /**
     * 查询设备信息列表
     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceInfo deviceInfo)
    {
        startPage();
        List<DeviceInfo> list = deviceInfoService.selectDeviceInfoList(deviceInfo);
        return getDataTable(list);
    }

    /**
     * 查询设备信息列表
     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:list')")
    @GetMapping("/listV2")
    public TableDataInfo listV2(DeviceInfo deviceInfo)
    {
        startPage();
        List<DeviceInfo> list = deviceInfoService.selectDeviceInfoListV2(deviceInfo);
        return getDataTable(list);
    }

    /**
     * 根据设备场景查找所有设备信息
     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:list')")
    @GetMapping("/getByScene")
    public AjaxResult getByScene(String scene)
    {
        List<DeviceInfo> list = deviceInfoService.getByScene(scene);
        return AjaxResult.success(list);
    }

//    /**
//     * 导出设备信息列表
//     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:export')")
//    @Log(title = "设备信息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, DeviceInfo deviceInfo)
//    {
//        List<DeviceInfo> list = deviceInfoService.selectDeviceInfoList(deviceInfo);
//        ExcelUtil<DeviceInfo> util = new ExcelUtil<DeviceInfo>(DeviceInfo.class);
//        util.exportExcel(response, list, "设备信息数据");
//    }

    /**
     * 获取设备信息详细信息
     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deviceInfoService.selectDeviceInfoById(id));
    }

    /**
     * 收藏
     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:edit')")
    @Log(title = "收藏", businessType = BusinessType.UPDATE)
    @PostMapping("/favorite/{id}")
    public AjaxResult updateFavorite(@PathVariable Long id)
    {
        return toAjax(deviceInfoService.updateFavorite(id));
    }

    /**
     * 取消收藏
     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:edit')")
    @Log(title = "取消收藏", businessType = BusinessType.UPDATE)
    @PostMapping("/cancelFavorite/{id}")
    public AjaxResult updateCancelFavorite(@PathVariable Long id)
    {
        return toAjax(deviceInfoService.updateCancelFavorite(id));
    }

    /**
     * 根据设备场景查找设备类型
     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:list')")
    @GetMapping("/getDeviceType")
    public AjaxResult getDeviceType(String scene)
    {
        List<String> list = deviceInfoService.getDeviceType(scene);
        return AjaxResult.success(list);
    }

//    /**
//     * 新增设备信息
//     */
//    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:add')")
//    @Log(title = "设备信息", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody DeviceInfo deviceInfo)
//    {
//        return toAjax(deviceInfoService.insertDeviceInfo(deviceInfo));
//    }
//
//    /**
//     * 修改设备信息
//     */
//    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:edit')")
//    @Log(title = "设备信息", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody DeviceInfo deviceInfo)
//    {
//        return toAjax(deviceInfoService.updateDeviceInfo(deviceInfo));
//    }

//    /**
//     * 删除设备信息
//     */
//    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:remove')")
//    @Log(title = "设备信息", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(deviceInfoService.deleteDeviceInfoByIds(ids));
//    }

    /**
     * 设备信息导入
     */
    @PostMapping("/import")
    public AjaxResult importDevice(MultipartFile file) throws IOException {
        ExcelUtil<DeviceInfoImportVO> util = new ExcelUtil<>(DeviceInfoImportVO.class);
        List<DeviceInfoImportVO> list = util.importExcel(file.getInputStream());
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.error("导入失败");
        }

        deviceInfoService.excelImport(list);
        return AjaxResult.success();
    }

    /**
     * 设备信息Excel模板
     */
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<DeviceInfoImportVO> util = new ExcelUtil<DeviceInfoImportVO>(DeviceInfoImportVO.class);
        util.exportExcel(response, Lists.newArrayList(), "设备信息");
    }

    /**
     * 获取所有设备名称
     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:list')")
    @GetMapping("/getDeviceName")
    public AjaxResult getDeviceName(String name)
    {
        List<String> list = deviceInfoService.getDeviceName(name);
        return AjaxResult.success(list);
    }

    /**
     * 上传图片
     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:list')")
    @PostMapping("/uploadPicture")
    public AjaxResult uploadPicture(@RequestBody BusinessSaveFileVO saveFileVO)
    {
        deviceInfoService.uploadPicture(saveFileVO);
        return AjaxResult.success();
    }

    /**
     * 修改设备经纬度
     */
    //@PreAuthorize("@ss.hasPermi('deviceInfo:deviceInfo:edit')")
    @Log(title = "修改设备经纬度", businessType = BusinessType.UPDATE)
    @PutMapping("/updateLon")
    public AjaxResult updateLon(@RequestBody DeviceInfo deviceInfo)
    {
        DeviceInfo newDeviceInfo = new DeviceInfo();
        newDeviceInfo.setId(deviceInfo.getId());
        newDeviceInfo.setLon(deviceInfo.getLon());
        newDeviceInfo.setLat(deviceInfo.getLat());

        deviceInfoService.updateDeviceInfo(newDeviceInfo);

        return AjaxResult.success();
    }


}
