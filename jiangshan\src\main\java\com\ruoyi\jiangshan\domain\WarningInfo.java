package com.ruoyi.jiangshan.domain;

import lombok.Data;

import java.util.Date;

/**
    * 设备预警信息
    */
@Data
public class WarningInfo {
    /**
    * 主键
    */
    private Long id;

    /**
    * 规则id
    */
    private Long conditionId;

    /**
    * 对象id
    */
    private Long monitorId;

    /**
    * 设备id
    */
    private Long deviceId;

    /**
    * 设备第三方id
    */
    private String deviceThirdId;

    /**
    * 比较符号条件
    */
    private String ruleCondition;

    /**
    * 监测项
    */
    private String monitorItem;

    /**
    * 比较值
    */
    private String monitorValue;

    /**
    * 监测单位
    */
    private String monitorUnit;

    /**
    * 预警原因
    */
    private String warningReason;

    /**
    * 告警等级 1-4
    */
    private Integer warningLevel;

    /**
    * 预警时间
    */
    private Date warningTime;

    /**
    * 预警地点
    */
    private String warningAddress;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 创建人
    */
    private String createBy;

    /**
     * 预警名称
     */
    private String warningName;

    /**
     * 设备数据id
     */
    private Long deviceValueId;

    /**
     * 预警数据
     */
    private String warningData;

    /**
     * 预警场景
     */
    private String warningScene;

    /**
     * 预警第三方id
     */
    private String eventThirdId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 统计时间范围
     */
    private String lenTime;

    /**
     * 监测数据起始时间
     */
    private Date startTime;

    /**
     * 监测数据截止时间
     */
    private Date endTime;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 行政区划code
     */
    private String eventStreetAreaCode;

    /**
     * 行政区划name
     */
    private String eventStreet;

    /**
     * 风险区编号
     */
    private String fxqbh;

    private Integer superviseFlag;

    private Integer overFlag;
}
