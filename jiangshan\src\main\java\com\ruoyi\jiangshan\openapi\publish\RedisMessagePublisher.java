package com.ruoyi.jiangshan.openapi.publish;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RedisMessagePublisher {
    @Autowired
    private StringRedisTemplate redisTemplate;

    public void publish(String channel, String message) {
//        log.info("RedisMessagePublisher, channel:{}, message:{}", channel, message);

        try {
            redisTemplate.convertAndSend(channel, message);
        } catch (Exception e) {
            log.info("发送消息失败, msg:{}", e.toString());
            throw new RuntimeException("接收设备数据失败" + e.getMessage());
        }
    }

}
