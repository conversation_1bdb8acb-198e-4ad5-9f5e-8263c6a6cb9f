package com.ruoyi.jiangshan.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 监测对象-河道对象 t_device_monitor_hedao
 * 
 * <AUTHOR>
 * @date 2025-03-26
 */
public class DeviceMonitorHedao extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String jcmc;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bdyz;

    public void setJcmc(String jcmc) 
    {
        this.jcmc = jcmc;
    }

    public String getJcmc() 
    {
        return jcmc;
    }
    public void setBdyz(String bdyz) 
    {
        this.bdyz = bdyz;
    }

    public String getBdyz() 
    {
        return bdyz;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("jcmc", getJcmc())
            .append("bdyz", getBdyz())
            .toString();
    }
}
