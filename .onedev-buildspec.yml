version: 25
jobs:
- name: CI
  steps:
  - !CheckoutStep
    name: checkout
    cloneCredential: !DefaultCredential {}
    withLfs: false
    withSubmodules: true
    condition: ALL_PREVIOUS_STEPS_WERE_SUCCESSFUL
  - !CommandStep
    name: build
    runInContainer: false
    image: ldev/build-environment:1.3
    interpreter: !PowerShellInterpreter
      commands:
      - mvn clean install -DskipTests=true
      - ''
      - cd ruoyi-admin/target
      - ''
      - Start-Process java -ArgumentList '-jar','ruoyi-admin.jar','-background'
    useTTY: false
    condition: ALL_PREVIOUS_STEPS_WERE_SUCCESSFUL
  - !CommandStep
    name: vue-build
    runInContainer: false
    interpreter: !PowerShellInterpreter
      commands:
      - cd ruoyi-ui
      - npm run dev
    useTTY: false
    condition: NEVER
  - !PublishJestReportStep
    name: publish test report
    reportName: Jest Test Report
    filePatterns: testResults.json
    condition: NEVER
  triggers:
  - !BranchUpdateTrigger {}
  retryCondition: never
  maxRetries: 3
  retryDelay: 30
  timeout: 3600
