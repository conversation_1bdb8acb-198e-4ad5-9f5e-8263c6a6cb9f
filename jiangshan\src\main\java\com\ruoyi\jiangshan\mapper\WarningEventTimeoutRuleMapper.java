package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.WarningEventTimeoutRule;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WarningEventTimeoutRuleMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarningEventTimeoutRule record);

    int insertSelective(WarningEventTimeoutRule record);

    WarningEventTimeoutRule selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarningEventTimeoutRule record);

    int updateByPrimaryKey(WarningEventTimeoutRule record);

    int updateBatch(List<WarningEventTimeoutRule> list);

    int batchInsert(@Param("list") List<WarningEventTimeoutRule> list);

    List<WarningEventTimeoutRule> listAll();

}
