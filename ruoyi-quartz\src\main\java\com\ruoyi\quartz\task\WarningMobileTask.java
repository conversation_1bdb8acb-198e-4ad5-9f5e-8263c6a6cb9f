package com.ruoyi.quartz.task;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.jiangshan.client.VoiceClient;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.enums.WarningCenterGenerateType;
import com.ruoyi.jiangshan.enums.WarningCenterReportType;
import com.ruoyi.jiangshan.mapper.CitysafetyReportMapper;
import com.ruoyi.jiangshan.mapper.CitysafetyRuleMapper;
import com.ruoyi.jiangshan.mapper.WarningEventMapper;
import com.ruoyi.jiangshan.service.impl.CitysafetyReportServiceImpl;
import com.ruoyi.jiangshan.service.impl.WarningEventServiceImpl;
import com.ruoyi.jiangshan.vo.CitySafetyQuartzTimeVO;
import com.ruoyi.jiangshan.vo.WarningEventMobileSendVO;
import javafx.beans.binding.ObjectExpression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("warningMobileTask")
public class WarningMobileTask {

    public void sendMsgTask() {
        WarningEventMapper warningEventMapper = SpringUtils.getBean(WarningEventMapper.class);
        WarningEventServiceImpl warningEventService = SpringUtils.getBean(WarningEventServiceImpl.class);
        VoiceClient voiceClient = SpringUtils.getBean(VoiceClient.class);

        List<WarningEvent> warningEventList = warningEventMapper.listNonSignEvent();
        if (CollectionUtils.isEmpty(warningEventList)) {
            return;
        }

        List<WarningEventMobileSendVO> mobileList = Lists.newArrayList();
        for (WarningEvent warningEvent : warningEventList) {
            WarningEventDepartment warningEventDepartment = warningEventService.getLastUserMobile(warningEvent.getId());

            if (Objects.nonNull(warningEventDepartment)) {
                String mobile = warningEventDepartment.getEmployeeMobile();

                if (StringUtils.isNotBlank(mobile)) {
                    WarningEventMobileSendVO warningEventMobileSendVO = new WarningEventMobileSendVO();
                    warningEventMobileSendVO.setMobile(mobile);
                    warningEventMobileSendVO.setEventName(warningEvent.getEventName());
                    mobileList.add(warningEventMobileSendVO);
                }
            }
        }

        log.info("sendMsgTask, mobile:{}", JSON.toJSONString(mobileList));

        Map<String, List<WarningEventMobileSendVO>> mobileMap = mobileList.stream()
                .collect(Collectors.groupingBy(WarningEventMobileSendVO::getMobile));


        if (CollectionUtils.isNotEmpty(mobileList)) {
            for (Map.Entry<String, List<WarningEventMobileSendVO>> mobileEntry : mobileMap.entrySet()) {
                String mobile = mobileEntry.getKey();
                List<WarningEventMobileSendVO> oneList = mobileEntry.getValue();
                if (oneList.size() > 1) {
                    voiceClient.sendMobileMsgWithContent(mobile, "您有多条新的事件告警，请及时处置");
                } else {
                    WarningEventMobileSendVO warningEventMobileSendVO = oneList.get(0);
                    voiceClient.sendMobileMsgWithContent(mobile, "您有一条新的事件告警，名称为" + warningEventMobileSendVO.getEventName() + "，请及时处置");
                }
            }

        }
    }
}
