package com.ruoyi.jiangshan.openapi.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class WarningEventOpenApiVO {

    /**
     * 风险区编号
     */
    @NotNull(message = "风险区编号不能为空")
    private String fxqbh;

    /** 预警事件编号 */
    @NotBlank(message = "预警事件编号不能为空")
    private String eventThirdId;

    /** 模型名称 */
    @NotBlank(message = "模型名称不能为空")
    private String modelName;

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String deviceThirdId;

//    /**
//     * 设备名称
//     */
////    @NotBlank(message = "设备名称不能为空")
//    private String deviceName;

    /**
     * 统计时间范围
     */
    private String lenTime;

    /**
     * 报警值
     */
    private String warningValue;

    /** 风险等级 1=一级告警,2=二级告警,3=三级告警,4=四级告警 */
    @NotNull(message = "风险等级不能为空, 风险等级 1=一级告警,2=二级告警,3=三级告警,4=四级告警")
    private Integer warningLevel;

    /**
     * 阈值
     */
    private String waringThreshold;

    /** 预警时间 yyyy-MM-dd HH:mm:ss*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "预警时间不能为空")
    private Date warningTime;

    /**
     * 监测数据起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 监测数据截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 经度
     */
    @NotBlank(message = "经度不能为空")
    private String lon;

    /**
     * 纬度
     */
    @NotBlank(message = "纬度不能为空")
    private String lat;

//    /** 事件名称 */
//    @NotBlank(message = "事件名称不能为空")
//    private String eventName;

    /**
     * 事件类型(场景) 传以下场景之一: 水利场景,桥梁场景,地质灾害场景,内涝场景,污水管线场景,燃气场景,消防场景
     */
    @NotBlank(message = "事件类型不能为空")
    private String eventType;

    /** 事件地点 */
//    @NotBlank(message = "事件地点不能为空")
    private String eventAddress;

//    /** 风险点具体描述 */
//    @NotBlank(message = "风险点具体描述不能为空")
//    private String warningDetail;

    /**
     * 行政区划(乡镇/街道)
     */
    @NotBlank(message = "行政区划名称不能为空")
    private String eventStreet;

    /**
     * 行政区划code
     */
    @NotBlank(message = "行政区划code不能为空")
    private String eventStreetAreaCode;
}
