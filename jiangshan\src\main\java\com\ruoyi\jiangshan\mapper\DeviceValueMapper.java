package com.ruoyi.jiangshan.mapper;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.ruoyi.jiangshan.domain.DeviceValue;
import com.ruoyi.jiangshan.vo.BusinessCountVO;
import com.ruoyi.jiangshan.vo.BusinessDateCountVO;
import com.ruoyi.jiangshan.vo.WarningReportDeviceVO;
import org.apache.ibatis.annotations.Param;

/**
 * 设备实时数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface DeviceValueMapper
{
    /**
     * 查询设备实时数据
     *
     * @param id 设备实时数据主键
     * @return 设备实时数据
     */
    public DeviceValue selectDeviceValueById(Long id);

    /**
     * 查询设备实时数据列表
     *
     * @param deviceValue 设备实时数据
     * @return 设备实时数据集合
     */
    public List<DeviceValue> selectDeviceValueList(DeviceValue deviceValue);

    /**
     * 新增设备实时数据
     *
     * @param deviceValue 设备实时数据
     * @return 结果
     */
    public int insertDeviceValue(DeviceValue deviceValue);

    /**
     * 修改设备实时数据
     *
     * @param deviceValue 设备实时数据
     * @return 结果
     */
    public int updateDeviceValue(DeviceValue deviceValue);

    /**
     * 删除设备实时数据
     *
     * @param id 设备实时数据主键
     * @return 结果
     */
    public int deleteDeviceValueById(Long id);

    /**
     * 批量删除设备实时数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceValueByIds(Long[] ids);

    List<BusinessDateCountVO> listByMonitorIdAndItem(@Param("id") Long id,
                                             @Param("monitorItem") String monitorItem,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime);

    List<BusinessCountVO> getMaxValueByDeviceType(@Param("deviceTypeList") List<String> deviceTypeList);

    List<BusinessDateCountVO> listByDateAndDeviceId(@Param("startTime") Date startTime,
                                                    @Param("endTime") Date endTime,
                                                    @Param("deviceThirdId") String deviceThirdId,
                                                    @Param("monitorItem") String monitorItem);

    List<BusinessDateCountVO> countByDeviceThirdIdAndItem(@Param("deviceThirdId") String deviceThirdId,
                                                          @Param("monitorItem") String monitorItem);

    void batchInsert(List<DeviceValue> deviceValueList);

    double getMaxValueByMonitorItem(@Param("monitorItem") String monitorItem,
                                  @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    double getAvgValueByMonitorItem(@Param("monitorItem") String monitorItem,
                                    @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<DeviceValue> listByMonitorIdListAndDate(@Param("list") List<Long> monitorIdList,
                                                 @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<DeviceValue> listByDeviceIdList(@Param("list") List<Long> deviceInfoIdList);

    List<WarningReportDeviceVO> listReport(@Param("sceneList") String[] sceneList,
                                           @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                           @Param("geoStr") String geoStr);

    List<DeviceValue> listByDeviceAndMonitorItem(@Param("deviceThirdId") String deviceThirdId, @Param("monitorItem") String monitorItem,
                                                 @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<DeviceValue> listByDeviceAndMonitorItemV2(@Param("deviceThirdId") String deviceThirdId, @Param("monitorItem") String monitorItem,
                                                 @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<DeviceValue> listByDeviceThirdId(@Param("deviceThirdId") String deviceThirdId);

    List<DeviceValue> listByDeviceThirdIdList(@Param("list") List<String> deviceThirdIdList, @Param("days") Integer days);

    List<DeviceValue> listByDeviceThirdIdListByHour(@Param("list") List<String> deviceThirdIdList, @Param("hour") Integer hour);

    List<DeviceValue> getLineData(@Param("deviceThirdId") String deviceThirdId,
                                  @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    String getMaxValueByDeviceTypeAndMonitorItem(@Param("deviceTypeList") List<String> deviceTypeList,
                                                 @Param("monitorItem") String monitorItem,
                                                 @Param("startTime") Date startTime,
                                                 @Param("endTime") Date endTime);

    String getLastValueByDeviceThirdId(@Param("deviceThirdId") String deviceThirdId, @Param("monitorItem") String monitorItem);

    Long getCount(@Param("deviceThirdId") String deviceThirdId, @Param("monitorItem") String monitorItem,
                  @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<DeviceValue> listByDeviceThirdIdListAndMonitorItem(@Param("list") List<String> deviceThirdIdList,
                                                            @Param("monitorItem") String monitorItem);

    /**
     * 统计指定时间范围内的设备数据数量
     */
    int countDeviceValueByTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 分页获取指定时间范围内的设备数据
     */
    List<DeviceValue> getDeviceValueByTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("offset") int offset,
            @Param("limit") int limit
    );

    String getAvgRainFall(Integer hour);

    List<DeviceValue> listRainFallInHour();

    List<DeviceValue> listByIdList(@Param("list") List<Long> deviceValueIdList);

    List<DeviceValue> listByDeviceThirdIdAndMonitorItemInTimeRange(@Param("list") List<String> deviceThirdIdList,
                                                                   @Param("monitorItem") String monitorItem,
                                                                   @Param("startTime") LocalDateTime startTime,
                                                                   @Param("endTime") LocalDateTime endTime);

    String getAvgRainFallByTime(@Param("startLdt") LocalDateTime startLdt, @Param("endLdt") LocalDateTime endLdt);
}
