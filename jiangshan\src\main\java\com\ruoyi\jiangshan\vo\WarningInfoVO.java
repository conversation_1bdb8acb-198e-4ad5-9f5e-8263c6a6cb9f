package com.ruoyi.jiangshan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
    * 设备预警信息
    */
@Data
public class WarningInfoVO {
    /**
    * 主键
    */
    private Long id;

    /**
    * 规则id
    */
    private Long conditionId;

    /**
    * 对象id
    */
    private Long monitorId;

    /**
    * 设备id
    */
    private Long deviceId;

    /**
    * 设备第三方id
    */
    private String deviceThirdId;

    /**
    * 比较符号条件
    */
    private String ruleCondition;

    /**
    * 监测项
    */
    private String monitorItem;

    /**
    * 比较值
    */
    private String monitorValue;

    /**
    * 监测单位
    */
    private String monitorUnit;

    /**
    * 预警原因
    */
    private String warningReason;

    /**
    * 告警等级 1-4
    */
    private Integer warningLevel;

    /**
    * 预警时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date warningTime;

    /**
    * 预警地点
    */
    private String warningAddress;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 创建人
    */
    private String createBy;

    /**
     * 预警名称
     */
    private String warningName;

    /**
     * 设备数据id
     */
    private Long deviceValueId;

    /**
     * 预警数据
     */
    private String warningData;

    /**
     * 预警场景
     */
    private String warningScene;

    private String monitorName;

    private String monitorType;

    private String deviceName;

    private String lon;

    private String lat;
}
