package com.ruoyi.jiangshan.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jiangshan.domain.*;
import com.ruoyi.jiangshan.enums.WarningLevelType;
import com.ruoyi.jiangshan.mapper.*;
import com.ruoyi.jiangshan.openapi.vo.WarningRuleOpenApiVO;
import com.ruoyi.system.mapper.SysUserMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.jiangshan.service.IWarningRuleService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 设备规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class WarningRuleServiceImpl implements IWarningRuleService
{
    @Autowired
    private WarningRuleMapper warningRuleMapper;
    @Autowired
    private WarningRuleConditionMapper warningRuleConditionMapper;
    @Autowired
    private DeviceInfoItemMapper deviceInfoItemMapper;
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Autowired
    private WarningModelMapper warningModelMapper;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询设备规则
     *
     * @param id 设备规则主键
     * @return 设备规则
     */
    @Override
    public WarningRule selectWarningRuleById(Long id)
    {
        WarningRule warningRule = warningRuleMapper.selectWarningRuleById(id);
        if (Objects.isNull(warningRule)) {
            return null;
        }

        fillConditionList(Lists.newArrayList(warningRule));
        fillMonitorName(Lists.newArrayList(warningRule));
        fillDeviceIdList(Lists.newArrayList(warningRule));
        fillOtherUserId(Lists.newArrayList(warningRule));

        return warningRule;
    }

    private void fillOtherUserId(List<WarningRule> warningRuleList) {
        if (CollectionUtils.isEmpty(warningRuleList)) {
            return;
        }
        for (WarningRule warningRule : warningRuleList) {
            if (StringUtils.isNotBlank(warningRule.getOtherUserId())) {
                List<String> otherUserIdList = Arrays.asList(StringUtils.split(warningRule.getOtherUserId(), ","));

                warningRule.setOtherUserIdList(otherUserIdList);
            }
        }
    }

    private void fillDeviceIdList(List<WarningRule> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return;
        }

        for (WarningRule warningRule : ruleList) {
            List<WarningRuleCondition> conditionList = warningRule.getConditionList();
            if (CollectionUtils.isNotEmpty(conditionList)) {
                for (WarningRuleCondition condition : conditionList) {
                    List<String> list = Arrays.asList(StringUtils.split(condition.getDeviceId(), ","));

                    List<Long> idList = list.stream()
                            .map(Long::valueOf)
                            .collect(Collectors.toList());

                    condition.setDeviceIdList(idList);
                }
            }
        }
    }

    /**
     * 查询设备规则列表
     *
     * @param warningRule 设备规则
     * @return 设备规则
     */
    @Override
    public List<WarningRule> selectWarningRuleList(WarningRule warningRule)
    {
        List<WarningRule> ruleList = warningRuleMapper.selectWarningRuleList(warningRule);

        fillConditionList(ruleList);
//        fillMonitorName(ruleList);
        fillModelName(ruleList);
        fillOtherUserId(ruleList);

        return ruleList;
    }

    private void fillModelName(List<WarningRule> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return;
        }

        List<String> modelIdList = ruleList.stream()
                .map(WarningRule::getModelId)
                .collect(Collectors.toList());

        List<WarningModel> warningModelList = warningModelMapper.listByIdList(modelIdList);

        Map<Long, String> modelMap = warningModelList.stream()
                .collect(Collectors.toMap(WarningModel::getId, WarningModel::getModelName));

        for (WarningRule rule : ruleList) {
            String modelName = modelMap.get(Long.parseLong(rule.getModelId()));

            rule.setModelName(modelName);
        }
    }

    private void fillMonitorName(List<WarningRule> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return;
        }

        List<String> modelIdList = ruleList.stream()
                .filter(Objects::nonNull)
                .map(WarningRule::getModelId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(modelIdList)) {
            return;
        }

        List<DeviceMonitor> deviceMonitorList = deviceMonitorMapper.listByIdList(modelIdList);

        Map<Long, String> monitorMap = deviceMonitorList.stream()
                .collect(Collectors.toMap(DeviceMonitor::getId, DeviceMonitor::getMonitorName));

        for (WarningRule rule : ruleList) {
            String modelName = monitorMap.get(Long.parseLong(rule.getModelId()));

            rule.setModelName(modelName);
        }
    }

    private void fillConditionList(List<WarningRule> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return;
        }

        List<Long> ruleIdList = ruleList.stream()
                .filter(Objects::nonNull)
                .map(WarningRule::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ruleIdList)) {
            return;
        }

        List<WarningRuleCondition> conditionList = warningRuleConditionMapper.listByRuleIdList(ruleIdList);

        Map<Long, List<WarningRuleCondition>> ruleConditionMap = conditionList.stream()
                .collect(Collectors.groupingBy(WarningRuleCondition::getRuleId));

        for (WarningRule rule : ruleList) {
            List<WarningRuleCondition> conditionOneList = ruleConditionMap.get(rule.getId());

            rule.setConditionList(conditionOneList);
        }
    }

    /**
     * 新增设备规则
     *
     * @param warningRule 设备规则
     * @return 结果
     */
    @Override
    @Transactional
    public int insertWarningRule(WarningRule warningRule)
    {
        checkCondition(warningRule.getConditionList());

        warningRule.setCreateTime(DateUtils.getNowDate());
        warningRule.setCreateBy(SecurityUtils.getUsername());

        List<WarningRuleCondition> conditionList = warningRule.getConditionList();

        if (CollectionUtils.isNotEmpty(conditionList)) {
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < conditionList.size(); i++) {
                WarningRuleCondition condition = conditionList.get(i);

                sb.append("当");
                sb.append(condition.getMonitorItem());
                sb.append(condition.getRuleCondition());
                sb.append(condition.getMonitorValue());
                sb.append(condition.getMonitorUnit());
                sb.append(",产生");
                sb.append(WarningLevelType.getByCode(condition.getWarningLevel()));

                if (i < conditionList.size() - 1) {
                    sb.append("; \n");
                } else {
                    sb.append("。");
                }
            }

            warningRule.setRuleDetail(sb.toString());
        }

        fillOtherUserIdList(warningRule);

        warningRuleMapper.insertWarningRule(warningRule);

        if (CollectionUtils.isNotEmpty(conditionList)) {
            for (WarningRuleCondition condition : conditionList) {
                condition.setRuleId(warningRule.getId());
                condition.setCreateTime(new Date());
                condition.setCreateBy(SecurityUtils.getUserId() + "");

                List<Long> deviceIdList = condition.getDeviceIdList();
                if (CollectionUtils.isNotEmpty(deviceIdList)) {
                    List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByIdList(deviceIdList);

                    List<String> deviceNameList = deviceInfoList.stream()
                            .map(DeviceInfo::getDeviceName)
                            .collect(Collectors.toList());

                    List<String> deviceThirdIdList = deviceInfoList.stream()
                            .map(DeviceInfo::getDeviceThirdId)
                            .collect(Collectors.toList());

                    condition.setDeviceId(StringUtils.join(deviceIdList, ","));
                    condition.setDeviceName(StringUtils.join(deviceNameList, ","));
                    condition.setDeviceThirdId(StringUtils.join(deviceThirdIdList, ","));
                }
            }
        }

        warningRuleConditionMapper.batchInsert(conditionList);

        return 1;
    }

    private void fillOtherUserIdList(WarningRule warningRule) {
        List<String> userIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(warningRule.getOtherUserIdList())) {
            userIdList = warningRule.getOtherUserIdList();

            String otherUserIdStr = StringUtils.join(userIdList, ",");
            warningRule.setOtherUserId(otherUserIdStr);

            userIdList.add(warningRule.getWarningUserId() + "");
        } else {
            userIdList = Lists.newArrayList(warningRule.getWarningUserId() + "");
        }

        List<String> accountList = sysUserMapper.listAccountByIds(userIdList);

        List<String> accountIdList = accountList.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        warningRule.setAllAccountId(StringUtils.join(accountIdList, ","));

        List<String> mobileList = sysUserMapper.listMobileByIds(userIdList);

        warningRule.setAllMobile(StringUtils.join(mobileList, ","));

        if (StringUtils.isBlank(warningRule.getAllAccountId())) {
            warningRule.setAllAccountId(null);
        }
        if (StringUtils.isBlank(warningRule.getAllMobile())) {
            warningRule.setAllMobile(null);
        }
    }

    private void checkCondition(List<WarningRuleCondition> conditionList) {
        if (CollectionUtils.isEmpty(conditionList)) {
            return;
        }

        Map<String, List<WarningRuleCondition>> map = conditionList.stream().collect(Collectors.groupingBy(item ->
                item.getDeviceId() + "-" + item.getMonitorItem() + "-" + item.getWarningLevel()));

        if (map.size() != conditionList.size()) {
            throw new RuntimeException("设备规则条件重复");
        }
    }

    /**
     * 修改设备规则
     *
     * @param warningRule 设备规则
     * @return 结果
     */
    @Override
    @Transactional
    public int updateWarningRule(WarningRule warningRule)
    {
        checkCondition(warningRule.getConditionList());

        warningRule.setUpdateTime(DateUtils.getNowDate());
        warningRule.setUpdateBy(SecurityUtils.getUsername());

        fillOtherUserIdList(warningRule);

        warningRuleMapper.updateWarningRule(warningRule);

        //删除condition
        warningRuleConditionMapper.deleteByRuleId(warningRule.getId());

        List<WarningRuleCondition> conditionList = warningRule.getConditionList();
        if (CollectionUtils.isNotEmpty(conditionList)) {
            for (WarningRuleCondition condition : conditionList) {
                condition.setRuleId(warningRule.getId());

                List<Long> deviceIdList = condition.getDeviceIdList();
                if (CollectionUtils.isNotEmpty(deviceIdList)) {
                    List<DeviceInfo> deviceInfoList = deviceInfoMapper.listByIdList(deviceIdList);

                    List<String> deviceNameList = deviceInfoList.stream()
                            .map(DeviceInfo::getDeviceName)
                            .collect(Collectors.toList());

                    List<String> deviceThirdIdList = deviceInfoList.stream()
                            .map(DeviceInfo::getDeviceThirdId)
                            .collect(Collectors.toList());

                    condition.setDeviceId(StringUtils.join(deviceIdList, ","));
                    condition.setDeviceName(StringUtils.join(deviceNameList, ","));
                    condition.setDeviceThirdId(StringUtils.join(deviceThirdIdList, ","));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(conditionList)) {
            warningRuleConditionMapper.batchInsert(conditionList);
        }

        return 1;
    }

    /**
     * 批量删除设备规则
     *
     * @param ids 需要删除的设备规则主键
     * @return 结果
     */
    @Override
    public int deleteWarningRuleByIds(Long[] ids)
    {
        warningRuleMapper.deleteWarningRuleByIds(ids);

        //删除规则
        for (Long id : ids) {
            warningRuleConditionMapper.deleteByRuleId(id);
        }

        return 1;
    }

    /**
     * 删除设备规则信息
     *
     * @param id 设备规则主键
     * @return 结果
     */
    @Override
    public int deleteWarningRuleById(Long id)
    {
        return warningRuleMapper.deleteWarningRuleById(id);
    }

    @Override
    public List<DeviceInfoItem> listMonitorItemByType(String type) {
        return deviceInfoItemMapper.listMonitorItemByType(type);
    }

    @Override
    public void saveWarningRule(WarningRuleOpenApiVO apiVO) {

    }
}
