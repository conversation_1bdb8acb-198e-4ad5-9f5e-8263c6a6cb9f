package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.DeviceInfoType;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeviceInfoTypeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeviceInfoType record);

    int insertSelective(DeviceInfoType record);

    DeviceInfoType selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeviceInfoType record);

    int updateByPrimaryKey(DeviceInfoType record);

    int updateBatch(List<DeviceInfoType> list);

    int batchInsert(@Param("list") List<DeviceInfoType> list);

    List<String> listByScene(@Param("scene") String scene);
}
