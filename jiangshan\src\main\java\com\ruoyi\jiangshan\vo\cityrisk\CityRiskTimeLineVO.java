package com.ruoyi.jiangshan.vo.cityrisk;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CityRiskTimeLineVO {

    private String key;

    private Double value;

    private List<BigDecimal> valueList;

    private Date startTime;

    private Date endTime;

    public CityRiskTimeLineVO(String key, Double value, Date startTime) {
        this.key = key;
        this.value = value;
        this.startTime = startTime;
    }
}
