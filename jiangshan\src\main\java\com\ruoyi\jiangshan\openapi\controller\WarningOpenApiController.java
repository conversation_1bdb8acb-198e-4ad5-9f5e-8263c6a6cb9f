package com.ruoyi.jiangshan.openapi.controller;

/**
 * openApi
 */
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin;
import com.ruoyi.jiangshan.openapi.vo.WarningEventOpenApiVO;
import com.ruoyi.jiangshan.openapi.vo.WarningEventQueryVO;
import com.ruoyi.jiangshan.openapi.vo.WarningEventThresholdQueryVO;
import com.ruoyi.jiangshan.openapi.vo.WarningEventThresholdSaveVO;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.service.IWarningModelService;
import com.ruoyi.jiangshan.vo.jczz.WarningEventJczzLogVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预警相关openApi
 */
@RestController
@RequestMapping("/openapi/warning")
@Slf4j
public class WarningOpenApiController {
    @Autowired
    private IWarningEventService warningEventService;
    @Autowired
    private IWarningModelService warningModelService;

    /**
     * 推送预警事件
     * @param apiVO
     * @return
     */
    @PostMapping("/saveWarningEvent")
    public AjaxResult saveWarningEvent(@RequestBody @Valid WarningEventOpenApiVO apiVO) {
        String eventThirdId = warningEventService.saveOpenApiWarningEvent(apiVO);

        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("eventThirdId", eventThirdId);

        return AjaxResult.success("操作成功", resultMap);
    }

    /**
     * 查询预警事件详情
     * @param eventThirdId
     * @return
     */
    @GetMapping("/detail")
    public AjaxResult getDetail(String eventThirdId) {
        WarningEvent warningEvent = warningEventService.getByEventThirdId(eventThirdId);

        return AjaxResult.success("操作成功", warningEvent);
    }

    /**
     * 查询阈值
     * @param apiVO
     * @return
     */
    @PostMapping("/getThreshold")
    public AjaxResult getThreshold(@RequestBody @Valid WarningEventThresholdQueryVO apiVO) {
        WarningRuleConditionShuxin warningRuleConditionShuxin = warningEventService.getThreshold(apiVO);

        return AjaxResult.success("操作成功", warningRuleConditionShuxin);
    }

    /**
     * 推送预警模型阈值（新增/更新）
     * @param saveVO
     * @return
     */
    @PostMapping("/saveThreshold")
    public AjaxResult saveThreshold(@RequestBody @Valid WarningEventThresholdSaveVO saveVO) {
        String fxqbh = warningModelService.saveThreshold(saveVO);

        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("fxqbh", fxqbh);

        return AjaxResult.success("操作成功");
    }

    /**
     * 查询未办结的橙色/红色预警事件
     * @return
     */
    @PostMapping("/listEvent")
    public AjaxResult listEvent(@RequestBody WarningEventQueryVO queryVO) {
        List<WarningEvent> eventList = warningEventService.listNonCompleteOrangeEvent(queryVO);

        return AjaxResult.success(eventList);
    }

    /**
     * 获取141事件详情
     * @return
     */
    @PostMapping("/get141Detail")
    public AjaxResult get141Detail(String bizCode) {
        WarningEventJczzLogVO jczzLogVO = warningEventService.get141Detail(bizCode);

        return AjaxResult.success(jczzLogVO);
    }
}
