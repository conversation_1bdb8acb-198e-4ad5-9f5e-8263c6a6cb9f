<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.DeviceInfoItemMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.DeviceInfoItem">
    <!--@mbg.generated-->
    <!--@Table t_device_info_item-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="device_type" jdbcType="VARCHAR" property="deviceType" />
    <result column="monitor_item" jdbcType="VARCHAR" property="monitorItem" />
    <result column="monitor_item_english" jdbcType="VARCHAR" property="monitorItemEnglish" />
    <result column="monitor_unit" jdbcType="VARCHAR" property="monitorUnit" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, device_type, monitor_item, monitor_item_english, monitor_unit, create_time, create_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_device_info_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_device_info_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoItem" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_item (device_type, monitor_item, monitor_item_english, monitor_unit,
      create_time, create_by)
    values (#{deviceType,jdbcType=VARCHAR}, #{monitorItem,jdbcType=VARCHAR}, #{monitorItemEnglish,jdbcType=VARCHAR},
      #{monitorUnit}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoItem" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deviceType != null">
        device_type,
      </if>
      <if test="monitorItem != null">
        monitor_item,
      </if>
      <if test="monitorItemEnglish != null">
        monitor_item_english,
      </if>
      <if test="monitorUnit != null">
        monitor_unit,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deviceType != null">
        #{deviceType,jdbcType=VARCHAR},
      </if>
      <if test="monitorItem != null">
        #{monitorItem,jdbcType=VARCHAR},
      </if>
      <if test="monitorItemEnglish != null">
        #{monitorItemEnglish,jdbcType=VARCHAR},
      </if>
      <if test="monitorUnit != null">
        #{monitorUnit,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoItem">
    <!--@mbg.generated-->
    update t_device_info_item
    <set>
      <if test="deviceType != null">
        device_type = #{deviceType,jdbcType=VARCHAR},
      </if>
      <if test="monitorItem != null">
        monitor_item = #{monitorItem,jdbcType=VARCHAR},
      </if>
      <if test="monitorItemEnglish != null">
        monitor_item_english = #{monitorItemEnglish,jdbcType=VARCHAR},
      </if>
      <if test="monitorUnit != null">
        monitor_unit = #{monitorUnit,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.DeviceInfoItem">
    <!--@mbg.generated-->
    update t_device_info_item
    set device_type = #{deviceType,jdbcType=VARCHAR},
      monitor_item = #{monitorItem,jdbcType=VARCHAR},
      monitor_item_english = #{monitorItemEnglish,jdbcType=VARCHAR},
      monitor_unit = #{monitorUnit,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_device_info_item
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="device_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="monitor_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorItem,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="monitor_item_english = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorItemEnglish,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="monitor_unit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.monitorUnit,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_device_info_item
    (device_type, monitor_item, monitor_item_english, monitor_unit, create_time, create_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deviceType,jdbcType=VARCHAR}, #{item.monitorItem,jdbcType=VARCHAR},
       #{item.monitorItemEnglish,jdbcType=VARCHAR}, #{item.monitorUnit,jdbcType=VARCHAR},
       #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="listMonitorItemByType" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_device_info_item
    where device_type = #{type,jdbcType=VARCHAR}
    order by create_time desc
  </select>
</mapper>
