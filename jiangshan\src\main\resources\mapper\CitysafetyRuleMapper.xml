<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.CitysafetyRuleMapper">

    <resultMap type="CitysafetyRule" id="CitysafetyRuleResult">
        <result property="id"    column="id"    />
        <result property="bizCode"    column="biz_code"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="scene"    column="scene"    />
        <result property="generateFrequency"    column="generate_frequency"    />
        <result property="generateType"    column="generate_type"    />
        <result property="generateTime"    column="generate_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectCitysafetyRuleVo">
        select id, biz_code, rule_name, scene, generate_frequency, generate_type,
               generate_time, create_time, create_by, status from t_citysafety_rule
    </sql>

    <select id="selectCitysafetyRuleList" parameterType="CitysafetyRule" resultMap="CitysafetyRuleResult">
        <include refid="selectCitysafetyRuleVo"/>
        <where>
            <if test="scene != null  and scene != ''"> and scene = #{scene}</if>
            <if test="ruleName != null and ruleName != ''"> and rule_name like concat('%',#{ruleName},'%')</if>
            <if test="generateFrequency != null"> and generate_frequency = #{generateFrequency}</if>
            <if test="generateType != null "> and generate_type = #{generateType}</if>
            <if test="generateTime != null "> and generate_time = #{generateTime}</if>
            <if test="startTime != null "> and create_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}</if>
            <if test="endTime != null "> and create_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}</if>
        </where>
    </select>

    <select id="selectCitysafetyRuleById" parameterType="Long" resultMap="CitysafetyRuleResult">
        <include refid="selectCitysafetyRuleVo"/>
        where id = #{id}
    </select>

    <insert id="insertCitysafetyRule" parameterType="CitysafetyRule" useGeneratedKeys="true" keyProperty="id">
        insert into t_citysafety_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizCode != null and bizCode != ''">biz_code,</if>
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="scene != null">scene,</if>
            <if test="generateFrequency != null">generate_frequency,</if>
            <if test="generateType != null">generate_type,</if>
            <if test="generateTime != null">generate_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="status != null">`status`,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bizCode != null and bizCode != ''">#{bizCode,jdbcType=VARCHAR},</if>
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="scene != null">#{scene},</if>
            <if test="generateFrequency != null">#{generateFrequency},</if>
            <if test="generateType != null">#{generateType},</if>
            <if test="generateTime != null">#{generateTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateCitysafetyRule" parameterType="CitysafetyRule">
        update t_citysafety_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="bizCode != null and bizCode != ''">biz_code = #{bizCode},</if>
            <if test="ruleName!= null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="scene != null">scene = #{scene},</if>
            <if test="generateFrequency != null">generate_frequency = #{generateFrequency},</if>
            <if test="generateType != null">generate_type = #{generateType},</if>
            <if test="generateTime != null">generate_time = #{generateTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="status != null">`status` = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCitysafetyRuleById" parameterType="Long">
        delete from t_citysafety_rule where id = #{id}
    </delete>

    <delete id="deleteCitysafetyRuleByIds" parameterType="String">
        delete from t_citysafety_rule where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getOne" resultMap="CitysafetyRuleResult">
        <include refid="selectCitysafetyRuleVo">
        </include>
        where generate_frequency = #{generateFrequency,jdbcType=INTEGER}
        limit 1
    </select>

    <select id="getByFrequnce" resultMap="CitysafetyRuleResult">
        <include refid="selectCitysafetyRuleVo">
        </include>
        where generate_frequency = #{reportType,jdbcType=INTEGER}
        and status = 1
        limit 1
    </select>

    <select id="listByFrequnce" resultMap="CitysafetyRuleResult">
        <include refid="selectCitysafetyRuleVo">
        </include>
        where generate_frequency = #{reportType,jdbcType=INTEGER}
        and status = 1
    </select>

    <select id="listByFrequnceWithOutStatus" resultMap="CitysafetyRuleResult">
        <include refid="selectCitysafetyRuleVo">
        </include>
        where generate_frequency = #{reportType,jdbcType=INTEGER}
    </select>
</mapper>
