package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.DeviceInfoFavorite;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeviceInfoFavoriteMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeviceInfoFavorite record);

    int insertSelective(DeviceInfoFavorite record);

    DeviceInfoFavorite selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeviceInfoFavorite record);

    int updateByPrimaryKey(DeviceInfoFavorite record);

    int updateBatch(List<DeviceInfoFavorite> list);

    int batchInsert(@Param("list") List<DeviceInfoFavorite> list);

    DeviceInfoFavorite getByUserId(@Param("id") Long id, @Param("userId") Long userId);

    void deleteByUserId(@Param("id") Long id, @Param("userId") Long userId);
}
