/**
 * Copyright(C):南京莱斯信息技术股份有限公司
 * 文件名称     :DisposeUtils
 * 编制人员     :szl
 * 创建日期     :2018-12-12
 * 版本         :v1.0
 * <p>
 * 修改记录
 * 版本信息     ：
 * 更改人员     ：
 * 更改日期     ：
 * 更改内容     ：
 * 更改原因     ：
 **/
package com.ruoyi.common.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Map;

public class DisposeUtils {

    /**
     * 编码请求参数
     *
     * @param parameters 请求参数
     * @return
     */
    public static String encoderToUTF8(Map<String, String> parameters) {
        StringBuffer sb = new StringBuffer();// 处理请求参数
        String params = "";// 编码之后的参数
        try {
            if (parameters != null) {
                if (parameters.size() > 0) {
                    // 编码请求参数
                    if (parameters.size() == 1) {
                        for (String name : parameters.keySet()) {
                            sb.append(name).append("=").append(
                                    java.net.URLEncoder.encode(parameters.get(name),
                                            "UTF-8"));
                        }
                        params = sb.toString();
                    } else {
                        for (String name : parameters.keySet()) {
                            sb.append(name).append("=").append(
                                    java.net.URLEncoder.encode(parameters.get(name),
                                            "UTF-8")).append("&");
                        }
                        String temp_params = sb.toString();
                        char indexChar = '&';
                        if (temp_params.indexOf(indexChar) != -1) {
                            params = temp_params.substring(0, temp_params.length() - 1);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return params;
    }

    public static String encoderToUTF8OfObj(Map<String, Object> parameters) {
        StringBuffer sb = new StringBuffer();// 处理请求参数
        String params = "";// 编码之后的参数
        try {
            if (parameters != null) {
                if (parameters.size() > 0) {
                    // 编码请求参数
                    if (parameters.size() == 1) {
                        for (String name : parameters.keySet()) {
                            sb.append(name).append("=").append((parameters.get(name) instanceof String)?
                                    java.net.URLEncoder.encode(String.valueOf(parameters.get(name)),"UTF-8") : parameters.get(name));
                        }
                        params = sb.toString();
                    } else {
                        for (String name : parameters.keySet()) {
                            sb.append(name).append("=").append((parameters.get(name) instanceof String)?
                                    java.net.URLEncoder.encode(String.valueOf(parameters.get(name)),"UTF-8") : parameters.get(name)).append("&");
                        }
                        String temp_params = sb.toString();
                        char indexChar = '&';
                        if (temp_params.indexOf(indexChar) != -1) {
                            params = temp_params.substring(0, temp_params.length() - 1);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return params;
    }


    /**
     * 设置通用响应头
     *
     * @param httpConn
     */
    public static void setHeaders(HttpURLConnection httpConn) {
        httpConn.setRequestProperty("Accept", "*/*");
        httpConn.setRequestProperty("Connection", "Keep-Alive");
        httpConn.setRequestProperty("User-Agent",
                "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)");
        httpConn.setRequestProperty("Accept-Charset", "UTF-8");
    }

    /**
     * 添加响应头参数
     *
     * @param httpConn
     * @param headers  响应头参数
     */
    public static void addHeaders(HttpURLConnection httpConn, Map<String, String> headers) {
        if (headers != null) {
            if (headers.size() > 0) {
                for (String header : headers.keySet()) {
                    httpConn.setRequestProperty(header, headers.get(header));
                }
            }
        }
    }

    /**
     * ""处理
     *
     * @param resultstr
     * @return
     */
    public static String decodeString(String resultstr) {
        return resultstr.replace("\\\\", "\\").replace("\"{", "{").replace("}\"", "}");
    }

    /**
     * Unicode转换
     *
     * @param theString
     * @return
     */
    public static String decodeUnicode(String theString) {
        char aChar;
        int len = theString.length();
        StringBuffer outBuffer = new StringBuffer(len);
        for (int x = 0; x < len; ) {
            aChar = theString.charAt(x++);
            if (aChar == '\\') {
                aChar = theString.charAt(x++);
                if (aChar == 'u') {
                    // Read the xxxx
                    int value = 0;
                    int forTimes = 4;
                    for (int i = 0; i < forTimes; i++) {
                        aChar = theString.charAt(x++);
                        switch (aChar) {
                            case '0':
                            case '1':
                            case '2':
                            case '3':
                            case '4':
                            case '5':
                            case '6':
                            case '7':
                            case '8':
                            case '9':
                                value = (value << 4) + aChar - '0';
                                break;
                            case 'a':
                            case 'b':
                            case 'c':
                            case 'd':
                            case 'e':
                            case 'f':
                                value = (value << 4) + 10 + aChar - 'a';
                                break;
                            case 'A':
                            case 'B':
                            case 'C':
                            case 'D':
                            case 'E':
                            case 'F':
                                value = (value << 4) + 10 + aChar - 'A';
                                break;
                            default:
                                throw new IllegalArgumentException(
                                        "Malformed   \\uxxxx   encoding.");
                        }

                    }
                    outBuffer.append((char) value);
                } else {
                    if (aChar == 't') {
                        aChar = '\t';
                    } else if (aChar == 'r') {
                        aChar = '\r';
                    } else if (aChar == 'n') {
                        aChar = '\n';
                    } else if (aChar == 'f') {
                        aChar = '\f';
                    }
                    outBuffer.append(aChar);
                }
            } else {
                outBuffer.append(aChar);
            }
        }
        String result = decodeString(outBuffer.toString());
        return result;
    }

    /**
     * 关闭流
     *
     * @param in
     * @param out
     */
    public static void closeStream(PrintWriter out, BufferedReader in) {
        try {
            if (out != null) {
                out.close();
            }
            if (in != null) {
                in.close();
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

    /*
     * 将时间戳转换为时间
     */
    public static String stampToDate(String s) {
        String res = "";
        try {
            if (s != "" && s != null) {
                Double sDou = Double.parseDouble(s);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                res = simpleDateFormat.format(sDou);
                Calendar now = Calendar.getInstance();
                now.setTime(simpleDateFormat.parse(res));
                now.add(Calendar.HOUR, +12);
                res = simpleDateFormat.format(now.getTimeInMillis());
            }
        } catch (ParseException pe) {
            pe.printStackTrace();
        }
        return res;
    }
    public static boolean isHaveData(String dataStr) {
        boolean flag = false;
        String indexStr1 = "[]";
        String indexStr2 = ":{}";
        if (dataStr.indexOf(indexStr1) != -1 || dataStr.indexOf(indexStr2) != -1) {
            flag = true;
        }
        return flag;
    }


}
