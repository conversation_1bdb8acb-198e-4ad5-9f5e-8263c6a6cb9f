<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jiangshan.mapper.WarningRuleConditionShuxinMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin">
    <!--@mbg.generated-->
    <!--@Table t_warning_rule_condition_shuxin-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="model_id" jdbcType="BIGINT" property="modelId" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="sjzt" jdbcType="VARCHAR" property="sjzt" />
    <result column="fxqbh" jdbcType="VARCHAR" property="fxqbh" />
    <result column="fxffqmc" jdbcType="VARCHAR" property="fxffqmc" />
    <result column="dcbh" jdbcType="VARCHAR" property="dcbh" />
    <result column="szdq" jdbcType="VARCHAR" property="szdq" />
    <result column="xzc" jdbcType="VARCHAR" property="xzc" />
    <result column="fflx" jdbcType="VARCHAR" property="fflx" />
    <result column="ffqfw" jdbcType="LONGVARCHAR" property="ffqfw" />
    <result column="zzt" jdbcType="VARCHAR" property="zzt" />
    <result column="wdx" jdbcType="VARCHAR" property="wdx" />
    <result column="fxdj" jdbcType="VARCHAR" property="fxdj" />
    <result column="syxhs" jdbcType="VARCHAR" property="syxhs" />
    <result column="syxrs" jdbcType="VARCHAR" property="syxrs" />
    <result column="czrk" jdbcType="VARCHAR" property="czrk" />
    <result column="syxcc" jdbcType="VARCHAR" property="syxcc" />
    <result column="xjfgld" jdbcType="VARCHAR" property="xjfgld" />
    <result column="xjfglddh" jdbcType="VARCHAR" property="xjfglddh" />
    <result column="xzfgld" jdbcType="VARCHAR" property="xzfgld" />
    <result column="xzfglddh" jdbcType="VARCHAR" property="xzfglddh" />
    <result column="zrzyszrr" jdbcType="VARCHAR" property="zrzyszrr" />
    <result column="zrzyszrrdh" jdbcType="VARCHAR" property="zrzyszrrdh" />
    <result column="qcqfwgy" jdbcType="VARCHAR" property="qcqfwgy" />
    <result column="qcqfwgydh" jdbcType="VARCHAR" property="qcqfwgydh" />
    <result column="sjly" jdbcType="VARCHAR" property="sjly" />
    <result column="brbqxmbh" jdbcType="VARCHAR" property="brbqxmbh" />
    <result column="zlgcxmbh" jdbcType="VARCHAR" property="zlgcxmbh" />
    <result column="jcdbm" jdbcType="VARCHAR" property="jcdbm" />
    <result column="tbr" jdbcType="VARCHAR" property="tbr" />
    <result column="ffqcjsj" jdbcType="VARCHAR" property="ffqcjsj" />
    <result column="tjxzsj" jdbcType="VARCHAR" property="tjxzsj" />
    <result column="hjtgsj" jdbcType="VARCHAR" property="hjtgsj" />
    <result column="yzlx" jdbcType="VARCHAR" property="yzlx" />
    <result column="zztgs" jdbcType="VARCHAR" property="zztgs" />
    <result column="czths" jdbcType="VARCHAR" property="czths" />
    <result column="czthjrs" jdbcType="VARCHAR" property="czthjrs" />
    <result column="level_1h_h" jdbcType="VARCHAR" property="level1hH" />
    <result column="level_1h_c" jdbcType="VARCHAR" property="level1hC" />
    <result column="level_1h_h1" jdbcType="VARCHAR" property="level1hH1" />
    <result column="level_3h_h" jdbcType="VARCHAR" property="level3hH" />
    <result column="level_3h_c" jdbcType="VARCHAR" property="level3hC" />
    <result column="level_3h_h3" jdbcType="VARCHAR" property="level3hH3" />
    <result column="level_6h_h" jdbcType="VARCHAR" property="level6hH" />
    <result column="level_6h_c" jdbcType="VARCHAR" property="level6hC" />
    <result column="level_6h_h6" jdbcType="VARCHAR" property="level6hH6" />
    <result column="level_12h_h" jdbcType="VARCHAR" property="level12hH" />
    <result column="level_12h_c" jdbcType="VARCHAR" property="level12hC" />
    <result column="level_12h_h12" jdbcType="VARCHAR" property="level12hH12" />
    <result column="level_24h_h" jdbcType="VARCHAR" property="level24hH" />
    <result column="level_24h_c" jdbcType="VARCHAR" property="level24hC" />
    <result column="level_24h_h24" jdbcType="VARCHAR" property="level24hH24" />
    <result column="hjyy" jdbcType="VARCHAR" property="hjyy" />
    <result column="hjyy_qt" jdbcType="VARCHAR" property="hjyyQt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, model_id, rule_id, sjzt, fxqbh, fxffqmc, dcbh, szdq, xzc, fflx, ffqfw, zzt, wdx,
    fxdj, syxhs, syxrs, czrk, syxcc, xjfgld, xjfglddh, xzfgld, xzfglddh, zrzyszrr, zrzyszrrdh,
    qcqfwgy, qcqfwgydh, sjly, brbqxmbh, zlgcxmbh, jcdbm, tbr, ffqcjsj, tjxzsj, hjtgsj,
    yzlx, zztgs, czths, czthjrs, level_1h_h, level_1h_c, level_1h_h1, level_3h_h, level_3h_c,
    level_3h_h3, level_6h_h, level_6h_c, level_6h_h6, level_12h_h, level_12h_c, level_12h_h12,
    level_24h_h, level_24h_c, level_24h_h24, hjyy, hjyy_qt
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_warning_rule_condition_shuxin
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_warning_rule_condition_shuxin
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_rule_condition_shuxin (model_id, rule_id, sjzt,
      fxqbh, fxffqmc, dcbh,
      szdq, xzc, fflx, ffqfw,
      zzt, wdx, fxdj, syxhs,
      syxrs, czrk, syxcc,
      xjfgld, xjfglddh, xzfgld,
      xzfglddh, zrzyszrr, zrzyszrrdh,
      qcqfwgy, qcqfwgydh, sjly,
      brbqxmbh, zlgcxmbh, jcdbm,
      tbr, ffqcjsj, tjxzsj,
      hjtgsj, yzlx, zztgs,
      czths, czthjrs, level_1h_h,
      level_1h_c, level_1h_h1, level_3h_h,
      level_3h_c, level_3h_h3, level_6h_h,
      level_6h_c, level_6h_h6, level_12h_h,
      level_12h_c, level_12h_h12, level_24h_h,
      level_24h_c, level_24h_h24, hjyy,
      hjyy_qt)
    values (#{modelId,jdbcType=BIGINT}, #{ruleId,jdbcType=BIGINT}, #{sjzt,jdbcType=VARCHAR},
      #{fxqbh,jdbcType=VARCHAR}, #{fxffqmc,jdbcType=VARCHAR}, #{dcbh,jdbcType=VARCHAR},
      #{szdq,jdbcType=VARCHAR}, #{xzc,jdbcType=VARCHAR}, #{fflx,jdbcType=VARCHAR}, #{ffqfw,jdbcType=LONGVARCHAR},
      #{zzt,jdbcType=VARCHAR}, #{wdx,jdbcType=VARCHAR}, #{fxdj,jdbcType=VARCHAR}, #{syxhs,jdbcType=VARCHAR},
      #{syxrs,jdbcType=VARCHAR}, #{czrk,jdbcType=VARCHAR}, #{syxcc,jdbcType=VARCHAR},
      #{xjfgld,jdbcType=VARCHAR}, #{xjfglddh,jdbcType=VARCHAR}, #{xzfgld,jdbcType=VARCHAR},
      #{xzfglddh,jdbcType=VARCHAR}, #{zrzyszrr,jdbcType=VARCHAR}, #{zrzyszrrdh,jdbcType=VARCHAR},
      #{qcqfwgy,jdbcType=VARCHAR}, #{qcqfwgydh,jdbcType=VARCHAR}, #{sjly,jdbcType=VARCHAR},
      #{brbqxmbh,jdbcType=VARCHAR}, #{zlgcxmbh,jdbcType=VARCHAR}, #{jcdbm,jdbcType=VARCHAR},
      #{tbr,jdbcType=VARCHAR}, #{ffqcjsj,jdbcType=VARCHAR}, #{tjxzsj,jdbcType=VARCHAR},
      #{hjtgsj,jdbcType=VARCHAR}, #{yzlx,jdbcType=VARCHAR}, #{zztgs,jdbcType=VARCHAR},
      #{czths,jdbcType=VARCHAR}, #{czthjrs,jdbcType=VARCHAR}, #{level1hH,jdbcType=VARCHAR},
      #{level1hC,jdbcType=VARCHAR}, #{level1hH1,jdbcType=VARCHAR}, #{level3hH,jdbcType=VARCHAR},
      #{level3hC,jdbcType=VARCHAR}, #{level3hH3,jdbcType=VARCHAR}, #{level6hH,jdbcType=VARCHAR},
      #{level6hC,jdbcType=VARCHAR}, #{level6hH6,jdbcType=VARCHAR}, #{level12hH,jdbcType=VARCHAR},
      #{level12hC,jdbcType=VARCHAR}, #{level12hH12,jdbcType=VARCHAR}, #{level24hH,jdbcType=VARCHAR},
      #{level24hC,jdbcType=VARCHAR}, #{level24hH24,jdbcType=VARCHAR}, #{hjyy,jdbcType=VARCHAR},
      #{hjyyQt,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_rule_condition_shuxin
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelId != null">
        model_id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="sjzt != null">
        sjzt,
      </if>
      <if test="fxqbh != null">
        fxqbh,
      </if>
      <if test="fxffqmc != null">
        fxffqmc,
      </if>
      <if test="dcbh != null">
        dcbh,
      </if>
      <if test="szdq != null">
        szdq,
      </if>
      <if test="xzc != null">
        xzc,
      </if>
      <if test="fflx != null">
        fflx,
      </if>
      <if test="ffqfw != null">
        ffqfw,
      </if>
      <if test="zzt != null">
        zzt,
      </if>
      <if test="wdx != null">
        wdx,
      </if>
      <if test="fxdj != null">
        fxdj,
      </if>
      <if test="syxhs != null">
        syxhs,
      </if>
      <if test="syxrs != null">
        syxrs,
      </if>
      <if test="czrk != null">
        czrk,
      </if>
      <if test="syxcc != null">
        syxcc,
      </if>
      <if test="xjfgld != null">
        xjfgld,
      </if>
      <if test="xjfglddh != null">
        xjfglddh,
      </if>
      <if test="xzfgld != null">
        xzfgld,
      </if>
      <if test="xzfglddh != null">
        xzfglddh,
      </if>
      <if test="zrzyszrr != null">
        zrzyszrr,
      </if>
      <if test="zrzyszrrdh != null">
        zrzyszrrdh,
      </if>
      <if test="qcqfwgy != null">
        qcqfwgy,
      </if>
      <if test="qcqfwgydh != null">
        qcqfwgydh,
      </if>
      <if test="sjly != null">
        sjly,
      </if>
      <if test="brbqxmbh != null">
        brbqxmbh,
      </if>
      <if test="zlgcxmbh != null">
        zlgcxmbh,
      </if>
      <if test="jcdbm != null">
        jcdbm,
      </if>
      <if test="tbr != null">
        tbr,
      </if>
      <if test="ffqcjsj != null">
        ffqcjsj,
      </if>
      <if test="tjxzsj != null">
        tjxzsj,
      </if>
      <if test="hjtgsj != null">
        hjtgsj,
      </if>
      <if test="yzlx != null">
        yzlx,
      </if>
      <if test="zztgs != null">
        zztgs,
      </if>
      <if test="czths != null">
        czths,
      </if>
      <if test="czthjrs != null">
        czthjrs,
      </if>
      <if test="level1hH != null">
        level_1h_h,
      </if>
      <if test="level1hC != null">
        level_1h_c,
      </if>
      <if test="level1hH1 != null">
        level_1h_h1,
      </if>
      <if test="level3hH != null">
        level_3h_h,
      </if>
      <if test="level3hC != null">
        level_3h_c,
      </if>
      <if test="level3hH3 != null">
        level_3h_h3,
      </if>
      <if test="level6hH != null">
        level_6h_h,
      </if>
      <if test="level6hC != null">
        level_6h_c,
      </if>
      <if test="level6hH6 != null">
        level_6h_h6,
      </if>
      <if test="level12hH != null">
        level_12h_h,
      </if>
      <if test="level12hC != null">
        level_12h_c,
      </if>
      <if test="level12hH12 != null">
        level_12h_h12,
      </if>
      <if test="level24hH != null">
        level_24h_h,
      </if>
      <if test="level24hC != null">
        level_24h_c,
      </if>
      <if test="level24hH24 != null">
        level_24h_h24,
      </if>
      <if test="hjyy != null">
        hjyy,
      </if>
      <if test="hjyyQt != null">
        hjyy_qt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelId != null">
        #{modelId,jdbcType=BIGINT},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="sjzt != null">
        #{sjzt,jdbcType=VARCHAR},
      </if>
      <if test="fxqbh != null">
        #{fxqbh,jdbcType=VARCHAR},
      </if>
      <if test="fxffqmc != null">
        #{fxffqmc,jdbcType=VARCHAR},
      </if>
      <if test="dcbh != null">
        #{dcbh,jdbcType=VARCHAR},
      </if>
      <if test="szdq != null">
        #{szdq,jdbcType=VARCHAR},
      </if>
      <if test="xzc != null">
        #{xzc,jdbcType=VARCHAR},
      </if>
      <if test="fflx != null">
        #{fflx,jdbcType=VARCHAR},
      </if>
      <if test="ffqfw != null">
        #{ffqfw,jdbcType=LONGVARCHAR},
      </if>
      <if test="zzt != null">
        #{zzt,jdbcType=VARCHAR},
      </if>
      <if test="wdx != null">
        #{wdx,jdbcType=VARCHAR},
      </if>
      <if test="fxdj != null">
        #{fxdj,jdbcType=VARCHAR},
      </if>
      <if test="syxhs != null">
        #{syxhs,jdbcType=VARCHAR},
      </if>
      <if test="syxrs != null">
        #{syxrs,jdbcType=VARCHAR},
      </if>
      <if test="czrk != null">
        #{czrk,jdbcType=VARCHAR},
      </if>
      <if test="syxcc != null">
        #{syxcc,jdbcType=VARCHAR},
      </if>
      <if test="xjfgld != null">
        #{xjfgld,jdbcType=VARCHAR},
      </if>
      <if test="xjfglddh != null">
        #{xjfglddh,jdbcType=VARCHAR},
      </if>
      <if test="xzfgld != null">
        #{xzfgld,jdbcType=VARCHAR},
      </if>
      <if test="xzfglddh != null">
        #{xzfglddh,jdbcType=VARCHAR},
      </if>
      <if test="zrzyszrr != null">
        #{zrzyszrr,jdbcType=VARCHAR},
      </if>
      <if test="zrzyszrrdh != null">
        #{zrzyszrrdh,jdbcType=VARCHAR},
      </if>
      <if test="qcqfwgy != null">
        #{qcqfwgy,jdbcType=VARCHAR},
      </if>
      <if test="qcqfwgydh != null">
        #{qcqfwgydh,jdbcType=VARCHAR},
      </if>
      <if test="sjly != null">
        #{sjly,jdbcType=VARCHAR},
      </if>
      <if test="brbqxmbh != null">
        #{brbqxmbh,jdbcType=VARCHAR},
      </if>
      <if test="zlgcxmbh != null">
        #{zlgcxmbh,jdbcType=VARCHAR},
      </if>
      <if test="jcdbm != null">
        #{jcdbm,jdbcType=VARCHAR},
      </if>
      <if test="tbr != null">
        #{tbr,jdbcType=VARCHAR},
      </if>
      <if test="ffqcjsj != null">
        #{ffqcjsj,jdbcType=VARCHAR},
      </if>
      <if test="tjxzsj != null">
        #{tjxzsj,jdbcType=VARCHAR},
      </if>
      <if test="hjtgsj != null">
        #{hjtgsj,jdbcType=VARCHAR},
      </if>
      <if test="yzlx != null">
        #{yzlx,jdbcType=VARCHAR},
      </if>
      <if test="zztgs != null">
        #{zztgs,jdbcType=VARCHAR},
      </if>
      <if test="czths != null">
        #{czths,jdbcType=VARCHAR},
      </if>
      <if test="czthjrs != null">
        #{czthjrs,jdbcType=VARCHAR},
      </if>
      <if test="level1hH != null">
        #{level1hH,jdbcType=VARCHAR},
      </if>
      <if test="level1hC != null">
        #{level1hC,jdbcType=VARCHAR},
      </if>
      <if test="level1hH1 != null">
        #{level1hH1,jdbcType=VARCHAR},
      </if>
      <if test="level3hH != null">
        #{level3hH,jdbcType=VARCHAR},
      </if>
      <if test="level3hC != null">
        #{level3hC,jdbcType=VARCHAR},
      </if>
      <if test="level3hH3 != null">
        #{level3hH3,jdbcType=VARCHAR},
      </if>
      <if test="level6hH != null">
        #{level6hH,jdbcType=VARCHAR},
      </if>
      <if test="level6hC != null">
        #{level6hC,jdbcType=VARCHAR},
      </if>
      <if test="level6hH6 != null">
        #{level6hH6,jdbcType=VARCHAR},
      </if>
      <if test="level12hH != null">
        #{level12hH,jdbcType=VARCHAR},
      </if>
      <if test="level12hC != null">
        #{level12hC,jdbcType=VARCHAR},
      </if>
      <if test="level12hH12 != null">
        #{level12hH12,jdbcType=VARCHAR},
      </if>
      <if test="level24hH != null">
        #{level24hH,jdbcType=VARCHAR},
      </if>
      <if test="level24hC != null">
        #{level24hC,jdbcType=VARCHAR},
      </if>
      <if test="level24hH24 != null">
        #{level24hH24,jdbcType=VARCHAR},
      </if>
      <if test="hjyy != null">
        #{hjyy,jdbcType=VARCHAR},
      </if>
      <if test="hjyyQt != null">
        #{hjyyQt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin">
    <!--@mbg.generated-->
    update t_warning_rule_condition_shuxin
    <set>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=BIGINT},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="sjzt != null">
        sjzt = #{sjzt,jdbcType=VARCHAR},
      </if>
      <if test="fxqbh != null">
        fxqbh = #{fxqbh,jdbcType=VARCHAR},
      </if>
      <if test="fxffqmc != null">
        fxffqmc = #{fxffqmc,jdbcType=VARCHAR},
      </if>
      <if test="dcbh != null">
        dcbh = #{dcbh,jdbcType=VARCHAR},
      </if>
      <if test="szdq != null">
        szdq = #{szdq,jdbcType=VARCHAR},
      </if>
      <if test="xzc != null">
        xzc = #{xzc,jdbcType=VARCHAR},
      </if>
      <if test="fflx != null">
        fflx = #{fflx,jdbcType=VARCHAR},
      </if>
      <if test="ffqfw != null">
        ffqfw = #{ffqfw,jdbcType=LONGVARCHAR},
      </if>
      <if test="zzt != null">
        zzt = #{zzt,jdbcType=VARCHAR},
      </if>
      <if test="wdx != null">
        wdx = #{wdx,jdbcType=VARCHAR},
      </if>
      <if test="fxdj != null">
        fxdj = #{fxdj,jdbcType=VARCHAR},
      </if>
      <if test="syxhs != null">
        syxhs = #{syxhs,jdbcType=VARCHAR},
      </if>
      <if test="syxrs != null">
        syxrs = #{syxrs,jdbcType=VARCHAR},
      </if>
      <if test="czrk != null">
        czrk = #{czrk,jdbcType=VARCHAR},
      </if>
      <if test="syxcc != null">
        syxcc = #{syxcc,jdbcType=VARCHAR},
      </if>
      <if test="xjfgld != null">
        xjfgld = #{xjfgld,jdbcType=VARCHAR},
      </if>
      <if test="xjfglddh != null">
        xjfglddh = #{xjfglddh,jdbcType=VARCHAR},
      </if>
      <if test="xzfgld != null">
        xzfgld = #{xzfgld,jdbcType=VARCHAR},
      </if>
      <if test="xzfglddh != null">
        xzfglddh = #{xzfglddh,jdbcType=VARCHAR},
      </if>
      <if test="zrzyszrr != null">
        zrzyszrr = #{zrzyszrr,jdbcType=VARCHAR},
      </if>
      <if test="zrzyszrrdh != null">
        zrzyszrrdh = #{zrzyszrrdh,jdbcType=VARCHAR},
      </if>
      <if test="qcqfwgy != null">
        qcqfwgy = #{qcqfwgy,jdbcType=VARCHAR},
      </if>
      <if test="qcqfwgydh != null">
        qcqfwgydh = #{qcqfwgydh,jdbcType=VARCHAR},
      </if>
      <if test="sjly != null">
        sjly = #{sjly,jdbcType=VARCHAR},
      </if>
      <if test="brbqxmbh != null">
        brbqxmbh = #{brbqxmbh,jdbcType=VARCHAR},
      </if>
      <if test="zlgcxmbh != null">
        zlgcxmbh = #{zlgcxmbh,jdbcType=VARCHAR},
      </if>
      <if test="jcdbm != null">
        jcdbm = #{jcdbm,jdbcType=VARCHAR},
      </if>
      <if test="tbr != null">
        tbr = #{tbr,jdbcType=VARCHAR},
      </if>
      <if test="ffqcjsj != null">
        ffqcjsj = #{ffqcjsj,jdbcType=VARCHAR},
      </if>
      <if test="tjxzsj != null">
        tjxzsj = #{tjxzsj,jdbcType=VARCHAR},
      </if>
      <if test="hjtgsj != null">
        hjtgsj = #{hjtgsj,jdbcType=VARCHAR},
      </if>
      <if test="yzlx != null">
        yzlx = #{yzlx,jdbcType=VARCHAR},
      </if>
      <if test="zztgs != null">
        zztgs = #{zztgs,jdbcType=VARCHAR},
      </if>
      <if test="czths != null">
        czths = #{czths,jdbcType=VARCHAR},
      </if>
      <if test="czthjrs != null">
        czthjrs = #{czthjrs,jdbcType=VARCHAR},
      </if>
      <if test="level1hH != null">
        level_1h_h = #{level1hH,jdbcType=VARCHAR},
      </if>
      <if test="level1hC != null">
        level_1h_c = #{level1hC,jdbcType=VARCHAR},
      </if>
      <if test="level1hH1 != null">
        level_1h_h1 = #{level1hH1,jdbcType=VARCHAR},
      </if>
      <if test="level3hH != null">
        level_3h_h = #{level3hH,jdbcType=VARCHAR},
      </if>
      <if test="level3hC != null">
        level_3h_c = #{level3hC,jdbcType=VARCHAR},
      </if>
      <if test="level3hH3 != null">
        level_3h_h3 = #{level3hH3,jdbcType=VARCHAR},
      </if>
      <if test="level6hH != null">
        level_6h_h = #{level6hH,jdbcType=VARCHAR},
      </if>
      <if test="level6hC != null">
        level_6h_c = #{level6hC,jdbcType=VARCHAR},
      </if>
      <if test="level6hH6 != null">
        level_6h_h6 = #{level6hH6,jdbcType=VARCHAR},
      </if>
      <if test="level12hH != null">
        level_12h_h = #{level12hH,jdbcType=VARCHAR},
      </if>
      <if test="level12hC != null">
        level_12h_c = #{level12hC,jdbcType=VARCHAR},
      </if>
      <if test="level12hH12 != null">
        level_12h_h12 = #{level12hH12,jdbcType=VARCHAR},
      </if>
      <if test="level24hH != null">
        level_24h_h = #{level24hH,jdbcType=VARCHAR},
      </if>
      <if test="level24hC != null">
        level_24h_c = #{level24hC,jdbcType=VARCHAR},
      </if>
      <if test="level24hH24 != null">
        level_24h_h24 = #{level24hH24,jdbcType=VARCHAR},
      </if>
      <if test="hjyy != null">
        hjyy = #{hjyy,jdbcType=VARCHAR},
      </if>
      <if test="hjyyQt != null">
        hjyy_qt = #{hjyyQt,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.jiangshan.domain.WarningRuleConditionShuxin">
    <!--@mbg.generated-->
    update t_warning_rule_condition_shuxin
    set model_id = #{modelId,jdbcType=BIGINT},
      rule_id = #{ruleId,jdbcType=BIGINT},
      sjzt = #{sjzt,jdbcType=VARCHAR},
      fxqbh = #{fxqbh,jdbcType=VARCHAR},
      fxffqmc = #{fxffqmc,jdbcType=VARCHAR},
      dcbh = #{dcbh,jdbcType=VARCHAR},
      szdq = #{szdq,jdbcType=VARCHAR},
      xzc = #{xzc,jdbcType=VARCHAR},
      fflx = #{fflx,jdbcType=VARCHAR},
      ffqfw = #{ffqfw,jdbcType=LONGVARCHAR},
      zzt = #{zzt,jdbcType=VARCHAR},
      wdx = #{wdx,jdbcType=VARCHAR},
      fxdj = #{fxdj,jdbcType=VARCHAR},
      syxhs = #{syxhs,jdbcType=VARCHAR},
      syxrs = #{syxrs,jdbcType=VARCHAR},
      czrk = #{czrk,jdbcType=VARCHAR},
      syxcc = #{syxcc,jdbcType=VARCHAR},
      xjfgld = #{xjfgld,jdbcType=VARCHAR},
      xjfglddh = #{xjfglddh,jdbcType=VARCHAR},
      xzfgld = #{xzfgld,jdbcType=VARCHAR},
      xzfglddh = #{xzfglddh,jdbcType=VARCHAR},
      zrzyszrr = #{zrzyszrr,jdbcType=VARCHAR},
      zrzyszrrdh = #{zrzyszrrdh,jdbcType=VARCHAR},
      qcqfwgy = #{qcqfwgy,jdbcType=VARCHAR},
      qcqfwgydh = #{qcqfwgydh,jdbcType=VARCHAR},
      sjly = #{sjly,jdbcType=VARCHAR},
      brbqxmbh = #{brbqxmbh,jdbcType=VARCHAR},
      zlgcxmbh = #{zlgcxmbh,jdbcType=VARCHAR},
      jcdbm = #{jcdbm,jdbcType=VARCHAR},
      tbr = #{tbr,jdbcType=VARCHAR},
      ffqcjsj = #{ffqcjsj,jdbcType=VARCHAR},
      tjxzsj = #{tjxzsj,jdbcType=VARCHAR},
      hjtgsj = #{hjtgsj,jdbcType=VARCHAR},
      yzlx = #{yzlx,jdbcType=VARCHAR},
      zztgs = #{zztgs,jdbcType=VARCHAR},
      czths = #{czths,jdbcType=VARCHAR},
      czthjrs = #{czthjrs,jdbcType=VARCHAR},
      level_1h_h = #{level1hH,jdbcType=VARCHAR},
      level_1h_c = #{level1hC,jdbcType=VARCHAR},
      level_1h_h1 = #{level1hH1,jdbcType=VARCHAR},
      level_3h_h = #{level3hH,jdbcType=VARCHAR},
      level_3h_c = #{level3hC,jdbcType=VARCHAR},
      level_3h_h3 = #{level3hH3,jdbcType=VARCHAR},
      level_6h_h = #{level6hH,jdbcType=VARCHAR},
      level_6h_c = #{level6hC,jdbcType=VARCHAR},
      level_6h_h6 = #{level6hH6,jdbcType=VARCHAR},
      level_12h_h = #{level12hH,jdbcType=VARCHAR},
      level_12h_c = #{level12hC,jdbcType=VARCHAR},
      level_12h_h12 = #{level12hH12,jdbcType=VARCHAR},
      level_24h_h = #{level24hH,jdbcType=VARCHAR},
      level_24h_c = #{level24hC,jdbcType=VARCHAR},
      level_24h_h24 = #{level24hH24,jdbcType=VARCHAR},
      hjyy = #{hjyy,jdbcType=VARCHAR},
      hjyy_qt = #{hjyyQt,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_warning_rule_condition_shuxin
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="model_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modelId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="rule_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ruleId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="sjzt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sjzt,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="fxqbh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fxqbh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="fxffqmc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fxffqmc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dcbh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dcbh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="szdq = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.szdq,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="xzc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.xzc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="fflx = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fflx,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ffqfw = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ffqfw,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="zzt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.zzt,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="wdx = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.wdx,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="fxdj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.fxdj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="syxhs = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.syxhs,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="syxrs = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.syxrs,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="czrk = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.czrk,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="syxcc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.syxcc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="xjfgld = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.xjfgld,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="xjfglddh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.xjfglddh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="xzfgld = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.xzfgld,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="xzfglddh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.xzfglddh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zrzyszrr = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.zrzyszrr,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zrzyszrrdh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.zrzyszrrdh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="qcqfwgy = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.qcqfwgy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="qcqfwgydh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.qcqfwgydh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sjly = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sjly,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="brbqxmbh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.brbqxmbh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zlgcxmbh = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.zlgcxmbh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="jcdbm = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.jcdbm,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="tbr = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.tbr,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ffqcjsj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ffqcjsj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="tjxzsj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.tjxzsj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="hjtgsj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.hjtgsj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="yzlx = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.yzlx,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="zztgs = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.zztgs,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="czths = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.czths,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="czthjrs = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.czthjrs,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_1h_h = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level1hH,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_1h_c = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level1hC,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_1h_h1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level1hH1,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_3h_h = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level3hH,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_3h_c = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level3hC,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_3h_h3 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level3hH3,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_6h_h = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level6hH,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_6h_c = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level6hC,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_6h_h6 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level6hH6,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_12h_h = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level12hH,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_12h_c = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level12hC,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_12h_h12 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level12hH12,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_24h_h = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level24hH,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_24h_c = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level24hC,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="level_24h_h24 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.level24hH24,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="hjyy = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.hjyy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="hjyy_qt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.hjyyQt,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_warning_rule_condition_shuxin
    (model_id, rule_id, sjzt, fxqbh, fxffqmc, dcbh, szdq, xzc, fflx, ffqfw, zzt, wdx,
      fxdj, syxhs, syxrs, czrk, syxcc, xjfgld, xjfglddh, xzfgld, xzfglddh, zrzyszrr,
      zrzyszrrdh, qcqfwgy, qcqfwgydh, sjly, brbqxmbh, zlgcxmbh, jcdbm, tbr, ffqcjsj,
      tjxzsj, hjtgsj, yzlx, zztgs, czths, czthjrs, level_1h_h, level_1h_c, level_1h_h1,
      level_3h_h, level_3h_c, level_3h_h3, level_6h_h, level_6h_c, level_6h_h6, level_12h_h,
      level_12h_c, level_12h_h12, level_24h_h, level_24h_c, level_24h_h24, hjyy, hjyy_qt
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.modelId,jdbcType=BIGINT}, #{item.ruleId,jdbcType=BIGINT}, #{item.sjzt,jdbcType=VARCHAR},
        #{item.fxqbh,jdbcType=VARCHAR}, #{item.fxffqmc,jdbcType=VARCHAR}, #{item.dcbh,jdbcType=VARCHAR},
        #{item.szdq,jdbcType=VARCHAR}, #{item.xzc,jdbcType=VARCHAR}, #{item.fflx,jdbcType=VARCHAR},
        #{item.ffqfw,jdbcType=LONGVARCHAR}, #{item.zzt,jdbcType=VARCHAR}, #{item.wdx,jdbcType=VARCHAR},
        #{item.fxdj,jdbcType=VARCHAR}, #{item.syxhs,jdbcType=VARCHAR}, #{item.syxrs,jdbcType=VARCHAR},
        #{item.czrk,jdbcType=VARCHAR}, #{item.syxcc,jdbcType=VARCHAR}, #{item.xjfgld,jdbcType=VARCHAR},
        #{item.xjfglddh,jdbcType=VARCHAR}, #{item.xzfgld,jdbcType=VARCHAR}, #{item.xzfglddh,jdbcType=VARCHAR},
        #{item.zrzyszrr,jdbcType=VARCHAR}, #{item.zrzyszrrdh,jdbcType=VARCHAR}, #{item.qcqfwgy,jdbcType=VARCHAR},
        #{item.qcqfwgydh,jdbcType=VARCHAR}, #{item.sjly,jdbcType=VARCHAR}, #{item.brbqxmbh,jdbcType=VARCHAR},
        #{item.zlgcxmbh,jdbcType=VARCHAR}, #{item.jcdbm,jdbcType=VARCHAR}, #{item.tbr,jdbcType=VARCHAR},
        #{item.ffqcjsj,jdbcType=VARCHAR}, #{item.tjxzsj,jdbcType=VARCHAR}, #{item.hjtgsj,jdbcType=VARCHAR},
        #{item.yzlx,jdbcType=VARCHAR}, #{item.zztgs,jdbcType=VARCHAR}, #{item.czths,jdbcType=VARCHAR},
        #{item.czthjrs,jdbcType=VARCHAR}, #{item.level1hH,jdbcType=VARCHAR}, #{item.level1hC,jdbcType=VARCHAR},
        #{item.level1hH1,jdbcType=VARCHAR}, #{item.level3hH,jdbcType=VARCHAR}, #{item.level3hC,jdbcType=VARCHAR},
        #{item.level3hH3,jdbcType=VARCHAR}, #{item.level6hH,jdbcType=VARCHAR}, #{item.level6hC,jdbcType=VARCHAR},
        #{item.level6hH6,jdbcType=VARCHAR}, #{item.level12hH,jdbcType=VARCHAR}, #{item.level12hC,jdbcType=VARCHAR},
        #{item.level12hH12,jdbcType=VARCHAR}, #{item.level24hH,jdbcType=VARCHAR}, #{item.level24hC,jdbcType=VARCHAR},
        #{item.level24hH24,jdbcType=VARCHAR}, #{item.hjyy,jdbcType=VARCHAR}, #{item.hjyyQt,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

  <select id="listByModelId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_rule_condition_shuxin
    where model_id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getArea" resultType="java.lang.String">
    select distinct szdq
    from t_warning_rule_condition_shuxin
  </select>

  <select id="getByFxqbh" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from t_warning_rule_condition_shuxin
    where fxqbh = #{fxqbh,jdbcType=VARCHAR}
  </select>

  <update id="updateByFxqbh">
    <!--@mbg.generated-->
    update t_warning_rule_condition_shuxin
    <set>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=BIGINT},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="sjzt != null">
        sjzt = #{sjzt,jdbcType=VARCHAR},
      </if>
      <if test="fxffqmc != null">
        fxffqmc = #{fxffqmc,jdbcType=VARCHAR},
      </if>
      <if test="dcbh != null">
        dcbh = #{dcbh,jdbcType=VARCHAR},
      </if>
      <if test="szdq != null">
        szdq = #{szdq,jdbcType=VARCHAR},
      </if>
      <if test="xzc != null">
        xzc = #{xzc,jdbcType=VARCHAR},
      </if>
      <if test="fflx != null">
        fflx = #{fflx,jdbcType=VARCHAR},
      </if>
      <if test="ffqfw != null">
        ffqfw = #{ffqfw,jdbcType=LONGVARCHAR},
      </if>
      <if test="zzt != null">
        zzt = #{zzt,jdbcType=VARCHAR},
      </if>
      <if test="wdx != null">
        wdx = #{wdx,jdbcType=VARCHAR},
      </if>
      <if test="fxdj != null">
        fxdj = #{fxdj,jdbcType=VARCHAR},
      </if>
      <if test="syxhs != null">
        syxhs = #{syxhs,jdbcType=VARCHAR},
      </if>
      <if test="syxrs != null">
        syxrs = #{syxrs,jdbcType=VARCHAR},
      </if>
      <if test="czrk != null">
        czrk = #{czrk,jdbcType=VARCHAR},
      </if>
      <if test="syxcc != null">
        syxcc = #{syxcc,jdbcType=VARCHAR},
      </if>
      <if test="xjfgld != null">
        xjfgld = #{xjfgld,jdbcType=VARCHAR},
      </if>
      <if test="xjfglddh != null">
        xjfglddh = #{xjfglddh,jdbcType=VARCHAR},
      </if>
      <if test="xzfgld != null">
        xzfgld = #{xzfgld,jdbcType=VARCHAR},
      </if>
      <if test="xzfglddh != null">
        xzfglddh = #{xzfglddh,jdbcType=VARCHAR},
      </if>
      <if test="zrzyszrr != null">
        zrzyszrr = #{zrzyszrr,jdbcType=VARCHAR},
      </if>
      <if test="zrzyszrrdh != null">
        zrzyszrrdh = #{zrzyszrrdh,jdbcType=VARCHAR},
      </if>
      <if test="qcqfwgy != null">
        qcqfwgy = #{qcqfwgy,jdbcType=VARCHAR},
      </if>
      <if test="qcqfwgydh != null">
        qcqfwgydh = #{qcqfwgydh,jdbcType=VARCHAR},
      </if>
      <if test="sjly != null">
        sjly = #{sjly,jdbcType=VARCHAR},
      </if>
      <if test="brbqxmbh != null">
        brbqxmbh = #{brbqxmbh,jdbcType=VARCHAR},
      </if>
      <if test="zlgcxmbh != null">
        zlgcxmbh = #{zlgcxmbh,jdbcType=VARCHAR},
      </if>
      <if test="jcdbm != null">
        jcdbm = #{jcdbm,jdbcType=VARCHAR},
      </if>
      <if test="tbr != null">
        tbr = #{tbr,jdbcType=VARCHAR},
      </if>
      <if test="ffqcjsj != null">
        ffqcjsj = #{ffqcjsj,jdbcType=VARCHAR},
      </if>
      <if test="tjxzsj != null">
        tjxzsj = #{tjxzsj,jdbcType=VARCHAR},
      </if>
      <if test="hjtgsj != null">
        hjtgsj = #{hjtgsj,jdbcType=VARCHAR},
      </if>
      <if test="yzlx != null">
        yzlx = #{yzlx,jdbcType=VARCHAR},
      </if>
      <if test="zztgs != null">
        zztgs = #{zztgs,jdbcType=VARCHAR},
      </if>
      <if test="czths != null">
        czths = #{czths,jdbcType=VARCHAR},
      </if>
      <if test="czthjrs != null">
        czthjrs = #{czthjrs,jdbcType=VARCHAR},
      </if>
      <if test="level1hH != null">
        level_1h_h = #{level1hH,jdbcType=VARCHAR},
      </if>
      <if test="level1hC != null">
        level_1h_c = #{level1hC,jdbcType=VARCHAR},
      </if>
      <if test="level1hH1 != null">
        level_1h_h1 = #{level1hH1,jdbcType=VARCHAR},
      </if>
      <if test="level3hH != null">
        level_3h_h = #{level3hH,jdbcType=VARCHAR},
      </if>
      <if test="level3hC != null">
        level_3h_c = #{level3hC,jdbcType=VARCHAR},
      </if>
      <if test="level3hH3 != null">
        level_3h_h3 = #{level3hH3,jdbcType=VARCHAR},
      </if>
      <if test="level6hH != null">
        level_6h_h = #{level6hH,jdbcType=VARCHAR},
      </if>
      <if test="level6hC != null">
        level_6h_c = #{level6hC,jdbcType=VARCHAR},
      </if>
      <if test="level6hH6 != null">
        level_6h_h6 = #{level6hH6,jdbcType=VARCHAR},
      </if>
      <if test="level12hH != null">
        level_12h_h = #{level12hH,jdbcType=VARCHAR},
      </if>
      <if test="level12hC != null">
        level_12h_c = #{level12hC,jdbcType=VARCHAR},
      </if>
      <if test="level12hH12 != null">
        level_12h_h12 = #{level12hH12,jdbcType=VARCHAR},
      </if>
      <if test="level24hH != null">
        level_24h_h = #{level24hH,jdbcType=VARCHAR},
      </if>
      <if test="level24hC != null">
        level_24h_c = #{level24hC,jdbcType=VARCHAR},
      </if>
      <if test="level24hH24 != null">
        level_24h_h24 = #{level24hH24,jdbcType=VARCHAR},
      </if>
      <if test="hjyy != null">
        hjyy = #{hjyy,jdbcType=VARCHAR},
      </if>
      <if test="hjyyQt != null">
        hjyy_qt = #{hjyyQt,jdbcType=VARCHAR},
      </if>
    </set>
    where fxqbh = #{fxqbh,jdbcType=VARCHAR}
  </update>
</mapper>
