package com.ruoyi.framework.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/25
 * @Description 职员信息详情
 */
@Data
public class EmployeeInfo {

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改时间
     */
    private String gmtModified;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * 修改时间
     */
    private String gmtCreate;

    /**
     * 状态
     */
    private String status;

    /**
     * 用户编码
     */
    private String employeeCode;
    private String EmpGender;
    private String govEmpRemarks;
    private String govEmpAvatar;
    private String empJobLevelCode;
    private String empBudgetedPostCode;
    private String empPoliticalStatusCode;
    private List<Object> positionExtProperties;



}
