package com.ruoyi.quartz.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.framework.vo.OrganizationNodeInfo;
import com.ruoyi.framework.web.service.dtalk.DeptDtalkService;
import com.ruoyi.jiangshan.domain.DeviceInfo;
import com.ruoyi.jiangshan.domain.DeviceInfoHikvisionVideo;
import com.ruoyi.jiangshan.domain.WarningDepartmentInfo;
import com.ruoyi.jiangshan.mapper.DeviceInfoHikvisionVideoMapper;
import com.ruoyi.jiangshan.mapper.DeviceInfoMapper;
import com.ruoyi.jiangshan.mapper.WarningDepartmentInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Component("deviceVideoMonitorTask")
public class DeviceVideoMonitorTask {

    /**
     * API网关的后端服务上下文为：/portal
     */
    private static final String ARTEMIS_PATH = "/artemis";

    /**
     * 拉取视频监控数据
     */
    public void pullVideo() {
        try {
            String response = callPostApiGetOrgList();

            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String callPostApiGetOrgList() throws Exception {
        log.info("拉取视频监控信息, start");

        DeviceInfoHikvisionVideoMapper deviceInfoHikvisionVideoMapper = SpringUtils.getBean(DeviceInfoHikvisionVideoMapper.class);
        DeviceInfoMapper deviceInfoMapper = SpringUtils.getBean(DeviceInfoMapper.class);
        /**
         * https://ip:port/artemis/api/resource/v1/org/orgList
         * 通过查阅AI Cloud开放平台文档或网关门户的文档可以看到获取组织列表的接口定义,该接口为POST请求的Rest接口, 入参为JSON字符串，接口协议为https。
         * ArtemisHttpUtil工具类提供了doPostStringArtemis调用POST请求的方法，入参可传JSON字符串, 请阅读开发指南了解方法入参，没有的参数可传null
         */
        ArtemisConfig config = new ArtemisConfig();
        config.setHost("*************:1443"); // 代理API网关nginx服务器ip端口
        config.setAppKey("28285666");  // 秘钥appkey
        config.setAppSecret("C5WAACEkmZRRVyONgTND");// 秘钥appSecret
        final String getCamsApi = ARTEMIS_PATH + "/api/resource/v1/cameras";
        Map<String, String> paramMap = new HashMap<String, String>();// post请求Form表单参数
        paramMap.put("pageNo", "1");
        paramMap.put("pageSize", "999");
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };

        String response = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");

        log.info("拉取视频监控信息, response:{}", response);

        //插入原始数据
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSON.parseObject(response);

            Map<String, Object> dataMap = (Map<String, Object>) jsonObject.get("data");
            List<DeviceInfoHikvisionVideo> list = JSON.parseArray(JSON.toJSONString(dataMap.get("list")), DeviceInfoHikvisionVideo.class);

            List<DeviceInfoHikvisionVideo> videoList = deviceInfoHikvisionVideoMapper.listAll();

            List<String> videoCodeList = videoList.stream()
                    .map(DeviceInfoHikvisionVideo::getCameraIndexCode)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(list)) {
                List<DeviceInfo> deviceInfoList = Lists.newArrayList();
                List<DeviceInfoHikvisionVideo> needInsertList = Lists.newArrayList();
                for (DeviceInfoHikvisionVideo deviceInfoHikvisionVideo : list) {
                    if (videoCodeList.contains(deviceInfoHikvisionVideo.getCameraIndexCode())) {
                        continue;
                    }

                    DeviceInfo deviceInfo = new DeviceInfo();
                    deviceInfo.setDeviceThirdId(deviceInfoHikvisionVideo.getCameraIndexCode());
                    deviceInfo.setDeviceName(deviceInfoHikvisionVideo.getName());
                    deviceInfo.setDeviceNum(deviceInfoHikvisionVideo.getGbIndexCode());
                    deviceInfo.setLon(deviceInfoHikvisionVideo.getLongitude());
                    deviceInfo.setLat(deviceInfoHikvisionVideo.getLatitude());
                    deviceInfo.setDeviceAddress(deviceInfoHikvisionVideo.getInstallPlace());
                    deviceInfo.setDeviceType("视频监控");
                    deviceInfo.setCreateTime(new Date());
                    deviceInfo.setCreateBy("SYSTEM");

                    deviceInfoList.add(deviceInfo);

                    needInsertList.add(deviceInfoHikvisionVideo);
                }

                if (CollectionUtils.isNotEmpty(needInsertList)) {
                    deviceInfoHikvisionVideoMapper.batchInsert(needInsertList);
                }

                if (CollectionUtils.isNotEmpty(deviceInfoList)) {
                    deviceInfoMapper.batchInsert(deviceInfoList);
                }

                for (DeviceInfoHikvisionVideo deviceInfoHikvisionVideo : list) {
                    if (Objects.nonNull(deviceInfoHikvisionVideo.getStatus())) {
                        DeviceInfo deviceInfo = new DeviceInfo();
                        deviceInfo.setDeviceThirdId(deviceInfoHikvisionVideo.getCameraIndexCode());
                        deviceInfo.setDeviceStatus(deviceInfoHikvisionVideo.getStatus());

                        log.info("pullVideo, updateDeviceStatus, deviceThirdId:{}, deviceStatus:{}",
                                deviceInfo.getDeviceThirdId(), deviceInfo.getDeviceStatus());

                        deviceInfoMapper.updateStatusByDeviceThirdId(deviceInfo);
                    }
                }
            }
        }

        return response;
    }
}
