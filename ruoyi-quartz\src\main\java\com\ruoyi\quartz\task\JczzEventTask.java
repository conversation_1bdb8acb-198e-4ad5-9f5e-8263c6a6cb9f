package com.ruoyi.quartz.task;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.framework.vo.OrganizationNodeInfo;
import com.ruoyi.framework.web.service.dtalk.DeptDtalkService;
import com.ruoyi.jiangshan.domain.WarningDepartmentInfo;
import com.ruoyi.jiangshan.domain.WarningEvent;
import com.ruoyi.jiangshan.mapper.WarningDepartmentInfoMapper;
import com.ruoyi.jiangshan.mapper.WarningEventMapper;
import com.ruoyi.jiangshan.service.IWarningEventService;
import com.ruoyi.jiangshan.service.impl.WarningEventServiceImpl;
import com.ruoyi.jiangshan.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Slf4j
@Component("jczzEventTask")
public class JczzEventTask {

    private final String APPCODE = "ZQXVZZCHROSIPVTYSZHS";

    /**
     * 更新事件状态
     */
    public void updateWarningEventStatus() throws ExecutionException {
        IWarningEventService warningEventService = SpringUtils.getBean(WarningEventServiceImpl.class);

        log.info("JczzEventTask, updateWarningEventStatus, start");

        warningEventService.updateStatusByJczz();

        log.info("JczzEventTask, updateWarningEventStatus, end");
    }
}
