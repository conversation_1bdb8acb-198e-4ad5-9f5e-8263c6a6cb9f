package com.ruoyi.jiangshan.mapper;

import java.util.List;
import com.ruoyi.jiangshan.domain.CitysafetyReport;

/**
 * 城市安全体检报告管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
public interface CitysafetyReportMapper
{
    /**
     * 查询城市安全体检报告管理
     *
     * @param id 城市安全体检报告管理主键
     * @return 城市安全体检报告管理
     */
    public CitysafetyReport selectCitysafetyReportById(Long id);

    /**
     * 查询城市安全体检报告管理列表
     *
     * @param citysafetyReport 城市安全体检报告管理
     * @return 城市安全体检报告管理集合
     */
    public List<CitysafetyReport> selectCitysafetyReportList(CitysafetyReport citysafetyReport);

    /**
     * 新增城市安全体检报告管理
     *
     * @param citysafetyReport 城市安全体检报告管理
     * @return 结果
     */
    public int insertCitysafetyReport(CitysafetyReport citysafetyReport);

    /**
     * 修改城市安全体检报告管理
     *
     * @param citysafetyReport 城市安全体检报告管理
     * @return 结果
     */
    public int updateCitysafetyReport(CitysafetyReport citysafetyReport);

    /**
     * 删除城市安全体检报告管理
     *
     * @param id 城市安全体检报告管理主键
     * @return 结果
     */
    public int deleteCitysafetyReportById(Long id);

    /**
     * 批量删除城市安全体检报告管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCitysafetyReportByIds(Long[] ids);

    void batchInsert(List<CitysafetyReport> reportList);

    long countAll();

}
