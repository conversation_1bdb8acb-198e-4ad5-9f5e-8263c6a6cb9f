package com.ruoyi.jiangshan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.jiangshan.domain.BusinessFile;
import com.ruoyi.jiangshan.domain.WarningEventDepartment;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class WarningEventVerifyVO {

    private Long id;

    /**
     * 附件上传
     */
    private List<BusinessFile> fileList;

    /**
     * 核实情况/挂起原因
     */
    private String reason;

    /**
     * 部门、人员数据
     */
    private List<WarningEventDepartment> departmentList;

    /**
     * 预计完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedCompleteTime;

    /**
     * 当前操作用户id
     */
    private String operateUserId;
}
