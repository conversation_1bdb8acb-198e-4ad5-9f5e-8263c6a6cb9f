package com.ruoyi.jiangshan.mapper;

import com.ruoyi.jiangshan.domain.DeviceMonitorFxffq;
import java.util.List;

import com.ruoyi.jiangshan.vo.RiskPreventVO;
import org.apache.ibatis.annotations.Param;

public interface DeviceMonitorFxffqMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DeviceMonitorFxffq record);

    int insertSelective(DeviceMonitorFxffq record);

    DeviceMonitorFxffq selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DeviceMonitorFxffq record);

    int updateByPrimaryKey(DeviceMonitorFxffq record);

    int updateBatch(List<DeviceMonitorFxffq> list);

    int batchInsert(@Param("list") List<DeviceMonitorFxffq> list);

    List<DeviceMonitorFxffq> listAll(RiskPreventVO riskPreventVO);
}
